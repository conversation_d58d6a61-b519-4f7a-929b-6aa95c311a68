import { useState } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { Redirect } from 'wouter';
import Header from '@/components/layout/Header';
import Sidebar from '@/components/layout/Sidebar';
import KanbanBoard from '@/components/pipeline/KanbanBoard';
import AddDealModal from '@/components/pipeline/AddDealModal';
import FilterDealsModal from '@/components/pipeline/FilterDealsModal';

export default function PipelineView() {
  const { user, isLoading } = useAuth();
  const [isAddDealModalOpen, setIsAddDealModalOpen] = useState(false);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [activeFilter, setActiveFilter] = useState<{
    priority: string | null;
    assignedToUserId: number | null;
    titleSearch: string | null;
    tags: string[] | null;
    contactPhone: string | null;
    contactName: string | null;
  }>({
    priority: null,
    assignedToUserId: null,
    titleSearch: null,
    tags: null,
    contactPhone: null,
    contactName: null,
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  if (!user) {
    return <Redirect to="/auth" />;
  }

  const handleApplyFilter = (filter: {
    priority: string | null;
    assignedToUserId: number | null;
    titleSearch: string | null;
    tags: string[] | null;
    contactPhone: string | null;
    contactName: string | null;
  }) => {
    setActiveFilter(filter);
    setIsFilterModalOpen(false);
  };

  return (
    <div className="h-screen flex flex-col overflow-hidden font-sans text-gray-800">
      <Header />
      
      <div className="flex flex-1 overflow-hidden">
        <Sidebar />
        
        <div className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-6">
            <KanbanBoard
              onAddDeal={() => setIsAddDealModalOpen(true)}
              onOpenFilterModal={() => setIsFilterModalOpen(true)}
              activeFilter={activeFilter}
            />
            
            <AddDealModal
              isOpen={isAddDealModalOpen}
              onClose={() => setIsAddDealModalOpen(false)}
            />
            
            <FilterDealsModal
              isOpen={isFilterModalOpen}
              onClose={() => setIsFilterModalOpen(false)}
              currentFilter={activeFilter}
              onApplyFilter={handleApplyFilter}
            />
          </div>
        </div>
      </div>
    </div>
  );
}