import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface TwilioFormData {
  accountName: string;
  accountSid: string;
  authToken: string;
  conversationServiceSid: string;
  whatsappNumber: string;
}

export function WhatsAppTwilioForm({ isOpen, onClose, onSuccess }: Props) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  
  const [formData, setFormData] = useState<TwilioFormData>({
    accountName: '',
    accountSid: '',
    authToken: '',
    conversationServiceSid: '',
    whatsappNumber: 'whatsapp:+***********' // Default Twilio sandbox number
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const resetForm = () => {
    setFormData({
      accountName: '',
      accountSid: '',
      authToken: '',
      conversationServiceSid: '',
      whatsappNumber: 'whatsapp:+***********'
    });
    setIsSubmitting(false);
    setIsValidating(false);
  };

  const validateCredentials = async () => {
    if (!formData.accountSid || !formData.authToken || !formData.conversationServiceSid) {
      toast({
        title: "Validation Error",
        description: "Account SID, Auth Token, and Conversation Service SID are required for validation.",
        variant: "destructive"
      });
      return false;
    }

    setIsValidating(true);
    try {

      const auth = btoa(`${formData.accountSid}:${formData.authToken}`);
      const response = await fetch(`https://conversations.twilio.com/v1/Services/${formData.conversationServiceSid}`, {
        headers: {
          'Authorization': `Basic ${auth}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        toast({
          title: "Credentials Valid",
          description: `Successfully validated Twilio service: ${data.friendly_name || formData.conversationServiceSid}`,
        });
        return true;
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Invalid credentials');
      }
    } catch (error: any) {
      toast({
        title: "Validation Failed",
        description: error.message || "Failed to validate Twilio credentials.",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsValidating(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsSubmitting(true);
    
    try {

      const isValid = await validateCredentials();
      if (!isValid) {
        setIsSubmitting(false);
        return;
      }

      const response = await fetch('/api/channel-connections', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channelType: 'whatsapp_twilio',
          accountId: formData.conversationServiceSid,
          accountName: formData.accountName,
          connectionData: {
            accountSid: formData.accountSid,
            authToken: formData.authToken,
            conversationServiceSid: formData.conversationServiceSid,
            whatsappNumber: formData.whatsappNumber
          }
        })
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Twilio WhatsApp connection created successfully!",
        });
        
        resetForm();
        onSuccess();
        onClose();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create connection');
      }
    } catch (error: any) {
      console.error('Error connecting to Twilio WhatsApp:', error);
      toast({
        title: "Connection Error",
        description: error.message || "Failed to connect to Twilio WhatsApp",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <i className="ri-whatsapp-line text-green-500"></i>
            Connect Twilio WhatsApp
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="accountName">Connection Name</Label>
              <Input
                id="accountName"
                name="accountName"
                value={formData.accountName}
                onChange={handleInputChange}
                placeholder="My Twilio WhatsApp"
                required
              />
              <p className="text-sm text-gray-500">
                A friendly name for this connection
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="accountSid">Account SID</Label>
              <Input
                id="accountSid"
                name="accountSid"
                value={formData.accountSid}
                onChange={handleInputChange}
                placeholder="ACxxxxx..."
                required
              />
              <p className="text-sm text-gray-500">
                Your Twilio Account SID from the console
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="authToken">Auth Token</Label>
              <Input
                id="authToken"
                name="authToken"
                type="password"
                value={formData.authToken}
                onChange={handleInputChange}
                placeholder="Your auth token..."
                required
              />
              <p className="text-sm text-gray-500">
                Your Twilio Auth Token (keep this secure)
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="conversationServiceSid">Conversation Service SID</Label>
              <div className="flex gap-2">
                <Input
                  id="conversationServiceSid"
                  name="conversationServiceSid"
                  value={formData.conversationServiceSid}
                  onChange={handleInputChange}
                  placeholder="ISxxxxx..."
                  required
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={validateCredentials}
                  disabled={isValidating || !formData.accountSid || !formData.authToken || !formData.conversationServiceSid}
                  className="whitespace-nowrap"
                >
                  {isValidating ? 'Validating...' : 'Test'}
                </Button>
              </div>
              <p className="text-sm text-gray-500">
                Your Twilio Conversations Service SID
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="whatsappNumber">WhatsApp Number</Label>
              <Input
                id="whatsappNumber"
                name="whatsappNumber"
                value={formData.whatsappNumber}
                onChange={handleInputChange}
                placeholder="whatsapp:+***********"
                required
              />
              <p className="text-sm text-gray-500">
                Your Twilio WhatsApp number (sandbox or approved number)
              </p>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-start">
              <i className="ri-information-line text-blue-500 mr-2 mt-0.5"></i>
              <div>
                <p className="text-sm text-blue-700 font-medium">Twilio WhatsApp Setup</p>
                <p className="text-xs text-blue-600 mt-1">
                  1. Create a Twilio account and Conversations Service<br/>
                  2. Enable WhatsApp for your service<br/>
                  3. Configure webhook: https://yourdomain.com/api/webhooks/twilio-whatsapp<br/>
                  4. Use sandbox number for testing or get approved for production
                </p>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              variant="outline" 
              className="btn-brand-primary"
              disabled={isSubmitting || isValidating}
            >
              {isSubmitting ? 'Connecting...' : 'Connect'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
