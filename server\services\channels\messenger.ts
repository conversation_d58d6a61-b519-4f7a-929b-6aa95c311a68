import {storage} from '../../storage';
import {InsertMessage, InsertConversation, InsertContact} from '@shared/schema';
import {EventEmitter} from 'events';
import axios from 'axios';
import crypto from 'crypto';
import { logger } from '../../utils/logger';

interface MessengerConnectionData {
  pageId: string;
  appId: string;
  appSecret?: string;
  webhookUrl?: string;
  verifyToken?: string;
  pageInfo?: any;
}

interface ConnectionState {
  isActive: boolean;
  lastActivity: Date;
  errorCount: number;
  lastError: string | null;
  pageInfo: any | null;
}

const activeConnections = new Map<number, boolean>();
const connectionStates = new Map<number, ConnectionState>();

const eventEmitter = new EventEmitter();

// Increase max listeners to prevent memory leak warnings
eventEmitter.setMaxListeners(50);

// Register with monitor for debugging
import { eventEmitterMonitor } from '../../utils/event-emitter-monitor';
eventEmitterMonitor.register('messenger-service', eventEmitter);

const MESSENGER_API_VERSION = 'v22.0';
const MESSENGER_GRAPH_URL = 'https://graph.facebook.com';

/**
 * Get or create connection state
 */
function getConnectionState(connectionId: number): ConnectionState {
  if (!connectionStates.has(connectionId)) {
    connectionStates.set(connectionId, {
      isActive: false,
      lastActivity: new Date(),
      errorCount: 0,
      lastError: null,
      pageInfo: null
    });
  }
  return connectionStates.get(connectionId)!;
}

/**
 * Update connection activity
 */
function updateConnectionActivity(connectionId: number, success: boolean = true, error?: string) {
  const state = getConnectionState(connectionId);
  state.lastActivity = new Date();

  if (success) {
    state.errorCount = 0;
    state.lastError = null;
  } else {
    state.errorCount++;
    state.lastError = error || 'Unknown error';
  }
}

/**
 * Process a message through the flow executor
 * This function handles flow execution for Messenger messages
 */
async function processMessageThroughFlowExecutor(
  message: any,
  conversation: any,
  contact: any,
  channelConnection: any
): Promise<void> {
  try {
    const flowExecutorModule = await import('../flow-executor');
    const flowExecutor = flowExecutorModule.default;

    if (contact) {
      await flowExecutor.processIncomingMessage(message, conversation, contact, channelConnection);
    }
  } catch (error) {
    logger.error('messenger', 'Error in flow executor:', error);
    throw error;
  }
}

/**
 * Find or create a conversation for a contact and channel
 * @param connectionId The channel connection ID
 * @param recipientId The recipient's Facebook user ID (PSID)
 * @param companyId The company ID for multi-tenant security
 * @returns The conversation object
 */
async function findOrCreateConversation(connectionId: number, recipientId: string, companyId: number) {
  if (!companyId) {
    throw new Error('Company ID is required for multi-tenant security');
  }

  // Find or create contact
  let contact = await storage.getContactByPhone(recipientId, companyId);

  if (!contact) {
    const contactData: InsertContact = {
      companyId: companyId,
      name: `Messenger User ${recipientId}`,
      phone: recipientId,
      email: null,
      avatarUrl: null,
      identifier: recipientId,
      identifierType: 'messenger',
      source: 'messenger',
      notes: null
    };

    contact = await storage.createContact(contactData);
  }

  // Find or create conversation
  let conversation = await storage.getConversationByContactAndChannel(
    contact.id,
    connectionId
  );

  if (!conversation) {
    const conversationData: InsertConversation = {
      companyId: companyId,
      contactId: contact.id,
      channelId: connectionId,
      channelType: 'messenger',
      status: 'open',
      assignedToUserId: null,
      lastMessageAt: new Date(),
    };

    conversation = await storage.createConversation(conversationData);
  }

  return conversation;
}

/**
 * Enhanced error handling utilities for Messenger service
 */

interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 30000, // 30 seconds
  backoffMultiplier: 2
};

/**
 * Determine if an error is retryable
 */
function isRetryableError(error: any): boolean {
  if (axios.isAxiosError(error)) {
    const status = error.response?.status;

    // Retry on server errors (5xx) and rate limiting (429)
    if (status && (status >= 500 || status === 429)) {
      return true;
    }

    // Retry on network errors
    if (error.code === 'ECONNRESET' || error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return true;
    }

    // Retry on timeout errors
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      return true;
    }
  }

  return false;
}

/**
 * Calculate delay for exponential backoff with jitter
 */
function calculateRetryDelay(attempt: number, config: RetryConfig): number {
  const exponentialDelay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt);
  const jitter = Math.random() * 0.1 * exponentialDelay; // Add 10% jitter
  return Math.min(exponentialDelay + jitter, config.maxDelay);
}

/**
 * Execute a function with retry logic and exponential backoff
 */
async function executeWithRetry<T>(
  operation: () => Promise<T>,
  operationName: string,
  connectionId?: number,
  config: RetryConfig = DEFAULT_RETRY_CONFIG
): Promise<T> {
  let lastError: any;

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      const result = await operation();

      // Update connection health on success
      if (connectionId && attempt > 0) {
        updateConnectionActivity(connectionId, true);
        logger.info('messenger', `${operationName} succeeded after ${attempt} retries`);
      }

      return result;
    } catch (error) {
      lastError = error;

      // Update connection health on failure
      if (connectionId) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        updateConnectionActivity(connectionId, false, errorMessage);
      }

      // Don't retry if this is the last attempt or error is not retryable
      if (attempt === config.maxRetries || !isRetryableError(error)) {
        break;
      }

      const delay = calculateRetryDelay(attempt, config);
      logger.warn('messenger', `${operationName} failed (attempt ${attempt + 1}/${config.maxRetries + 1}), retrying in ${delay}ms:`, error instanceof Error ? error.message : error);

      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  // All retries exhausted
  logger.error('messenger', `${operationName} failed after ${config.maxRetries + 1} attempts:`, lastError);
  throw lastError;
}

/**
 * Get connection health status
 */
export function getConnectionHealth(connectionId: number): {
  isActive: boolean;
  lastActivity: Date;
  errorCount: number;
  lastError: string | null;
  healthScore: number;
} {
  const state = getConnectionState(connectionId);
  const isActive = activeConnections.has(connectionId);


  let healthScore = 100;
  if (state.errorCount > 0) {
    healthScore = Math.max(0, 100 - (state.errorCount * 10));
  }

  const timeSinceActivity = Date.now() - state.lastActivity.getTime();
  if (timeSinceActivity > 300000) { // 5 minutes
    healthScore = Math.max(0, healthScore - 20);
  }

  return {
    isActive,
    lastActivity: state.lastActivity,
    errorCount: state.errorCount,
    lastError: state.lastError,
    healthScore
  };
}

/**
 * Verify webhook signature for Messenger
 * @param payload The raw payload from the webhook
 * @param signature The signature from the X-Hub-Signature-256 header
 * @param appSecret The app secret for verification
 * @returns True if signature is valid
 */
export function verifyWebhookSignature(payload: string, signature: string, appSecret: string): boolean {
  try {
    const expectedSignature = crypto
      .createHmac('sha256', appSecret)
      .update(payload, 'utf8')
      .digest('hex');

    const providedSignature = signature.replace('sha256=', '');

    return crypto.timingSafeEqual(
      Buffer.from(expectedSignature, 'hex'),
      Buffer.from(providedSignature, 'hex')
    );
  } catch (error) {
    console.error('Error verifying Messenger webhook signature:', error);
    return false;
  }
}

/**
 * Verify webhook signature against any configured Messenger connection
 * @param payload The raw payload from the webhook
 * @param signature The signature from the X-Hub-Signature-256 header
 * @returns True if signature is valid for any connection
 */
async function verifyWebhookSignatureForAnyConnection(payload: string, signature: string): Promise<boolean> {
  try {
    const connections = await storage.getChannelConnections(null);
    const messengerConnections = connections.filter((conn: any) => conn.channelType === 'messenger');

    for (const connection of messengerConnections) {
      const connectionData = connection.connectionData as MessengerConnectionData;
      if (connectionData?.appSecret) {
        const isValid = verifyWebhookSignature(payload, signature, connectionData.appSecret);
        if (isValid) {
          console.log(`Webhook signature verified for connection ${connection.id}`);
          return true;
        }
      }
    }

    console.warn('Webhook signature could not be verified against any connection');
    return false;
  } catch (error) {
    console.error('Error verifying webhook signature:', error);
    return false;
  }
}

/**
 * Connect to Facebook Messenger Page
 * @param connectionId The ID of the channel connection
 * @param userId The user ID who owns this connection
 * @param companyId The company ID for multi-tenant security
 */
export async function connectToMessenger(connectionId: number, userId: number, companyId?: number): Promise<void> {
  try {
    logger.info('messenger', `Connecting to Messenger for connection ${connectionId} by user ${userId} (company: ${companyId})`);

    const connection = await storage.getChannelConnection(connectionId);
    if (!connection) {
      throw new Error(`Connection with ID ${connectionId} not found`);
    }

    // Multi-tenant security: Verify company scoping if provided
    if (companyId && connection.companyId && connection.companyId !== companyId) {
      logger.error('messenger', `Company ID mismatch: Connection ${connectionId} belongs to company ${connection.companyId}, but user is from company ${companyId}`);
      throw new Error(`Access denied: Connection does not belong to company ${companyId}`);
    }

    if (connection.userId !== userId) {
      logger.error('messenger', `Unauthorized access attempt to connection ${connectionId} by user ${userId}`);
      throw new Error('Unauthorized access to channel connection');
    }

    activeConnections.set(connectionId, true);
    updateConnectionActivity(connectionId, true);

    const accessToken = connection.accessToken;
    const connectionData = connection.connectionData as MessengerConnectionData;
    const pageId = connectionData?.pageId;

    if (!accessToken) {
      throw new Error('Messenger page access token is missing');
    }

    if (!pageId) {
      throw new Error('Messenger page ID is missing');
    }


    const validationResult = await validateConnectionConfiguration(connectionData, accessToken);
    if (!validationResult.success) {
      await storage.updateChannelConnectionStatus(connectionId, 'error');

      eventEmitter.emit('connectionError', {
        connectionId,
        error: validationResult.error
      });

      throw new Error(`Connection validation failed: ${validationResult.error}`);
    }


    await storage.updateChannelConnectionStatus(connectionId, 'connected');

    const updatedConnectionData = {
      ...(connectionData || {}),
      pageInfo: validationResult.pageInfo,
      lastConnectedAt: new Date().toISOString(),
      lastValidatedAt: new Date().toISOString()
    };

    await storage.updateChannelConnection(connectionId, {
      connectionData: updatedConnectionData
    });

    logger.info('messenger', `Connection ${connectionId} established successfully for page: ${validationResult.pageInfo?.name}`);

    eventEmitter.emit('connectionStatusUpdate', {
      connectionId,
      status: 'connected',
      pageInfo: validationResult.pageInfo
    });
  } catch (error: any) {
    logger.error('messenger', `Error connecting to Messenger connection ${connectionId}:`, error.message);
    await storage.updateChannelConnectionStatus(connectionId, 'error');
    throw error;
  }
}

/**
 * Disconnect from Messenger
 * @param connectionId The ID of the channel connection
 * @param userId The user ID who owns this connection
 */
export async function disconnectFromMessenger(connectionId: number, userId: number): Promise<boolean> {
  try {
    const connection = await storage.getChannelConnection(connectionId);
    if (!connection) {
      throw new Error(`Connection with ID ${connectionId} not found`);
    }


    if (connection.userId !== userId) {
      throw new Error('Unauthorized access to channel connection');
    }

    activeConnections.delete(connectionId);
    updateConnectionActivity(connectionId, true);

    await storage.updateChannelConnectionStatus(connectionId, 'disconnected');

    

    eventEmitter.emit('connectionStatusUpdate', {
      connectionId,
      status: 'disconnected'
    });

    return true;
  } catch (error: any) {
    console.error('Error disconnecting from Messenger:', error);
    return false;
  }
}

/**
 * Check if Messenger connection is active
 * @param connectionId The ID of the channel connection
 * @returns True if connection is active
 */
export function isMessengerConnectionActive(connectionId: number): boolean {
  return activeConnections.has(connectionId);
}

/**
 * Get all active Messenger connections
 * @returns Array of active connection IDs
 */
export function getActiveMessengerConnections(): number[] {
  return Array.from(activeConnections.keys());
}

/**
 * Enhanced send message function that integrates with the inbox system
 * @param connectionId The channel connection ID
 * @param userId The user ID sending the message
 * @param companyId The company ID for multi-tenant security
 * @param to The recipient Facebook user ID (PSID)
 * @param message The message content
 * @returns The saved message object
 */
export async function sendMessage(connectionId: number, userId: number, companyId: number, to: string, message: string) {
  try {
    if (!companyId) {
      throw new Error('Company ID is required for multi-tenant security');
    }

    const connection = await storage.getChannelConnection(connectionId);
    if (!connection) {
      throw new Error(`Connection with ID ${connectionId} not found`);
    }

    // Multi-tenant security: Verify company scoping
    if (connection.companyId !== companyId) {
      throw new Error(`Access denied: Connection does not belong to company ${companyId}`);
    }

    const accessToken = connection.accessToken;
    const connectionData = connection.connectionData as MessengerConnectionData;
    const pageId = connectionData?.pageId;

    if (!accessToken) {
      throw new Error('Messenger page access token is missing');
    }

    if (!pageId) {
      throw new Error('Messenger page ID is missing');
    }

    // Send via Messenger API
    const response = await axios.post(
      `${MESSENGER_GRAPH_URL}/${MESSENGER_API_VERSION}/me/messages`,
      {
        recipient: {
          id: to
        },
        message: {
          text: message
        }
      },
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      }
    );

    if (response.status === 200 && response.data) {
      const messageId = response.data.message_id;

      // Find or create conversation
      const conversation = await findOrCreateConversation(connectionId, to, companyId);

      // Save message to database
      const savedMessage = await storage.createMessage({
        conversationId: conversation.id,
        direction: 'outbound',
        type: 'text',
        content: message,
        senderId: userId,
        senderType: 'user',
        isFromBot: false,
        externalId: messageId || `messenger-${Date.now()}`,
        metadata: JSON.stringify({
          messenger_message_id: messageId,
          timestamp: new Date().toISOString(),
          page_id: pageId,
          recipient_id: to
        })
      });

      // Update conversation
      await storage.updateConversation(conversation.id, {
        lastMessageAt: new Date()
      });

      // Get the full conversation data
      const fullConversation = await storage.getConversation(conversation.id);

      // Emit event for real-time updates
      eventEmitter.emit('messageSent', {
        message: savedMessage,
        conversation: fullConversation
      });

      return savedMessage;
    } else {
      throw new Error('Failed to send message: Unknown error');
    }
  } catch (error: any) {
    logger.error('messenger', 'Error sending Messenger message:', error.response?.data || error.message);
    throw new Error(error.response?.data?.error?.message || error.message);
  }
}

/**
 * Send a text message via Messenger (legacy function for backward compatibility)
 * @param connectionId The ID of the channel connection
 * @param to The recipient Facebook user ID (PSID)
 * @param message The message text to send
 * @returns Promise with send result
 */
export async function sendMessengerMessage(
  connectionId: number,
  to: string,
  message: string,
  userId?: number
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    return await executeWithRetry(
      async () => {
        const connection = await storage.getChannelConnection(connectionId);
        if (!connection) {
          throw new Error(`Connection with ID ${connectionId} not found`);
        }

        if (userId && connection.userId !== userId) {
          throw new Error('Unauthorized access to channel connection');
        }

        const accessToken = connection.accessToken;
        const connectionData = connection.connectionData as MessengerConnectionData;
        const pageId = connectionData?.pageId;

        if (!accessToken) {
          throw new Error('Messenger page access token is missing');
        }

        if (!pageId) {
          throw new Error('Messenger page ID is missing');
        }

        const response = await axios.post(
          `${MESSENGER_GRAPH_URL}/${MESSENGER_API_VERSION}/me/messages`,
          {
            recipient: {
              id: to
            },
            message: {
              text: message
            }
          },
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            },
            timeout: 10000 // 10 second timeout
          }
        );

        if (response.status === 200 && response.data) {
          const messageId = response.data.message_id;
          return {
            success: true,
            messageId
          };
        } else {
          throw new Error('Failed to send message: Unknown error');
        }
      },
      'Messenger message sending',
      connectionId
    );
  } catch (error: any) {
    logger.error('messenger', `Error sending message via connection ${connectionId}:`, error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message
    };
  }
}

// Note: The enhanced sendMessage function is defined above for unified inbox integration

/**
 * Send a message with quick replies via Messenger
 * @param connectionId The ID of the channel connection
 * @param to The recipient Facebook user ID (PSID)
 * @param message The message text to send
 * @param quickReplies Array of quick reply options
 * @returns Promise with send result
 */
export async function sendMessengerQuickReply(
  connectionId: number,
  to: string,
  message: string,
  quickReplies: Array<{ title: string; payload: string }>
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    const connection = await storage.getChannelConnection(connectionId);
    if (!connection) {
      throw new Error(`Connection with ID ${connectionId} not found`);
    }

    const accessToken = connection.accessToken;

    if (!accessToken) {
      throw new Error('Messenger page access token is missing');
    }

    const formattedQuickReplies = quickReplies.map(reply => ({
      content_type: 'text',
      title: reply.title,
      payload: reply.payload
    }));

    const response = await axios.post(
      `${MESSENGER_GRAPH_URL}/${MESSENGER_API_VERSION}/me/messages`,
      {
        recipient: {
          id: to
        },
        message: {
          text: message,
          quick_replies: formattedQuickReplies
        }
      },
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.status === 200 && response.data) {
      const messageId = response.data.message_id;

      return {
        success: true,
        messageId
      };
    } else {
      return {
        success: false,
        error: 'Failed to send quick reply message: Unknown error'
      };
    }
  } catch (error: any) {
    console.error('Error sending Messenger quick reply:', error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message
    };
  }
}

/**
 * Send a media message via Messenger
 * @param connectionId The ID of the channel connection
 * @param to The recipient Facebook user ID (PSID)
 * @param mediaUrl The URL of the media to send
 * @param mediaType The type of media (image, video, audio, file)
 * @returns Promise with send result
 */
export async function sendMessengerMediaMessage(
  connectionId: number,
  to: string,
  mediaUrl: string,
  mediaType: 'image' | 'video' | 'audio' | 'file'
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    const connection = await storage.getChannelConnection(connectionId);
    if (!connection) {
      throw new Error(`Connection with ID ${connectionId} not found`);
    }

    const accessToken = connection.accessToken;

    if (!accessToken) {
      throw new Error('Messenger page access token is missing');
    }

    const mediaRequest: any = {
      recipient: {
        id: to
      },
      message: {
        attachment: {
          type: mediaType,
          payload: {
            url: mediaUrl,
            is_reusable: true
          }
        }
      }
    };

    const response = await axios.post(
      `${MESSENGER_GRAPH_URL}/${MESSENGER_API_VERSION}/me/messages`,
      mediaRequest,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.status === 200 && response.data) {
      const messageId = response.data.message_id;

      return {
        success: true,
        messageId
      };
    } else {
      return {
        success: false,
        error: 'Failed to send media message: Unknown error'
      };
    }
  } catch (error: any) {
    console.error('Error sending Messenger media message:', error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message
    };
  }
}

/**
 * Send a button template message via Messenger
 * @param connectionId The ID of the channel connection
 * @param to The recipient Facebook user ID (PSID)
 * @param text The message text
 * @param buttons Array of button options
 * @returns Promise with send result
 */
export async function sendMessengerButtonTemplate(
  connectionId: number,
  to: string,
  text: string,
  buttons: Array<{ type: string; title: string; payload?: string; url?: string }>
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    const connection = await storage.getChannelConnection(connectionId);
    if (!connection) {
      throw new Error(`Connection with ID ${connectionId} not found`);
    }

    const accessToken = connection.accessToken;

    if (!accessToken) {
      throw new Error('Messenger page access token is missing');
    }

    const response = await axios.post(
      `${MESSENGER_GRAPH_URL}/${MESSENGER_API_VERSION}/me/messages`,
      {
        recipient: {
          id: to
        },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: 'button',
              text: text,
              buttons: buttons
            }
          }
        }
      },
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.status === 200 && response.data) {
      const messageId = response.data.message_id;

      return {
        success: true,
        messageId
      };
    } else {
      return {
        success: false,
        error: 'Failed to send button template: Unknown error'
      };
    }
  } catch (error: any) {
    console.error('Error sending Messenger button template:', error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message
    };
  }
}

/**
 * Process incoming webhook from Messenger
 * @param body The webhook payload
 * @param signature The webhook signature for verification
 * @param companyId Optional company ID for multi-tenant security
 */
export async function processWebhook(body: any, signature?: string, companyId?: number): Promise<void> {
  try {
    console.log('Processing Messenger webhook:', {
      hasSignature: !!signature,
      bodyType: typeof body,
      companyId: companyId || 'not_specified'
    });


    if (body['hub.mode'] === 'subscribe' && body['hub.verify_token']) {
      console.log('Webhook verification request received');
      return;
    }


    if (signature && typeof body === 'string') {
      const isValidSignature = await verifyWebhookSignatureForAnyConnection(body, signature);
      if (!isValidSignature) {
        console.error('Invalid webhook signature - rejecting request');
        throw new Error('Invalid webhook signature');
      }
    }

    if (body.entry && Array.isArray(body.entry)) {
      for (const entry of body.entry) {
        if (entry.messaging && Array.isArray(entry.messaging)) {
          for (const messagingEvent of entry.messaging) {
            await handleIncomingMessengerMessage(messagingEvent, companyId);
          }
        }
      }
    }
  } catch (error) {
    console.error('Error processing Messenger webhook:', error);
    throw error; // Re-throw to ensure proper HTTP error response
  }
}

/**
 * Handle an incoming message from Messenger webhook
 * @param messagingEvent The messaging event from the webhook
 * @param companyId Optional company ID for multi-tenant security
 */
async function handleIncomingMessengerMessage(messagingEvent: any, companyId?: number): Promise<void> {
  let connection: any = null;

  try {
    logger.debug('messenger', 'Processing incoming Messenger message event');

    const senderId = messagingEvent.sender?.id;
    const recipientId = messagingEvent.recipient?.id;
    const message = messagingEvent.message;
    const postback = messagingEvent.postback;

    if (!senderId || !recipientId) {
      logger.warn('messenger', 'Missing sender or recipient ID in message event');
      return;
    }

    // Use company-scoped connection lookup for better security
    const connections = await storage.getChannelConnectionsByType('messenger');
    connection = connections.find((conn: any) => {
      const connectionData = conn.connectionData as MessengerConnectionData;
      return connectionData?.pageId === recipientId;
    });

    if (!connection) {
      logger.warn('messenger', `No Messenger connection found for page ID: ${recipientId}`);
      return;
    }

    // Multi-tenant security: Verify company scoping if provided
    if (companyId && connection.companyId !== companyId) {
      logger.warn('messenger', `Company ID mismatch for connection ${connection.id}: expected ${companyId}, got ${connection.companyId}`);
      return;
    }

    if (!connection.companyId) {
      logger.error('messenger', `Connection ${connection.id} missing companyId - security violation`);
      return;
    }

    let contact = await storage.getContactByPhone(senderId, connection.companyId);
    if (!contact) {
      const insertContactData: InsertContact = {
        companyId: connection.companyId,
        phone: senderId,
        name: `Messenger User ${senderId}`,
        source: 'messenger',
        identifier: senderId,
        identifierType: 'messenger'
      };

      contact = await storage.createContact(insertContactData);
      
    }

    let conversation = await storage.getConversationByContactAndChannel(
      contact.id,
      connection.id
    );

    if (!conversation) {
      const insertConversationData: InsertConversation = {
        companyId: connection.companyId,
        contactId: contact.id,
        channelType: 'messenger',
        channelId: connection.id,
        status: 'open',
        lastMessageAt: new Date()
      };

      conversation = await storage.createConversation(insertConversationData);
      
    }

    let messageText = '';
    let messageType = 'text';
    let mediaUrl = null;
    let channelMessageId = null;

    if (message) {
      channelMessageId = message.mid;

      if (message.text) {
        messageText = message.text;
        messageType = 'text';
      } else if (message.attachments && message.attachments.length > 0) {
        const attachment = message.attachments[0];
        messageType = attachment.type || 'media';
        mediaUrl = attachment.payload?.url;
        messageText = `[${messageType.toUpperCase()}]`;
      } else if (message.quick_reply) {
        messageText = message.quick_reply.payload;
        messageType = 'quick_reply';
      }
    } else if (postback) {
      messageText = postback.payload || postback.title;
      messageType = 'postback';
      channelMessageId = `postback_${Date.now()}`;
    }

    if (!messageText) {
      
      return;
    }

    const insertMessageData: InsertMessage = {
      conversationId: conversation.id,
      content: messageText,
      type: messageType,
      direction: 'inbound',
      status: 'delivered',
      externalId: channelMessageId,
      mediaUrl: mediaUrl,
      metadata: JSON.stringify({
        channelType: 'messenger',
        timestamp: messagingEvent.timestamp || Date.now(),
        senderId: senderId,
        recipientId: recipientId,
        pageId: recipientId
      })
    };

    const savedMessage = await storage.createMessage(insertMessageData);
    updateConnectionActivity(connection.id, true);

    // Update conversation with latest message timestamp
    await storage.updateConversation(conversation.id, {
      lastMessageAt: new Date(),
      status: 'active'
    });

    // Get updated conversation data
    const conversationWithContact = await storage.getConversation(conversation.id);

    logger.info('messenger', `Message received from ${senderId} via connection ${connection.id}`);

    // Broadcast real-time updates via WebSocket
    if ((global as any).broadcastToAllClients) {
      (global as any).broadcastToAllClients({
        type: 'newMessage',
        data: savedMessage
      });

      (global as any).broadcastToAllClients({
        type: 'conversationUpdated',
        data: conversationWithContact
      });

      // Update unread count
      try {
        const unreadCount = await storage.getUnreadCount(conversation.id);
        (global as any).broadcastToAllClients({
          type: 'unreadCountUpdated',
          data: {
            conversationId: conversation.id,
            unreadCount
          }
        });
      } catch (error) {
        logger.error('messenger', 'Error broadcasting unread count update:', error);
      }
    }

    // Emit event for other services
    eventEmitter.emit('messageReceived', {
      message: savedMessage,
      conversation: conversationWithContact,
      contact: contact,
      connection: connection
    });


    try {
      if (connection.companyId && !conversation.botDisabled) {
        logger.debug('messenger', `Message eligible for flow processing: conversation ${conversation.id}`);

        // Process message through flow executor
        await processMessageThroughFlowExecutor(savedMessage, conversation, contact, connection);
      }
    } catch (flowError: any) {
      logger.error('messenger', `Error processing message through flows:`, flowError.message);
    }

  } catch (error: any) {
    logger.error('messenger', `Error handling incoming Messenger message:`, error.message);
    if (connection?.id) {
      updateConnectionActivity(connection.id, false, error.message);
    }
  }
}

/**
 * Subscribe to Messenger events
 * @param eventType The type of event to subscribe to
 * @param callback The callback function to call when the event occurs
 * @returns A function to unsubscribe from the event
 */
export function subscribeToMessengerEvents(
  eventType: 'connectionStatusUpdate' | 'connectionError' | 'messageReceived' | 'messageSent',
  callback: (data: any) => void
): () => void {
  eventEmitter.on(eventType, callback);
  return () => eventEmitter.off(eventType, callback);
}

/**
 * Test webhook configuration
 * @param webhookUrl The webhook URL to test
 * @param verifyToken The verify token to test
 * @returns Promise<boolean> indicating success
 */
export async function testWebhookConfiguration(
  webhookUrl: string,
  verifyToken: string
): Promise<{ success: boolean; error?: string }> {
  try {

    const url = new URL(webhookUrl);
    if (url.protocol !== 'https:') {
      return { success: false, error: 'Webhook URL must use HTTPS' };
    }

    if (!url.pathname.includes('/api/webhooks/messenger')) {
      return { success: false, error: 'Webhook URL must point to /api/webhooks/messenger endpoint' };
    }


    const testParams = new URLSearchParams({
      'hub.mode': 'subscribe',
      'hub.verify_token': verifyToken,
      'hub.challenge': 'test_challenge_' + Date.now()
    });

    const testResponse = await axios.get(`${webhookUrl}?${testParams.toString()}`, {
      timeout: 10000,
      validateStatus: (status) => status === 200 || status === 403
    });

    if (testResponse.status === 200) {
      return { success: true };
    } else {
      return { success: false, error: 'Webhook verification failed - check verify token configuration' };
    }
  } catch (error: any) {
    console.error('Error testing webhook configuration:', error.message);
    return {
      success: false,
      error: error.code === 'ECONNREFUSED'
        ? 'Could not connect to webhook URL - check if server is accessible'
        : error.message || 'Webhook test failed'
    };
  }
}

/**
 * Validate Messenger connection configuration
 * @param connectionData The connection configuration to validate
 * @returns Promise with validation result
 */
export async function validateConnectionConfiguration(
  connectionData: MessengerConnectionData,
  accessToken: string
): Promise<{ success: boolean; error?: string; pageInfo?: any }> {
  try {

    const response = await axios.get(
      `${MESSENGER_GRAPH_URL}/${MESSENGER_API_VERSION}/${connectionData.pageId}`,
      {
        params: {
          fields: 'id,name,picture,category,followers_count,access_token'
        },
        headers: {
          'Authorization': `Bearer ${accessToken}`
        },
        timeout: 10000
      }
    );

    if (response.status === 200 && response.data) {
      return {
        success: true,
        pageInfo: response.data
      };
    } else {
      return {
        success: false,
        error: 'Failed to validate page access'
      };
    }
  } catch (error: any) {
    console.error('Error validating Messenger connection:', error.response?.data || error.message);

    if (error.response?.status === 403) {
      return {
        success: false,
        error: 'Access denied - check page access token permissions'
      };
    } else if (error.response?.status === 404) {
      return {
        success: false,
        error: 'Page not found - check page ID'
      };
    } else {
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message || 'Connection validation failed'
      };
    }
  }
}

/**
 * Set up webhook subscription for Messenger
 * @param connectionId The ID of the channel connection
 * @param callbackUrl The webhook callback URL
 * @param verifyToken The webhook verify token
 * @returns Promise<boolean> indicating success
 */
export async function setupWebhookSubscription(
  connectionId: number,
  callbackUrl: string,
  verifyToken: string
): Promise<boolean> {
  try {
    const connection = await storage.getChannelConnection(connectionId);
    if (!connection) {
      throw new Error(`Connection with ID ${connectionId} not found`);
    }

    const accessToken = connection.accessToken;
    const connectionData = connection.connectionData as MessengerConnectionData;
    const appId = connectionData?.appId;

    if (!accessToken) {
      throw new Error('Messenger page access token is missing');
    }

    if (!appId) {
      throw new Error('Messenger app ID is missing');
    }

    const response = await axios.post(
      `${MESSENGER_GRAPH_URL}/${MESSENGER_API_VERSION}/${appId}/subscriptions`,
      {
        object: 'page',
        callback_url: callbackUrl,
        verify_token: verifyToken,
        fields: [
          'messages',
          'messaging_postbacks',
          'messaging_optins',
          'message_deliveries',
          'message_reads'
        ]
      },
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return response.status === 200;
  } catch (error: any) {
    console.error('Error setting up Messenger webhook subscription:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Set up persistent menu for Messenger page
 * @param connectionId The ID of the channel connection
 * @param menuItems Array of menu items
 * @returns Promise<boolean> indicating success
 */
export async function setupPersistentMenu(
  connectionId: number,
  menuItems: Array<{ type: string; title: string; payload?: string; url?: string }>
): Promise<boolean> {
  try {
    const connection = await storage.getChannelConnection(connectionId);
    if (!connection) {
      throw new Error(`Connection with ID ${connectionId} not found`);
    }

    const accessToken = connection.accessToken;

    if (!accessToken) {
      throw new Error('Messenger page access token is missing');
    }

    const response = await axios.post(
      `${MESSENGER_GRAPH_URL}/${MESSENGER_API_VERSION}/me/messenger_profile`,
      {
        persistent_menu: [
          {
            locale: 'default',
            composer_input_disabled: false,
            call_to_actions: menuItems
          }
        ]
      },
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return response.status === 200;
  } catch (error: any) {
    console.error('Error setting up Messenger persistent menu:', error.response?.data || error.message);
    return false;
  }
}

export default {
  connect: connectToMessenger,
  disconnect: disconnectFromMessenger,
  sendMessage: sendMessengerMessage,
  sendQuickReply: sendMessengerQuickReply,
  sendMedia: sendMessengerMediaMessage,
  sendButtonTemplate: sendMessengerButtonTemplate,
  isActive: isMessengerConnectionActive,
  getActiveConnections: getActiveMessengerConnections,
  subscribeToEvents: subscribeToMessengerEvents,
  processWebhook: processWebhook,
  setupWebhook: setupWebhookSubscription,
  setupPersistentMenu: setupPersistentMenu,
  verifyWebhookSignature,
  testWebhookConfiguration,
  validateConnectionConfiguration,
  getConnectionHealth
};
