import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, Loader2, XCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export default function PaymentSuccessPage() {
  const [_, navigate] = useLocation();
  const { toast } = useToast();
  const [isVerifying, setIsVerifying] = useState(true);
  const [verificationSuccess, setVerificationSuccess] = useState(false);
  const [paymentDetails, setPaymentDetails] = useState<any>(null);

  const urlParams = new URLSearchParams(window.location.search);
  const source = urlParams.get('source');
  const transaction_id = urlParams.get('transaction_id');
  const session_id = urlParams.get('session_id');
  // Moyasar can send payment ID with different parameter names
  const payment_id = urlParams.get('id') || urlParams.get('payment_id') || urlParams.get('paymentId');
  const status = urlParams.get('status');
  const message = urlParams.get('message');



  const verifyPaymentMutation = useMutation({
    mutationFn: async () => {
      if (!transaction_id) return null;

      const requestBody: any = {
        transactionId: transaction_id,
        source: source || "stripe"
      };

      if (source === 'moyasar' && payment_id) {
        requestBody.paymentId = payment_id;
      } else if (session_id) {
        requestBody.session_id = session_id;
      }

      const res = await apiRequest("POST", "/api/payment/verify", requestBody);

      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.error || "Failed to verify payment");
      }

      return res.json();
    },
    onSuccess: (data) => {
      setVerificationSuccess(true);
      setIsVerifying(false);
      setPaymentDetails(data);
      toast({
        title: "Payment Successful",
        description: "Your subscription has been activated successfully.",
      });
    },
    onError: (error: any) => {
      setVerificationSuccess(false);
      setIsVerifying(false);
      toast({
        title: "Payment Verification Failed",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  useEffect(() => {
    if (source === 'moyasar' && status && payment_id) {
      // Moyasar can send different success statuses: 'paid', 'captured', etc.
      const successStatuses = ['paid', 'captured', 'authorized'];
      const isSuccessful = successStatuses.includes(status.toLowerCase());

      if (isSuccessful) {
        setVerificationSuccess(true);
        setIsVerifying(false);
        setPaymentDetails({
          paymentId: payment_id,
          status: status,
          message: message,
          source: source
        });
        toast({
          title: "Payment Successful",
          description: message || "Your payment has been processed successfully.",
        });

        if (transaction_id) {
          verifyPaymentMutation.mutate();
        }
      } else {
        setVerificationSuccess(false);
        setIsVerifying(false);
        setPaymentDetails({
          paymentId: payment_id,
          status: status,
          message: message,
          source: source
        });
        toast({
          title: "Payment Failed",
          description: message || "Your payment could not be processed.",
          variant: "destructive"
        });
      }
    } else if (source === 'stripe' && session_id) {
      verifyPaymentMutation.mutate();
    } else if (transaction_id) {
      verifyPaymentMutation.mutate();
    } else {
      setVerificationSuccess(false);
      setIsVerifying(false);
      toast({
        title: "Payment Verification Failed",
        description: "Missing payment information. Please contact support if you completed a payment.",
        variant: "destructive"
      });
    }
  }, [transaction_id, source, status, payment_id, message, session_id]);

  return (
    <div className="container max-w-md py-12">
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">
            {isVerifying
              ? "Payment Processing"
              : (verificationSuccess
                ? "Payment Successful"
                : (paymentDetails?.status === 'failed' || status === 'failed'
                  ? "Payment Failed"
                  : "Payment Verification Failed"))}
          </CardTitle>
          <CardDescription>
            {isVerifying
              ? "We're verifying your payment..."
              : (verificationSuccess
                ? "Your subscription has been activated successfully."
                : (paymentDetails?.status === 'failed' || status === 'failed'
                  ? (paymentDetails?.message || message || "Your payment could not be processed. Please try again.")
                  : "We couldn't verify your payment. Please contact support."))}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex justify-center py-6">
            {isVerifying ? (
              <Loader2 className="h-16 w-16 animate-spin text-primary" />
            ) : (
              verificationSuccess ? (
                <CheckCircle className="h-16 w-16 text-green-500" />
              ) : (
                <XCircle className="h-16 w-16 text-red-500" />
              )
            )}
          </div>

          {paymentDetails && (
            <div className="space-y-3 text-sm">
              <div className="border-t pt-4">
                <h3 className="font-medium text-center mb-3">Payment Details</h3>
                <div className="space-y-2">
                  {paymentDetails.paymentId && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Payment ID:</span>
                      <span className="font-mono text-xs">{paymentDetails.paymentId}</span>
                    </div>
                  )}
                  {transaction_id && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Transaction ID:</span>
                      <span className="font-mono text-xs">{transaction_id}</span>
                    </div>
                  )}
                  {paymentDetails.source && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Payment Method:</span>
                      <span className="capitalize">{paymentDetails.source}</span>
                    </div>
                  )}
                  {paymentDetails.status && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Status:</span>
                      <span className={`capitalize ${
                        paymentDetails.status === 'paid' ? 'text-green-600' :
                        paymentDetails.status === 'failed' ? 'text-red-600' :
                        'text-amber-600'
                      }`}>
                        {paymentDetails.status}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-center gap-4">
          {!verificationSuccess && !isVerifying && (
            <Button variant="outline" onClick={() => navigate("/settings")} className="btn-brand-primary">
              Try Again
            </Button>
          )}
          <Button variant="outline" onClick={() => navigate("/settings")} disabled={isVerifying}>
            Return to Settings
          </Button>
          {verificationSuccess && (
            <Button variant="outline" className="btn-brand-primary" onClick={() => navigate("/inbox")} disabled={isVerifying}>
              Go to Inbox
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
