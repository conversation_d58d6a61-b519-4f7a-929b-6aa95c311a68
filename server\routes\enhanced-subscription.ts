import { Router } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { db } from '../db';
import {
  companies,
  subscriptionEvents
} from '@shared/schema';
import { eq, desc, count } from 'drizzle-orm';
import { subscriptionManager } from '../services/subscription-manager';
import { subscriptionScheduler } from '../services/subscription-scheduler';
import { SubscriptionWebhookHandler } from '../services/subscription-webhooks';
import { prorationService } from '../services/proration-service';
import { gracePeriodService } from '../services/grace-period-service';
import { usageTrackingService } from '../services/usage-tracking-service';
import { dunningService } from '../services/dunning-service';
import { subscriptionPausingService } from '../services/subscription-pausing-service';
import { planDowngradeService } from '../services/plan-downgrade-service';
import { ensureAuthenticated, ensureSuperAdmin } from '../middleware';
import { logger } from '../utils/logger';

const router = Router();

// Validation schemas
const enableRenewalSchema = z.object({
  paymentMethodId: z.string().optional()
});

const planChangeSchema = z.object({
  planId: z.number().int().positive(),
  effectiveDate: z.string().datetime().optional(),
  prorationMode: z.enum(['immediate', 'next_cycle']).default('immediate'),
  reason: z.string().optional()
});

const pauseSubscriptionSchema = z.object({
  pauseUntil: z.string().datetime().optional(),
  reason: z.string().optional()
});

/**
 * Get comprehensive subscription status
 */
router.get('/status', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const status = await subscriptionManager.getSubscriptionStatus(companyId);
    res.json(status);

  } catch (error) {
    logger.error('enhanced-subscription', 'Error getting subscription status:', error);
    res.status(500).json({ error: 'Failed to get subscription status' });
  }
});

/**
 * Enable automatic renewal
 */
router.post('/enable-renewal', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const validation = enableRenewalSchema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({ 
        error: 'Invalid request data',
        details: validation.error.errors
      });
    }

    const { paymentMethodId } = validation.data;

    // Get Stripe configuration
    const stripeSettings = await storage.getAppSetting('payment_stripe');
    if (!stripeSettings?.value) {
      return res.status(400).json({ error: 'Stripe not configured' });
    }

    const stripeConfig = stripeSettings.value as any;
    subscriptionManager['stripe'] = require('stripe')(stripeConfig.secretKey, {
      apiVersion: '2025-04-30.basil'
    });

    const result = await subscriptionManager.enableAutomaticRenewal(companyId, paymentMethodId);

    if (result.success) {
      res.json({
        success: true,
        message: 'Automatic renewal enabled successfully',
        subscriptionId: result.subscriptionId,
        nextBillingDate: result.nextBillingDate
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('enhanced-subscription', 'Error enabling automatic renewal:', error);
    res.status(500).json({ error: 'Failed to enable automatic renewal' });
  }
});

/**
 * Disable automatic renewal
 */
router.post('/disable-renewal', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const result = await subscriptionManager.disableAutomaticRenewal(companyId);

    if (result.success) {
      res.json({
        success: true,
        message: 'Automatic renewal disabled successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('enhanced-subscription', 'Error disabling automatic renewal:', error);
    res.status(500).json({ error: 'Failed to disable automatic renewal' });
  }
});

/**
 * Calculate proration for plan change
 */
router.post('/calculate-proration', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const { planId, effectiveDate } = req.body;

    if (!planId) {
      return res.status(400).json({ error: 'Plan ID required' });
    }

    // Verify the target plan exists
    const targetPlan = await storage.getPlan(planId);
    if (!targetPlan) {
      return res.status(404).json({ error: 'Target plan not found' });
    }

    // Calculate proration
    const prorationCalculation = await prorationService.calculateProration(
      companyId,
      planId,
      effectiveDate ? new Date(effectiveDate) : new Date()
    );

    res.json({
      success: true,
      proration: prorationCalculation,
      targetPlan: {
        id: targetPlan.id,
        name: targetPlan.name,
        price: targetPlan.price
      }
    });

  } catch (error) {
    logger.error('enhanced-subscription', 'Error calculating proration:', error);
    res.status(500).json({ error: 'Failed to calculate proration' });
  }
});

/**
 * Change subscription plan
 */
router.post('/change-plan', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const validation = planChangeSchema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({
        error: 'Invalid request data',
        details: validation.error.errors
      });
    }

    const { planId, effectiveDate, prorationMode, reason } = validation.data;

    // Verify the target plan exists
    const targetPlan = await storage.getPlan(planId);
    if (!targetPlan) {
      return res.status(404).json({ error: 'Target plan not found' });
    }

    // Check if company is already on this plan
    const company = await storage.getCompany(companyId);
    if (!company) {
      return res.status(404).json({ error: 'Company not found' });
    }

    if (company.planId === planId) {
      return res.status(400).json({ error: 'Company is already on this plan' });
    }

    // Validate that the target plan is active
    if (!targetPlan.isActive) {
      return res.status(400).json({ error: 'Target plan is not available' });
    }

    // Additional validation for plan changes
    if (company.subscriptionStatus === 'cancelled') {
      return res.status(400).json({
        error: 'Cannot change plan for cancelled subscription. Please reactivate first.'
      });
    }

    // Execute plan change with proration
    const result = await prorationService.changePlan(companyId, planId, {
      effectiveDate: effectiveDate ? new Date(effectiveDate) : undefined,
      prorationMode,
      reason,
      triggeredBy: 'customer'
    });

    if (result.success) {
      // Trigger cache invalidation and real-time updates for immediate plan changes
      if (prorationMode === 'immediate') {
        try {
          // Get updated company data to broadcast
          const updatedCompany = await storage.getCompany(companyId);
          if (updatedCompany && (global as any).broadcastToCompany) {
            // Broadcast plan change event to all connected clients for this company
            (global as any).broadcastToCompany({
              type: 'plan_updated',
              data: {
                companyId,
                newPlan: updatedCompany.plan,
                planId: updatedCompany.planId,
                timestamp: new Date().toISOString(),
                changeType: 'immediate'
              }
            }, companyId);
          }
        } catch (broadcastError) {
          console.error('Error broadcasting plan update:', broadcastError);
          // Don't fail the request if broadcast fails
        }
      }

      res.json({
        success: true,
        message: prorationMode === 'immediate' ?
          'Plan changed successfully' :
          'Plan change scheduled successfully',
        changeId: result.changeId,
        newPlan: targetPlan.name,
        prorationCalculation: result.prorationCalculation,
        transactionId: result.transactionId
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('enhanced-subscription', 'Error changing plan:', error);

    // Provide more specific error messages
    let errorMessage = 'Failed to change plan';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message.includes('Company not found')) {
        errorMessage = 'Company not found';
        statusCode = 404;
      } else if (error.message.includes('Plan not found')) {
        errorMessage = 'Target plan not found';
        statusCode = 404;
      } else if (error.message.includes('already on this plan')) {
        errorMessage = 'Company is already on this plan';
        statusCode = 400;
      } else if (error.message.includes('insufficient funds') || error.message.includes('payment')) {
        errorMessage = 'Payment processing failed';
        statusCode = 402;
      } else {
        errorMessage = error.message;
      }
    }

    res.status(statusCode).json({
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? error : undefined
    });
  }
});

/**
 * Pause subscription
 */
router.post('/pause', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const validation = pauseSubscriptionSchema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({
        error: 'Invalid request data',
        details: validation.error.errors
      });
    }

    const { pauseUntil, reason } = validation.data;

    const result = await subscriptionPausingService.pauseSubscription(companyId, {
      pauseUntil: pauseUntil ? new Date(pauseUntil) : undefined,
      reason,
      preserveData: true,
      notifyOnResume: true
    });

    if (result.success) {
      res.json({
        success: true,
        message: 'Subscription paused successfully',
        pauseEndDate: result.pauseEndDate
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('enhanced-subscription', 'Error pausing subscription:', error);
    res.status(500).json({ error: 'Failed to pause subscription' });
  }
});

/**
 * Resume paused subscription
 */
router.post('/resume', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const result = await subscriptionPausingService.resumeSubscription(companyId, 'customer_request');

    if (result.success) {
      res.json({
        success: true,
        message: 'Subscription resumed successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('enhanced-subscription', 'Error resuming subscription:', error);
    res.status(500).json({ error: 'Failed to resume subscription' });
  }
});

/**
 * Get pause status
 */
router.get('/pause/status', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const pauseStatus = await subscriptionPausingService.getPauseStatus(companyId);
    res.json(pauseStatus);

  } catch (error) {
    logger.error('enhanced-subscription', 'Error getting pause status:', error);
    res.status(500).json({ error: 'Failed to get pause status' });
  }
});

/**
 * Check if subscription can be paused
 */
router.get('/pause/can-pause', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const canPause = await subscriptionPausingService.canPauseSubscription(companyId);
    res.json(canPause);

  } catch (error) {
    logger.error('enhanced-subscription', 'Error checking pause eligibility:', error);
    res.status(500).json({ error: 'Failed to check pause eligibility' });
  }
});

/**
 * Extend pause duration
 */
router.post('/pause/extend', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const { additionalDays, reason } = req.body;

    if (!additionalDays || additionalDays <= 0) {
      return res.status(400).json({ error: 'Additional days must be a positive number' });
    }

    const result = await subscriptionPausingService.extendPause(companyId, additionalDays, reason);

    if (result.success) {
      res.json({
        success: true,
        message: 'Pause extended successfully',
        newPauseEndDate: result.pauseEndDate
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('enhanced-subscription', 'Error extending pause:', error);
    res.status(500).json({ error: 'Failed to extend pause' });
  }
});

/**
 * Preview plan downgrade impact
 */
router.post('/downgrade/preview', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const { targetPlanId } = req.body;

    if (!targetPlanId) {
      return res.status(400).json({ error: 'Target plan ID required' });
    }

    const preview = await planDowngradeService.previewDowngrade(companyId, targetPlanId);
    res.json(preview);

  } catch (error) {
    logger.error('enhanced-subscription', 'Error previewing downgrade:', error);
    res.status(500).json({ error: 'Failed to preview downgrade' });
  }
});

/**
 * Execute plan downgrade
 */
router.post('/downgrade/execute', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const {
      targetPlanId,
      preserveData = true,
      notifyUsers = true,
      gracePeriodDays = 7,
      reason = 'customer_request'
    } = req.body;

    if (!targetPlanId) {
      return res.status(400).json({ error: 'Target plan ID required' });
    }

    const result = await planDowngradeService.executeDowngrade(companyId, targetPlanId, {
      preserveData,
      notifyUsers,
      gracePeriodDays,
      reason
    });

    if (result.success) {
      res.json({
        success: true,
        message: 'Plan downgraded successfully',
        restrictedFeatures: result.restrictedFeatures,
        dataActions: result.dataActions
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('enhanced-subscription', 'Error executing downgrade:', error);
    res.status(500).json({ error: 'Failed to execute downgrade' });
  }
});

/**
 * Get grace period status
 */
router.get('/grace-period/status', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const status = await gracePeriodService.getGracePeriodStatus(companyId);
    res.json(status);

  } catch (error) {
    logger.error('enhanced-subscription', 'Error getting grace period status:', error);
    res.status(500).json({ error: 'Failed to get grace period status' });
  }
});

/**
 * Check if feature is allowed during grace period
 */
router.get('/grace-period/feature/:feature', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const { feature } = req.params;
    if (!feature) {
      return res.status(400).json({ error: 'Feature name required' });
    }

    const isAllowed = await gracePeriodService.isFeatureAllowed(companyId, feature);

    res.json({
      feature,
      allowed: isAllowed
    });

  } catch (error) {
    logger.error('enhanced-subscription', 'Error checking feature access:', error);
    res.status(500).json({ error: 'Failed to check feature access' });
  }
});

/**
 * Recover from grace period (manual payment)
 */
router.post('/grace-period/recover', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const { transactionId } = req.body;

    // Check if company is in grace period
    const status = await gracePeriodService.getGracePeriodStatus(companyId);
    if (!status.isInGracePeriod) {
      return res.status(400).json({ error: 'Company is not in grace period' });
    }

    await gracePeriodService.recoverFromGracePeriod(companyId, transactionId);

    res.json({
      success: true,
      message: 'Successfully recovered from grace period'
    });

  } catch (error) {
    logger.error('enhanced-subscription', 'Error recovering from grace period:', error);
    res.status(500).json({ error: 'Failed to recover from grace period' });
  }
});

/**
 * Get usage status
 */
router.get('/usage/status', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const usageStatus = await usageTrackingService.getUsageStatus(companyId);
    res.json(usageStatus);

  } catch (error) {
    logger.error('enhanced-subscription', 'Error getting usage status:', error);
    res.status(500).json({ error: 'Failed to get usage status' });
  }
});

/**
 * Check if usage is allowed for a metric
 */
router.get('/usage/check/:metric', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const { metric } = req.params;
    const requestedAmount = parseInt(req.query.amount as string) || 1;

    if (!metric) {
      return res.status(400).json({ error: 'Metric name required' });
    }

    const isAllowed = await usageTrackingService.isUsageAllowed(companyId, metric, requestedAmount);

    res.json({
      metric,
      requestedAmount,
      allowed: isAllowed
    });

  } catch (error) {
    logger.error('enhanced-subscription', 'Error checking usage allowance:', error);
    res.status(500).json({ error: 'Failed to check usage allowance' });
  }
});

/**
 * Update usage for a metric
 */
router.post('/usage/update', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const { metric, increment = 1 } = req.body;

    if (!metric) {
      return res.status(400).json({ error: 'Metric name required' });
    }

    const result = await usageTrackingService.updateUsage(companyId, metric, increment);

    if (result.success) {
      res.json({
        success: true,
        metric,
        newUsage: result.newUsage,
        limitReached: result.limitReached,
        warningTriggered: result.warningTriggered,
        blocked: result.blocked
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('enhanced-subscription', 'Error updating usage:', error);
    res.status(500).json({ error: 'Failed to update usage' });
  }
});

/**
 * Get dunning status
 */
router.get('/dunning/status', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const dunningStatus = await dunningService.getDunningStatus(companyId);
    res.json(dunningStatus);

  } catch (error) {
    logger.error('enhanced-subscription', 'Error getting dunning status:', error);
    res.status(500).json({ error: 'Failed to get dunning status' });
  }
});

/**
 * Cancel dunning process (admin only)
 */
router.post('/admin/dunning/:companyId/cancel', ensureSuperAdmin, async (req: any, res) => {
  try {
    const { companyId } = req.params;
    const { reason = 'admin_cancellation' } = req.body;

    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    await dunningService.cancelDunningProcess(parseInt(companyId), reason);

    res.json({
      success: true,
      message: 'Dunning process cancelled successfully'
    });

  } catch (error) {
    logger.error('enhanced-subscription', 'Error cancelling dunning process:', error);
    res.status(500).json({ error: 'Failed to cancel dunning process' });
  }
});

/**
 * Start dunning process manually (admin only)
 */
router.post('/admin/dunning/:companyId/start', ensureSuperAdmin, async (req: any, res) => {
  try {
    const { companyId } = req.params;
    const { paymentTransactionId, reason = 'manual_start' } = req.body;

    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    await dunningService.startDunningProcess(parseInt(companyId), paymentTransactionId, reason);

    res.json({
      success: true,
      message: 'Dunning process started successfully'
    });

  } catch (error) {
    logger.error('enhanced-subscription', 'Error starting dunning process:', error);
    res.status(500).json({ error: 'Failed to start dunning process' });
  }
});

/**
 * Get subscription events (audit trail)
 */
router.get('/events', ensureAuthenticated, async (req: any, res) => {
  try {
    const companyId = req.user?.companyId;
    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    // TODO: Implement events retrieval from subscriptionEvents table
    
    res.json({
      events: [],
      total: 0,
      limit,
      offset
    });

  } catch (error) {
    logger.error('enhanced-subscription', 'Error getting subscription events:', error);
    res.status(500).json({ error: 'Failed to get subscription events' });
  }
});

/**
 * Stripe webhook endpoint
 */
router.post('/webhooks/stripe', async (req, res) => {
  try {
    const signature = req.headers['stripe-signature'] as string;
    if (!signature) {
      return res.status(400).json({ error: 'Missing Stripe signature' });
    }

    // Get Stripe configuration
    const stripeSettings = await storage.getAppSetting('payment_stripe');
    if (!stripeSettings?.value) {
      return res.status(400).json({ error: 'Stripe not configured' });
    }

    const stripeConfig = stripeSettings.value as any;
    const webhookHandler = new SubscriptionWebhookHandler({
      stripeSecretKey: stripeConfig.secretKey,
      webhookSecret: stripeConfig.webhookSecret || 'whsec_test'
    });

    const result = await webhookHandler.processWebhook(req.body, signature);

    if (result.success) {
      res.json({ received: true });
    } else {
      res.status(400).json({ error: result.error });
    }

  } catch (error) {
    logger.error('enhanced-subscription', 'Error processing Stripe webhook:', error);
    res.status(400).json({ error: 'Webhook processing failed' });
  }
});

// Admin routes
/**
 * Get comprehensive subscription overview (admin only)
 */
router.get('/admin/overview', ensureSuperAdmin, async (_req, res) => {
  try {
    // Get subscription statistics
    const [totalCompanies] = await db
      .select({ count: count() })
      .from(companies);

    const [activeSubscriptions] = await db
      .select({ count: count() })
      .from(companies)
      .where(eq(companies.subscriptionStatus, 'active'));

    const [trialSubscriptions] = await db
      .select({ count: count() })
      .from(companies)
      .where(eq(companies.subscriptionStatus, 'trial'));

    const [pausedSubscriptions] = await db
      .select({ count: count() })
      .from(companies)
      .where(eq(companies.subscriptionStatus, 'paused'));

    const [gracePeriodSubscriptions] = await db
      .select({ count: count() })
      .from(companies)
      .where(eq(companies.subscriptionStatus, 'grace_period'));

    // Get recent subscription events
    const recentEvents = await db
      .select()
      .from(subscriptionEvents)
      .orderBy(desc(subscriptionEvents.createdAt))
      .limit(10);

    // Get scheduler status
    const schedulerStatus = subscriptionScheduler.getStatus();

    res.json({
      statistics: {
        totalCompanies: totalCompanies.count,
        activeSubscriptions: activeSubscriptions.count,
        trialSubscriptions: trialSubscriptions.count,
        pausedSubscriptions: pausedSubscriptions.count,
        gracePeriodSubscriptions: gracePeriodSubscriptions.count
      },
      recentEvents,
      schedulerStatus
    });

  } catch (error) {
    logger.error('enhanced-subscription', 'Error getting admin overview:', error);
    res.status(500).json({ error: 'Failed to get admin overview' });
  }
});

/**
 * Get company subscription details (admin only)
 */
router.get('/admin/company/:companyId', ensureSuperAdmin, async (req, res) => {
  try {
    const { companyId } = req.params;

    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const company = await storage.getCompany(parseInt(companyId));
    if (!company) {
      return res.status(404).json({ error: 'Company not found' });
    }

    const plan = await storage.getPlan(company.planId!);

    // Get subscription status
    const subscriptionStatus = await subscriptionManager.getSubscriptionStatus(parseInt(companyId));

    // Get usage status
    const usageStatus = await usageTrackingService.getUsageStatus(parseInt(companyId));

    // Get dunning status
    const dunningStatus = await dunningService.getDunningStatus(parseInt(companyId));

    // Get pause status
    const pauseStatus = await subscriptionPausingService.getPauseStatus(parseInt(companyId));

    // Get grace period status
    const gracePeriodStatus = await gracePeriodService.getGracePeriodStatus(parseInt(companyId));

    // Get recent events
    const recentEvents = await db
      .select()
      .from(subscriptionEvents)
      .where(eq(subscriptionEvents.companyId, parseInt(companyId)))
      .orderBy(desc(subscriptionEvents.createdAt))
      .limit(20);

    res.json({
      company,
      plan,
      subscriptionStatus,
      usageStatus,
      dunningStatus,
      pauseStatus,
      gracePeriodStatus,
      recentEvents
    });

  } catch (error) {
    logger.error('enhanced-subscription', 'Error getting company details:', error);
    res.status(500).json({ error: 'Failed to get company details' });
  }
});

/**
 * Update company subscription (admin only)
 */
router.put('/admin/company/:companyId/subscription', ensureSuperAdmin, async (req, res) => {
  try {
    const { companyId } = req.params;
    const {
      subscriptionStatus,
      subscriptionEndDate,
      planId,
      autoRenewal,
      reason = 'admin_update'
    } = req.body;

    if (!companyId) {
      return res.status(400).json({ error: 'Company ID required' });
    }

    const company = await storage.getCompany(parseInt(companyId));
    if (!company) {
      return res.status(404).json({ error: 'Company not found' });
    }

    const updateData: any = {};

    if (subscriptionStatus) updateData.subscriptionStatus = subscriptionStatus;
    if (subscriptionEndDate) updateData.subscriptionEndDate = new Date(subscriptionEndDate);
    if (planId) updateData.planId = planId;
    if (typeof autoRenewal === 'boolean') updateData.autoRenewal = autoRenewal;

    await storage.updateCompany(parseInt(companyId), updateData);

    // Log admin update
    await subscriptionManager.logSubscriptionEvent(
      parseInt(companyId),
      'admin_subscription_update',
      {
        updates: updateData,
        reason
      },
      company.subscriptionStatus || 'inactive',
      subscriptionStatus || company.subscriptionStatus || 'inactive',
      'admin'
    );

    res.json({
      success: true,
      message: 'Subscription updated successfully'
    });

  } catch (error) {
    logger.error('enhanced-subscription', 'Error updating company subscription:', error);
    res.status(500).json({ error: 'Failed to update subscription' });
  }
});

/**
 * Adjust usage manually (admin only)
 */
router.post('/admin/company/:companyId/usage/adjust', ensureSuperAdmin, async (req, res) => {
  try {
    const { companyId } = req.params;
    const { metric, newUsage, reason = 'admin_adjustment' } = req.body;

    if (!companyId || !metric || typeof newUsage !== 'number') {
      return res.status(400).json({ error: 'Company ID, metric, and newUsage are required' });
    }

    const result = await usageTrackingService.adjustUsage(
      parseInt(companyId),
      metric,
      newUsage,
      reason
    );

    if (result.success) {
      res.json({
        success: true,
        message: 'Usage adjusted successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('enhanced-subscription', 'Error adjusting usage:', error);
    res.status(500).json({ error: 'Failed to adjust usage' });
  }
});

/**
 * Get scheduler status (admin only)
 */
router.get('/admin/scheduler/status', ensureSuperAdmin, async (_req, res) => {
  try {
    const status = subscriptionScheduler.getStatus();
    res.json(status);
  } catch (error) {
    logger.error('enhanced-subscription', 'Error getting scheduler status:', error);
    res.status(500).json({ error: 'Failed to get scheduler status' });
  }
});

/**
 * Start scheduler (admin only)
 */
router.post('/admin/scheduler/start', ensureSuperAdmin, async (_req, res) => {
  try {
    subscriptionScheduler.start();
    res.json({ success: true, message: 'Scheduler started' });
  } catch (error) {
    logger.error('enhanced-subscription', 'Error starting scheduler:', error);
    res.status(500).json({ error: 'Failed to start scheduler' });
  }
});

/**
 * Stop scheduler (admin only)
 */
router.post('/admin/scheduler/stop', ensureSuperAdmin, async (_req, res) => {
  try {
    subscriptionScheduler.stop();
    res.json({ success: true, message: 'Scheduler stopped' });
  } catch (error) {
    logger.error('enhanced-subscription', 'Error stopping scheduler:', error);
    res.status(500).json({ error: 'Failed to stop scheduler' });
  }
});

export default router;
