import React, { useEffect, useRef, useState } from 'react';
import { useParams, useLocation } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { Save, Eye, Settings, ArrowLeft } from 'lucide-react';
import { Link } from 'wouter';
import AdminLayout from '@/components/admin/AdminLayout';

// Import GrapeJS
import grapesjs from 'grapesjs';
import 'grapesjs/dist/css/grapes.min.css';

interface Website {
  id: number;
  title: string;
  slug: string;
  description?: string;
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string;
  grapesData: any;
  grapesHtml?: string;
  grapesCss?: string;
  grapesJs?: string;
  favicon?: string;
  customCss?: string;
  customJs?: string;
  customHead?: string;
  status: 'draft' | 'published' | 'archived';
  googleAnalyticsId?: string;
  facebookPixelId?: string;
  templateId?: number;
  theme?: string;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export default function WebsiteBuilderEditor() {
  const params = useParams();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const websiteId = params.id ? parseInt(params.id) : null;
  const isEditing = websiteId !== null;
  
  const editorRef = useRef<HTMLDivElement>(null);
  const studioRef = useRef<any>(null);
  
  const [websiteData, setWebsiteData] = useState({
    title: '',
    slug: '',
    description: '',
    metaTitle: '',
    metaDescription: '',
    metaKeywords: '',
    status: 'draft' as 'draft' | 'published' | 'archived',
    googleAnalyticsId: '',
    facebookPixelId: '',
    theme: 'default',
    customCss: '',
    customJs: '',
    customHead: '',
    favicon: ''
  });

  const [showSaveModal, setShowSaveModal] = useState(false);
  const [tempSaveData, setTempSaveData] = useState({
    title: '',
    slug: ''
  });

  // Fetch website data if editing
  const { data: website, isLoading } = useQuery<Website>({
    queryKey: ['admin-website', websiteId],
    queryFn: async () => {
      if (!websiteId) return null;
      const response = await fetch(`/api/admin/websites/${websiteId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch website');
      }
      return response.json();
    },
    enabled: !!websiteId
  });

  // Initialize GrapeJS
  useEffect(() => {
    // Add Font Awesome CSS if not already present
    if (!document.querySelector('link[href*="font-awesome"]')) {
      const fontAwesome = document.createElement('link');
      fontAwesome.rel = 'stylesheet';
      fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css';
      document.head.appendChild(fontAwesome);
    }

    if (!editorRef.current) return;

    const initGrapeJS = () => {
      try {
        // Initialize GrapeJS with default interface - let it handle its own layout
        const editor = grapesjs.init({
          container: editorRef.current!,
          height: '100%',
          width: 'auto',
          storageManager: false, // We'll handle storage manually
          fromElement: false,

          // Load existing content if available
          components: website?.grapesHtml || '<h1>Welcome to your new website!</h1><p>Start building your amazing website here.</p>',
          style: website?.grapesCss || '',

          // Add default blocks since GrapeJS doesn't include them by default
          blockManager: {
            blocks: [
              {
                id: 'section',
                label: '<i class="fa fa-square-o"></i><br>Section',
                attributes: { class: 'gjs-block-section' },
                content: `<section>
                  <h1>Insert title here</h1>
                  <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                </section>`,
              },
              {
                id: 'text',
                label: '<i class="fa fa-text-width"></i><br>Text',
                content: '<div data-gjs-type="text">Insert your text here</div>',
              },
              {
                id: 'image',
                label: '<i class="fa fa-picture-o"></i><br>Image',
                select: true,
                content: { type: 'image' },
                activate: true,
              },
              {
                id: 'video',
                label: '<i class="fa fa-youtube-play"></i><br>Video',
                content: {
                  type: 'video',
                  src: 'img/video2.webm',
                  style: {
                    height: '350px',
                    width: '615px'
                  }
                },
              },
              {
                id: 'button',
                label: '<i class="fa fa-hand-pointer-o"></i><br>Button',
                content: '<a class="btn">Click me</a>',
              },
              {
                id: 'divider',
                label: '<i class="fa fa-minus"></i><br>Divider',
                content: '<hr/>',
              },
              {
                id: 'text-box',
                label: '<i class="fa fa-square"></i><br>Text Box',
                content: '<div class="text-box"><p>Text box content</p></div>',
              },
              {
                id: 'quote',
                label: '<i class="fa fa-quote-left"></i><br>Quote',
                content: '<blockquote class="quote">Lorem ipsum dolor sit amet, consectetur adipiscing elit</blockquote>',
              },
              {
                id: 'link',
                label: '<i class="fa fa-link"></i><br>Link',
                content: '<a href="#">Link</a>',
              },
              {
                id: 'grid-2',
                label: '<i class="fa fa-columns"></i><br>2 Columns',
                content: '<div class="row"><div class="cell">Column 1</div><div class="cell">Column 2</div></div>',
              },
              {
                id: 'grid-3',
                label: '<i class="fa fa-th-large"></i><br>3 Columns',
                content: '<div class="row"><div class="cell">Column 1</div><div class="cell">Column 2</div><div class="cell">Column 3</div></div>',
              },
              {
                id: 'list',
                label: '<i class="fa fa-list"></i><br>List',
                content: `<ul>
                  <li>List item 1</li>
                  <li>List item 2</li>
                  <li>List item 3</li>
                </ul>`,
              },
              {
                id: 'map',
                label: '<i class="fa fa-map-marker"></i><br>Map',
                content: {
                  type: 'map',
                  style: {
                    height: '350px'
                  }
                },
              }
            ]
          }
        });

        // Add some basic CSS for the blocks and block styling
        editor.addStyle(`
          .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
          }
          .cell {
            flex: 1;
            padding: 10px;
            min-height: 75px;
            background: #f8f9fa;
            border: 1px dashed #ddd;
            margin: 0 10px;
          }
          .btn {
            display: inline-block;
            padding: 8px 16px;
            background: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
          }
          .text-box {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
          }
          .quote {
            font-style: italic;
            border-left: 4px solid #007cba;
            padding-left: 15px;
            margin: 15px 0;
          }
          section {
            padding: 20px;
            margin: 10px 0;
          }
          section h1 {
            margin-bottom: 10px;
            color: #333;
          }
          section p {
            line-height: 1.6;
            color: #666;
          }
        `);

        // Add custom CSS for block styling
        const blockStyle = document.createElement('style');
        blockStyle.textContent = `
          /* Block Manager Styling */
          .gjs-block {
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;
            text-align: center !important;
            padding: 15px 10px !important;
            min-height: 80px !important;
          }

          .gjs-block i {
            font-size: 24px !important;
            margin-bottom: 8px !important;
            display: block !important;
            color: #bbb !important;
          }

          .gjs-block:hover i {
            color: #007cba !important;
          }

          .gjs-block {
            font-size: 12px !important;
            font-weight: 500 !important;
            color: #fff !important;
            line-height: 1.2 !important;
          }

          .gjs-block:hover {
            color: #333 !important;
          }

          .gjs-block:hover {
            background-color: #f0f8ff !important;
            border-color: #007cba !important;
          }
        `;
        document.head.appendChild(blockStyle);



        // Load existing project data if available
        if (website?.grapesData && Object.keys(website.grapesData).length > 0) {
          try {
            editor.loadProjectData(website.grapesData);
          } catch (error) {
            console.warn('Failed to load existing project data:', error);
          }
        }

        studioRef.current = editor;
        console.log('GrapeJS initialized successfully');

      } catch (error) {
        console.error('Failed to initialize GrapeJS:', error);
        toast({
          title: 'Error',
          description: 'Failed to initialize the website builder. Please try again.',
          variant: 'destructive'
        });
      }
    };

    initGrapeJS();

    return () => {
      if (studioRef.current) {
        try {
          studioRef.current.destroy();
        } catch (error) {
          console.warn('Error destroying GrapeJS:', error);
        }
      }
    };
  }, [website, toast]);

  // Update form data when website is loaded
  useEffect(() => {
    if (website) {
      setWebsiteData({
        title: website.title || '',
        slug: website.slug || '',
        description: website.description || '',
        metaTitle: website.metaTitle || '',
        metaDescription: website.metaDescription || '',
        metaKeywords: website.metaKeywords || '',
        status: website.status || 'draft',
        googleAnalyticsId: website.googleAnalyticsId || '',
        facebookPixelId: website.facebookPixelId || '',
        theme: website.theme || 'default',
        customCss: website.customCss || '',
        customJs: website.customJs || '',
        customHead: website.customHead || '',
        favicon: website.favicon || ''
      });
    }
  }, [website]);

  // Save website mutation
  const saveWebsiteMutation = useMutation({
    mutationFn: async (data: any) => {
      const url = isEditing ? `/api/admin/websites/${websiteId}` : '/api/admin/websites';
      const method = isEditing ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to save website');
      }
      
      return response.json();
    },
    onSuccess: (savedWebsite) => {
      queryClient.invalidateQueries({ queryKey: ['admin-websites'] });
      queryClient.invalidateQueries({ queryKey: ['admin-website', websiteId] });
      
      toast({
        title: 'Success',
        description: `Website ${isEditing ? 'updated' : 'created'} successfully`
      });
      
      if (!isEditing) {
        setLocation(`/admin/website-builder/edit/${savedWebsite.id}`);
      }
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to save website',
        variant: 'destructive'
      });
    }
  });

  const handleSave = async () => {
    // For new websites, show modal to collect title and slug
    if (!isEditing && (!websiteData.title || !websiteData.slug)) {
      setShowSaveModal(true);
      return;
    }

    // For existing websites, validate required fields
    if (!websiteData.title || !websiteData.slug) {
      toast({
        title: 'Validation Error',
        description: 'Title and slug are required',
        variant: 'destructive'
      });
      return;
    }

    performSave();
  };

  const performSave = async (overrideData = {}) => {

    let grapesData = {};
    let grapesHtml = '';
    let grapesCss = '';
    let grapesJs = '';

    // Get data from GrapeJS if available
    if (studioRef.current) {
      try {
        // Get project data from GrapeJS
        grapesData = studioRef.current.getProjectData();

        // Get rendered HTML/CSS/JS
        grapesHtml = studioRef.current.getHtml();
        grapesCss = studioRef.current.getCss();
        grapesJs = studioRef.current.getJs();
      } catch (error) {
        console.error('Error getting GrapeJS data:', error);
      }
    }

    const saveData = {
      ...websiteData,
      ...overrideData,
      grapesData,
      grapesHtml,
      grapesCss,
      grapesJs
    };

    saveWebsiteMutation.mutate(saveData);
  };

  const handleModalSave = () => {
    if (!tempSaveData.title || !tempSaveData.slug) {
      toast({
        title: 'Validation Error',
        description: 'Title and slug are required',
        variant: 'destructive'
      });
      return;
    }

    // Update websiteData with the modal data for future use
    setWebsiteData(prev => ({
      ...prev,
      title: tempSaveData.title,
      slug: tempSaveData.slug
    }));

    setShowSaveModal(false);

    // Perform save immediately with the modal data
    performSave({
      title: tempSaveData.title,
      slug: tempSaveData.slug
    });
  };

  const handlePreview = () => {
    if (website?.status === 'published') {
      window.open('/website', '_blank');
    } else {
      toast({
        title: 'Preview Unavailable',
        description: 'Please publish the website first to preview it',
        variant: 'destructive'
      });
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p>Loading website builder...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <>

      <div className="h-screen flex flex-col">
        {/* Custom Header for Save/Preview */}
        <div className="bg-white border-b px-4 py-2 flex items-center justify-between relative z-50">
          <div className="flex items-center gap-4">
            <Link href="/admin/website-builder">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
            </Link>
            <div>
              <h1 className="text-lg font-semibold">
                {isEditing ? `Edit: ${website?.title}` : 'New Website'}
              </h1>
              <p className="text-sm text-gray-600">
                {isEditing ? `/${website?.slug}` : 'Create a new website'}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePreview}
              disabled={!website?.status || website.status !== 'published'}
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>
            <Button
              size="sm"
              onClick={handleSave}
              disabled={saveWebsiteMutation.isPending}
            >
              <Save className="w-4 h-4 mr-2" />
              {saveWebsiteMutation.isPending ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </div>

        {/* GrapeJS Editor - Let it handle its own default layout */}
        <div className="flex-1">
          <div ref={editorRef} className="h-full w-full"></div>
        </div>
      </div>

      {/* Save Modal for New Websites */}
      {showSaveModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-md">
            <h3 className="text-lg font-semibold mb-4">Save Website</h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="title">Website Title *</Label>
                <Input
                  id="title"
                  value={tempSaveData.title}
                  onChange={(e) => setTempSaveData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter website title"
                />
              </div>
              <div>
                <Label htmlFor="slug">URL Slug *</Label>
                <Input
                  id="slug"
                  value={tempSaveData.slug}
                  onChange={(e) => setTempSaveData(prev => ({ ...prev, slug: e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '-') }))}
                  placeholder="enter-url-slug"
                />
                <p className="text-sm text-gray-500 mt-1">This will be your website URL</p>
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowSaveModal(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleModalSave}
                disabled={!tempSaveData.title || !tempSaveData.slug}
              >
                Save Website
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
