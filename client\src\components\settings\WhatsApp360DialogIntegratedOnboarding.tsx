import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface OnboardingFormData {
  connectionName: string;
}

export function WhatsApp360DialogIntegratedOnboarding({ isOpen, onClose, onSuccess }: Props) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCheckingConfig, setIsCheckingConfig] = useState(false);
  const [partnerConfigured, setPartnerConfigured] = useState(false);
  const [partnerId, setPartnerId] = useState('');
  
  const [formData, setFormData] = useState<OnboardingFormData>({
    connectionName: 'My WhatsApp Business'
  });


  useEffect(() => {
    if (isOpen) {
      checkPartnerConfiguration();
    }
  }, [isOpen]);

  const checkPartnerConfiguration = async () => {
    setIsCheckingConfig(true);
    try {
      const response = await fetch('/api/admin/partner-configurations/360dialog');
      if (response.ok) {
        const config = await response.json();
        setPartnerConfigured(true);
        setPartnerId(config.partnerId);
      } else {
        setPartnerConfigured(false);
      }
    } catch (error) {
      console.error('Error checking partner configuration:', error);
      setPartnerConfigured(false);
    } finally {
      setIsCheckingConfig(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const resetForm = () => {
    setFormData({
      connectionName: 'My WhatsApp Business'
    });
    setIsSubmitting(false);
  };

  const handleIntegratedOnboarding = () => {
    if (!partnerConfigured || !partnerId) {
      toast({
        title: "Configuration Error",
        description: "360Dialog Partner API is not configured. Please contact your administrator.",
        variant: "destructive"
      });
      return;
    }


    const baseUrl = 'https://hub.360dialog.com/dashboard/app';
    const redirectUrl = `${window.location.origin}/settings/channels/360dialog/callback`;
    const onboardingUrl = `${baseUrl}/${partnerId}/permissions?redirect_url=${encodeURIComponent(redirectUrl)}`;


    const popup = window.open(
      onboardingUrl,
      '360dialog-onboarding',
      'width=600,height=700,scrollbars=yes,resizable=yes'
    );

    if (!popup) {
      toast({
        title: "Popup Blocked",
        description: "Please allow popups for this site and try again.",
        variant: "destructive"
      });
      return;
    }


    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== window.location.origin) {
        return;
      }

      if (event.data.type === '360dialog-onboarding-success') {
        window.removeEventListener('message', handleMessage);
        popup.close();
        

        handleOnboardingSuccess(event.data.clientId, event.data.channels);
      } else if (event.data.type === '360dialog-onboarding-error') {
        window.removeEventListener('message', handleMessage);
        popup.close();
        
        toast({
          title: "Onboarding Failed",
          description: event.data.error || "Failed to complete WhatsApp Business onboarding.",
          variant: "destructive"
        });
      }
    };

    window.addEventListener('message', handleMessage);


    const checkClosed = setInterval(() => {
      if (popup.closed) {
        clearInterval(checkClosed);
        window.removeEventListener('message', handleMessage);
      }
    }, 1000);
  };

  const handleOnboardingSuccess = async (clientId: string, channels: string[]) => {
    setIsSubmitting(true);
    
    try {

      const response = await fetch('/api/channel-connections', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channelType: 'whatsapp_360dialog',
          accountId: clientId,
          accountName: formData.connectionName,
          connectionData: {
            clientId,
            channels,
            onboardedAt: new Date().toISOString()
          }
        })
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "360Dialog WhatsApp connection created successfully! Your channels are being set up.",
        });
        
        resetForm();
        onSuccess();
        onClose();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create connection');
      }
    } catch (error: any) {
      console.error('Error creating 360Dialog connection:', error);
      toast({
        title: "Connection Error",
        description: error.message || "Failed to create 360Dialog WhatsApp connection",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (isCheckingConfig) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-[500px]">
          <div className="flex items-center justify-center p-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p>Checking configuration...</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (!partnerConfigured) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <i className="ri-whatsapp-line text-green-500"></i>
              360Dialog WhatsApp Integration
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
              <div className="flex items-start">
                <i className="ri-information-line text-yellow-500 mr-2 mt-0.5"></i>
                <div>
                  <p className="text-sm text-yellow-700 font-medium">Partner Configuration Required</p>
                  <p className="text-xs text-yellow-600 mt-1">
                    360Dialog Partner API is not configured. Please contact your system administrator to set up the Partner API credentials.
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={handleClose}>
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <i className="ri-whatsapp-line text-green-500"></i>
            Connect 360Dialog WhatsApp Business
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="connectionName">Connection Name</Label>
              <Input
                id="connectionName"
                name="connectionName"
                value={formData.connectionName}
                onChange={handleInputChange}
                placeholder="My WhatsApp Business"
                required
              />
              <p className="text-sm text-gray-500">
                A friendly name for this WhatsApp Business connection
              </p>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-start">
              <i className="ri-information-line text-blue-500 mr-2 mt-0.5"></i>
              <div>
                <p className="text-sm text-blue-700 font-medium">Integrated Onboarding</p>
                <p className="text-xs text-blue-600 mt-1">
                  Click "Start Onboarding" to connect your WhatsApp Business account through 360Dialog's secure onboarding process. You'll be guided through:
                </p>
                <ul className="text-xs text-blue-600 mt-2 ml-4 list-disc">
                  <li>WhatsApp Business account verification</li>
                  <li>Phone number registration</li>
                  <li>Business profile setup</li>
                  <li>API access configuration</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              className="btn-brand-primary"
              onClick={handleIntegratedOnboarding}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Processing...' : 'Start Onboarding'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
