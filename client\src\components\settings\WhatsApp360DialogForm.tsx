import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface Dialog360FormData {
  accountName: string;
  apiKey: string;
  phoneNumber: string;
  webhookUrl: string;
}

export function WhatsApp360DialogForm({ isOpen, onClose, onSuccess }: Props) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  
  const [formData, setFormData] = useState<Dialog360FormData>({
    accountName: '',
    apiKey: '',
    phoneNumber: '',
    webhookUrl: `${window.location.origin}/api/webhooks/360dialog-whatsapp`
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const resetForm = () => {
    setFormData({
      accountName: '',
      apiKey: '',
      phoneNumber: '',
      webhookUrl: `${window.location.origin}/api/webhooks/360dialog-whatsapp`
    });
    setIsSubmitting(false);
    setIsValidating(false);
  };

  const validateCredentials = async () => {
    if (!formData.apiKey || !formData.phoneNumber) {
      toast({
        title: "Validation Error",
        description: "API Key and Phone Number are required for validation.",
        variant: "destructive"
      });
      return false;
    }

    setIsValidating(true);
    try {

      const response = await fetch('https://waba-v2.360dialog.io/v1/configs/webhook', {
        headers: {
          'D360-API-KEY': formData.apiKey
        }
      });
      
      if (response.ok || response.status === 404) { // 404 is OK if no webhook is configured yet
        toast({
          title: "Credentials Valid",
          description: `Successfully validated 360Dialog API key for phone number: ${formData.phoneNumber}`,
        });
        return true;
      } else {
        const errorData = await response.json();
        throw new Error(errorData.meta?.developer_message || 'Invalid credentials');
      }
    } catch (error: any) {
      toast({
        title: "Validation Failed",
        description: error.message || "Failed to validate 360Dialog credentials.",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsValidating(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsSubmitting(true);
    
    try {

      const isValid = await validateCredentials();
      if (!isValid) {
        setIsSubmitting(false);
        return;
      }

      const response = await fetch('/api/channel-connections', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channelType: 'whatsapp_360dialog',
          accountId: formData.phoneNumber,
          accountName: formData.accountName,
          connectionData: {
            apiKey: formData.apiKey,
            phoneNumber: formData.phoneNumber,
            webhookUrl: formData.webhookUrl
          }
        })
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "360Dialog WhatsApp connection created successfully!",
        });
        
        resetForm();
        onSuccess();
        onClose();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create connection');
      }
    } catch (error: any) {
      console.error('Error connecting to 360Dialog WhatsApp:', error);
      toast({
        title: "Connection Error",
        description: error.message || "Failed to connect to 360Dialog WhatsApp",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <i className="ri-whatsapp-line text-green-500"></i>
            Connect 360Dialog WhatsApp
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="accountName">Connection Name</Label>
              <Input
                id="accountName"
                name="accountName"
                value={formData.accountName}
                onChange={handleInputChange}
                placeholder="My 360Dialog WhatsApp"
                required
              />
              <p className="text-sm text-gray-500">
                A friendly name for this connection
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="apiKey">API Key</Label>
              <Input
                id="apiKey"
                name="apiKey"
                type="password"
                value={formData.apiKey}
                onChange={handleInputChange}
                placeholder="Your 360Dialog API key..."
                required
              />
              <p className="text-sm text-gray-500">
                Your 360Dialog API key from the Hub dashboard
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="phoneNumber">WhatsApp Phone Number</Label>
              <div className="flex gap-2">
                <Input
                  id="phoneNumber"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleInputChange}
                  placeholder="+1234567890"
                  required
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={validateCredentials}
                  disabled={isValidating || !formData.apiKey || !formData.phoneNumber}
                  className="whitespace-nowrap"
                >
                  {isValidating ? 'Validating...' : 'Test'}
                </Button>
              </div>
              <p className="text-sm text-gray-500">
                Your WhatsApp Business phone number registered with 360Dialog
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="webhookUrl">Webhook URL</Label>
              <Input
                id="webhookUrl"
                name="webhookUrl"
                value={formData.webhookUrl}
                onChange={handleInputChange}
                placeholder="https://yourdomain.com/api/webhooks/360dialog-whatsapp"
                required
              />
              <p className="text-sm text-gray-500">
                URL where 360Dialog will send webhook notifications
              </p>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-start">
              <i className="ri-information-line text-blue-500 mr-2 mt-0.5"></i>
              <div>
                <p className="text-sm text-blue-700 font-medium">360Dialog WhatsApp Setup</p>
                <p className="text-xs text-blue-600 mt-1">
                  1. Create a 360Dialog account and get WhatsApp Business approved<br/>
                  2. Generate an API key in the 360Dialog Hub<br/>
                  3. Configure webhook URL in your 360Dialog dashboard<br/>
                  4. 360Dialog provides direct access to WhatsApp Business API
                </p>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              variant="outline" 
              className="btn-brand-primary"
              disabled={isSubmitting || isValidating}
            >
              {isSubmitting ? 'Connecting...' : 'Connect'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
