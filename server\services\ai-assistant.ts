import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { Message, Contact, Conversation, ChannelConnection } from '@shared/schema';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as crypto from 'crypto';
import elevenLabsService, { ElevenLabsConfig } from './elevenlabs-service';

interface ConversationMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata?: string | null;
}

interface AIProviderInterface {
  generateResponse(
    messages: ConversationMessage[],
    options: {
      systemPrompt?: string;
      enableFunctionCalling?: boolean;
      enableAudio?: boolean;
      enableImage?: boolean;
      enableVideo?: boolean;
      enableVoiceProcessing?: boolean;
      enableTextToSpeech?: boolean;
      ttsProvider?: string;
      ttsVoice?: string;
      voiceResponseMode?: string;
      maxAudioDuration?: number;
      functionDefinitions?: any[];
      model?: string;
      elevenLabsApiKey?: string;
      elevenLabsVoiceId?: string;
      elevenLabsCustomVoiceId?: string;
      elevenLabsModel?: string;
      elevenLabsStability?: number;
      elevenLabsSimilarityBoost?: number;
      elevenLabsStyle?: number;
      elevenLabsUseSpeakerBoost?: boolean;
    }
  ): Promise<{
    text: string;
    audioUrl?: string;
    functionCalls?: any[];
  }>;
}

function generateMessagesWithHistory(
  message: Message,
  _contact: Contact,
  systemPrompt: string,
  conversationHistory: Message[] = [],
  historyLimit: number = 5
): ConversationMessage[] {
  const messages: ConversationMessage[] = [
    { role: 'system', content: systemPrompt }
  ];

  const recentHistory = conversationHistory
    .sort((a, b) => {
      const timeA = a.sentAt ? a.sentAt.getTime() : 0;
      const timeB = b.sentAt ? b.sentAt.getTime() : 0;
      return timeA - timeB;
    })
    .slice(-historyLimit);


  recentHistory.forEach(historyMsg => {
    const role = historyMsg.direction === 'inbound' ? 'user' : 'assistant';
    if (historyMsg.content) {
      let metadata = null;

      if (historyMsg.type === 'audio') {
        if (historyMsg.metadata) {
          metadata = typeof historyMsg.metadata === 'string' ? historyMsg.metadata : JSON.stringify(historyMsg.metadata);
        } else if (historyMsg.mediaUrl) {
          metadata = JSON.stringify({
            mediaUrl: historyMsg.mediaUrl,
            mediaType: 'audio'
          });
        }
      } else if (historyMsg.mediaUrl) {
        metadata = JSON.stringify({
          mediaUrl: historyMsg.mediaUrl,
          mediaType: historyMsg.type
        });
      }

      messages.push({
        role,
        content: historyMsg.content,
        metadata
      });
    }
  });

  if (!recentHistory.find(m => m.id === message.id) && message.content) {
    let metadata = null;

    if (message.type === 'audio') {
      if (message.metadata) {
        metadata = typeof message.metadata === 'string' ? message.metadata : JSON.stringify(message.metadata);
      } else if (message.mediaUrl) {
        metadata = JSON.stringify({
          mediaUrl: message.mediaUrl,
          mediaType: 'audio'
        });
      }
    } else if (message.mediaUrl) {
      metadata = JSON.stringify({
        mediaUrl: message.mediaUrl,
        mediaType: message.type
      });
    }

    messages.push({
      role: 'user',
      content: message.content,
      metadata
    });
  }

  return messages;
}

class OpenAIProvider implements AIProviderInterface {
  private client: OpenAI;

  constructor(apiKey: string) {
    this.client = new OpenAI({ apiKey });
  }

  /**
   * Convert audio file to text using OpenAI Whisper
   */
  private async transcribeAudio(audioPath: string): Promise<string> {
    try {
      const audioFile = await fs.readFile(audioPath);
      const audioBuffer = Buffer.from(audioFile);

      const fileExtension = path.extname(audioPath).toLowerCase();
      let tempFileName = `temp_audio_${Date.now()}`;

      if (fileExtension === '.ogg' || fileExtension === '.oga') {
        tempFileName += '.ogg';
      } else if (fileExtension === '.mp3') {
        tempFileName += '.mp3';
      } else if (fileExtension === '.wav') {
        tempFileName += '.wav';
      } else if (fileExtension === '.m4a') {
        tempFileName += '.m4a';
      } else {
        tempFileName += '.mp3';
      }

      const tempPath = path.join(process.cwd(), 'temp', tempFileName);

      await fs.mkdir(path.dirname(tempPath), { recursive: true });
      await fs.writeFile(tempPath, audioBuffer);

      try {
        const fs_node = await import('fs');

        let transcription = '';
        let lastError: Error | null = null;
        const maxRetries = 3;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
          try {
            const result = await this.client.audio.transcriptions.create({
              file: await OpenAI.toFile(fs_node.createReadStream(tempPath), path.basename(tempPath)),
              model: 'whisper-1',
              language: 'en',
              response_format: 'text'
            });

            transcription = result;
            break;

          } catch (error) {
            lastError = error as Error;

            if (attempt === maxRetries) {
              throw lastError;
            }

            const waitTime = Math.pow(2, attempt) * 1000;
            await new Promise(resolve => setTimeout(resolve, waitTime));
          }
        }

        await fs.unlink(tempPath).catch(() => {});

        return transcription;
      } catch (error) {
        await fs.unlink(tempPath).catch(() => {});
        throw error;
      }
    } catch (error) {
      console.error('OpenAI Provider: Error transcribing audio:', error);
      throw new Error(`Audio transcription failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert text to speech using OpenAI TTS
   */
  private async generateSpeech(text: string, voice: string = 'alloy'): Promise<string> {
    try {
      let response: any;
      let lastError: Error | null = null;
      const maxRetries = 3;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          response = await this.client.audio.speech.create({
            model: 'tts-1',
            voice: voice as any,
            input: text,
            response_format: 'mp3'
          });

          break;

        } catch (error) {
          lastError = error as Error;

          if (attempt === maxRetries) {
            throw lastError;
          }

          const waitTime = Math.pow(2, attempt) * 1000;
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }

      const audioId = crypto.randomBytes(16).toString('hex');
      const audioFileName = `tts_${audioId}.mp3`;
      const audioDir = path.join(process.cwd(), 'media', 'audio');
      const audioPath = path.join(audioDir, audioFileName);
      const audioUrl = `media/audio/${audioFileName}`;

      await fs.mkdir(audioDir, { recursive: true });

      const audioBuffer = Buffer.from(await response.arrayBuffer());
      await fs.writeFile(audioPath, audioBuffer);

      return audioUrl;
    } catch (error) {
      console.error('OpenAI Provider: Error generating speech:', error);
      throw new Error(`Speech generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async generateResponse(
    messages: ConversationMessage[],
    options: {
      systemPrompt?: string;
      enableFunctionCalling?: boolean;
      enableAudio?: boolean;
      enableImage?: boolean;
      enableVideo?: boolean;
      enableVoiceProcessing?: boolean;
      enableTextToSpeech?: boolean;
      ttsProvider?: string;
      ttsVoice?: string;
      voiceResponseMode?: string;
      maxAudioDuration?: number;
      functionDefinitions?: any[];
      model?: string;
      elevenLabsApiKey?: string;
      elevenLabsVoiceId?: string;
      elevenLabsCustomVoiceId?: string;
      elevenLabsModel?: string;
      elevenLabsStability?: number;
      elevenLabsSimilarityBoost?: number;
      elevenLabsStyle?: number;
      elevenLabsUseSpeakerBoost?: boolean;
    }
  ): Promise<{
    text: string;
    audioUrl?: string;
    functionCalls?: any[];
  }> {
    try {


      const processedMessages = await Promise.all(messages.map(async (msg) => {
        const userMessagesWithMetadata = messages.filter(m => m.role === 'user' && m.metadata);
        const isLatestUserMessageWithMetadata = userMessagesWithMetadata.length > 0 &&
                                               userMessagesWithMetadata[userMessagesWithMetadata.length - 1] === msg;
        const shouldTranscribe = options.enableVoiceProcessing && msg.metadata && msg.role === 'user' && isLatestUserMessageWithMetadata;

        if (shouldTranscribe) {
          try {
            if (!msg.metadata) {
              throw new Error('No metadata available for transcription');
            }
            const metadata = JSON.parse(msg.metadata);

            let audioPath: string | null = null;

            const isAudioMessage = metadata.mediaType === 'audio' ||
                                 metadata.whatsappMessage?.message?.audioMessage ||
                                 metadata.audioPath ||
                                 (metadata.mediaUrl && metadata.mediaUrl.includes('/audio/')) ||
                                 (metadata.mediaUrl && metadata.mediaUrl.includes('.mp3')) ||
                                 (metadata.mediaUrl && metadata.mediaUrl.includes('.ogg')) ||
                                 (metadata.mediaUrl && metadata.mediaUrl.includes('.wav'));

            if (isAudioMessage) {
              const maxDuration = options.maxAudioDuration || 30;
              const audioDuration = metadata.whatsappMessage?.message?.audioMessage?.seconds;

              if (audioDuration && audioDuration > maxDuration) {
                return {
                  role: msg.role,
                  content: `Your audio message is too long for processing. Please send a shorter message (under ${maxDuration} seconds) or type your message instead.`,
                  metadata: msg.metadata
                };
              }

              if (metadata.mediaUrl) {
                audioPath = metadata.mediaUrl;
              } else if (metadata.audioPath) {
                audioPath = metadata.audioPath;
              }

              if (audioPath) {
                try {
                  let fullAudioPath: string;

                  if (audioPath.startsWith('/media/')) {
                    fullAudioPath = path.join(process.cwd(), 'public', audioPath.slice(1));
                  } else if (audioPath.startsWith('media/')) {
                    fullAudioPath = path.join(process.cwd(), 'public', audioPath);
                  } else if (path.isAbsolute(audioPath)) {
                    fullAudioPath = audioPath;
                  } else {
                    fullAudioPath = path.join(process.cwd(), audioPath);
                  }

                  try {
                    await fs.access(fullAudioPath);
                  } catch (fileError) {
                    throw new Error(`Audio file not found: ${fullAudioPath}`);
                  }

                  const transcribedText = await this.transcribeAudio(fullAudioPath);

                  const enhancedContent = transcribedText || 'Voice message (transcription failed)';

                  return {
                    role: msg.role,
                    content: enhancedContent,
                    metadata: msg.metadata
                  };
                } catch (transcriptionError) {
                  return {
                    role: msg.role,
                    content: `Voice message (transcription failed: ${transcriptionError instanceof Error ? transcriptionError.message : 'Unknown error'})`,
                    metadata: msg.metadata
                  };
                }
              }
            }
          } catch (error) {
          }
        }

        return {
          role: msg.role,
          content: msg.content,
          metadata: msg.metadata
        };
      }));

      const apiMessages = processedMessages;

      let modelToUse = options.model || "gpt-4-turbo";

      switch (modelToUse) {
        case 'gpt-4.1-nano':
          modelToUse = "gpt-4o-mini";
          break;
        case 'gpt-4.1-mini':
          modelToUse = "gpt-4o-mini";
          break;
        case 'gpt-4o':
          modelToUse = "gpt-4o";
          break;
        case 'gpt-4-turbo':
          modelToUse = "gpt-4-turbo";
          break;
        case 'gpt-3.5-turbo':
          modelToUse = "gpt-3.5-turbo";
          break;
        default:
          modelToUse = "gpt-4-turbo";
      }

      let maxTokens = 800;
      let temperature = 0.7;

      if (modelToUse === "gpt-4o-mini") {
        maxTokens = modelToUse.includes('nano') ? 400 : 600;
        temperature = 0.5;
      }

      const apiParams: any = {
        model: modelToUse,
        messages: apiMessages,
        temperature: temperature,
        max_tokens: maxTokens
      };

      if (options.enableFunctionCalling && options.functionDefinitions) {
        apiParams.tools = options.functionDefinitions.map(funcDef => ({
          type: "function",
          function: funcDef
        }));
        apiParams.tool_choice = "auto";

      }

      const response = await this.client.chat.completions.create(apiParams);

      let functionCalls: Array<{ name: string, arguments: any }> = [];

      if (response.choices[0]?.message?.tool_calls) {
        functionCalls = response.choices[0].message.tool_calls.map(tool => {
          if (tool.type === 'function') {
            try {
              return {
                name: tool.function.name,
                arguments: JSON.parse(tool.function.arguments)
              };
            } catch (e) {
              console.error(`OpenAI Provider: Error parsing function arguments:`, e);
              return {
                name: tool.function.name,
                arguments: tool.function.arguments
              };
            }
          }
          return null;
        }).filter(Boolean) as Array<{ name: string, arguments: any }>;

      }

      const text = response.choices[0]?.message?.content || "";

      let shouldGenerateTTS = false;
      const voiceResponseMode = options.voiceResponseMode || 'always';



      const userSentVoiceMessage = (() => {
        const userMessages = messages.filter(msg => msg.role === 'user');

        if (userMessages.length === 0) {
          return false;
        }

        const lastUserMessage = userMessages[userMessages.length - 1];

        let currentUserMessage = lastUserMessage;

        if (!lastUserMessage.metadata && userMessages.length >= 2) {
          const secondLastUserMessage = userMessages[userMessages.length - 2];

          if (lastUserMessage.content === secondLastUserMessage.content && secondLastUserMessage.metadata) {
            currentUserMessage = secondLastUserMessage;
          }
        }

        if (!currentUserMessage.metadata) {
          return false;
        }

        try {
          const metadata = JSON.parse(currentUserMessage.metadata);

          const hasMediaType = metadata.mediaType === 'audio';
          const hasAudioMessage = !!metadata.whatsappMessage?.message?.audioMessage;
          const hasAudioPath = !!metadata.audioPath;
          const hasAudioUrl = metadata.mediaUrl && (
            metadata.mediaUrl.includes('/audio/') ||
            metadata.mediaUrl.includes('.mp3') ||
            metadata.mediaUrl.includes('.ogg') ||
            metadata.mediaUrl.includes('.wav')
          );

          const isVoiceMessage = hasMediaType || hasAudioMessage || hasAudioPath || hasAudioUrl;

          if (isVoiceMessage) {
            const maxDuration = options.maxAudioDuration || 30;
            const audioDuration = metadata.whatsappMessage?.message?.audioMessage?.seconds;

            if (audioDuration && audioDuration > maxDuration) {
              return false;
            }
          }

          return isVoiceMessage;
        } catch (error) {
          return false;
        }
      })();

      if (options.enableTextToSpeech && text) {
        switch (voiceResponseMode) {
          case 'always':
            shouldGenerateTTS = true;
            break;

          case 'voice_only':
          case 'voice-to-voice':
            shouldGenerateTTS = userSentVoiceMessage;
            break;

          case 'never':
            shouldGenerateTTS = false;
            break;

          default:
            shouldGenerateTTS = true;
        }
      }

      let audioUrl: string | undefined;
      if (shouldGenerateTTS) {
        try {
          const ttsProvider = options.ttsProvider || 'openai';

          if (ttsProvider === 'elevenlabs') {
            if (!options.elevenLabsApiKey) {
              console.error('OpenAI Provider: ElevenLabs API key is required for ElevenLabs TTS');
            } else {
              const voiceId = options.elevenLabsCustomVoiceId && options.elevenLabsCustomVoiceId.trim()
                ? options.elevenLabsCustomVoiceId.trim()
                : options.elevenLabsVoiceId;

              if (!voiceId || voiceId === 'custom') {
                console.error('OpenAI Provider: ElevenLabs voice ID is required for ElevenLabs TTS');
              } else {
                const elevenLabsConfig: ElevenLabsConfig = {
                  apiKey: options.elevenLabsApiKey,
                  voiceId: voiceId,
                  model: options.elevenLabsModel,
                  stability: options.elevenLabsStability,
                  similarityBoost: options.elevenLabsSimilarityBoost,
                  style: options.elevenLabsStyle,
                  useSpeakerBoost: options.elevenLabsUseSpeakerBoost
                };
                audioUrl = await elevenLabsService.generateSpeech(text, elevenLabsConfig);
              }
            }
          } else {
            audioUrl = await this.generateSpeech(text, options.ttsVoice || 'alloy');
          }
        } catch (error) {
          console.error('OpenAI Provider: Error generating TTS audio:', error);
        }
      }

      return { text, audioUrl, functionCalls };
    } catch (error) {
      console.error('OpenAI Provider: Error in generateResponse', error);
      return {
        text: `I apologize, but I'm having trouble processing your request: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}

class AnthropicProvider implements AIProviderInterface {
  private client: Anthropic;

  constructor(apiKey: string) {
    this.client = new Anthropic({ apiKey });
  }

  async generateResponse(
    messages: ConversationMessage[],
    options: {
      systemPrompt?: string;
      enableFunctionCalling?: boolean;
      enableAudio?: boolean;
      enableImage?: boolean;
      enableVideo?: boolean;
      enableVoiceProcessing?: boolean;
      enableTextToSpeech?: boolean;
      ttsProvider?: string;
      ttsVoice?: string;
      voiceResponseMode?: string;
      maxAudioDuration?: number;
      model?: string;
      functionDefinitions?: any[];
      elevenLabsApiKey?: string;
      elevenLabsVoiceId?: string;
      elevenLabsCustomVoiceId?: string;
      elevenLabsModel?: string;
      elevenLabsStability?: number;
      elevenLabsSimilarityBoost?: number;
      elevenLabsStyle?: number;
      elevenLabsUseSpeakerBoost?: boolean;
    }
  ): Promise<{
    text: string;
    audioUrl?: string;
    functionCalls?: any[];
  }> {
    try {

      const anthropicMessages = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      const systemPrompt = options.systemPrompt || messages.find(m => m.role === 'system')?.content;

      const validMessages = anthropicMessages
        .filter(m => m.role !== 'system')
        .map(m => ({
          role: m.role === 'user' ? 'user' as const : 'assistant' as const,
          content: m.content
        }));

      // Prepare tools for function calling
      let tools: any[] = [];
      if (options.enableFunctionCalling && options.functionDefinitions && options.functionDefinitions.length > 0) {
        tools = options.functionDefinitions.map(func => ({
          name: func.name,
          description: func.description,
          input_schema: func.parameters
        }));
      }

      const requestParams: any = {
        model: options.model || "claude-3-sonnet-20240229",
        messages: validMessages,
        system: systemPrompt || "You are a helpful assistant.",
        max_tokens: 800
      };

      // Add tools if function calling is enabled
      if (tools.length > 0) {
        requestParams.tools = tools;
      }

      const response = await this.client.messages.create(requestParams);

      let responseText = "I apologize, but I couldn't generate a response. Please try again.";
      let functionCalls: Array<{ name: string, arguments: any }> = [];

      if (response.content && response.content.length > 0) {
        for (const content of response.content) {
          if ('text' in content) {
            responseText = content.text;
          } else if ('type' in content && content.type === 'tool_use') {
            // Handle function calls
            functionCalls.push({
              name: content.name,
              arguments: content.input
            });
          }
        }
      }

      return {
        text: responseText,
        functionCalls: functionCalls.length > 0 ? functionCalls : undefined
      };
    } catch (error) {
      console.error('Anthropic Provider: Error in generateResponse', error);
      return {
        text: `I apologize, but I'm having trouble processing your request: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}

class GeminiProvider implements AIProviderInterface {
  private client: GoogleGenerativeAI;

  constructor(apiKey: string) {
    this.client = new GoogleGenerativeAI(apiKey);
  }

  async generateResponse(
    messages: ConversationMessage[],
    options: {
      systemPrompt?: string;
      enableFunctionCalling?: boolean;
      enableAudio?: boolean;
      enableImage?: boolean;
      enableVideo?: boolean;
      enableVoiceProcessing?: boolean;
      enableTextToSpeech?: boolean;
      ttsProvider?: string;
      ttsVoice?: string;
      voiceResponseMode?: string;
      maxAudioDuration?: number;
      model?: string;
      functionDefinitions?: any[];
      elevenLabsApiKey?: string;
      elevenLabsVoiceId?: string;
      elevenLabsCustomVoiceId?: string;
      elevenLabsModel?: string;
      elevenLabsStability?: number;
      elevenLabsSimilarityBoost?: number;
      elevenLabsStyle?: number;
      elevenLabsUseSpeakerBoost?: boolean;
    }
  ): Promise<{
    text: string;
    audioUrl?: string;
    functionCalls?: any[];
  }> {
    try {

      const systemMessage = messages.find(m => m.role === 'system');
      const systemPrompt = systemMessage?.content || options.systemPrompt || 'You are a helpful assistant.';

      const conversationMessages = messages.filter(m => m.role !== 'system');

      if (conversationMessages.length === 0) {
        return { text: "I'm ready to help you. What can I assist you with today?" };
      }

      const latestMessage = conversationMessages[conversationMessages.length - 1];
      let hasMedia = false;
      let mediaUrl: string | undefined;

      if (latestMessage.role === 'user' && latestMessage.metadata) {
        try {
          const metadata = JSON.parse(latestMessage.metadata as string);
          if (metadata.mediaUrl) {
            mediaUrl = metadata.mediaUrl;
            hasMedia = true;
          }
        } catch (error) {
          console.error('Gemini Provider: Failed to parse message metadata:', error);
        }
      }


      const modelOptions = ["gemini-2.5-pro-preview-03-25", "gemini-pro", "gemini-1.5-flash", "gemini-2.5-flash-preview-04-17"];

      let lastError: Error | null = null;
      let result = null;

      for (const modelName of modelOptions) {
        try {
          // Prepare tools for function calling
          let tools: any[] = [];
          if (options.enableFunctionCalling && options.functionDefinitions && options.functionDefinitions.length > 0) {
            tools = options.functionDefinitions.map(func => ({
              functionDeclarations: [{
                name: func.name,
                description: func.description,
                parameters: func.parameters
              }]
            }));
          }

          const modelConfig: any = {
            model: modelName,
            generationConfig: {
              temperature: 0.7,
              maxOutputTokens: 800,
            }
          };

          // Add tools if function calling is enabled
          if (tools.length > 0) {
            modelConfig.tools = tools;
          }

          const model = this.client.getGenerativeModel(modelConfig);

          if (hasMedia &&
            (options.enableImage || options.enableVideo || options.enableAudio) &&
            mediaUrl &&
            modelName.includes('2.5-pro')) {

            const historyParts: any[] = [];

            historyParts.push({
              role: 'user',
              parts: [{ text: `System: ${systemPrompt}` }]
            });

            historyParts.push({
              role: 'model',
              parts: [{ text: "I'll follow these instructions." }]
            });

            for (let i = 0; i < conversationMessages.length - 1; i++) {
              const msg = conversationMessages[i];
              historyParts.push({
                role: msg.role === 'user' ? 'user' : 'model',
                parts: [{ text: msg.content }]
              });
            }

            const mediaType = mediaUrl.includes('/image/') ? 'image/jpeg' :
              mediaUrl.includes('/video/') ? 'video/mp4' :
                mediaUrl.includes('/audio/') ? 'audio/mpeg' : 'application/octet-stream';

            const fs = await import('fs/promises');
            const path = await import('path');


            try {
              const mediaPath = path.join(process.cwd(), mediaUrl.startsWith('/') ? mediaUrl.slice(1) : mediaUrl);

              const mediaBuffer = await fs.readFile(mediaPath);
              const mediaBase64 = mediaBuffer.toString('base64');

              const userMessage = latestMessage.content || "What is in this media file?";

              let mediaContent = null;

              if (mediaType.startsWith('image/') && options.enableImage) {
                mediaContent = {
                  role: 'user',
                  parts: [
                    { text: userMessage },
                    {
                      inlineData: {
                        data: mediaBase64,
                        mimeType: mediaType
                      }
                    }
                  ]
                };
              } else if (mediaType.startsWith('video/') && options.enableVideo) {
                mediaContent = {
                  role: 'user',
                  parts: [
                    { text: userMessage },
                    {
                      inlineData: {
                        data: mediaBase64,
                        mimeType: mediaType
                      }
                    }
                  ]
                };
              } else if (mediaType.startsWith('audio/') && options.enableAudio) {
                mediaContent = {
                  role: 'user',
                  parts: [
                    { text: userMessage },
                    {
                      inlineData: {
                        data: mediaBase64,
                        mimeType: mediaType
                      }
                    }
                  ]
                };
              } else {
                mediaContent = {
                  role: 'user',
                  parts: [{ text: userMessage }]
                };
              }


              const contents = [...historyParts, mediaContent];
              result = await model.generateContent({ contents } as any);


            } catch (error) {
              const mediaError = error as Error;
              console.error(`Gemini Provider: Error processing media:`, mediaError);
              throw new Error(`Media processing failed: ${mediaError.message}`);
            }

          } else {

            const promptText = `${systemPrompt}\n\nConversation history:\n`;

            let conversationText = '';
            conversationMessages.forEach((msg) => {
              const role = msg.role === 'user' ? 'User' : 'Assistant';
              conversationText += `${role}: ${msg.content}\n`;
            });

            const finalPrompt = promptText + conversationText + '\nPlease respond to the latest message in the conversation.';


            result = await model.generateContent(finalPrompt);
          }

          break;
        } catch (error) {
          const modelError = error as Error;
          console.error(`Gemini Provider: Failed to use model "${modelName}":`, modelError.message);
          lastError = modelError;

        }
      }

      if (!result) {
        console.error('Gemini Provider: All models failed to generate content');
        throw lastError || new Error('All Gemini models failed without specific error');
      }

      let responseText = "";
      let functionCalls: Array<{ name: string, arguments: any }> = [];

      // Extract text and function calls from response
      const response = result.response;
      if (response.candidates && response.candidates.length > 0) {
        const candidate = response.candidates[0];
        if (candidate.content && candidate.content.parts) {
          for (const part of candidate.content.parts) {
            if (part.text) {
              responseText += part.text;
            } else if (part.functionCall) {
              functionCalls.push({
                name: part.functionCall.name,
                arguments: part.functionCall.args || {}
              });
            }
          }
        }
      }

      // Fallback to simple text extraction if no parts found
      if (!responseText && !functionCalls.length) {
        responseText = result.response.text();
      }

      return {
        text: responseText || "I apologize, but I couldn't generate a response. Please try again.",
        functionCalls: functionCalls.length > 0 ? functionCalls : undefined
      };
    } catch (error) {
      console.error('Gemini Provider: Error in generateResponse:', error);
      return {
        text: "I'm sorry, I encountered a technical issue. Please try again with a different question."
      };
    }
  }
}

class TranslationService {
  /**
   * Detect if text is in a foreign language (not the target language)
   */
  private async detectLanguage(text: string, provider: string, apiKey: string): Promise<string> {
    try {
      if (provider === 'openai') {
        const openai = new OpenAI({ apiKey: apiKey || process.env.OPENAI_API_KEY });

        const response = await openai.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'You are a language detection expert. Respond with only the ISO 639-1 language code (2 letters) of the given text. Examples: "en" for English, "es" for Spanish, "fr" for French, etc. If uncertain, respond with "unknown".'
            },
            {
              role: 'user',
              content: `Detect the language of this text: "${text}"`
            }
          ],
          max_tokens: 10,
          temperature: 0
        });

        return response.choices[0]?.message?.content?.trim().toLowerCase() || 'unknown';
      }


      return this.simpleLanguageDetection(text);
    } catch (error) {
      console.error('Language detection error:', error);
      return 'unknown';
    }
  }

  /**
   * Simple heuristic language detection as fallback
   */
  private simpleLanguageDetection(text: string): string {

    const patterns = {
      es: /\b(hola|gracias|por favor|buenos días|buenas tardes|cómo estás|qué tal)\b/i,
      fr: /\b(bonjour|merci|s'il vous plaît|comment allez-vous|bonsoir|salut)\b/i,
      de: /\b(hallo|danke|bitte|guten tag|wie geht es|auf wiedersehen)\b/i,
      it: /\b(ciao|grazie|prego|buongiorno|come stai|arrivederci)\b/i,
      pt: /\b(olá|obrigado|por favor|bom dia|como está|tchau)\b/i,
      ru: /[а-яё]/i,
      ar: /[ا-ي]/,
      zh: /[\u4e00-\u9fff]/,
      ja: /[\u3040-\u309f\u30a0-\u30ff]/,
      ko: /[\uac00-\ud7af]/
    };

    for (const [lang, pattern] of Object.entries(patterns)) {
      if (pattern.test(text)) {
        return lang;
      }
    }

    return 'en'; // Default to English if no pattern matches
  }

  /**
   * Translate text using the specified provider
   */
  async translateText(
    text: string,
    targetLanguage: string,
    provider: string,
    apiKey: string
  ): Promise<string> {
    try {
      if (provider === 'openai') {
        const openai = new OpenAI({ apiKey: apiKey || process.env.OPENAI_API_KEY });

        const languageNames: Record<string, string> = {
          en: 'English', es: 'Spanish', fr: 'French', de: 'German', it: 'Italian',
          pt: 'Portuguese', ru: 'Russian', ja: 'Japanese', ko: 'Korean', zh: 'Chinese',
          ar: 'Arabic', hi: 'Hindi', tr: 'Turkish', nl: 'Dutch', sv: 'Swedish',
          da: 'Danish', no: 'Norwegian', fi: 'Finnish', pl: 'Polish', cs: 'Czech',
          hu: 'Hungarian', ro: 'Romanian', bg: 'Bulgarian', hr: 'Croatian',
          sk: 'Slovak', sl: 'Slovenian', et: 'Estonian', lv: 'Latvian',
          lt: 'Lithuanian', mt: 'Maltese', ga: 'Irish', cy: 'Welsh'
        };

        const targetLanguageName = languageNames[targetLanguage] || targetLanguage;

        const response = await openai.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: `You are a professional translator. Translate the given text to ${targetLanguageName}. Maintain the original tone and meaning. Respond with only the translation, no additional text.`
            },
            {
              role: 'user',
              content: text
            }
          ],
          max_tokens: 1000,
          temperature: 0.3
        });

        return response.choices[0]?.message?.content?.trim() || text;
      }


      console.warn('Google Translate provider not yet implemented, using OpenAI fallback');
      return await this.translateText(text, targetLanguage, 'openai', apiKey);

    } catch (error) {
      console.error('Translation error:', error);
      return text; // Return original text if translation fails
    }
  }

  /**
   * Check if translation is needed and perform translation
   */
  async processTranslation(
    text: string,
    targetLanguage: string,
    provider: string,
    apiKey: string
  ): Promise<{ needsTranslation: boolean; translatedText?: string; detectedLanguage?: string }> {
    try {

      const detectedLanguage = await this.detectLanguage(text, provider, apiKey);


      const needsTranslation = detectedLanguage !== targetLanguage && detectedLanguage !== 'unknown';

      if (!needsTranslation) {
        return { needsTranslation: false, detectedLanguage };
      }


      const translatedText = await this.translateText(text, targetLanguage, provider, apiKey);

      return {
        needsTranslation: true,
        translatedText,
        detectedLanguage
      };
    } catch (error) {
      console.error('Translation processing error:', error);
      return { needsTranslation: false };
    }
  }
}

class AIAssistantService {
  public translationService = new TranslationService();

  private getProvider(provider: string, apiKey: string): AIProviderInterface {

    if (!provider) {
      provider = 'gemini';
    }

    switch (provider.toLowerCase()) {
      case 'openai':
        const openaiKey = apiKey || process.env.OPENAI_API_KEY;
        if (!openaiKey) {

          return this.getProvider('gemini', '');
        }
        return new OpenAIProvider(openaiKey);

      case 'anthropic':
      case 'claude':
        const anthropicKey = apiKey || process.env.ANTHROPIC_API_KEY;
        if (!anthropicKey) {
          return this.getProvider('gemini', '');
        }
        return new AnthropicProvider(anthropicKey);

      case 'gemini':
        const geminiKey = apiKey || process.env.GOOGLE_API_KEY;

        if (!geminiKey) {

          throw new Error('Google API key is required for Gemini provider. Please set the GOOGLE_API_KEY environment variable or provide an API key in the node settings.');
        }
        return new GeminiProvider(geminiKey);

      case 'deepseek':
        return this.getProvider('gemini', '');

      default:
        return this.getProvider('gemini', '');
    }
  }

  async processMessage(
    message: Message,
    _conversation: Conversation,
    contact: Contact,
    _channelConnection: ChannelConnection,
    config: {
      provider: string;
      model: string;
      apiKey: string;
      systemPrompt: string;
      enableHistory: boolean;
      historyLimit?: number;
      enableAudio: boolean;
      enableImage: boolean;
      enableVideo: boolean;
      enableVoiceProcessing?: boolean;
      enableTextToSpeech?: boolean;
      ttsProvider?: string;
      ttsVoice?: string;
      voiceResponseMode?: string;
      maxAudioDuration?: number;
      enableFunctionCalling: boolean;
      enableTaskExecution?: boolean;
      tasks?: any[];
      enableGoogleCalendar?: boolean;
      calendarFunctions?: any[];
      elevenLabsApiKey?: string;
      elevenLabsVoiceId?: string;
      elevenLabsCustomVoiceId?: string;
      elevenLabsModel?: string;
      elevenLabsStability?: number;
      elevenLabsSimilarityBoost?: number;
      elevenLabsStyle?: number;
      elevenLabsUseSpeakerBoost?: boolean;
    },
    conversationHistory: Message[] = []
  ): Promise<{
    text: string;
    audioUrl?: string;
    functionCalls?: any[];
    triggeredTasks?: string[];
    triggeredCalendarFunctions?: any[];
  }> {
    try {
      if (!config.apiKey && config.provider !== 'gemini') {
        console.error('AI Assistant: API key is missing');
        return {
          text: "Error: API key is missing. Please configure the AI Assistant node with a valid API key."
        };
      }

      const provider = this.getProvider(config.provider, config.apiKey);

      let functionDefinitions: any[] = [];
      const shouldEnableTaskFunctions = config.enableTaskExecution && config.tasks && config.tasks.length > 0;
      const shouldEnableCalendarFunctions = config.enableGoogleCalendar && config.calendarFunctions && config.calendarFunctions.length > 0;
      const shouldEnableFunctionCalling = shouldEnableTaskFunctions || shouldEnableCalendarFunctions;

      // Add task execution functions
      if (shouldEnableTaskFunctions && config.tasks) {
        functionDefinitions = config.tasks
          .filter(task => task.enabled)
          .map(task => task.functionDefinition);
      }

      // Add calendar functions
      if (shouldEnableCalendarFunctions && config.calendarFunctions) {
        const calendarFunctionDefs = config.calendarFunctions
          .filter((func: any) => func.enabled)
          .map((func: any) => func.functionDefinition);
        functionDefinitions = [...functionDefinitions, ...calendarFunctionDefs];
      }





      let enhancedSystemPrompt = config.systemPrompt;
      if (shouldEnableFunctionCalling && functionDefinitions.length > 0) {
        enhancedSystemPrompt = `${config.systemPrompt}

IMPORTANT FUNCTION CALLING RULES:
- Only call functions when the user explicitly requests the specific action
- Do NOT call functions for general greetings, questions, or casual conversation
- Do NOT call functions unless there is clear, unambiguous user intent
- For greetings like "Hello", "Hi", "How are you?" - respond normally without calling any functions
- Only use functions when the user specifically asks for something that matches the function's purpose
- Be conservative - when in doubt, respond normally without calling functions`;
      }

      const messages: ConversationMessage[] = config.enableHistory
        ? generateMessagesWithHistory(message, contact, enhancedSystemPrompt, conversationHistory, config.historyLimit || 5)
        : [
          { role: 'system', content: enhancedSystemPrompt },
          { role: 'user', content: message.content || '' }
        ] as ConversationMessage[];

      try {
        const response = await provider.generateResponse(messages, {
          systemPrompt: enhancedSystemPrompt,
          enableFunctionCalling: shouldEnableFunctionCalling,
          enableAudio: config.enableAudio,
          enableImage: config.enableImage,
          enableVideo: config.enableVideo,
          enableVoiceProcessing: config.enableVoiceProcessing,
          enableTextToSpeech: config.enableTextToSpeech,
          ttsProvider: config.ttsProvider,
          ttsVoice: config.ttsVoice,
          voiceResponseMode: config.voiceResponseMode,
          maxAudioDuration: config.maxAudioDuration,
          functionDefinitions,
          model: config.model,
          elevenLabsApiKey: config.elevenLabsApiKey,
          elevenLabsVoiceId: config.elevenLabsVoiceId,
          elevenLabsCustomVoiceId: config.elevenLabsCustomVoiceId,
          elevenLabsModel: config.elevenLabsModel,
          elevenLabsStability: config.elevenLabsStability,
          elevenLabsSimilarityBoost: config.elevenLabsSimilarityBoost,
          elevenLabsStyle: config.elevenLabsStyle,
          elevenLabsUseSpeakerBoost: config.elevenLabsUseSpeakerBoost
        });

        const triggeredTasks: string[] = [];
        const triggeredCalendarFunctions: any[] = [];

        if (response.functionCalls && response.functionCalls.length > 0) {


          // Handle task execution functions
          if (config.enableTaskExecution && config.tasks) {
            for (const functionCall of response.functionCalls) {
              const matchingTask = config.tasks.find(task =>
                task.enabled && task.functionDefinition.name === functionCall.name
              );
              if (matchingTask) {

                triggeredTasks.push(matchingTask.outputHandle);
              }
            }
          }

          // Handle calendar functions
          if (config.enableGoogleCalendar && config.calendarFunctions) {
            for (const functionCall of response.functionCalls) {
              const matchingCalendarFunction = config.calendarFunctions.find((func: any) =>
                func.enabled && func.functionDefinition.name === functionCall.name
              );
              if (matchingCalendarFunction) {

                triggeredCalendarFunctions.push({
                  ...functionCall,
                  functionConfig: matchingCalendarFunction
                });
              }
            }
          }
        }

        return {
          ...response,
          triggeredTasks,
          triggeredCalendarFunctions
        };
      } catch (providerError) {
        console.error('AI Assistant: Error calling provider.generateResponse:', providerError);
        return {
          text: "I apologize, but I'm having trouble processing your request at the moment. Please try again later."
        };
      }
    } catch (error) {
      console.error('Error in AI Assistant service:', error);
      return {
        text: `Error processing your message: ${error instanceof Error ? error.message : 'Unknown error occurred'}`
      };
    }
  }
}


const aiAssistantService = new AIAssistantService();
export default aiAssistantService;