import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { toast } from "@/hooks/use-toast";
import { useQueryClient } from '@tanstack/react-query';
import { Loader2, RefreshCw, Wifi, WifiOff, AlertTriangle, CheckCircle, Clock, Power, PowerOff } from "lucide-react";

interface ConnectionControlProps {
  connectionId: number;
  status: string;
  onStatusChange?: (newStatus: string) => void;
  onReconnectClick?: () => void;
  diagnostics?: any;
  showDiagnostics?: boolean;
  channelType?: string;
}

const ConnectionControl: React.FC<ConnectionControlProps> = ({
  connectionId,
  status,
  onStatusChange,
  onReconnectClick,
  diagnostics,
  showDiagnostics = false,
  channelType
}) => {
  const queryClient = useQueryClient();
  const [isReconnecting, setIsReconnecting] = useState(false);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [lastReconnectTime, setLastReconnectTime] = useState<Date | null>(null);
  const [localStatus, setLocalStatus] = useState(status);

  // Sync local status with prop status and handle state transitions
  useEffect(() => {
    setLocalStatus(status);

    if (status === 'reconnecting') {
      setReconnectAttempts(prev => prev + 1);
      setLastReconnectTime(new Date());
    } else if (status === 'connected' || status === 'active') {
      setReconnectAttempts(0);
      setIsReconnecting(false);
      setIsDisconnecting(false);
    } else if (status === 'disconnected' || status === 'error' || status === 'failed') {
      setIsReconnecting(false);
      setIsDisconnecting(false);
    }
  }, [status]);

  // Auto-refresh connection data periodically for real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Invalidate and refetch channel connections to get latest status
      queryClient.invalidateQueries({ queryKey: ['/api/channel-connections'] });
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [queryClient]);

  const handleReconnect = async () => {
    if (!connectionId || isReconnecting || isDisconnecting) return;

    if (onReconnectClick) {
      onReconnectClick();
      return;
    }

    setIsReconnecting(true);
    setReconnectAttempts(prev => prev + 1);
    setLastReconnectTime(new Date());

    try {
      const response = await fetch(`/api/channel-connections/${connectionId}/reconnect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to reconnect WhatsApp');
      }

      toast({
        title: "Reconnection initiated",
        description: `Attempting to reconnect your WhatsApp connection... (Attempt ${reconnectAttempts + 1})`,
      });

      // Update local status immediately for better UX
      setLocalStatus('reconnecting');
      if (onStatusChange) {
        onStatusChange('reconnecting');
      }

      // Refresh connection data after a short delay to get updated status
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['/api/channel-connections'] });
      }, 2000);

    } catch (error) {
      console.error('Error reconnecting WhatsApp:', error);
      toast({
        title: "Reconnection failed",
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive"
      });
      setIsReconnecting(false);
      setLocalStatus('error');

      // Refresh connection data to get accurate status
      queryClient.invalidateQueries({ queryKey: ['/api/channel-connections'] });
    }
  };

  const handleDisconnect = async () => {
    if (!connectionId || isDisconnecting || isReconnecting) return;

    setIsDisconnecting(true);

    try {
      const response = await fetch(`/api/whatsapp/disconnect/${connectionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to disconnect WhatsApp');
      }

      toast({
        title: "Disconnection successful",
        description: "Your WhatsApp connection has been disconnected.",
      });

      // Update local status immediately for better UX
      setLocalStatus('disconnected');
      if (onStatusChange) {
        onStatusChange('disconnected');
      }

      // Refresh connection data to get updated status
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['/api/channel-connections'] });
      }, 1000);

    } catch (error) {
      console.error('Error disconnecting WhatsApp:', error);
      toast({
        title: "Disconnection failed",
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive"
      });
      setIsDisconnecting(false);

      // Refresh connection data to get accurate status
      queryClient.invalidateQueries({ queryKey: ['/api/channel-connections'] });
    }
  };

  const getStatusInfo = () => {
    // Use local status for more responsive UI updates
    const normalizedStatus = localStatus?.toLowerCase()?.trim() || 'unknown';
    switch (normalizedStatus) {
      case 'connected':
      case 'active':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          label: 'Connected',
          description: 'WhatsApp connection is active and ready'
        };
      case 'connecting':
        return {
          icon: Loader2,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          label: 'Connecting',
          description: 'Establishing WhatsApp connection...'
        };
      case 'reconnecting':
        return {
          icon: RefreshCw,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          label: 'Reconnecting',
          description: `Reconnecting... (Attempt ${reconnectAttempts})`
        };
      case 'qr_code':
        return {
          icon: Clock,
          color: 'text-purple-600',
          bgColor: 'bg-purple-100',
          label: 'QR Code',
          description: 'Waiting for QR code scan'
        };
      case 'error':
      case 'failed':
      case 'timeout':
        return {
          icon: AlertTriangle,
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          label: 'Error',
          description: 'Connection failed - click to retry'
        };
      case 'disconnected':
      case 'logged_out':
      case 'inactive':
        return {
          icon: WifiOff,
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          label: 'Disconnected',
          description: 'WhatsApp connection is offline'
        };
      case 'unknown':
      case 'offline':
      case 'closed':
      default:
        return {
          icon: WifiOff,
          color: 'text-orange-600',
          bgColor: 'bg-orange-100',
          label: 'Unknown',
          description: 'Connection status unknown - click reconnect to restore connection'
        };
    }
  };

  const formatLastReconnectTime = () => {
    if (!lastReconnectTime) return '';
    const now = new Date();
    const diff = now.getTime() - lastReconnectTime.getTime();
    const minutes = Math.floor(diff / 60000);
    const seconds = Math.floor((diff % 60000) / 1000);

    if (minutes > 0) {
      return `${minutes}m ${seconds}s ago`;
    }
    return `${seconds}s ago`;
  };

  const normalizedStatus = localStatus?.toLowerCase()?.trim() || 'unknown';



  const isConnected = normalizedStatus === 'connected' || normalizedStatus === 'active';

  const disconnectedStates = [
    'error', 'disconnected', 'failed', 'timeout', 'logged_out',
    'inactive', 'unknown', 'offline', 'closed', 'qr_code'
  ];


  const isWhatsAppOfficial = channelType === 'whatsapp_official';
  const showReconnect = onReconnectClick && disconnectedStates.includes(normalizedStatus) && !isReconnecting && !isWhatsAppOfficial;

  const showDisconnect = onReconnectClick && isConnected && !isDisconnecting && !isReconnecting && !isWhatsAppOfficial;



  const statusInfo = getStatusInfo();
  const StatusIcon = statusInfo.icon;

  return (
    <TooltipProvider>
      <div className="flex items-center gap-2">

        {showDisconnect && (
          <Button
            onClick={handleDisconnect}
            disabled={isDisconnecting}
            size="sm"
            variant="outline"
            className="h-7 px-2 text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
          >
            {isDisconnecting ? (
              <>
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                <span className="text-xs">Disconnecting...</span>
              </>
            ) : (
              <>
                <PowerOff className="h-3 w-3 mr-1" />
                <span className="text-xs">Disconnect</span>
              </>
            )}
          </Button>
        )}

        {showReconnect && (
          <Button
            onClick={handleReconnect}
            disabled={isReconnecting}
            size="sm"
            variant="outline"
            className="h-7 px-2 text-green-600 hover:text-green-700 hover:bg-green-50 border-green-200"
          >
            {isReconnecting ? (
              <>
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                <span className="text-xs">Reconnecting...</span>
              </>
            ) : normalizedStatus === 'qr_code' ? (
              <>
                <RefreshCw className="h-3 w-3 mr-1" />
                <span className="text-xs">Rescan QR</span>
              </>
            ) : (
              <>
                <Power className="h-3 w-3 mr-1" />
                <span className="text-xs">Reconnect</span>
              </>
            )}
          </Button>
        )}

        {diagnostics && showDiagnostics && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge
                variant={diagnostics.healthScore > 70 ? "default" : diagnostics.healthScore > 40 ? "secondary" : "destructive"}
                className="text-xs"
              >
                {diagnostics.healthScore}%
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-sm">
                <p className="font-medium">Connection Health</p>
                <p className="text-xs text-muted-foreground">
                  Score: {diagnostics.healthScore}/100
                </p>
                <p className="text-xs text-muted-foreground">
                  Errors: {diagnostics.errorCount}
                </p>
                {diagnostics.lastError && (
                  <p className="text-xs text-red-500 mt-1">
                    Last Error: {diagnostics.lastError}
                  </p>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </TooltipProvider>
  );
};

export default ConnectionControl;