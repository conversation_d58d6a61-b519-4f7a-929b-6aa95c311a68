import { useState } from 'react';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { formatDistanceToNow } from 'date-fns';
import { MoreHorizontal, User, Clock, Calendar, Tag } from 'lucide-react';
import { Deal } from '@shared/schema';
import { apiRequest } from '@/lib/queryClient';
import { useLocation } from 'wouter';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ContactAvatar } from '@/components/contacts/ContactAvatar';
import EditDealModal from './EditDealModal';
import DealDetailsModal from './DealDetailsModal';
import ContactDetailsModal from './ContactDetailsModal';

interface DealCardProps {
  deal: Deal;
}

export default function DealCard({ deal }: DealCardProps) {
  const queryClient = useQueryClient();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditDealModalOpen, setIsEditDealModalOpen] = useState(false);
  const [isDealDetailsModalOpen, setIsDealDetailsModalOpen] = useState(false);
  const [isContactDetailsModalOpen, setIsContactDetailsModalOpen] = useState(false);

  const { data: contact } = useQuery({
    queryKey: ['/api/contacts', deal.contactId],
    queryFn: () => apiRequest('GET', `/api/contacts/${deal.contactId}`)
      .then(res => res.json()),
    enabled: !!deal.contactId,
  });

  const { data: assignedUser } = useQuery({
    queryKey: ['/api/users', deal.assignedToUserId],
    queryFn: () => apiRequest('GET', `/api/users/${deal.assignedToUserId}`)
      .then(res => res.json()),
    enabled: !!deal.assignedToUserId,
  });

  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest('DELETE', `/api/deals/${id}`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/deals'] });

      if (deal.stage) {
        queryClient.invalidateQueries({ queryKey: [`/api/deals/stage/${deal.stage}`] });
      }

      if (deal.stageId) {
        queryClient.invalidateQueries({ queryKey: [`/api/deals/stageId/${deal.stageId}`] });
      }
    },
    onError: (error: Error) => {
      console.error('Error deleting deal:', error);
    },
  });

  const handleDelete = () => {
    deleteMutation.mutate(deal.id);
    setIsDeleteDialogOpen(false);
  };

  const handleEditDeal = () => {
    setIsEditDealModalOpen(true);
  };

  const handleViewDetails = () => {
    setIsDealDetailsModalOpen(true);
  };

  const handleViewContact = () => {
    setIsContactDetailsModalOpen(true);
  };

  const handleContactClick = () => {
    if (!contact?.id || !contact?.identifierType) return;

    localStorage.setItem('selectedContactId', contact.id.toString());
    localStorage.setItem('selectedChannelType', contact.identifierType);

    setLocation('/');

    toast({
      title: "Redirecting to inbox",
      description: `Opening conversation with ${contact.name}`,
    });
  };

  const priorityColors = {
    low: 'bg-blue-500',
    medium: 'bg-yellow-500',
    high: 'bg-red-500',
  };

  const priorityColor = priorityColors[deal.priority as keyof typeof priorityColors] || 'bg-gray-500';

  return (
    <div className="bg-card border rounded-md shadow-sm p-4 mb-3 cursor-grab hover:shadow transition-shadow duration-200">
      <div className="flex justify-between items-start mb-3">
        <h3 className="text-base font-medium truncate">{deal.title}</h3>
        <div className="flex items-center space-x-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className={`h-2.5 w-2.5 rounded-full ${priorityColor}`} />
              </TooltipTrigger>
              <TooltipContent>
                <p className="capitalize">{deal.priority} priority</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={handleEditDeal}>Edit Deal</DropdownMenuItem>
              <DropdownMenuItem onClick={handleViewDetails}>View Details</DropdownMenuItem>
              <DropdownMenuItem onClick={handleViewContact}>View Contact</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setIsDeleteDialogOpen(true)} className="text-destructive">
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {contact && (
        <div
          className="flex items-center mb-3 p-2 rounded-md hover:bg-gray-50 cursor-pointer transition-colors"
          onClick={handleContactClick}
          title={`Open conversation with ${contact.name}`}
        >
          <ContactAvatar
            contact={contact}
            size="sm"
            className="mr-3"
          />
          <div className="flex-1 min-w-0">
            <div className="flex items-center">
              <span className="text-sm font-medium text-gray-900 truncate">
                {contact.name}
              </span>
              {contact.identifierType && (
                <span className="ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-100">
                  {contact.identifierType === 'whatsapp' && <i className="ri-whatsapp-line text-green-600 mr-1"></i>}
                  {contact.identifierType === 'whatsapp_unofficial' && <i className="ri-whatsapp-line text-green-600 mr-1"></i>}
                  {contact.identifierType === 'messenger' && <i className="ri-messenger-line text-blue-600 mr-1"></i>}
                  {contact.identifierType === 'instagram' && <i className="ri-instagram-line text-pink-600 mr-1"></i>}
                  {contact.identifierType}
                </span>
              )}
            </div>
            {assignedUser && (
              <div className="text-xs text-gray-500 mt-1">
                Assigned to: {assignedUser.fullName || assignedUser.username}
              </div>
            )}
          </div>
        </div>
      )}

      <div className="space-y-2 text-sm">
        {deal.description && (
          <p className="text-muted-foreground text-sm line-clamp-2 mb-3">{deal.description}</p>
        )}

        {deal.value && (
          <div className="font-medium">
            ${new Intl.NumberFormat().format(deal.value)}
          </div>
        )}

        <div className="flex flex-wrap gap-2 text-xs text-muted-foreground pt-2">
          {deal.assignedToUserId && (
            <div className="flex items-center">
              <User className="h-3.5 w-3.5 mr-1" />
              <span>Assigned</span>
            </div>
          )}

          {deal.lastActivityAt && (
            <div className="flex items-center" title={new Date(deal.lastActivityAt).toLocaleString()}>
              <Clock className="h-3.5 w-3.5 mr-1" />
              <span>{formatDistanceToNow(new Date(deal.lastActivityAt), { addSuffix: true })}</span>
            </div>
          )}

          {deal.dueDate && (
            <div className="flex items-center" title={new Date(deal.dueDate).toLocaleString()}>
              <Calendar className="h-3.5 w-3.5 mr-1" />
              <span>{new Date(deal.dueDate).toLocaleDateString()}</span>
            </div>
          )}
        </div>

        {deal.tags && deal.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {deal.tags.map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs flex items-center">
                <Tag className="h-3 w-3 mr-1" />
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </div>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will remove the deal "{deal.title}" from your pipeline. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <EditDealModal
        deal={deal}
        isOpen={isEditDealModalOpen}
        onClose={() => setIsEditDealModalOpen(false)}
      />

      <DealDetailsModal
        deal={deal}
        isOpen={isDealDetailsModalOpen}
        onClose={() => setIsDealDetailsModalOpen(false)}
      />

      <ContactDetailsModal
        contactId={deal.contactId}
        isOpen={isContactDetailsModalOpen}
        onClose={() => setIsContactDetailsModalOpen(false)}
      />
    </div>
  );
}