import { useEffect, useRef, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { usePageVisibility } from './usePageVisibility';
import { useConversations } from '@/context/ConversationContext';

interface AutoRefreshConfig {
  enabled?: boolean;
  intervalMs?: number;
  minHiddenTimeForRefresh?: number;
  maxRetries?: number;
  retryDelayMs?: number;
}

const DEFAULT_CONFIG: Required<AutoRefreshConfig> = {
  enabled: true,
  intervalMs: 5 * 60 * 1000, // 5 minutes
  minHiddenTimeForRefresh: 10 * 60 * 1000, // 10 minutes
  maxRetries: 3,
  retryDelayMs: 2000
};

export function useInboxAutoRefresh(config: AutoRefreshConfig = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const queryClient = useQueryClient();
  const { refetchConversations, refetchContacts } = useConversations();
  const { isVisible, wasHidden, getHiddenDuration, resetWasHidden } = usePageVisibility();
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = useRef(0);
  const isRefreshingRef = useRef(false);
  const lastRefreshTimeRef = useRef(Date.now());

  // Check if user is actively interacting (typing, clicking, etc.)
  const isUserInteracting = useCallback(() => {
    const activeElement = document.activeElement;
    
    // Check if user is typing in message input
    const isTypingInMessageInput = activeElement?.closest('[data-message-input]') !== null;
    
    // Check if user is in any input field
    const isInInputField = activeElement && (
      activeElement.tagName === 'INPUT' ||
      activeElement.tagName === 'TEXTAREA' ||
      activeElement.hasAttribute('contenteditable')
    );

    // Check if user recently interacted (within last 30 seconds)
    const timeSinceLastRefresh = Date.now() - lastRefreshTimeRef.current;
    const recentlyRefreshed = timeSinceLastRefresh < 30000;

    return isTypingInMessageInput || isInInputField || recentlyRefreshed;
  }, []);

  // Silent refresh function with retry logic
  const performSilentRefresh = useCallback(async (reason: string = 'periodic') => {
    if (!finalConfig.enabled || isRefreshingRef.current || isUserInteracting()) {
      return;
    }

    isRefreshingRef.current = true;
    
    try {
      // Store current scroll position and selected conversation
      const conversationListElement = document.querySelector('[data-conversation-list]');
      const scrollTop = conversationListElement?.scrollTop || 0;
      
      // Perform the refresh
      await Promise.allSettled([
        refetchConversations(),
        refetchContacts(),
        queryClient.invalidateQueries({ queryKey: ['/api/conversations'] }),
        queryClient.invalidateQueries({ queryKey: ['/api/contacts'] })
      ]);

      // Restore scroll position after a brief delay
      setTimeout(() => {
        if (conversationListElement) {
          conversationListElement.scrollTop = scrollTop;
        }
      }, 100);

      lastRefreshTimeRef.current = Date.now();
      retryCountRef.current = 0;
      
    } catch (error) {
      // Implement exponential backoff for retries
      if (retryCountRef.current < finalConfig.maxRetries) {
        retryCountRef.current++;
        const delay = finalConfig.retryDelayMs * Math.pow(2, retryCountRef.current - 1);

        retryTimeoutRef.current = setTimeout(() => {
          performSilentRefresh(`retry-${retryCountRef.current}`);
        }, delay);
      }
    } finally {
      isRefreshingRef.current = false;
    }
  }, [finalConfig, refetchConversations, refetchContacts, queryClient, isUserInteracting]);

  // Handle page visibility changes
  useEffect(() => {
    if (!finalConfig.enabled) return;

    if (isVisible && wasHidden) {
      const hiddenDuration = getHiddenDuration();

      // If page was hidden for more than the threshold, refresh immediately
      if (hiddenDuration >= finalConfig.minHiddenTimeForRefresh) {
        performSilentRefresh('visibility-return');
      }
      
      resetWasHidden();
    }
  }, [isVisible, wasHidden, getHiddenDuration, resetWasHidden, performSilentRefresh, finalConfig]);

  // Set up periodic refresh interval
  useEffect(() => {
    if (!finalConfig.enabled) return;

    // Clear existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Only run periodic refresh when page is visible
    if (isVisible) {
      intervalRef.current = setInterval(() => {
        performSilentRefresh('periodic');
      }, finalConfig.intervalMs);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isVisible, finalConfig.enabled, finalConfig.intervalMs, performSilentRefresh]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  // Manual refresh function for external use
  const manualRefresh = useCallback(() => {
    performSilentRefresh('manual');
  }, [performSilentRefresh]);

  return {
    manualRefresh,
    isRefreshing: isRefreshingRef.current,
    lastRefreshTime: lastRefreshTimeRef.current
  };
}
