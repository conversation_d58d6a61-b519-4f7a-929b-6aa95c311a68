import express, { type Express } from "express";
import { storage } from "./storage";
import whatsAppOfficialService from "./services/channels/whatsapp-official";

/**
 * Register webhook endpoints before any JSON middleware to avoid body parsing conflicts
 * This ensures webhooks receive raw bodies for proper signature verification
 */
export function registerWebhookRoutes(app: Express): void {
  
  // WhatsApp webhook verification (GET)
  app.get('/api/webhooks/whatsapp', async (req, res) => {
    const mode = req.query['hub.mode'];
    const token = req.query['hub.verify_token'];
    const challenge = req.query['hub.challenge'];
    
    // Enhanced logging for debugging
    console.log('WhatsApp webhook verification attempt:', {
      mode,
      token: token ? `${token.toString().substring(0, 8)}...` : 'undefined',
      challenge: challenge ? `${challenge.toString().substring(0, 8)}...` : 'undefined',
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });

    if (mode !== 'subscribe') {
      console.log('❌ WhatsApp webhook verification failed: Invalid mode');
      return res.status(403).send('Forbidden');
    }

    try {
      // Check database for WhatsApp Business API connections with matching verify tokens
      const whatsappConnections = await storage.getChannelConnectionsByType('whatsapp_official');
      
      let matchingConnection = null;
      for (const connection of whatsappConnections) {
        const connectionData = connection.connectionData as any;
        if (connectionData?.verifyToken === token) {
          matchingConnection = connection;
          break;
        }
      }

      // Also check global environment variable as fallback
      const globalToken = process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN || 'default_verify_token';
      const isGlobalMatch = token === globalToken;

      if (matchingConnection || isGlobalMatch) {
        console.log('✅ WhatsApp webhook verification successful:', {
          matchType: matchingConnection ? 'database_connection' : 'global_env',
          connectionId: matchingConnection?.id,
          accountName: matchingConnection?.accountName
        });
        res.status(200).send(challenge);
      } else {
        console.log('❌ WhatsApp webhook verification failed:', {
          receivedToken: token,
          globalToken: globalToken,
          checkedConnections: whatsappConnections.length,
          availableTokens: whatsappConnections.map(conn => {
            const data = conn.connectionData as any;
            return data?.verifyToken ? `${data.verifyToken.substring(0, 8)}...` : 'none';
          })
        });
        res.status(403).send('Forbidden');
      }
    } catch (error) {
      console.error('Error during WhatsApp webhook verification:', error);
      res.status(500).send('Internal Server Error');
    }
  });

  // WhatsApp webhook message processing (POST) - with raw body parsing
  app.post('/api/webhooks/whatsapp', express.raw({ type: 'application/json' }), async (req, res) => {
    try {
      const signature = req.headers['x-hub-signature-256'] as string;
      const body = req.body;

      console.log('WhatsApp webhook received:', {
        hasSignature: !!signature,
        bodyType: typeof body,
        bodyConstructor: body?.constructor?.name,
        isBuffer: Buffer.isBuffer(body),
        bodyLength: body?.length || 'unknown',
        contentType: req.get('content-type'),
        headers: {
          'x-hub-signature-256': signature ? 'present' : 'missing',
          'user-agent': req.get('user-agent')
        }
      });

      // Parse payload first to extract phone number for company scoping
      const payload = JSON.parse(body.toString());

      console.log('Processing WhatsApp webhook payload:', {
        object: payload.object,
        entryCount: payload.entry?.length || 0
      });

      // Extract phone number from webhook payload for company scoping
      let phoneNumberId: string | null = null;
      if (payload.entry && payload.entry.length > 0) {
        const entry = payload.entry[0];
        if (entry.changes && entry.changes.length > 0) {
          const change = entry.changes[0];
          if (change.value && change.value.metadata) {
            phoneNumberId = change.value.metadata.phone_number_id;
          }
        }
      }

      // Find the specific connection for this webhook to ensure proper company scoping
      let targetConnection = null;
      let appSecret = null;
      let secretSource = 'none';

      if (phoneNumberId) {
        // Find connection by phone number ID for proper company scoping
        const whatsappConnections = await storage.getChannelConnectionsByType('whatsapp_official');
        targetConnection = whatsappConnections.find(conn => {
          const data = conn.connectionData as any;
          return data?.phoneNumberId === phoneNumberId || data?.businessAccountId === phoneNumberId;
        });

        if (targetConnection) {
          const connectionData = targetConnection.connectionData as any;
          appSecret = connectionData?.appSecret;
          secretSource = `connection_${targetConnection.id}_company_${targetConnection.companyId}`;

          console.log('Found target connection:', {
            connectionId: targetConnection.id,
            companyId: targetConnection.companyId,
            phoneNumberId,
            hasAppSecret: !!appSecret
          });
        } else {
          console.warn('No connection found for phone number ID:', phoneNumberId);
        }
      }

      // Fallback to global app secret if no connection-specific secret found
      if (!appSecret) {
        appSecret = process.env.FACEBOOK_APP_SECRET;
        secretSource = 'global_env';
      }

      console.log('App secret configuration:', {
        usingSecretFrom: secretSource,
        hasSignature: !!signature,
        phoneNumberId
      });

      // Verify webhook signature with proper company scoping
      if (appSecret && signature) {
        // Body should be a Buffer from express.raw() middleware
        if (!Buffer.isBuffer(body)) {
          console.error('Expected Buffer but got:', typeof body, body?.constructor?.name);
          return res.status(400).send('Invalid request body - expected raw body');
        }

        const isValid = whatsAppOfficialService.verifyWebhookSignature(signature, body, appSecret);
        if (!isValid) {
          console.log('❌ WhatsApp webhook signature verification failed for:', {
            phoneNumberId,
            connectionId: targetConnection?.id,
            companyId: targetConnection?.companyId
          });
          return res.status(403).send('Forbidden');
        }
        console.log('✅ WhatsApp webhook signature verified successfully for:', {
          phoneNumberId,
          connectionId: targetConnection?.id,
          companyId: targetConnection?.companyId
        });
      } else {
        console.log('⚠️ Skipping signature verification:', {
          reason: !appSecret ? 'No app secret configured' : 'No signature provided',
          phoneNumberId,
          connectionId: targetConnection?.id
        });
      }

      // Process webhook with company context
      await whatsAppOfficialService.processWebhook(payload, targetConnection?.companyId);

      res.status(200).send('OK');
    } catch (error) {
      console.error('Error processing WhatsApp webhook:', error);
      res.status(500).send('Internal Server Error');
    }
  });

  // Test endpoint to verify webhook setup
  app.get('/api/webhooks/test', (req, res) => {
    res.json({
      message: 'Webhook routes are working',
      timestamp: new Date().toISOString(),
      registeredBefore: 'JSON middleware'
    });
  });

  console.log('✅ Webhook routes registered before JSON middleware');
}
