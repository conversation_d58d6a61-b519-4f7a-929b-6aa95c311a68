import { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { Link, useLocation } from 'wouter';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from '@/hooks/use-translation';
import { queryClient, apiRequest } from '@/lib/queryClient';
import { Loader2, Plus, Edit, Trash2, Calendar, FileCode, Check, ArrowUpDown, Copy, Users, MessageSquare, CheckCircle, Activity } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { format } from 'date-fns';
import Header from '@/components/layout/Header';
import Sidebar from '@/components/layout/Sidebar';

function FlowAssignmentsDialog({ flowId, flowName }: { flowId: number, flowName: string }) {
  const { toast } = useToast();
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);

  const { data: channels, isLoading: isLoadingChannels } = useQuery({
    queryKey: ['/api/channel-connections'],
    queryFn: async () => {
      const res = await fetch('/api/channel-connections');
      if (!res.ok) throw new Error('Failed to load channels');
      return res.json();
    },
    enabled: open
  });

  const { data: assignments, isLoading: isLoadingAssignments } = useQuery({
    queryKey: ['/api/flow-assignments', { flowId }],
    queryFn: async () => {
      const res = await fetch(`/api/flow-assignments?flowId=${flowId}`);
      if (!res.ok) throw new Error('Failed to load assignments');
      return res.json();
    },
    enabled: open
  });

  const createAssignmentMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest('POST', '/api/flow-assignments', data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/flow-assignments'] });
      toast({
        title: t('flows.flow_assigned', 'Flow assigned'),
        description: t('flows.flow_assigned_success', 'Flow has been assigned to channel successfully.')
      });
    },
    onError: (error: any) => {
      toast({
        title: t('flows.error_assigning_flow', 'Error assigning flow'),
        description: error.message || t('common.something_went_wrong', 'Something went wrong'),
        variant: 'destructive'
      });
    }
  });

  const updateAssignmentStatusMutation = useMutation({
    mutationFn: async ({ id, isActive }: { id: number; isActive: boolean }) => {
      const response = await apiRequest('PATCH', `/api/flow-assignments/${id}/status`, { isActive });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/flow-assignments'] });
      toast({
        title: t('flows.assignment_updated', 'Assignment updated'),
        description: t('flows.assignment_updated_success', 'Flow assignment status has been updated.')
      });
    },
    onError: (error: any) => {
      toast({
        title: t('flows.error_updating_assignment', 'Error updating assignment'),
        description: error.message || t('common.something_went_wrong', 'Something went wrong'),
        variant: 'destructive'
      });
    }
  });

  const getChannelName = (channelId: number) => {
    const channel = channels?.find((c: any) => c.id === channelId);
    return channel ? `${channel.accountName} (${channel.channelType})` : 'Unknown channel';
  };

  const handleAssignToChannel = async (channelId: number) => {
    try {
      await createAssignmentMutation.mutateAsync({
        flowId,
        channelId,
        isActive: false
      });
    } catch (error) {
      console.error('Error assigning flow:', error);
    }
  };

  const handleUpdateStatus = async (assignmentId: number, isActive: boolean) => {
    try {
      await updateAssignmentStatusMutation.mutateAsync({
        id: assignmentId,
        isActive
      });
    } catch (error) {
      console.error('Error updating assignment status:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Calendar className="h-4 w-4 mr-2" />
          {t('flows.assign', 'Assign')}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t('flows.manage_flow_assignments', 'Manage Flow Assignments')}</DialogTitle>
          <DialogDescription>
            {t('flows.assign_flow_description', 'Assign "{{flowName}}" to channels and manage active assignments.', { flowName })}
          </DialogDescription>
        </DialogHeader>

        {(isLoadingChannels || isLoadingAssignments) ? (
          <div className="flex justify-center p-4">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        ) : (
          <div className="space-y-4 max-h-[400px] overflow-y-auto">
            <div>
              <h3 className="text-sm font-medium mb-2">{t('flows.active_assignments', 'Active Assignments')}</h3>
              {assignments && assignments.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t('flows.channel', 'Channel')}</TableHead>
                      <TableHead>{t('flows.status', 'Status')}</TableHead>
                      <TableHead>{t('flows.actions', 'Actions')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {assignments.map((assignment: any) => (
                      <TableRow key={assignment.id}>
                        <TableCell>{getChannelName(assignment.channelId)}</TableCell>
                        <TableCell>
                          <Badge variant={assignment.isActive ? "default" : "outline"}>
                            {assignment.isActive ? t('flows.active', 'Active') : t('flows.inactive', 'Inactive')}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleUpdateStatus(assignment.id, !assignment.isActive)}
                          >
                            {assignment.isActive ? t('flows.deactivate', 'Deactivate') : t('flows.activate', 'Activate')}
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <p className="text-sm text-muted-foreground">{t('flows.no_assignments_yet', 'No assignments yet')}</p>
              )}
            </div>

            <div>
              <h3 className="text-sm font-medium mb-2">{t('flows.available_channels', 'Available Channels')}</h3>
              {channels && channels.length > 0 ? (
                <div className="grid gap-2">
                  {channels
                    .filter((channel: any) =>
                      !assignments || !assignments.some((a: any) => a.channelId === channel.id)
                    )
                    .map((channel: any) => (
                      <div
                        key={channel.id}
                        className="flex justify-between items-center p-2 border rounded-md"
                      >
                        <div className="text-sm">
                          {channel.accountName} <span className="text-xs text-muted-foreground">({channel.channelType})</span>
                        </div>
                        <Button
                          variant="brand"
                          size="sm"
                          onClick={() => handleAssignToChannel(channel.id)}
                        >
                          {t('flows.assign', 'Assign')}
                        </Button>
                      </div>
                    ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">{t('flows.no_channels_available', 'No channels available')}</p>
              )}
            </div>
          </div>
        )}

        <DialogFooter>
          <DialogClose asChild>
            <Button variant="brand">{t('common.close', 'Close')}</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

function DeleteFlowDialog({ flowId, flowName, onDeleted }: { flowId: number, flowName: string, onDeleted: () => void }) {
  const { toast } = useToast();
  const { t } = useTranslation();
  const [isDeleting, setIsDeleting] = useState(false);

  const deleteFlowMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest('DELETE', `/api/flows/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/flows'] });
      toast({
        title: t('flows.flow_deleted', 'Flow deleted'),
        description: t('flows.flow_deleted_success', 'Flow has been deleted successfully.')
      });
      onDeleted();
    },
    onError: (error: any) => {
      toast({
        title: t('flows.error_deleting_flow', 'Error deleting flow'),
        description: error.message || t('common.something_went_wrong', 'Something went wrong'),
        variant: 'destructive'
      });
    }
  });

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await deleteFlowMutation.mutateAsync(flowId);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="bg-destructive text-destructive-foreground">
          <Trash2 className="h-4 w-4 mr-2" />
          {t('common.delete', 'Delete')}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('flows.delete_flow', 'Delete Flow')}</DialogTitle>
          <DialogDescription>
            {t('flows.delete_flow_confirmation', 'Are you sure you want to delete "{{flowName}}"? This action cannot be undone.', { flowName })}
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="gap-2 sm:gap-0">
          <DialogClose asChild>
            <Button variant="brand">{t('common.cancel', 'Cancel')}</Button>
          </DialogClose>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            {t('common.delete', 'Delete')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}


export default function FlowsPage() {
  const { toast } = useToast();
  const { t } = useTranslation();
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  const { data: flows, isLoading } = useQuery({
    queryKey: ['/api/flows'],
    queryFn: async () => {
      const res = await fetch('/api/flows');
      if (!res.ok) throw new Error('Failed to load flows');
      return res.json();
    }
  });

  const { data: planInfo } = useQuery({
    queryKey: ['/api/user/plan-info'],
    queryFn: async () => {
      try {
        const res = await fetch('/api/user/plan-info');
        if (!res.ok) return null;
        return res.json();
      } catch (error) {
        console.error('Error fetching plan info:', error);
        return null;
      }
    }
  });

  const duplicateFlowMutation = useMutation({
    mutationFn: async (flowId: number) => {
      const res = await fetch(`/api/flows/${flowId}/duplicate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.message || 'Failed to duplicate flow');
      }
      return res.json();
    },
    onSuccess: (duplicatedFlow) => {
      queryClient.invalidateQueries({ queryKey: ['/api/flows'] });
      queryClient.invalidateQueries({ queryKey: ['/api/user/plan-info'] });
      toast({
        title: t('flows.flow_duplicated', 'Flow duplicated'),
        description: t('flows.flow_duplicated_description', 'Flow "{{flowName}}" has been duplicated successfully.', {
          flowName: duplicatedFlow.name
        })
      });
    },
    onError: (error: any) => {
      toast({
        title: t('flows.duplicate_failed', 'Duplication failed'),
        description: error.message || t('flows.duplicate_error', 'Failed to duplicate flow. Please try again.'),
        variant: 'destructive'
      });
    }
  });

  const handleDuplicateFlow = (flowId: number) => {
    duplicateFlowMutation.mutate(flowId);
  };

  const sortedFlows = flows ? [...flows].sort((a, b) => {
    const dateA = new Date(a.updatedAt);
    const dateB = new Date(b.updatedAt);
    return sortDirection === 'desc' ? dateB.getTime() - dateA.getTime() : dateA.getTime() - dateB.getTime();
  }) : [];

  const toggleSortDirection = () => {
    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (error) {
      return 'Invalid date';
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">{t('flows.active', 'Active')}</Badge>;
      case 'inactive':
        return <Badge variant="secondary">{t('flows.inactive', 'Inactive')}</Badge>;
      case 'draft':
        return <Badge variant="secondary">{t('flows.draft', 'Draft')}</Badge>;
      case 'archived':
        return <Badge variant="destructive">{t('flows.archived', 'Archived')}</Badge>;
      default:
        return <Badge variant="default">{status}</Badge>;
    }
  };

  return (
    <div className="h-screen flex flex-col overflow-hidden font-sans text-gray-800">
      <Header />

      <div className="flex flex-1 overflow-hidden">
        <Sidebar />

        <div className="flex-1 overflow-y-auto p-6">
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl">{t('nav.flow_builder', 'Flow Builder')}</h1>
                <p className="text-muted-foreground">
                  {t('flows.page_description', 'Create and manage automated conversation flows for your channels.')}
                </p>
                {planInfo && (
                  <div className="mt-1 text-sm flex items-center gap-2">
                    <div className="bg-muted px-2 py-1 rounded-md flex items-center">
                      <span className="text-muted-foreground">
                        <span className={planInfo.remainingFlows === 0 ? "text-destructive font-medium" : "font-medium"}>
                          {planInfo.currentFlowCount}
                        </span> / {planInfo.plan.maxFlows} {t('flows.flows', 'flows')}
                      </span>
                      {planInfo.remainingFlows === 0 && (
                        <span className="ml-2 text-destructive font-medium bg-destructive/10 px-2 py-0.5 rounded-full text-xs">
                          {t('flows.limit_reached', 'Limit reached')}
                        </span>
                      )}
                    </div>
                    {planInfo.remainingFlows === 0 && (
                      <span className="text-muted-foreground text-xs">
                        {t('flows.contact_admin_upgrade', 'Contact your administrator to upgrade your plan')}
                      </span>
                    )}
                  </div>
                )}
              </div>
              <div className="flex items-center gap-2">
                {planInfo && planInfo.remainingFlows === 0 ? (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button disabled>
                          <Plus className="h-4 w-4 mr-2" />
                          {t('flows.new_flow', 'New Flow')}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{t('flows.plan_limit_tooltip', "You've reached your plan's flow limit")}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ) : (
                  <Link href="/flows/new">
                    <Button className="flex items-center gap-2">
                      <Plus className="h-4 w-4" />
                      {t('flows.new_flow', 'New Flow')}
                    </Button>
                  </Link>
                )}
              </div>
            </div>

            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : (
              <div className="grid gap-4">
                {sortedFlows.length === 0 ? (
                  <Card>
                    <CardContent className="flex flex-col items-center justify-center py-12">
                      <FileCode className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-semibold mb-2">{t('flows.no_flows_found', 'No flows found')}</h3>
                      <p className="text-muted-foreground text-center mb-4">
                        {t('flows.get_started_message', 'Get started by creating your first automated conversation flow')}
                      </p>
                      <Link href="/flows/new">
                        <Button>
                          <Plus className="h-4 w-4 mr-2" />
                          {t('flows.new_flow', 'New Flow')}
                        </Button>
                      </Link>
                    </CardContent>
                  </Card>
                ) : (
                  sortedFlows.map((flow: any) => (
                    <Card key={flow.id}>
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div className="space-y-1">
                            <CardTitle className="flex items-center gap-2">
                              {flow.name}
                              {getStatusBadge(flow.status)}
                            </CardTitle>
                            {flow.description && (
                              <p className="text-sm text-muted-foreground">
                                {flow.description}
                              </p>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <Link href={`/flows/${flow.id}`}>
                              <Button variant="outline" size="sm">
                                <Edit className="h-4 w-4 mr-2" />
                                {t('common.edit', 'Edit')}
                              </Button>
                            </Link>
                            <FlowAssignmentsDialog flowId={flow.id} flowName={flow.name} />
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleDuplicateFlow(flow.id)}
                                    disabled={duplicateFlowMutation.isPending}
                                  >
                                    {duplicateFlowMutation.isPending ? (
                                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                    ) : (
                                      <Copy className="h-4 w-4 mr-2" />
                                    )}
                                    {t('flows.duplicate', 'Duplicate')}
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{t('flows.duplicate_tooltip', 'Create a copy of this flow')}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <DeleteFlowDialog
                              flowId={flow.id}
                              flowName={flow.name}
                              onDeleted={() => {

                                window.location.reload();
                              }}
                            />
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <p className="text-muted-foreground">{t('flows.status', 'Status')}</p>
                            <p className="font-medium">{flow.status}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">{t('flows.updated', 'Updated')}</p>
                            <p className="font-medium">{formatDate(flow.updatedAt)}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">{t('flows.version', 'Version')}</p>
                            <p className="font-medium">v{flow.version}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}