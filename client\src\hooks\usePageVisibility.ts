import { useEffect, useState, useCallback } from 'react';

export interface PageVisibilityState {
  isVisible: boolean;
  wasHidden: boolean;
  hiddenTime: number | null;
  visibleTime: number | null;
}

export function usePageVisibility() {
  const [state, setState] = useState<PageVisibilityState>({
    isVisible: !document.hidden,
    wasHidden: false,
    hiddenTime: null,
    visibleTime: Date.now()
  });

  const handleVisibilityChange = useCallback(() => {
    const isVisible = !document.hidden;
    const now = Date.now();

    setState(prevState => {
      if (isVisible && !prevState.isVisible) {
        // Page became visible
        return {
          isVisible: true,
          wasHidden: true,
          hiddenTime: prevState.hiddenTime,
          visibleTime: now
        };
      } else if (!isVisible && prevState.isVisible) {
        // Page became hidden
        return {
          isVisible: false,
          wasHidden: false,
          hiddenTime: now,
          visibleTime: prevState.visibleTime
        };
      }
      return prevState;
    });
  }, []);

  useEffect(() => {
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleVisibilityChange);
    window.addEventListener('blur', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleVisibilityChange);
      window.removeEventListener('blur', handleVisibilityChange);
    };
  }, [handleVisibilityChange]);

  const getHiddenDuration = useCallback(() => {
    if (state.hiddenTime && state.visibleTime && state.visibleTime > state.hiddenTime) {
      return state.visibleTime - state.hiddenTime;
    }
    return 0;
  }, [state.hiddenTime, state.visibleTime]);

  const resetWasHidden = useCallback(() => {
    setState(prevState => ({
      ...prevState,
      wasHidden: false
    }));
  }, []);

  return {
    ...state,
    getHiddenDuration,
    resetWasHidden
  };
}
