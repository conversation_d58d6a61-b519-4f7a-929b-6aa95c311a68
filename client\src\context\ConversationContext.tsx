import { createContext, useContext, useState, useEffect, ReactNode, useMemo } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import useSocket from '@/hooks/useSocket';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from '@/hooks/use-translation';
import { useAuth } from '@/hooks/use-auth';
import { apiRequest } from '@/lib/queryClient';

interface ConversationsResponse {
  conversations: any[];
  total?: number;
  page?: number;
  limit?: number;
  totalPages?: number;
}

interface ConversationContextProps {
  activeConversationId: number | null;
  setActiveConversationId: (id: number | null) => void;
  activeChannelId: number | null;
  setActiveChannelId: (id: number | null) => void;
  conversations: any[];
  contacts: any[];
  messages: Record<number, any[]>;
  messagesPagination: Record<number, { page: number; hasMore: boolean; loading: boolean }>;
  isLoading: boolean;
  isWebSocketConnected: boolean;
  sendMessage: (conversationId: number, content: string, isBot?: boolean) => void;
  sendMediaMessage: (conversationId: number, file: File, caption?: string) => Promise<any>;
  loadMoreMessages: (conversationId: number) => Promise<void>;
  replyToMessage: any | null;
  setReplyToMessage: (message: any | null) => void;
  refetchConversations: () => Promise<any>;
  refetchContacts: () => Promise<any>;
}

const ConversationContext = createContext<ConversationContextProps | undefined>(undefined);


function isWhatsAppGroupChatId(phoneNumber: string | null | undefined): boolean {
  if (!phoneNumber) {
    return false;
  }


  const numericOnly = phoneNumber.replace(/[^0-9]/g, '');



  return numericOnly.length >= 15 && numericOnly.startsWith('120');
}

function filterGroupChatsFromConversations(conversations: any[]): any[] {
  return conversations.filter(conversation => {

    if (conversation.is_group || conversation.isGroup) {
      return false;
    }


    if (conversation.group_jid || conversation.groupJid) {
      return false;
    }


    if (conversation.contact) {
      const phone = conversation.contact.phone || conversation.contact.identifier;
      if (isWhatsAppGroupChatId(phone)) {
        return false;
      }
    }


    if (conversation.phone && isWhatsAppGroupChatId(conversation.phone)) {
      return false;
    }


    if (conversation.identifier && isWhatsAppGroupChatId(conversation.identifier)) {
      return false;
    }

    return true;
  });
}

function filterGroupChatsFromContacts(contacts: any[]): any[] {
  return contacts.filter(contact => {

    if (isWhatsAppGroupChatId(contact.phone)) {
      return false;
    }


    if (isWhatsAppGroupChatId(contact.identifier)) {
      return false;
    }

    return true;
  });
}

function normalizePhoneNumber(phone: string): string {
  if (!phone) return '';

  let normalized = phone.replace(/[^\d+]/g, '');

  if (normalized.startsWith('+')) {
    return normalized;
  } else {
    normalized = normalized.replace(/^0+/, '');
    if (normalized.length > 10) {
      return '+' + normalized;
    }
    return normalized;
  }
}

function deduplicateContactsByPhone(contacts: any[]): any[] {
  const phoneMap = new Map();

  contacts.forEach(contact => {
    const normalizedPhone = normalizePhoneNumber(contact.phone || '');

    if (normalizedPhone && !phoneMap.has(normalizedPhone)) {
      phoneMap.set(normalizedPhone, contact);
    } else if (normalizedPhone) {
      const existingContact = phoneMap.get(normalizedPhone);

      if (new Date(contact.createdAt) > new Date(existingContact.createdAt)) {
        phoneMap.set(normalizedPhone, contact);
      }
    } else if (!normalizedPhone) {

      phoneMap.set(`no-phone-${contact.id}`, contact);
    }
  });

  return Array.from(phoneMap.values());
}

function deduplicateConversationsByContact(conversations: any[]): any[] {
  const contactMap = new Map();

  conversations.forEach(conversation => {
    const contactId = conversation.contactId;
    const channelId = conversation.channelId;
    const key = `${contactId}-${channelId}`;

    if (!contactMap.has(key)) {
      contactMap.set(key, conversation);
    } else {
      const existingConversation = contactMap.get(key);

      if (new Date(conversation.lastMessageAt) > new Date(existingConversation.lastMessageAt)) {
        contactMap.set(key, conversation);
      }
    }
  });

  return Array.from(contactMap.values());
}

interface ConversationProviderProps {
  children: ReactNode;
}

export function ConversationProvider({ children }: ConversationProviderProps) {
  const [activeConversationId, setActiveConversationId] = useState<number | null>(null);
  const [activeChannelId, setActiveChannelId] = useState<number | null>(null);
  const [messages, setMessages] = useState<Record<number, any[]>>({});
  const [messagesPagination, setMessagesPagination] = useState<Record<number, { page: number; hasMore: boolean; loading: boolean }>>({});
  const [replyToMessage, setReplyToMessage] = useState<any | null>(null);
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { t } = useTranslation();
  const { user, isLoading: authLoading } = useAuth();

  const { isConnected, sendMessage: wsSend, onMessage } = useSocket('/ws');

  const { data: conversationsResponse, isLoading: isLoadingConversations, refetch: refetchConversations } = useQuery<ConversationsResponse | any[]>({
    queryKey: ['/api/conversations'],
    refetchOnWindowFocus: true, // Enable refetch on window focus
    refetchOnMount: true,
    staleTime: 2 * 60 * 1000, // 2 minutes stale time
    gcTime: 5 * 60 * 1000, // 5 minutes garbage collection time
    refetchInterval: false, // Disable automatic interval refetch (handled by auto-refresh hook)
    enabled: !!user && !authLoading, // Only fetch when user is authenticated
  });


  const conversations = useMemo(() => {
    const rawConversations = Array.isArray(conversationsResponse)
      ? conversationsResponse
      : (conversationsResponse as ConversationsResponse)?.conversations || [];


    const filteredConversations = filterGroupChatsFromConversations(rawConversations);


    return deduplicateConversationsByContact(filteredConversations);
  }, [conversationsResponse]);

  const { data: contactsResponse = [], isLoading: isLoadingContacts, refetch: refetchContacts } = useQuery({
    queryKey: ['/api/contacts'],
    refetchOnWindowFocus: true, // Enable refetch on window focus
    refetchOnMount: true,
    staleTime: 2 * 60 * 1000, // 2 minutes stale time
    gcTime: 5 * 60 * 1000, // 5 minutes garbage collection time
    refetchInterval: false, // Disable automatic interval refetch (handled by auto-refresh hook)
    enabled: !!user && !authLoading, // Only fetch when user is authenticated
  });


  const contacts = useMemo(() => {
    const rawContacts = Array.isArray(contactsResponse) ? contactsResponse : [];


    const filteredContacts = filterGroupChatsFromContacts(rawContacts);


    return deduplicateContactsByPhone(filteredContacts);
  }, [contactsResponse]);

  useEffect(() => {
    if (isConnected) {
      refetchConversations();
      refetchContacts();
    }
  }, [isConnected, refetchConversations, refetchContacts]);

  useEffect(() => {
    const autoRefreshInterval = setInterval(() => {
      if (isConnected) {
        refetchConversations();
      }
    }, 30000);

    return () => clearInterval(autoRefreshInterval);
  }, [isConnected, refetchConversations]);

  const fetchMessages = async (conversationId: number, page: number = 1, append: boolean = false) => {

    if (!user || authLoading) {
      return;
    }

    try {
      setMessagesPagination(prev => ({
        ...prev,
        [conversationId]: {
          ...(prev[conversationId] || {}),
          page: 1,
          hasMore: true,
          loading: true
        }
      }));

      const res = await apiRequest('GET', `/api/conversations/${conversationId}/messages?page=${page}&limit=25`);
      const data = await res.json();

      setMessages(prev => {
        const existingMessages = prev[conversationId] || [];
        const newMessages = append ? [...data.messages, ...existingMessages] : data.messages;

        return {
          ...prev,
          [conversationId]: newMessages
        };
      });

      setMessagesPagination(prev => ({
        ...prev,
        [conversationId]: {
          page: data.pagination.page,
          hasMore: data.pagination.hasMore,
          loading: false
        }
      }));
    } catch (err) {
      setMessagesPagination(prev => ({
        ...prev,
        [conversationId]: {
          ...(prev[conversationId] || {}),
          page: 1,
          hasMore: true,
          loading: false
        }
      }));

      toast({
        title: t('common.error', 'Error'),
        description: t('inbox.failed_load_messages', 'Failed to load conversation messages'),
        variant: "destructive"
      });
    }
  };

  const loadMoreMessages = async (conversationId: number) => {
    const pagination = messagesPagination[conversationId];
    if (!pagination || pagination.loading || !pagination.hasMore) return;

    await fetchMessages(conversationId, pagination.page + 1, true);
  };

  useEffect(() => {
    if (activeConversationId && user && !authLoading) {
      setMessagesPagination(prev => ({
        ...prev,
        [activeConversationId]: { page: 1, hasMore: true, loading: false }
      }));

      const markConversationAsRead = async () => {
        try {
          const response = await fetch(`/api/conversations/${activeConversationId}/mark-read`, {
            method: 'POST',
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          if (!response.ok) {
          }
        } catch (error) {
        }
      };

      fetchMessages(activeConversationId);
      markConversationAsRead();
    }
  }, [activeConversationId, user, authLoading, toast]);

  useEffect(() => {
    const unsubscribe = onMessage('messageDeleted', (data) => {
      const { messageId, conversationId } = data.data;
      if (messageId && conversationId) {
        setMessages(prev => ({
          ...prev,
          [conversationId]: (prev[conversationId] || []).filter(msg => msg.id !== messageId)
        }));
      }
    });

    return unsubscribe;
  }, [onMessage]);

  useEffect(() => {
    const unsubscribe = onMessage('conversationHistoryCleared', (data) => {
      const { conversationId } = data.data;
      if (conversationId) {
        setMessages(prev => ({
          ...prev,
          [conversationId]: []
        }));

        setMessagesPagination(prev => ({
          ...prev,
          [conversationId]: { page: 1, hasMore: false, loading: false }
        }));

        toast({
          title: "Chat History Cleared",
          description: "All messages have been removed from this conversation.",
          variant: "default"
        });
      }
    });

    return unsubscribe;
  }, [onMessage, toast]);

  useEffect(() => {
    const unsubscribe = onMessage('newMessage', (data) => {
      const message = data.data;
      if (message && message.conversationId) {
        setMessages(prev => {
          const existingMessages = prev[message.conversationId] || [];

          const isDuplicate = existingMessages.some(existingMsg => {
            if (existingMsg.id === message.id) {
              return true;
            }

            if (existingMsg.externalId && message.externalId && existingMsg.externalId === message.externalId) {
              return true;
            }

            if (existingMsg.content === message.content &&
                existingMsg.direction === message.direction &&
                existingMsg.type === message.type &&
                Math.abs(new Date(existingMsg.createdAt).getTime() - new Date(message.createdAt).getTime()) < 2000) {
              return true;
            }

            if (message.mediaUrl && existingMsg.mediaUrl &&
                existingMsg.mediaUrl === message.mediaUrl &&
                existingMsg.direction === message.direction &&
                Math.abs(new Date(existingMsg.createdAt).getTime() - new Date(message.createdAt).getTime()) < 10000) {
              return true;
            }

            if (message.type && existingMsg.type &&
                message.type === existingMsg.type &&
                message.direction === existingMsg.direction &&
                ['image', 'video', 'audio', 'document'].includes(message.type) &&
                (message.content === `${message.type.charAt(0).toUpperCase() + message.type.slice(1)} message` ||
                 existingMsg.content === `${existingMsg.type.charAt(0).toUpperCase() + existingMsg.type.slice(1)} message`) &&
                Math.abs(new Date(existingMsg.createdAt).getTime() - new Date(message.createdAt).getTime()) < 5000) {
              return true;
            }

            return false;
          });

          if (isDuplicate) {
            return prev;
          }

          return {
            ...prev,
            [message.conversationId]: [
              ...existingMessages,
              message
            ]
          };
        });

        queryClient.invalidateQueries({
          queryKey: ['/api/conversations']
        });

        queryClient.invalidateQueries({
          queryKey: ['/api/conversations', message.conversationId, 'messages']
        });

        const audio = new Audio('/assets/notification.mp3');
        audio.play().catch(() => {
        });
      }
    });

    return unsubscribe;
  }, [onMessage, queryClient]);


  useEffect(() => {
    const unsubscribe = onMessage('conversationUpdated', (data) => {
      const updatedConversation = data.data;


      const filteredConversations = filterGroupChatsFromConversations([updatedConversation]);
      if (filteredConversations.length === 0) {
        return; // Don't update group chat conversations
      }

      queryClient.setQueryData(['/api/conversations'], (old: any) => {
        if (!old || !old.conversations) return old;
        return {
          ...old,
          conversations: old.conversations.map((conv: any) =>
            conv.id === updatedConversation.id ? { ...conv, ...updatedConversation } : conv
          )
        };
      });

      queryClient.invalidateQueries({
        queryKey: ['/api/conversations']
      });
    });

    return unsubscribe;
  }, [onMessage, queryClient]);

  useEffect(() => {
    const unsubscribe = onMessage('conversationCreated', (data) => {
      const newConversation = data.data;


      const filteredConversations = filterGroupChatsFromConversations([newConversation]);
      if (filteredConversations.length === 0) {
        return; // Don't add group chat conversations
      }

      queryClient.setQueryData(['/api/conversations'], (old: any) => {
        if (!old || !old.conversations) return old;

        const exists = old.conversations.some((conv: any) => conv.id === newConversation.id);
        if (exists) return old;

        return {
          ...old,
          conversations: [newConversation, ...old.conversations]
        };
      });

      queryClient.invalidateQueries({
        queryKey: ['/api/conversations']
      });
    });

    return unsubscribe;
  }, [onMessage, queryClient]);

  useEffect(() => {
    const unsubscribe = onMessage('conversationAssigned', (data) => {
      const { conversationId, agentId } = data.data;

      queryClient.setQueryData(['/api/conversations'], (old: any) => {
        if (!old || !old.conversations) return old;
        return {
          ...old,
          conversations: old.conversations.map((conv: any) =>
            conv.id === conversationId ? { ...conv, assignedToUserId: agentId } : conv
          )
        };
      });

      toast({
        title: t('inbox.conversation_assigned', 'Conversation Assigned'),
        description: agentId
          ? t('inbox.conversation_assigned_to_agent', 'Conversation has been assigned to an agent')
          : t('inbox.conversation_unassigned', 'Conversation has been unassigned'),
      });
    });

    return unsubscribe;
  }, [onMessage, queryClient, toast]);

  useEffect(() => {
    const unsubscribe = onMessage('conversationUnassigned', (data) => {
      const { conversationId } = data.data;

      queryClient.setQueryData(['/api/conversations'], (old: any) => {
        if (!old || !old.conversations) return old;
        return {
          ...old,
          conversations: old.conversations.map((conv: any) =>
            conv.id === conversationId ? { ...conv, assignedToUserId: null } : conv
          )
        };
      });

      toast({
        title: t('inbox.conversation_unassigned', 'Conversation Unassigned'),
        description: t('inbox.conversation_unassigned_desc', 'Conversation has been unassigned'),
      });
    });

    return unsubscribe;
  }, [onMessage, queryClient, toast]);

  useEffect(() => {
    const unsubscribe = onMessage('unreadCountUpdated', (data) => {
      const { conversationId, unreadCount } = data.data;

      queryClient.setQueryData(['/api/conversations'], (old: any) => {
        if (!old || !old.conversations) return old;
        return {
          ...old,
          conversations: old.conversations.map((conv: any) =>
            conv.id === conversationId ? { ...conv, unreadCount } : conv
          )
        };
      });
    });

    return unsubscribe;
  }, [onMessage, queryClient]);

  useEffect(() => {
    const unsubscribe = onMessage('newConversation', (data) => {
      const newConversation = data.data;


      const filteredConversations = filterGroupChatsFromConversations([newConversation]);
      if (filteredConversations.length === 0) {
        return; // Don't add group chat conversations
      }

      queryClient.setQueryData(['/api/conversations'], (old: any) => {
        if (!old || !old.conversations) {
          return {
            conversations: [newConversation],
            total: 1,
            page: 1,
            limit: 50,
            totalPages: 1
          };
        }

        const exists = old.conversations.some((conv: any) => conv.id === newConversation.id);
        if (exists) {
          return old;
        }

        return {
          ...old,
          conversations: [newConversation, ...old.conversations],
          total: old.total + 1
        };
      });

      queryClient.invalidateQueries({
        queryKey: ['/api/conversations']
      });
    });

    return unsubscribe;
  }, [onMessage, queryClient, toast]);

  useEffect(() => {
    const unsubscribe = onMessage('conversationBotStatusUpdated', (data) => {
      const { conversationId, botDisabled, disabledAt, disableDuration, disableReason } = data.data;

      queryClient.setQueryData(['bot-status', conversationId], {
        conversationId,
        botDisabled,
        disabledAt,
        disableDuration,
        disableReason
      });

      queryClient.setQueryData(['/api/conversations'], (old: any) => {
        if (!old || !old.conversations) return old;

        return {
          ...old,
          conversations: old.conversations.map((conv: any) =>
            conv.id === conversationId
              ? { ...conv, botDisabled, disabledAt, disableDuration, disableReason }
              : conv
          )
        };
      });
    });

    return unsubscribe;
  }, [onMessage, queryClient]);

  const sendMessage = (conversationId: number, content: string, isBot: boolean = false) => {
    if (!isConnected) {
      toast({
        title: t('inbox.not_connected', 'Not Connected'),
        description: t('inbox.cannot_send_message', 'Cannot send message, not connected to server'),
        variant: "destructive"
      });
      return;
    }

    wsSend({
      type: 'sendMessage',
      message: {
        conversationId,
        content,
        isFromBot: isBot
      }
    });
  };

  const sendMediaMessage = async (conversationId: number, file: File, caption?: string): Promise<any> => {
    try {
      if (!isConnected) {
        toast({
          title: t('inbox.not_connected', 'Not Connected'),
          description: t('inbox.cannot_send_media', 'Cannot send media, not connected to server'),
          variant: "destructive"
        });
        throw new Error(t('inbox.not_connected_server', 'Not connected to server'));
      }

      const conversation = (conversations as any[]).find((conv: any) => conv.id === conversationId);
      if (!conversation) {
        throw new Error(t('inbox.conversation_not_found', 'Conversation not found'));
      }

      const response = await new Promise<{
        ok: boolean;
        status: number;
        statusText: string;
        json: () => Promise<any>;
      }>((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        const xhrFormData = new FormData();
        xhrFormData.append('file', file);
        if (caption) {
          xhrFormData.append('caption', caption);
        }

        xhr.onload = function() {
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve({
              ok: true,
              status: xhr.status,
              statusText: xhr.statusText,
              json: () => JSON.parse(xhr.responseText)
            });
          } else {
            resolve({
              ok: false,
              status: xhr.status,
              statusText: xhr.statusText,
              json: () => {
                try {
                  return JSON.parse(xhr.responseText);
                } catch (e) {
                  return { error: xhr.statusText };
                }
              }
            });
          }
        };

        xhr.onerror = function() {
          reject(new Error(t('inbox.network_error', 'Network error')));
        };

        xhr.upload.onprogress = function(event) {
          if (event.lengthComputable) {
          }
        };

        xhr.open('POST', `/api/conversations/${conversationId}/upload-media`, true);
        xhr.withCredentials = true;

        xhr.send(xhrFormData);
      });

      if (!response.ok) {
        try {
          const errorData = await response.json();
          throw new Error(errorData.error || errorData.message || t('inbox.failed_send_media', 'Failed to send media message'));
        } catch (jsonError) {
          throw new Error(t('inbox.server_error', 'Server error ({{status}}): {{statusText}}', {
            status: response.status,
            statusText: response.statusText
          }));
        }
      }

      const messageData = await response.json();

      return messageData;
    } catch (err: any) {
      toast({
        title: t('common.error', 'Error'),
        description: err.message || t('inbox.failed_send_media', 'Failed to send media message'),
        variant: "destructive"
      });
      throw err;
    }
  };

  const contextValue: ConversationContextProps = {
    activeConversationId,
    setActiveConversationId,
    activeChannelId,
    setActiveChannelId,
    conversations: conversations as any[],
    contacts: contacts as any[],
    messages,
    messagesPagination,
    isLoading: isLoadingConversations || isLoadingContacts,
    isWebSocketConnected: isConnected,
    sendMessage,
    sendMediaMessage,
    loadMoreMessages,
    replyToMessage,
    setReplyToMessage,
    refetchConversations,
    refetchContacts
  };

  return (
    <ConversationContext.Provider value={contextValue}>
      {children}
    </ConversationContext.Provider>
  );
}

export function useConversations() {
  const context = useContext(ConversationContext);
  if (context === undefined) {
    throw new Error('useConversations must be used within a ConversationProvider');
  }
  return context;
}
