import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useQuery, useMutation } from '@tanstack/react-query';
import { queryClient, apiRequest } from '@/lib/queryClient';
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Checkbox
} from "@/components/ui/checkbox";
import {
  Label
} from "@/components/ui/label";
import {
  Input
} from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, CheckCircle2, XCircle, Save, Loader2 } from "lucide-react";

interface RolePermission {
  id: number;
  companyId: number;
  role: 'admin' | 'agent';
  permissions: Record<string, boolean>;
  createdAt: string;
  updatedAt: string;
}


const PERMISSION_GROUPS = {
  conversations: {
    title: 'Conversation Management',
    permissions: {
      view_all_conversations: 'View All Conversations',
      view_assigned_conversations: 'View Assigned Conversations',
      assign_conversations: 'Assign Conversations',
      manage_conversations: 'Manage Conversations'
    }
  },
  contacts: {
    title: 'Contact Management',
    permissions: {
      view_contacts: 'View Contacts',
      manage_contacts: 'Manage Contacts'
    }
  },
  campaigns: {
    title: 'Campaign Management',
    permissions: {
      view_campaigns: 'View Campaigns',
      create_campaigns: 'Create Campaigns',
      edit_campaigns: 'Edit Campaigns',
      delete_campaigns: 'Delete Campaigns',
      manage_templates: 'Manage Templates',
      manage_segments: 'Manage Segments',
      view_campaign_analytics: 'View Campaign Analytics',
      manage_whatsapp_accounts: 'Manage WhatsApp Accounts'
    }
  },
  pipeline: {
    title: 'Pipeline Management',
    permissions: {
      view_pipeline: 'View Pipeline',
      manage_pipeline: 'Manage Pipeline',
      create_deals: 'Create Deals',
      edit_deals: 'Edit Deals',
      delete_deals: 'Delete Deals',
      manage_pipeline_stages: 'Manage Pipeline Stages'
    }
  },
  channels: {
    title: 'Channel Management',
    permissions: {
      view_channels: 'View Channels',
      manage_channels: 'Manage Channels',
      configure_channels: 'Configure Channels'
    }
  },
  flows: {
    title: 'Flow Management',
    permissions: {
      view_flows: 'View Flows',
      manage_flows: 'Manage Flows'
    }
  },
  analytics: {
    title: 'Analytics',
    permissions: {
      view_analytics: 'View Analytics',
      view_detailed_analytics: 'View Detailed Analytics'
    }
  },
  team: {
    title: 'Team Management',
    permissions: {
      view_team: 'View Team',
      manage_team: 'Manage Team'
    }
  },
  settings: {
    title: 'Settings',
    permissions: {
      view_settings: 'View Settings',
      manage_settings: 'Manage Settings'
    }
  },
  calendar: {
    title: 'Calendar',
    permissions: {
      view_calendar: 'View Calendar',
      manage_calendar: 'Manage Calendar'
    }
  },
  pages: {
    title: 'Page Management',
    permissions: {
      view_pages: 'View Pages',
      manage_pages: 'Manage Pages'
    }
  }
};

export function RolesAndPermissions() {

  const [showEditRoleModal, setShowEditRoleModal] = useState(false);
  const [currentRole, setCurrentRole] = useState<'admin' | 'agent' | null>(null);
  const [editingPermissions, setEditingPermissions] = useState<Record<string, boolean>>({});
  const { toast } = useToast();


  const { data: rolePermissions = [], isLoading, refetch } = useQuery<RolePermission[]>({
    queryKey: ['/api/role-permissions'],
    refetchOnWindowFocus: false
  });


  const updateRolePermissionsMutation = useMutation({
    mutationFn: async (data: { role: 'admin' | 'agent'; permissions: Record<string, boolean> }) => {
      const res = await apiRequest('PUT', `/api/role-permissions/${data.role}`, {
        permissions: data.permissions
      });
      return await res.json();
    },
    onSuccess: (_, variables) => {
      toast({
        title: 'Permissions Updated',
        description: `${variables.role === 'admin' ? 'Administrator' : 'Agent'} permissions have been updated successfully.`,
      });
      setShowEditRoleModal(false);
      refetch();
      queryClient.invalidateQueries({ queryKey: ['userPermissions'] });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to update permissions: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  const handleEditRole = (role: 'admin' | 'agent') => {
    const roleData = rolePermissions.find(rp => rp.role === role);
    setCurrentRole(role);
    setEditingPermissions(roleData?.permissions || {});
    setShowEditRoleModal(true);
  };

  const handleSavePermissions = () => {
    if (!currentRole) return;

    updateRolePermissionsMutation.mutate({
      role: currentRole,
      permissions: editingPermissions
    });
  };

  const togglePermission = (permission: string, value: boolean) => {
    setEditingPermissions(prev => ({
      ...prev,
      [permission]: value
    }));
  };

  const getRoleData = (role: 'admin' | 'agent') => {
    return rolePermissions.find(rp => rp.role === role);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">
          Roles & Permissions
        </h3>
      </div>

      <div className="space-y-4">
        {/* Administrator Role */}
        <Card className="overflow-hidden">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-lg">Administrator</CardTitle>
                <p className="text-sm text-gray-500">Full access to all features and settings</p>
              </div>
              <Button variant="outline" onClick={() => handleEditRole('admin')}>
                Edit Permissions
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
              {Object.entries(PERMISSION_GROUPS).map(([groupKey, group]) => {
                const adminData = getRoleData('admin');
                const hasAnyPermission = Object.keys(group.permissions).some(
                  permission => adminData?.permissions[permission]
                );

                return (
                  <div
                    key={`admin-${groupKey}`}
                    className={`flex items-center p-3 rounded-md border ${hasAnyPermission ? 'bg-green-50' : 'bg-red-50'}`}
                  >
                    {hasAnyPermission ? (
                      <CheckCircle2 className="h-4 w-4 text-green-600 mr-2" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600 mr-2" />
                    )}
                    <span className={`text-sm ${hasAnyPermission ? 'text-green-800' : 'text-red-800'}`}>
                      {group.title}
                    </span>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Agent Role */}
        <Card className="overflow-hidden">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-lg">Agent</CardTitle>
                <p className="text-sm text-gray-500">Limited access to core features</p>
              </div>
              <Button variant="outline" onClick={() => handleEditRole('agent')}>
                Edit Permissions
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
              {Object.entries(PERMISSION_GROUPS).map(([groupKey, group]) => {
                const agentData = getRoleData('agent');
                const hasAnyPermission = Object.keys(group.permissions).some(
                  permission => agentData?.permissions[permission]
                );

                return (
                  <div
                    key={`agent-${groupKey}`}
                    className={`flex items-center p-3 rounded-md border ${hasAnyPermission ? 'bg-green-50' : 'bg-red-50'}`}
                  >
                    {hasAnyPermission ? (
                      <CheckCircle2 className="h-4 w-4 text-green-600 mr-2" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600 mr-2" />
                    )}
                    <span className={`text-sm ${hasAnyPermission ? 'text-green-800' : 'text-red-800'}`}>
                      {group.title}
                    </span>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      <Dialog open={showEditRoleModal} onOpenChange={setShowEditRoleModal}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Edit {currentRole === 'admin' ? 'Administrator' : 'Agent'} Permissions
            </DialogTitle>
            <DialogDescription>
              Configure permissions for the {currentRole === 'admin' ? 'Administrator' : 'Agent'} role. Changes will apply to all users with this role.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <Tabs defaultValue={Object.keys(PERMISSION_GROUPS)[0]} className="w-full">
              <TabsList className="grid w-full grid-cols-5">
                {Object.entries(PERMISSION_GROUPS).slice(0, 5).map(([groupKey, group]) => (
                  <TabsTrigger key={groupKey} value={groupKey} className="text-xs">
                    {group.title.split(' ')[0]}
                  </TabsTrigger>
                ))}
              </TabsList>
              <TabsList className="grid w-full grid-cols-4 mt-2">
                {Object.entries(PERMISSION_GROUPS).slice(5).map(([groupKey, group]) => (
                  <TabsTrigger key={groupKey} value={groupKey} className="text-xs">
                    {group.title.split(' ')[0]}
                  </TabsTrigger>
                ))}
              </TabsList>

              {Object.entries(PERMISSION_GROUPS).map(([groupKey, group]) => (
                <TabsContent key={groupKey} value={groupKey} className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">{group.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {Object.entries(group.permissions).map(([permission, label]) => (
                          <div key={permission} className="flex items-center space-x-2">
                            <Checkbox
                              id={`edit-${permission}`}
                              checked={editingPermissions[permission] || false}
                              onCheckedChange={(checked) => togglePermission(permission, !!checked)}
                            />
                            <Label
                              htmlFor={`edit-${permission}`}
                              className="text-sm font-normal cursor-pointer"
                            >
                              {label}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              ))}
            </Tabs>
          </div>

          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setShowEditRoleModal(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleSavePermissions}
              disabled={updateRolePermissionsMutation.isPending}
              className="btn-brand-primary"
            >
              {updateRolePermissionsMutation.isPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              <Save className="mr-2 h-4 w-4" />
              Save Permissions
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

    </div>
  );
}