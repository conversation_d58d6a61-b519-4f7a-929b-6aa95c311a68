import { format } from 'date-fns';
import { useState, useEffect } from 'react';
import { Download, Loader2, Trash2, Reply, MoreHorizontal, Mail, ArrowRight } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from '@/hooks/use-translation';
import { useBranding } from '@/contexts/branding-context';
import { useConversations } from '@/context/ConversationContext';
import QuotedMessagePreview from './QuotedMessagePreview';
import OptimizedMediaBubble from './OptimizedMediaBubble';
import { GroupParticipantAvatar } from '@/components/groups/GroupParticipantAvatar';
import { stripAgentSignature } from '@/utils/messageUtils';
import { parseFormattedText, hasFormatting } from '@/utils/textFormatter';
import BotIcon from '@/components/ui/bot-icon';
import './QuotedMessage.css';

interface ChannelCapabilities {
  supportsReply: boolean;
  supportsDelete: boolean;
  supportsQuotedMessages: boolean;
  deleteTimeLimit?: number;
  replyFormat: 'quoted' | 'threaded' | 'mention';
}

interface MessageBubbleProps {
  message: any;
  contact: any;
  channelType?: string;
  onReply?: (message: any) => void;
  onQuotedMessageClick?: (quotedMessageId: string) => void;
  conversation?: any; // Add conversation prop for group chat context
}

export default function MessageBubble({ message, contact, channelType, onReply, onQuotedMessageClick, conversation }: MessageBubbleProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const [localMediaUrl, setLocalMediaUrl] = useState<string | null>(null);
  const [isHovered, setIsHovered] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [channelCapabilities, setChannelCapabilities] = useState<ChannelCapabilities | null>(null);
  const [isLoadingCapabilities, setIsLoadingCapabilities] = useState(false);
  const [emailAttachments, setEmailAttachments] = useState<any[]>([]);
  const [isLoadingAttachments, setIsLoadingAttachments] = useState(false);
  const { toast } = useToast();
  const { t } = useTranslation();
  const { branding } = useBranding();
  const { activeConversationId } = useConversations();
  
  const isInbound = message.direction === 'inbound';

  const isFromBot = message.isFromBot || (message.senderId === null && message.senderType === null && message.direction === 'outbound');
  const timestamp = message.sentAt || 
                   (message.metadata?.timestamp 
                     ? new Date(message.metadata.timestamp) 
                     : message.createdAt);
  
  const formattedTime = format(new Date(timestamp), 'h:mm a');

  useEffect(() => {
    const fetchChannelCapabilities = async () => {
      if (!activeConversationId) return;

      setIsLoadingCapabilities(true);
      try {
        const response = await fetch(`/api/conversations/${activeConversationId}/capabilities`, {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          setChannelCapabilities(data.capabilities);
        } else {
          console.error('Failed to fetch channel capabilities');
          setChannelCapabilities({
            supportsReply: true,
            supportsDelete: true,
            supportsQuotedMessages: false,
            replyFormat: 'mention'
          });
        }
      } catch (error) {
        console.error('Error fetching channel capabilities:', error);
        setChannelCapabilities({
          supportsReply: true,
          supportsDelete: true,
          supportsQuotedMessages: false,
          replyFormat: 'mention'
        });
      } finally {
        setIsLoadingCapabilities(false);
      }
    };

    fetchChannelCapabilities();
  }, [activeConversationId]);

  useEffect(() => {
    const fetchEmailAttachments = async () => {
      if (channelType === 'email' && message.id) {
        setIsLoadingAttachments(true);
        try {
          const response = await fetch(`/api/v1/messages/${message.id}/email-attachments`);
          if (response.ok) {
            const data = await response.json();
            setEmailAttachments(data.attachments || []);
          }
        } catch (error) {
          console.error('Error fetching email attachments:', error);
        } finally {
          setIsLoadingAttachments(false);
        }
      }
    };

    fetchEmailAttachments();
  }, [channelType, message.id]);

  const getMediaUrlFromMetadata = () => {
    if (!message.metadata) return null;
    
    try {
      const metadata = typeof message.metadata === 'string' 
        ? JSON.parse(message.metadata) 
        : message.metadata;
      
      return metadata.mediaUrl || null;
    } catch (e) {
      return null;
    }
  };
  
  const downloadMedia = async () => {
    if (isDownloading) return;
    
    setIsDownloading(true);
    try {
      const response = await fetch(`/api/messages/${message.id}/download-media`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || t('message_bubble.download_failed', 'Failed to download media'));
      }

      const data = await response.json();
      setLocalMediaUrl(data.mediaUrl);

      toast({
        title: t('message_bubble.media_downloaded', 'Media downloaded'),
        description: t('message_bubble.download_success', 'Media has been downloaded successfully'),
        variant: 'default'
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : t('message_bubble.download_failed', 'Failed to download media');

      toast({
        title: t('message_bubble.download_failed_title', 'Download failed'),
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsDownloading(false);
    }
  };

  const handleDeleteMessage = async () => {
    if (!message.id) return;

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/messages/${message.id}`, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || t('message_bubble.delete_failed', 'Failed to delete message'));
      }

      toast({
        title: t('message_bubble.message_deleted', 'Message deleted'),
        description: t('message_bubble.delete_success', 'Message has been deleted successfully'),
        variant: 'default'
      });

      setShowDeleteConfirm(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : t('message_bubble.delete_failed', 'Failed to delete message');

      toast({
        title: t('message_bubble.delete_failed_title', 'Delete failed'),
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleReplyToMessage = () => {
    if (onReply) {
      onReply(message);
    }
  };

  const isMessageTooOldToDelete = () => {
    if (!channelCapabilities?.deleteTimeLimit) return false;

    const messageAge = Date.now() - new Date(message.sentAt || message.createdAt).getTime();
    const timeLimitMs = channelCapabilities.deleteTimeLimit * 60 * 1000;

    return messageAge > timeLimitMs;
  };

  const getAvailableActions = () => {
    if (!channelCapabilities) return { canReply: false, canDelete: false };

    const canReply = channelCapabilities.supportsReply;
    const canDelete = channelCapabilities.supportsDelete && !isMessageTooOldToDelete();

    return { canReply, canDelete };
  };

  const { canReply, canDelete } = getAvailableActions();

  const getQuotedMessageInfo = () => {
    try {
      if (!message.metadata) return null;

      let metadata;
      if (typeof message.metadata === 'string') {
        metadata = JSON.parse(message.metadata);
      } else {
        metadata = message.metadata;
      }

      if (metadata.isQuotedMessage && metadata.quotedMessageId) {
        return {
          isQuotedMessage: true,
          quotedMessageId: metadata.quotedMessageId
        };
      }

      return null;
    } catch (error) {
      console.error('Error parsing message metadata for quoted message detection:', error);
      return null;
    }
  };

  const quotedInfo = getQuotedMessageInfo();

  const handleDeleteConfirm = () => {
    setShowDeleteConfirm(true);
  };

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(false);
  };

  const isWhatsAppMessage = () => {
    return channelType === 'whatsapp' ||
           channelType === 'whatsapp_unofficial' ||
           channelType === 'whatsapp_official';
  };

  const isMessageTooOld = () => {
    if (!message.createdAt) return false;
    const messageAge = Date.now() - new Date(message.createdAt).getTime();
    const maxAge = 72 * 60 * 1000;
    return messageAge > maxAge;
  };


  const isGroupChat = () => {
    return conversation?.isGroup === true;
  };

  const getParticipantInfo = () => {
    if (!isGroupChat() || !isInbound) return null;

    return {
      jid: message.groupParticipantJid,
      name: message.groupParticipantName,
      phone: message.groupParticipantJid?.split('@')[0]
    };
  };

  const formatParticipantName = (participantInfo: any) => {
    if (!participantInfo) return '';

    if (participantInfo.name && participantInfo.name !== participantInfo.phone) {
      return participantInfo.name;
    }


    const phone = participantInfo.phone;
    if (phone && phone.length > 10) {
      return `+${phone.slice(0, -10)} ${phone.slice(-10, -7)} ${phone.slice(-7, -4)} ${phone.slice(-4)}`;
    }

    return phone || participantInfo.jid || t('groups.unknown_participant', 'Unknown Participant');
  };

  const renderEmailContent = () => {
    const { emailSubject, emailHtml, emailPlainText, emailFrom, emailTo, emailInReplyTo, emailReferences } = message;


    const isThreaded = emailInReplyTo || (emailReferences && emailReferences.length > 0);
    const isReply = emailSubject && (emailSubject.startsWith('Re:') || emailSubject.startsWith('RE:'));

    return (
      <div className="email-message">
        {/* Email Threading Indicator */}
        {isThreaded && (
          <div className="email-thread-indicator mb-2 flex items-center gap-2 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
            <Mail className="w-3 h-3" />
            <ArrowRight className="w-3 h-3" />
            <span>{isReply ? t('message_bubble.email.reply_in_thread', 'Reply in thread') : t('message_bubble.email.part_of_thread', 'Part of email thread')}</span>
          </div>
        )}

        {/* Email Subject */}
        {emailSubject && (
          <div className="email-subject mb-2 pb-2 border-b border-gray-200">
            <h4 className="font-semibold text-sm text-gray-800 flex items-center gap-2">
              {isReply && <Reply className="w-4 h-4 text-blue-600" />}
              {emailSubject}
            </h4>
          </div>
        )}

        {/* Email Headers */}
        <div className="email-headers mb-3 text-xs text-gray-600 space-y-1">
          {emailFrom && (
            <div>
              <span className="font-medium">{t('message_bubble.email.from', 'From')}:</span> {emailFrom}
            </div>
          )}
          {emailTo && (
            <div>
              <span className="font-medium">{t('message_bubble.email.to', 'To')}:</span> {emailTo}
            </div>
          )}
          {isThreaded && emailInReplyTo && (
            <div className="text-blue-600">
              <span className="font-medium">{t('message_bubble.email.in_reply_to', 'In reply to')}:</span> {emailInReplyTo}
            </div>
          )}
        </div>

        {/* Email Content */}
        <div className="email-content">
          {emailHtml ? (
            <div
              className="email-html-content prose prose-sm max-w-none"
              dangerouslySetInnerHTML={{
                __html: emailHtml.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
              }}
            />
          ) : emailPlainText ? (
            <div className="whitespace-pre-wrap break-words text-sm">
              {emailPlainText}
            </div>
          ) : (
            <div className="whitespace-pre-wrap break-words text-sm">
              {stripAgentSignature(message.content || '')}
            </div>
          )}
        </div>

        {/* Email Attachments */}
        {emailAttachments.length > 0 && (
          <div className="email-attachments mt-3 pt-3 border-t border-gray-200">
            <div className="text-xs text-gray-600 mb-2 font-medium">
              <i className="ri-attachment-line mr-1"></i>
              {t('message_bubble.email.attachments', 'Attachments')} ({emailAttachments.length})
            </div>
            <div className="space-y-2">
              {emailAttachments.map((attachment: any, index: number) => (
                <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded text-xs">
                  <div className="flex items-center flex-1 min-w-0">
                    <i className="ri-file-line mr-2 text-gray-500 flex-shrink-0"></i>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{attachment.filename}</div>
                      <div className="text-gray-500">
                        {attachment.contentType} • {Math.round(attachment.size / 1024)}KB
                      </div>
                    </div>
                  </div>
                  <a
                    href={attachment.downloadUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 rounded text-xs transition-all ml-2 flex-shrink-0"
                  >
                    <Download className="h-3 w-3" />
                    <span>{t('message_bubble.download', 'Download')}</span>
                  </a>
                </div>
              ))}
            </div>
          </div>
        )}

        {isLoadingAttachments && (
          <div className="email-attachments mt-3 pt-3 border-t border-gray-200">
            <div className="flex items-center text-xs text-gray-500">
              <Loader2 className="h-3 w-3 animate-spin mr-2" />
              {t('message_bubble.email.loading_attachments', 'Loading attachments...')}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderMessageContent = () => {
    const { type = 'text', content, channelType } = message;
    const mediaUrl = message.mediaUrl || localMediaUrl || getMediaUrlFromMetadata();


    if (channelType === 'email' && (message.emailHtml || message.emailPlainText || message.emailSubject)) {
      return renderEmailContent();
    }

    const displayContent = stripAgentSignature(content || '');

    if (['image', 'video', 'audio', 'document', 'sticker'].includes(type)) {
      return (
        <OptimizedMediaBubble
          message={message}
          mediaUrl={mediaUrl}
          onDownload={downloadMedia}
          isDownloading={isDownloading}
        />
      );
    }

    switch (type) {
      case 'text':
      default:
        if (hasFormatting(displayContent)) {
          const formattedNodes = parseFormattedText(displayContent);
          return (
            <div className="whitespace-pre-wrap break-words">
              {formattedNodes.map((node, index) => (
                <span key={index}>{node}</span>
              ))}
            </div>
          );
        }

        return <p className="whitespace-pre-wrap break-words">{displayContent}</p>;
    }
  };

  if (isInbound) {
    const participantInfo = getParticipantInfo();

    return (
      <div className="flex mb-4" data-external-id={message.externalId}>
        <div className="flex-shrink-0 mr-2">
          {isGroupChat() && participantInfo ? (
            <GroupParticipantAvatar
              participantJid={participantInfo.jid}
              participantName={participantInfo.name}
              connectionId={conversation?.channelId}
              size="md"
              enableAutoFetch={true}
            />
          ) : (
            <img
              src={contact?.avatarUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(contact?.name || 'User')}&background=random`}
              alt={contact?.name || t('message_bubble.contact_avatar', 'Contact avatar')}
              className="w-8 h-8 rounded-full"
            />
          )}
        </div>
        <div className="max-w-[75%] md:max-w-[70%]">
          {isGroupChat() && participantInfo && (
            <div className="mb-1">
              <span className="text-xs font-medium text-gray-600">
                {formatParticipantName(participantInfo)}
              </span>
            </div>
          )}
          <div
            className="relative group"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <div className="bg-white p-3 rounded-lg chat-bubble-contact shadow-sm">
              {quotedInfo && (
                <QuotedMessagePreview
                  quotedMessageId={quotedInfo.quotedMessageId}
                  isInbound={true}
                  onQuotedMessageClick={onQuotedMessageClick}
                />
              )}
              {renderMessageContent()}
              {message.status && message.status !== 'delivered' && (
                <div className="text-right mt-1">
                  <span className="text-xs text-gray-400">
                    {message.status === 'read' && <i className="ri-eye-line"></i>}
                    {message.status === 'failed' && <i className="ri-error-warning-line text-red-500"></i>}
                  </span>
                </div>
              )}
            </div>

            {(isHovered || showDeleteConfirm) && (canReply || canDelete) && (
              <div className="absolute top-13 right-0 flex items-center gap-1 bg-white rounded-lg shadow-lg border border-gray-200 p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10">
                {canReply && (
                  <button
                    onClick={handleReplyToMessage}
                    className="p-1.5 rounded-md hover:bg-gray-100 text-gray-600 hover:text-blue-600 transition-colors min-h-[32px] min-w-[32px] flex items-center justify-center"
                    title={t('message_bubble.reply', 'Reply to this message')}
                    aria-label={t('message_bubble.reply', 'Reply to this message')}
                  >
                    <Reply className="h-4 w-4" />
                  </button>
                )}
                {canDelete && (
                  <button
                    onClick={handleDeleteConfirm}
                    className="p-1.5 rounded-md hover:bg-gray-100 text-gray-600 hover:text-red-600 transition-colors min-h-[32px] min-w-[32px] flex items-center justify-center"
                    title={t('message_bubble.delete', 'Delete this message')}
                    aria-label={t('message_bubble.delete', 'Delete this message')}
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                )}
              </div>
            )}
          </div>
          <div className="mt-1 text-xs text-gray-500">{formattedTime}</div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="flex mb-4 justify-end" data-external-id={message.externalId}>
      <div className="max-w-[75%] md:max-w-[70%]">
        
        <div
          className="relative group"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div className="bg-sky-500 p-3 rounded-lg chat-bubble-user text-white shadow-sm">
            {quotedInfo && (
              <QuotedMessagePreview
                quotedMessageId={quotedInfo.quotedMessageId}
                isInbound={false}
                onQuotedMessageClick={onQuotedMessageClick}
              />
            )}
            {renderMessageContent()}
            {message.status && message.status !== 'sent' && (
              <div className="text-right mt-1">
                <span className="text-xs text-sky-100">
                  {message.status === 'delivered' && <i className="ri-check-double-line"></i>}
                  {message.status === 'read' && <i className="ri-check-double-line font-bold"></i>}
                  {message.status === 'sending' && <i className="ri-time-line"></i>}
                  {message.status === 'failed' && <i className="ri-error-warning-line"></i>}
                </span>
              </div>
            )}
          </div>

          {(isHovered || showDeleteConfirm) && (canReply || canDelete) && (
            <div className="absolute top-13 left-2 flex items-center gap-1 bg-white rounded-lg shadow-lg border border-gray-200 p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10">
              {canReply && (
                <button
                  onClick={handleReplyToMessage}
                  className="p-1.5 rounded-md hover:bg-gray-100 text-gray-600 hover:text-blue-600 transition-colors min-h-[32px] min-w-[32px] flex items-center justify-center"
                  title={t('message_bubble.reply', 'Reply to this message')}
                  aria-label={t('message_bubble.reply', 'Reply to this message')}
                >
                  <Reply className="h-4 w-4" />
                </button>
              )}
              {canDelete && (
                <button
                  onClick={handleDeleteConfirm}
                  className="p-1.5 rounded-md hover:bg-gray-100 text-gray-600 hover:text-red-600 transition-colors min-h-[32px] min-w-[32px] flex items-center justify-center"
                  title={t('message_bubble.delete', 'Delete this message')}
                  aria-label={t('message_bubble.delete', 'Delete this message')}
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              )}
            </div>
          )}
        </div>
        <div className="mt-1 text-xs text-gray-500 text-right">{formattedTime}</div>
      </div>
      <div className="flex-shrink-0 ml-2">
        {isFromBot ? (
          <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
            <BotIcon size={16} color="#7c3aed" />
          </div>
        ) : message.senderType === 'user' ? (
          <div className="w-8 h-8 rounded-full bg-sky-200 flex items-center justify-center text-sky-700 font-medium">
            <span>A</span>
          </div>
        ) : (
          <div className="w-8 h-8 rounded-full bg-sky-100 flex items-center justify-center">
            <i className="ri-customer-service-2-line text-sky-600"></i>
          </div>
        )}
      </div>

      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                  <Trash2 className="h-5 w-5 text-red-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    {isWhatsAppMessage()
                      ? (isGroupChat()
                          ? t('message_bubble.confirm_delete_whatsapp_group_title', 'Delete Group Message for Everyone')
                          : t('message_bubble.confirm_delete_whatsapp_title', 'Delete Message for Everyone')
                        )
                      : t('message_bubble.confirm_delete_title', 'Delete Message')
                    }
                  </h3>
                  <p className="text-sm text-gray-500">
                    {isWhatsAppMessage()
                      ? (isMessageTooOld()
                          ? (isGroupChat()
                              ? t('message_bubble.confirm_delete_whatsapp_group_old', `This group message is too old to be deleted from WhatsApp (72-minute limit). It will only be deleted from ${branding.appName}.`, { appName: branding.appName })
                              : t('message_bubble.confirm_delete_whatsapp_old', `This message is too old to be deleted from WhatsApp (72-minute limit). It will only be deleted from ${branding.appName}.`, { appName: branding.appName })
                            )
                          : (isGroupChat()
                              ? t('message_bubble.confirm_delete_whatsapp_group_message', `This message will be deleted from both ${branding.appName} and all group participants' WhatsApp chats. This action cannot be undone.`, { appName: branding.appName })
                              : t('message_bubble.confirm_delete_whatsapp_message', `This message will be deleted from both ${branding.appName} and the recipient's WhatsApp chat. This action cannot be undone.`, { appName: branding.appName })
                            )
                        )
                      : t('message_bubble.confirm_delete_message', 'Are you sure you want to delete this message? This action cannot be undone.')
                    }
                  </p>
                </div>
              </div>

              <div className="bg-gray-50 p-3 rounded-md mb-4">
                <p className="text-sm text-gray-700 line-clamp-3">
                  {stripAgentSignature(message.content || '') || t('message_bubble.media_message', 'Media message')}
                </p>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={handleDeleteCancel}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  disabled={isDeleting}
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  onClick={handleDeleteMessage}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      {t('message_bubble.deleting', 'Deleting...')}
                    </>
                  ) : (
                    <>
                      <Trash2 className="h-4 w-4 mr-2" />
                      {t('message_bubble.delete_confirm', 'Delete')}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
