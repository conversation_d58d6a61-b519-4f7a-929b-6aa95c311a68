import { useState, useEffect } from 'react';
import { useConversations } from '@/context/ConversationContext';
import { useTranslation } from '@/hooks/use-translation';
import ConversationItem from './ConversationItem';
import NewConversationModal from './NewConversationModal';
import { useQuery } from '@tanstack/react-query';
import { usePermissions } from '@/hooks/usePermissions';
import { useAuth } from '@/hooks/use-auth';
import { useMobileLayout } from '@/contexts/mobile-layout-context';

export default function ConversationList() {
  const { conversations, isLoading, activeConversationId, setActiveConversationId, activeChannelId, setActiveChannelId } = useConversations();
  const { t } = useTranslation();
  const [filterStatus, setFilterStatus] = useState<'all' | 'unassigned' | 'assigned' | 'assigned_to_me'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isNewConversationModalOpen, setIsNewConversationModalOpen] = useState(false);

  const { user } = useAuth();
  const { canViewAllConversations, canOnlyViewAssignedConversations } = usePermissions();
  const { isMobile, isTablet, toggleConversationList, setConversationListOpen } = useMobileLayout();

  type CurrentUser = { id: string; [key: string]: any };

  const { data: currentUser } = useQuery<CurrentUser>({
    queryKey: ['/api/users/me'],
  });

  useEffect(() => {
    if (canOnlyViewAssignedConversations() && !user?.isSuperAdmin) {
      setFilterStatus('assigned_to_me');
    }
  }, [canOnlyViewAssignedConversations, user?.isSuperAdmin]);

  const handleConversationCreated = (conversation: any) => {
    if (conversation && conversation.id) {
      setActiveConversationId(conversation.id);
      if (isMobile) {
        setConversationListOpen(false);
      }
    }
  };

  const handleConversationClick = (conversationId: number) => {
    setActiveConversationId(conversationId);
    if (isMobile) {
      setConversationListOpen(false);
    }
  };

  if (isLoading) {
    return (
      <div className={`
        ${isMobile ? 'w-full' : 'w-72 lg:w-80'}
        border-r border-gray-200 bg-white flex-shrink-0 overflow-hidden flex flex-col
        ${isMobile ? 'h-full' : ''}
      `}>
        <div className="p-3 sm:p-4 border-b border-gray-200">
          <h2 className="text-lg font-medium">{t('inbox.conversations', 'Conversations')}</h2>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-pulse flex flex-col w-full p-3 sm:p-4 space-y-3">
            <div className="h-10 sm:h-12 bg-gray-200 rounded w-full"></div>
            <div className="h-16 sm:h-20 bg-gray-200 rounded w-full"></div>
            <div className="h-16 sm:h-20 bg-gray-200 rounded w-full"></div>
            <div className="h-16 sm:h-20 bg-gray-200 rounded w-full"></div>
          </div>
        </div>
      </div>
    );
  }

  const filteredConversations = conversations
    .filter(conversation => {
      if (activeChannelId !== null) {
        return conversation.channelId === activeChannelId;
      }
      return true;
    })
    .filter(conversation => {
      if (canOnlyViewAssignedConversations() && !user?.isSuperAdmin) {
        if (conversation.assignedToUserId !== currentUser?.id) {
          return false;
        }
      }

      if (filterStatus === 'all') return true;
      if (filterStatus === 'assigned') return conversation.assignedToUserId !== null;
      if (filterStatus === 'unassigned') return conversation.assignedToUserId === null;
      if (filterStatus === 'assigned_to_me') return conversation.assignedToUserId === currentUser?.id;
      return true;
    })
    .filter(conversation => {
      if (!searchQuery) return true;

      const query = searchQuery.toLowerCase().trim();
      const contact = conversation.contact;

      if (!contact) return false;

      if (contact.name?.toLowerCase().includes(query)) {
        return true;
      }

      if (contact.phone?.toLowerCase().includes(query)) {
        return true;
      }

      if (contact.email?.toLowerCase().includes(query)) {
        return true;
      }

      if (contact.tags && Array.isArray(contact.tags)) {
        return contact.tags.some((tag: string) =>
          tag.toLowerCase().includes(query)
        );
      }

      return false;
    })
    .sort((a, b) => new Date(b.lastMessageAt).getTime() - new Date(a.lastMessageAt).getTime());

  return (
    <div
      className={`
        ${isMobile ? 'w-full' : 'w-72 lg:w-80'}
        border-r border-gray-200 bg-white flex-shrink-0 overflow-hidden flex flex-col
        ${isMobile ? 'h-full' : ''}
      `}
      onClick={(e) => e.stopPropagation()}
    >
      <div className="p-3 sm:p-4 border-b border-gray-200 flex justify-between items-center">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            {isMobile && (
              <button
                onClick={toggleConversationList}
                className="p-2 rounded-md hover:bg-gray-100 lg:hidden"
                aria-label={t('inbox.close_conversations', 'Close conversations')}
              >
                <i className="ri-close-line text-lg text-gray-600"></i>
              </button>
            )}
            <h2 className="text-lg font-medium truncate">{t('inbox.conversations', 'Conversations')}</h2>
          </div>
          {activeChannelId && (
            <div className="text-xs text-gray-500 mt-1 flex items-center">
              <span className="truncate">{t('inbox.filtered_by_channel', 'Filtered by channel')}</span>
              <button
                onClick={() => setActiveChannelId(null)}
                className="ml-2 p-1 text-primary-600 hover:text-primary-800 rounded-full min-h-[44px] min-w-[44px] flex items-center justify-center"
                title={t('inbox.clear_channel_filter', 'Clear channel filter')}
              >
                <i className="ri-close-line text-xs"></i>
              </button>
            </div>
          )}
        </div>
        <div className="flex space-x-2 ml-2">
          <button
            className="p-2 sm:p-1.5 rounded-md bg-primary-50 text-primary-600 hover:bg-primary-100 min-h-[44px] min-w-[44px] flex items-center justify-center"
            onClick={() => setIsNewConversationModalOpen(true)}
            title={t('inbox.start_new_conversation', 'Start new conversation')}
          >
            <i className="ri-user-add-line text-lg sm:text-base"></i>
          </button>
        </div>
      </div>

      <div className="p-3 sm:p-4 border-b border-gray-200 flex items-center bg-gray-50">
        <div className="relative flex-1">
          <input
            type="search"
            placeholder={t('inbox.search_conversations_enhanced', 'Search by name, tag, phone, email...')}
            className="w-full pl-9 pr-4 py-3 sm:py-2 rounded-lg border border-gray-300 bg-white text-base sm:text-sm"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <i className="ri-search-line absolute left-3 top-3.5 sm:top-2.5 text-gray-400"></i>
        </div>
      </div>

      <div className="flex px-3 sm:px-4 py-2 border-b border-gray-200 space-x-1 overflow-x-auto scrollbar-hide">
        {(canViewAllConversations() || user?.isSuperAdmin) && (
          <button
            className={`px-3 sm:px-4 py-2 sm:py-1 rounded-full text-sm font-medium whitespace-nowrap min-h-[23px] sm:min-h-auto flex items-center ${
              filterStatus === 'all'
                ? 'bg-primary-100 text-primary-700'
                : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
            onClick={() => setFilterStatus('all')}
          >
            {t('inbox.filter.all', 'All')}
          </button>
        )}

        {(canViewAllConversations() || user?.isSuperAdmin) && (
          <button
            className={`px-3 sm:px-4 py-2 sm:py-1 rounded-full text-sm font-medium whitespace-nowrap min-h-[23px] sm:min-h-auto flex items-center ${
              filterStatus === 'unassigned'
                ? 'bg-primary-100 text-primary-700'
                : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
            onClick={() => setFilterStatus('unassigned')}
          >
            {t('inbox.filter.unassigned', 'Unassigned')}
          </button>
        )}

        <button
          className={`px-3 sm:px-4 py-2 sm:py-1 rounded-full text-sm font-medium whitespace-nowrap min-h-[23px] sm:min-h-auto flex items-center ${
            filterStatus === 'assigned_to_me'
              ? 'bg-primary-100 text-primary-700'
              : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
          }`}
          onClick={() => setFilterStatus('assigned_to_me')}
        >
          {t('inbox.filter.my_chats', 'My Chats')}
        </button>

        {(canViewAllConversations() || user?.isSuperAdmin) && (
          <button
            className={`px-3 sm:px-4 py-2 sm:py-1 rounded-full text-sm font-medium whitespace-nowrap min-h-[23px] sm:min-h-auto flex items-center ${
              filterStatus === 'assigned'
                ? 'bg-primary-100 text-primary-700'
                : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
            onClick={() => setFilterStatus('assigned')}
          >
            {t('inbox.filter.assigned', 'Assigned')}
          </button>
        )}
      </div>

      <div className="overflow-y-auto flex-1 scrollbar-hide" data-conversation-list>
        {filteredConversations.length === 0 ? (
          <div className="p-4 sm:p-6 text-center text-gray-500">
            <div className="text-sm sm:text-base">
              {t('inbox.no_conversations_found', 'No conversations found')}
            </div>
          </div>
        ) : (
          filteredConversations.map(conversation => (
            <ConversationItem
              key={conversation.id}
              conversation={conversation}
              isActive={conversation.id === activeConversationId}
              onClick={() => handleConversationClick(conversation.id)}
              searchQuery={searchQuery}
            />
          ))
        )}
      </div>

      <NewConversationModal
        isOpen={isNewConversationModalOpen}
        onClose={() => setIsNewConversationModalOpen(false)}
        onConversationCreated={handleConversationCreated}
      />
    </div>
  );
}

