import { Express, Request, Response } from "express";
import { storage } from "./storage";
import { z } from "zod";


const ensureAuthenticated = (req: Request, res: Response, next: any) => {
  if (req.isAuthenticated()) {
    return next();
  }
  res.status(401).json({ message: 'Unauthorized' });
};


const ensureSuperAdmin = (req: Request, res: Response, next: any) => {
  if (req.isAuthenticated() && req.user && (req.user as any).isSuperAdmin) {
    return next();
  }
  res.status(403).json({ message: 'Super admin access required' });
};


const planSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  price: z.number().min(0, "Price must be a positive number"),
  maxUsers: z.number().int().min(1, "Max users must be at least 1"),
  maxContacts: z.number().int().min(0, "Max contacts must be a positive number"),
  maxChannels: z.number().int().min(0, "Max channels must be a positive number"),
  maxFlows: z.number().int().min(0, "Max flows must be a positive number"),
  maxCampaigns: z.number().int().min(0, "Max campaigns must be a positive number").default(5),
  maxCampaignRecipients: z.number().int().min(0, "Max campaign recipients must be a positive number").default(1000),
  campaignFeatures: z.array(z.string()).default(["basic_campaigns"]),
  isActive: z.boolean().default(true),
  isFree: z.boolean().default(false),
  hasTrialPeriod: z.boolean().default(false),
  trialDays: z.number().int().min(0, "Trial days must be a positive number").default(0),
  features: z.array(z.string()).default([])
});

export function registerPlanRoutes(app: Express) {

  app.get("/api/plans", ensureAuthenticated, async (req, res) => {
    try {
      const plans = await storage.getAllPlans();

      const activePlans = plans.filter(plan => plan.isActive);
      res.json(activePlans);
    } catch (error) {
      console.error("Error fetching plans:", error);
      res.status(500).json({ error: "Failed to fetch plans" });
    }
  });


  app.get("/api/plans/public", async (req, res) => {
    try {
      const plans = await storage.getAllPlans();

      const activePlans = plans.filter(plan => plan.isActive);
      res.json(activePlans);
    } catch (error) {
      console.error("Error fetching plans:", error);
      res.status(500).json({ error: "Failed to fetch plans" });
    }
  });


  app.get("/api/plans/registration", async (req, res) => {
    try {
      const plans = await storage.getAllPlans();

      const registrationPlans = plans.filter(plan =>
        plan.isActive && (plan.isFree || plan.hasTrialPeriod)
      );
      res.json(registrationPlans);
    } catch (error) {
      console.error("Error fetching registration plans:", error);
      res.status(500).json({ error: "Failed to fetch registration plans" });
    }
  });


  app.get("/api/admin/plans", ensureSuperAdmin, async (req, res) => {
    try {
      const plans = await storage.getAllPlans();
      res.json(plans);
    } catch (error) {
      console.error("Error fetching plans:", error);
      res.status(500).json({ error: "Failed to fetch plans" });
    }
  });


  app.get("/api/admin/plans/:id", ensureSuperAdmin, async (req, res) => {
    try {
      const planId = parseInt(req.params.id);
      const plan = await storage.getPlan(planId);

      if (!plan) {
        return res.status(404).json({ error: "Plan not found" });
      }

      res.json(plan);
    } catch (error) {
      console.error("Error fetching plan:", error);
      res.status(500).json({ error: "Failed to fetch plan" });
    }
  });


  app.post("/api/admin/plans", ensureSuperAdmin, async (req, res) => {
    try {

      const transformedBody = { ...req.body };
      if (transformedBody.price !== undefined) {
        transformedBody.price = parseFloat(transformedBody.price);
      }
      if (transformedBody.maxUsers !== undefined) {
        transformedBody.maxUsers = parseInt(transformedBody.maxUsers);
      }
      if (transformedBody.maxContacts !== undefined) {
        transformedBody.maxContacts = parseInt(transformedBody.maxContacts);
      }
      if (transformedBody.maxChannels !== undefined) {
        transformedBody.maxChannels = parseInt(transformedBody.maxChannels);
      }
      if (transformedBody.maxFlows !== undefined) {
        transformedBody.maxFlows = parseInt(transformedBody.maxFlows);
      }
      if (transformedBody.maxCampaigns !== undefined) {
        transformedBody.maxCampaigns = parseInt(transformedBody.maxCampaigns);
      }
      if (transformedBody.maxCampaignRecipients !== undefined) {
        transformedBody.maxCampaignRecipients = parseInt(transformedBody.maxCampaignRecipients);
      }
      if (transformedBody.trialDays !== undefined) {
        transformedBody.trialDays = parseInt(transformedBody.trialDays);
      }


      const validationResult = planSchema.safeParse(transformedBody);

      if (!validationResult.success) {
        return res.status(400).json({
          error: "Validation failed",
          details: validationResult.error.format()
        });
      }

      const planData = validationResult.data;


      const newPlan = await storage.createPlan({
        name: planData.name,
        description: planData.description || "",
        price: planData.price,
        maxUsers: planData.maxUsers,
        maxContacts: planData.maxContacts,
        maxChannels: planData.maxChannels,
        maxFlows: planData.maxFlows,
        maxCampaigns: planData.maxCampaigns,
        maxCampaignRecipients: planData.maxCampaignRecipients,
        campaignFeatures: planData.campaignFeatures,
        isActive: planData.isActive,
        isFree: planData.isFree,
        hasTrialPeriod: planData.hasTrialPeriod,
        trialDays: planData.trialDays,
        features: planData.features
      });

      res.status(201).json(newPlan);
    } catch (error) {
      console.error("Error creating plan:", error);
      res.status(500).json({ error: "Failed to create plan" });
    }
  });


  app.put("/api/admin/plans/:id", ensureSuperAdmin, async (req, res) => {
    try {
      const planId = parseInt(req.params.id);


      const existingPlan = await storage.getPlan(planId);
      if (!existingPlan) {
        return res.status(404).json({ error: "Plan not found" });
      }


      const transformedBody = { ...req.body };
      if (transformedBody.price !== undefined) {
        transformedBody.price = parseFloat(transformedBody.price);
      }
      if (transformedBody.maxUsers !== undefined) {
        transformedBody.maxUsers = parseInt(transformedBody.maxUsers);
      }
      if (transformedBody.maxContacts !== undefined) {
        transformedBody.maxContacts = parseInt(transformedBody.maxContacts);
      }
      if (transformedBody.maxChannels !== undefined) {
        transformedBody.maxChannels = parseInt(transformedBody.maxChannels);
      }
      if (transformedBody.maxFlows !== undefined) {
        transformedBody.maxFlows = parseInt(transformedBody.maxFlows);
      }
      if (transformedBody.maxCampaigns !== undefined) {
        transformedBody.maxCampaigns = parseInt(transformedBody.maxCampaigns);
      }
      if (transformedBody.maxCampaignRecipients !== undefined) {
        transformedBody.maxCampaignRecipients = parseInt(transformedBody.maxCampaignRecipients);
      }
      if (transformedBody.trialDays !== undefined) {
        transformedBody.trialDays = parseInt(transformedBody.trialDays);
      }


      const validationResult = planSchema.partial().safeParse(transformedBody);

      if (!validationResult.success) {
        return res.status(400).json({
          error: "Validation failed",
          details: validationResult.error.format()
        });
      }

      const planData = validationResult.data;


      const updatedPlan = await storage.updatePlan(planId, planData);

      res.json(updatedPlan);
    } catch (error) {
      console.error("Error updating plan:", error);
      res.status(500).json({ error: "Failed to update plan" });
    }
  });


  app.delete("/api/admin/plans/:id", ensureSuperAdmin, async (req, res) => {
    try {
      const planId = parseInt(req.params.id);


      const existingPlan = await storage.getPlan(planId);
      if (!existingPlan) {
        return res.status(404).json({ error: "Plan not found" });
      }


      const success = await storage.deletePlan(planId);

      if (!success) {
        return res.status(500).json({ error: "Failed to delete plan" });
      }

      res.json({ message: "Plan deleted successfully" });
    } catch (error) {
      console.error("Error deleting plan:", error);
      res.status(500).json({ error: "Failed to delete plan" });
    }
  });
}
