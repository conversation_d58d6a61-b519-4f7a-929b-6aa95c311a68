import * as cron from 'node-cron';
import crypto from 'crypto';
import { BackupService, BackupSchedule } from './backup-service';
import { storage } from '../storage';

export class BackupScheduler {
  private scheduledTasks: Map<string, cron.ScheduledTask> = new Map();
  private backupService: BackupService;

  constructor() {
    this.backupService = new BackupService();
    this.initializeScheduler();
  }

  private async initializeScheduler(): Promise<void> {
    try {
      await this.loadAndStartSchedules();

      this.scheduleCleanupTask();

      
    } catch (error) {
      console.error('Error initializing backup scheduler:', error);
    }
  }

  private async loadAndStartSchedules(): Promise<void> {
    try {
      const config = await storage.getAppSetting('backup_config');
      const schedules = (config?.value as any)?.schedules || [];

      for (const schedule of schedules) {
        if (schedule.enabled) {
          await this.startSchedule(schedule);
        }
      }
    } catch (error) {
      console.error('Error loading backup schedules:', error);
    }
  }

  async updateSchedules(schedules: BackupSchedule[]): Promise<void> {
    try {
      this.stopAllSchedules();

      for (const schedule of schedules) {
        if (schedule.enabled) {
          await this.startSchedule(schedule);
        }
      }

      
    } catch (error) {
      console.error('Error updating backup schedules:', error);
      throw error;
    }
  }

  private async startSchedule(schedule: BackupSchedule): Promise<void> {
    try {
      const cronExpression = this.buildCronExpression(schedule);

      if (!cron.validate(cronExpression)) {
        throw new Error(`Invalid cron expression: ${cronExpression}`);
      }

      const task = cron.schedule(cronExpression, async () => {
        await this.executeScheduledBackup(schedule);
      });

      this.scheduledTasks.set(schedule.id, task);

      
    } catch (error) {
      console.error(`Error starting schedule ${schedule.id}:`, error);
      throw error;
    }
  }

  private buildCronExpression(schedule: BackupSchedule): string {
    const [hours, minutes] = schedule.time.split(':').map(Number);

    switch (schedule.frequency) {
      case 'daily':
        return `${minutes} ${hours} * * *`;

      case 'weekly':
        const dayOfWeek = schedule.day_of_week || 0;
        return `${minutes} ${hours} * * ${dayOfWeek}`;

      case 'monthly':
        const dayOfMonth = schedule.day_of_month || 1;
        return `${minutes} ${hours} ${dayOfMonth} * *`;

      default:
        throw new Error(`Unsupported frequency: ${schedule.frequency}`);
    }
  }

  private async executeScheduledBackup(schedule: BackupSchedule): Promise<void> {
    try {
      

      const description = `Scheduled ${schedule.frequency} backup`;

      const backup = await this.backupService.createBackup({
        type: 'scheduled',
        description,
        storage_locations: schedule.storage_locations
      });

      

      await this.logBackupOperation(schedule.id, 'success', backup.id);

    } catch (error) {
      console.error(`Scheduled backup failed for schedule ${schedule.id}:`, error);

      await this.logBackupOperation(schedule.id, 'failed', null, error instanceof Error ? error.message : String(error));
    }
  }

  private async logBackupOperation(
    scheduleId: string,
    status: 'success' | 'failed',
    backupId: string | null,
    errorMessage?: string
  ): Promise<void> {
    try {
      const logs = await this.getBackupLogs();

      const logEntry = {
        id: crypto.randomUUID(),
        schedule_id: scheduleId,
        backup_id: backupId,
        status,
        timestamp: new Date().toISOString(),
        error_message: errorMessage
      };

      logs.push(logEntry);

      if (logs.length > 100) {
        logs.splice(0, logs.length - 100);
      }

      await storage.saveAppSetting('backup_logs', logs);
    } catch (error) {
      console.error('Error logging backup operation:', error);
    }
  }

  async getBackupLogs(): Promise<Array<{
    id: string;
    schedule_id: string;
    backup_id: string | null;
    status: 'success' | 'failed';
    timestamp: string;
    error_message?: string;
    metadata?: any;
  }>> {
    try {
      const setting = await storage.getAppSetting('backup_logs');
      return (setting?.value as any) || [];
    } catch (error) {
      console.error('Error getting backup logs:', error);
      return [];
    }
  }

  private scheduleCleanupTask(): void {
    cron.schedule('0 3 * * *', async () => {
      try {


        const config = await storage.getAppSetting('backup_config');
        const retentionDays = (config?.value as any)?.retention_days || 30;

        const result = await this.backupService.cleanupOldBackups(retentionDays);



        if (result.errors.length > 0) {
          console.error('Cleanup errors:', result.errors);
        }

        await this.logCleanupOperation(result.deleted, result.errors);

      } catch (error) {
        console.error('Error during scheduled cleanup:', error);
      }
    });
  }

  private async logCleanupOperation(deleted: number, errors: string[]): Promise<void> {
    try {
      const logs = await this.getBackupLogs();

      const logEntry = {
        id: crypto.randomUUID(),
        schedule_id: 'cleanup',
        backup_id: null,
        status: (errors.length === 0 ? 'success' : 'failed') as 'success' | 'failed',
        timestamp: new Date().toISOString(),
        error_message: errors.length > 0 ? errors.join('; ') : undefined,
        metadata: { deleted_count: deleted }
      };

      logs.push(logEntry);
      await storage.saveAppSetting('backup_logs', logs);
    } catch (error) {
      console.error('Error logging cleanup operation:', error);
    }
  }

  private stopAllSchedules(): void {
    this.scheduledTasks.forEach((task) => {
      task.stop();
      task.destroy();
    });
    this.scheduledTasks.clear();
  }

  stopSchedule(scheduleId: string): void {
    const task = this.scheduledTasks.get(scheduleId);
    if (task) {
      task.stop();
      task.destroy();
      this.scheduledTasks.delete(scheduleId);
      
    }
  }

  getActiveSchedules(): string[] {
    return Array.from(this.scheduledTasks.keys());
  }

  async getNextScheduledRuns(): Promise<Array<{
    schedule_id: string;
    next_run: Date;
    frequency: string;
  }>> {
    try {
      const config = await storage.getAppSetting('backup_config');
      const schedules = (config?.value as any)?.schedules || [];

      const nextRuns = [];

      for (const schedule of schedules) {
        if (schedule.enabled) {
          const nextRun = this.calculateNextRun(schedule);
          nextRuns.push({
            schedule_id: schedule.id,
            next_run: nextRun,
            frequency: schedule.frequency
          });
        }
      }

      return nextRuns.sort((a, b) => a.next_run.getTime() - b.next_run.getTime());
    } catch (error) {
      console.error('Error getting next scheduled runs:', error);
      return [];
    }
  }

  private calculateNextRun(schedule: BackupSchedule): Date {
    const now = new Date();
    const [hours, minutes] = schedule.time.split(':').map(Number);

    let nextRun = new Date();
    nextRun.setHours(hours, minutes, 0, 0);

    switch (schedule.frequency) {
      case 'daily':
        if (nextRun <= now) {
          nextRun.setDate(nextRun.getDate() + 1);
        }
        break;

      case 'weekly':
        const targetDay = schedule.day_of_week || 0;
        const currentDay = nextRun.getDay();
        let daysUntilTarget = targetDay - currentDay;

        if (daysUntilTarget <= 0 || (daysUntilTarget === 0 && nextRun <= now)) {
          daysUntilTarget += 7;
        }

        nextRun.setDate(nextRun.getDate() + daysUntilTarget);
        break;

      case 'monthly':
        const targetDate = schedule.day_of_month || 1;
        nextRun.setDate(targetDate);

        if (nextRun <= now) {
          nextRun.setMonth(nextRun.getMonth() + 1);
        }
        break;
    }

    return nextRun;
  }

  shutdown(): void {
    
    this.stopAllSchedules();
  }
}

let backupScheduler: BackupScheduler | null = null;

export function getBackupScheduler(): BackupScheduler {
  if (!backupScheduler) {
    backupScheduler = new BackupScheduler();
  }
  return backupScheduler;
}
