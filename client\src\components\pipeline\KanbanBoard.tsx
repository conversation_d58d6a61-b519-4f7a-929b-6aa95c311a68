import { useState, useMemo } from 'react';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import DealCard from '@/components/pipeline/DealCard';
import { Deal, PipelineStage } from '@shared/schema';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from '@/hooks/use-translation';
import { PlusCircle, Filter, Edit2, Trash2, MoreHorizontal, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface KanbanBoardProps {
  onAddDeal: () => void;
  onOpenFilterModal: () => void;
  activeFilter: {
    priority: string | null;
    assignedToUserId: number | null;
    titleSearch: string | null;
    tags: string[] | null;
    contactPhone: string | null;
    contactName: string | null;
  };
}

export default function KanbanBoard({ onAddDeal, onOpenFilterModal, activeFilter }: KanbanBoardProps) {
  const { toast } = useToast();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [editingStageId, setEditingStageId] = useState<number | null>(null);
  const [newStageName, setNewStageName] = useState('');
  const [newStageColor, setNewStageColor] = useState('#3a86ff');
  const [editedStageName, setEditedStageName] = useState('');
  const [editedStageColor, setEditedStageColor] = useState('');
  const [isAddingStage, setIsAddingStage] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [stageToDelete, setStageToDelete] = useState<PipelineStage | null>(null);
  const [targetStageId, setTargetStageId] = useState<number | null>(null);

  const stageColors = [
    '#4361ee',
    '#3a86ff',
    '#7209b7',
    '#f72585',
    '#4cc9f0',
    '#4895ef',
    '#560bad',
    '#f3722c',
    '#f8961e',
    '#90be6d',
    '#43aa8b',
    '#577590',
  ];

  const {
    data: pipelineStages = [],
    isLoading: isLoadingStages
  } = useQuery({
    queryKey: ['/api/pipeline/stages'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/pipeline/stages');
      const data = await res.json();
      return data;
    },
  });

  const {
    data: deals = [],
    isLoading: isLoadingDeals
  } = useQuery({
    queryKey: ['/api/deals', activeFilter],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      if (activeFilter.priority) {
        queryParams.append('priority', activeFilter.priority);
      }
      if (activeFilter.assignedToUserId) {
        queryParams.append('assignedTo', activeFilter.assignedToUserId.toString());
      }
      if (activeFilter.titleSearch) {
        queryParams.append('titleSearch', activeFilter.titleSearch);
      }
      if (activeFilter.tags && activeFilter.tags.length > 0) {
        queryParams.append('tags', activeFilter.tags.join(','));
      }
      if (activeFilter.contactPhone) {
        queryParams.append('contactPhone', activeFilter.contactPhone);
      }
      if (activeFilter.contactName) {
        queryParams.append('contactName', activeFilter.contactName);
      }

      const url = `/api/deals${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const res = await apiRequest('GET', url);
      return res.json();
    },
  });

  const organizedDeals = useMemo(() => {
    if (!pipelineStages || pipelineStages.length === 0) return {};

    const newOrganizedDeals: Record<number, Deal[]> = {};

    pipelineStages.forEach((stage: PipelineStage) => {
      newOrganizedDeals[stage.id] = [];
    });

    if (deals && Array.isArray(deals) && deals.length > 0) {
      deals.forEach((deal: Deal) => {
        if (deal.stageId && newOrganizedDeals[deal.stageId]) {
          newOrganizedDeals[deal.stageId].push(deal);
        }
      });
    }

    return newOrganizedDeals;
  }, [deals, pipelineStages]);

  const [optimisticDeals, setOptimisticDeals] = useState<Record<number, Deal[]>>({});

  const createStageMutation = useMutation({
    mutationFn: async ({ name, color }: { name: string; color: string }) => {
      const response = await apiRequest('POST', '/api/pipeline/stages', {
        name,
        color,
        order: pipelineStages.length + 1
      });
      const data = await response.json();
      return data;
    },
    onSuccess: (data) => {
      setNewStageName('');
      setNewStageColor('#3a86ff');
      setIsAddingStage(false);


      queryClient.invalidateQueries({ queryKey: ['/api/pipeline/stages'] });
      queryClient.refetchQueries({ queryKey: ['/api/pipeline/stages'] });


      queryClient.invalidateQueries({ queryKey: ['/api/deals'] });

      toast({
        title: t('common.success', 'Success'),
        description: t('pipeline.stage_created_success', 'Stage created successfully'),
      });
    },
    onError: (error: Error) => {
      toast({
        title: t('common.error', 'Error'),
        description: t('pipeline.stage_create_failed', 'Failed to create stage: {{error}}', { error: error.message }),
        variant: 'destructive',
      });
    },
  });

  const updateStageMutation = useMutation({
    mutationFn: async ({ id, name, color }: { id: number; name: string; color: string }) => {
      const response = await apiRequest('PUT', `/api/pipeline/stages/${id}`, { name, color });
      return response.json();
    },
    onSuccess: () => {
      setEditingStageId(null);
      setEditedStageName('');
      setEditedStageColor('');
      queryClient.invalidateQueries({ queryKey: ['/api/pipeline/stages'] });
      toast({
        title: t('common.success', 'Success'),
        description: t('pipeline.stage_updated_success', 'Stage updated successfully'),
      });
    },
    onError: (error: Error) => {
      toast({
        title: t('common.error', 'Error'),
        description: t('pipeline.stage_update_failed', 'Failed to update stage: {{error}}', { error: error.message }),
        variant: 'destructive',
      });
    },
  });

  const deleteStageMutation = useMutation({
    mutationFn: async ({ id, moveToStageId }: { id: number; moveToStageId?: number }) => {
      const url = `/api/pipeline/stages/${id}${moveToStageId ? `?moveToStageId=${moveToStageId}` : ''}`;
      const response = await apiRequest('DELETE', url);
      return response.ok;
    },
    onSuccess: () => {
      setShowDeleteDialog(false);
      setStageToDelete(null);
      setTargetStageId(null);
      queryClient.invalidateQueries({ queryKey: ['/api/pipeline/stages'] });
      queryClient.invalidateQueries({ queryKey: ['/api/deals'] });
      toast({
        title: t('common.success', 'Success'),
        description: t('pipeline.stage_deleted_success', 'Stage deleted successfully'),
      });
    },
    onError: (error: Error) => {
      toast({
        title: t('common.error', 'Error'),
        description: t('pipeline.stage_delete_failed', 'Failed to delete stage: {{error}}', { error: error.message }),
        variant: 'destructive',
      });
    },
  });

  const updateDealStageMutation = useMutation({
    mutationFn: async ({ dealId, stageId }: { dealId: number, stageId: number }) => {
      const response = await apiRequest('PATCH', `/api/deals/${dealId}/stageId`, { stageId });
      return response.json();
    },
    onSuccess: () => {
      setOptimisticDeals({});
      queryClient.invalidateQueries({ queryKey: ['/api/deals'] });
    },
    onError: (error: Error) => {
      setOptimisticDeals({});
      toast({
        title: t('common.error', 'Error'),
        description: t('pipeline.deal_update_failed', 'Failed to update deal stage: {{error}}', { error: error.message }),
        variant: 'destructive',
      });
    },
  });

  const handleAddStage = () => {
    if (!newStageName.trim()) {
      toast({
        title: t('common.error', 'Error'),
        description: t('pipeline.stage_name_required', 'Stage name cannot be empty'),
        variant: 'destructive',
      });
      return;
    }

    createStageMutation.mutate({ name: newStageName, color: newStageColor });
  };

  const handleEditStage = (stage: PipelineStage) => {
    setEditingStageId(stage.id);
    setEditedStageName(stage.name);
    setEditedStageColor(stage.color);
  };

  const handleUpdateStage = (id: number) => {
    if (!editedStageName.trim()) {
      toast({
        title: t('common.error', 'Error'),
        description: t('pipeline.stage_name_required', 'Stage name cannot be empty'),
        variant: 'destructive',
      });
      return;
    }

    updateStageMutation.mutate({ id, name: editedStageName, color: editedStageColor });
  };

  const handleCancelEdit = () => {
    setEditingStageId(null);
    setEditedStageName('');
    setEditedStageColor('');
  };

  const handleDeleteStage = (stage: PipelineStage) => {
    setStageToDelete(stage);
    setShowDeleteDialog(true);
  };

  const confirmDeleteStage = () => {
    if (!stageToDelete) return;

    deleteStageMutation.mutate({
      id: stageToDelete.id,
      moveToStageId: targetStageId || undefined
    });
  };

  const currentDeals = Object.keys(optimisticDeals).length > 0 ? optimisticDeals : organizedDeals;

  const handleDragEnd = (result: any) => {
    const { destination, source, draggableId } = result;

    if (!destination ||
        (destination.droppableId === source.droppableId &&
         destination.index === source.index)) {
      return;
    }

    const dealId = parseInt(draggableId);
    const sourceStageId = parseInt(source.droppableId);
    const destinationStageId = parseInt(destination.droppableId);

    if (sourceStageId !== destinationStageId) {
      const deal = currentDeals[sourceStageId]?.find(d => d.id === dealId);
      if (!deal) return;

      const newOptimisticDeals = { ...organizedDeals };
      newOptimisticDeals[sourceStageId] = newOptimisticDeals[sourceStageId].filter(d => d.id !== dealId);

      const updatedDeal = { ...deal, stageId: destinationStageId };
      newOptimisticDeals[destinationStageId] = [
        ...newOptimisticDeals[destinationStageId].slice(0, destination.index),
        updatedDeal,
        ...newOptimisticDeals[destinationStageId].slice(destination.index)
      ];

      setOptimisticDeals(newOptimisticDeals);

      updateDealStageMutation.mutate({ dealId, stageId: destinationStageId });
    } else {
      const newOptimisticDeals = { ...organizedDeals };
      const [removed] = newOptimisticDeals[sourceStageId].splice(source.index, 1);
      newOptimisticDeals[sourceStageId].splice(destination.index, 0, removed);
      setOptimisticDeals(newOptimisticDeals);
    }
  };

  const isLoading = isLoadingStages || isLoadingDeals;

  return (
    <div className="flex flex-col h-full">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl ">{t('nav.pipeline', 'Pipeline')}</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onOpenFilterModal}
            className="flex items-center gap-1"
          >
            <Filter className="h-4 w-4" />
            {t('common.filter', 'Filter')}
            {(activeFilter.priority || activeFilter.assignedToUserId || activeFilter.titleSearch ||
              (activeFilter.tags && activeFilter.tags.length > 0) || activeFilter.contactPhone || activeFilter.contactName) && (
              <span className="ml-1 flex h-2 w-2 rounded-full bg-primary"></span>
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAddingStage(true)}
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" />
            {t('pipeline.add_stage', 'Add Stage')}
          </Button>
          <Button onClick={onAddDeal} className="flex items-center gap-1 btn-brand-primary">
            <PlusCircle className="h-4 w-4" />
            {t('pipeline.add_deal', 'Add Deal')}
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full" />
        </div>
      ) : (
        <>
          {pipelineStages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 p-6 text-center bg-secondary/20 rounded-lg">
              <h3 className="text-lg  mb-2">{t('pipeline.no_stages', 'No Pipeline Stages')}</h3>
              <p className="text-muted-foreground mb-4">{t('pipeline.create_first_stage', 'Create your first stage to get started')}</p>
              <Button onClick={() => setIsAddingStage(true)}>{t('pipeline.add_first_stage', 'Add First Stage')}</Button>
            </div>
          ) : (
            <DragDropContext onDragEnd={handleDragEnd}>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6 gap-4 h-full overflow-hidden">
                {pipelineStages.map((stage: PipelineStage) => (
                  <div key={stage.id} className="flex flex-col h-full">
                    <div
                      className="flex justify-between items-center p-2 mb-2 rounded-md shadow-md"
                      style={{
                        borderLeft: `4px solid ${stage.color}`,
                        backgroundColor: `${stage.color}20`,
                      }}
                    >
                      <div className="flex items-center gap-1.5">
                        <h3 className="font-medium text-sm">{stage.name}</h3>
                        <span className="text-xs text-muted-foreground bg-background rounded-full px-1.5 py-0.5">
                          {currentDeals[stage.id]?.length || 0}
                        </span>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-7 w-7">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditStage(stage)}>
                            <Edit2 className="mr-2 h-4 w-4" />
                            {t('common.edit', 'Edit')}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDeleteStage(stage)}>
                            <Trash2 className="mr-2 h-4 w-4" />
                            {t('common.delete', 'Delete')}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <Droppable droppableId={stage.id.toString()}>
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.droppableProps}
                          className={`flex-1 p-2 rounded-md overflow-y-auto shadow-md ${
                            snapshot.isDraggingOver ? 'bg-secondary/50' : ''
                          }`}
                          style={{
                            minHeight: '70vh',
                            backgroundColor: stage.color ? `${stage.color}10` : undefined,
                            border: `1px solid ${stage.color}30`,
                          }}
                        >
                          {currentDeals[stage.id]?.map((deal, index) => (
                            <Draggable key={deal.id} draggableId={deal.id.toString()} index={index}>
                              {(provided, snapshot) => (
                                <div
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  {...provided.dragHandleProps}
                                  style={{
                                    ...provided.draggableProps.style,
                                    opacity: snapshot.isDragging ? 0.8 : 1,
                                  }}
                                >
                                  <DealCard deal={deal} />
                                </div>
                              )}
                            </Draggable>
                          ))}
                          {provided.placeholder}
                        </div>
                      )}
                    </Droppable>
                  </div>
                ))}
              </div>
            </DragDropContext>
          )}
        </>
      )}

      <Dialog open={isAddingStage} onOpenChange={setIsAddingStage}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('pipeline.add_stage_title', 'Add Pipeline Stage')}</DialogTitle>
            <DialogDescription>
              {t('pipeline.add_stage_description', 'Create a new stage for your pipeline. Stages help organize your deals and track their progress.')}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t('pipeline.stage_name', 'Stage Name')}</Label>
              <Input
                id="name"
                placeholder={t('pipeline.stage_name_placeholder', 'e.g., Discovery, Negotiation, Proposal')}
                value={newStageName}
                onChange={(e) => setNewStageName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="color">{t('pipeline.stage_color', 'Stage Color')}</Label>
              <div className="flex flex-wrap gap-2">
                {stageColors.map((color) => (
                  <div
                    key={color}
                    className={`w-8 h-8 rounded-full cursor-pointer border-2 ${newStageColor === color ? 'border-black dark:border-white' : 'border-transparent'}`}
                    style={{ backgroundColor: color }}
                    onClick={() => setNewStageColor(color)}
                  />
                ))}
              </div>
              <div className="mt-2 flex items-center">
                <div className="w-6 h-6 rounded-full mr-2" style={{ backgroundColor: newStageColor }} />
                <span className="text-sm">{newStageColor}</span>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingStage(false)}>
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button onClick={handleAddStage}>
              {t('pipeline.create_stage', 'Create Stage')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={!!editingStageId} onOpenChange={(open) => !open && setEditingStageId(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('pipeline.edit_stage_title', 'Edit Pipeline Stage')}</DialogTitle>
            <DialogDescription>
              {t('pipeline.edit_stage_description', 'Update the stage name and color.')}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">{t('pipeline.stage_name', 'Stage Name')}</Label>
              <Input
                id="edit-name"
                placeholder={t('pipeline.stage_name_placeholder', 'e.g., Discovery, Negotiation, Proposal')}
                value={editedStageName}
                onChange={(e) => setEditedStageName(e.target.value)}
                autoFocus
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-color">{t('pipeline.stage_color', 'Stage Color')}</Label>
              <div className="flex flex-wrap gap-2">
                {stageColors.map((color) => (
                  <div
                    key={color}
                    className={`w-8 h-8 rounded-full cursor-pointer border-2 ${editedStageColor === color ? 'border-black dark:border-white' : 'border-transparent'}`}
                    style={{ backgroundColor: color }}
                    onClick={() => setEditedStageColor(color)}
                  />
                ))}
              </div>
              <div className="mt-2 flex items-center">
                <div className="w-6 h-6 rounded-full mr-2" style={{ backgroundColor: editedStageColor }} />
                <span className="text-sm">{editedStageColor}</span>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelEdit}>
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button className="btn-brand-primary" onClick={() => editingStageId && handleUpdateStage(editingStageId)}>
              {t('pipeline.update_stage', 'Update Stage')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('pipeline.delete_stage_title', 'Delete Pipeline Stage')}</DialogTitle>
            <DialogDescription>
              {stageToDelete && currentDeals[stageToDelete.id]?.length > 0 ? (
                <>
                  {t('pipeline.stage_contains_deals', 'This stage contains {{count}} deals. Where would you like to move them?', { count: currentDeals[stageToDelete.id]?.length })}
                </>
              ) : (
                <>
                  {t('pipeline.delete_stage_confirmation', 'Are you sure you want to delete this stage? This action cannot be undone.')}
                </>
              )}
            </DialogDescription>
          </DialogHeader>

          {stageToDelete && currentDeals[stageToDelete.id]?.length > 0 && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="targetStage">{t('pipeline.move_deals_to', 'Move deals to')}</Label>
                <select
                  id="targetStage"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={targetStageId || ""}
                  onChange={(e) => setTargetStageId(parseInt(e.target.value))}
                >
                  <option value="">{t('pipeline.select_stage', 'Select a stage')}</option>
                  {pipelineStages
                    .filter((s: PipelineStage) => stageToDelete && s.id !== stageToDelete.id)
                    .map((stage: PipelineStage) => (
                      <option key={stage.id} value={stage.id}>
                        {stage.name}
                      </option>
                    ))}
                </select>
              </div>

              {!targetStageId && (
                <Alert variant="destructive">
                  <AlertDescription>
                    {t('pipeline.select_target_stage_warning', 'You must select a target stage or the deals in this stage will be lost.')}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setShowDeleteDialog(false);
              setStageToDelete(null);
              setTargetStageId(null);
            }}>
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteStage}
              disabled={!!stageToDelete && currentDeals[stageToDelete.id]?.length > 0 && !targetStageId}
            >
              {t('pipeline.delete_stage', 'Delete Stage')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}