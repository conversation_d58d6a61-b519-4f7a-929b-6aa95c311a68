import { storage } from '../../storage';
import {
  InsertMessage,
  InsertConversation,
  InsertContact,
} from '@shared/schema';
import { EventEmitter } from 'events';
import axios, { AxiosError } from 'axios';
import crypto from 'crypto';
import { logger } from '../../utils/logger';


interface InstagramAccountInfo {
  id: string;
  username: string;
  name: string;
  profile_picture_url: string;
  followers_count: number;
}

interface InstagramConnectionData {
  instagramAccountId?: string;
  accountInfo?: InstagramAccountInfo;
  appId?: string;
  appSecret?: string;
  webhookUrl?: string;
  verifyToken?: string;
  lastConnectedAt?: string;
  lastValidatedAt?: string;
  accessTokenExpiresAt?: string;
  lastTokenRefresh?: string;
  features?: {
    mediaMessages?: boolean;
    stories?: boolean;
    quickReplies?: boolean;
    templates?: boolean;
  };
  settings?: {
    autoResponses?: boolean;
    messageTemplates?: any[];
    quickReplies?: any[];
  };
}

interface ChannelConnection {
  id: number;
  userId: number;
  companyId: number;
  accessToken?: string | null;
  connectionData?: InstagramConnectionData | Record<string, any> | null;
  channelType: 'instagram' | string;
  status: 'connected' | 'disconnected' | 'error' | 'pending' | string;
}

type Contact = ReturnType<typeof storage.createContact> extends Promise<infer T> ? T : any;
type Conversation = ReturnType<typeof storage.createConversation> extends Promise<infer T> ? T : any;
type Message = ReturnType<typeof storage.createMessage> extends Promise<infer T> ? T : any;


interface InstagramWebhookMessageAttachment {
  type: string;
  payload?: {
    url?: string;
    sticker_id?: string;
  };
  title?: string;
  url?: string;
}

interface InstagramMediaUploadResponse {
  id: string;
}

interface InstagramMessageTemplate {
  id: string;
  name: string;
  content: string;
  mediaType?: 'image' | 'video';
  mediaUrl?: string;
  quickReplies?: string[];
}

interface InstagramQuickReply {
  content_type: 'text';
  title: string;
  payload: string;
}

interface InstagramWebhookMessage {
  mid: string;
  text?: string;
  attachments?: InstagramWebhookMessageAttachment[];
  timestamp?: number;
  is_deleted?: boolean;
  is_echo?: boolean;
}

interface InstagramWebhookMessagingEvent {
  sender?: { id: string };
  recipient?: { id: string };
  timestamp?: number;
  message?: InstagramWebhookMessage;
  postback?: { title: string; payload: string; };
  reaction?: { reaction: string; emoji: string; action: string; mid: string; };
}

interface InstagramWebhookEntry {
  id: string;
  time: number;
  messaging?: InstagramWebhookMessagingEvent[];
}

interface InstagramWebhookBody {
  object: 'instagram' | string;
  entry?: InstagramWebhookEntry[];
  'hub.mode'?: 'subscribe';
  'hub.verify_token'?: string;
  'hub.challenge'?: string;
}

interface ConnectionStatusUpdatePayload {
  connectionId: number;
  status: ChannelConnection['status'];
  accountInfo?: InstagramAccountInfo;
}

interface ConnectionErrorPayload {
  connectionId: number;
  error: string;
}

interface MessageReceivedPayload {
  message: Message;
  conversation: Conversation;
  contact: Contact;
  connection: ChannelConnection;
}

interface InstagramAccountInfoResponse extends InstagramAccountInfo {}
interface InstagramMessageSendResponse {
  message_id: string;
  recipient_id?: string;
}
interface InstagramSubscriptionResponse {
    success?: boolean;
}


interface InstagramMediaMessageRequestBody {
  recipient: { id: string };
  message: {
    attachment: {
      type: 'image' | 'video';
      payload: { url: string; is_reusable?: boolean };
    };
    text?: string;
  };
}

declare global {
  var broadcastToAllClients: ((payload: { type: string; data: any }) => void) | undefined;
}


interface ConnectionState {
  isActive: boolean;
  lastActivity: Date;
  errorCount: number;
  lastError: string | null;
  accountInfo: any | null;
}

const activeConnections = new Map<number, boolean>();
const connectionStates = new Map<number, ConnectionState>();
const eventEmitter = new EventEmitter();

// Increase max listeners to prevent memory leak warnings
eventEmitter.setMaxListeners(50);

// Register with monitor for debugging
import { eventEmitterMonitor } from '../../utils/event-emitter-monitor';
eventEmitterMonitor.register('instagram-service', eventEmitter);

/**
 * Get or create connection state
 */
function getConnectionState(connectionId: number): ConnectionState {
  if (!connectionStates.has(connectionId)) {
    connectionStates.set(connectionId, {
      isActive: false,
      lastActivity: new Date(),
      errorCount: 0,
      lastError: null,
      accountInfo: null
    });
  }
  return connectionStates.get(connectionId)!;
}

/**
 * Update connection activity
 */
function updateConnectionActivity(connectionId: number, success: boolean = true, error?: string) {
  const state = getConnectionState(connectionId);
  state.lastActivity = new Date();

  if (success) {
    state.errorCount = 0;
    state.lastError = null;
  } else {
    state.errorCount++;
    state.lastError = error || 'Unknown error';
  }
}

/**
 * Process a message through the flow executor
 * This function handles flow execution for Instagram messages
 */
async function processMessageThroughFlowExecutor(
  message: any,
  conversation: any,
  contact: any,
  channelConnection: any
): Promise<void> {
  try {
    const flowExecutorModule = await import('../flow-executor');
    const flowExecutor = flowExecutorModule.default;

    if (contact) {
      await flowExecutor.processIncomingMessage(message, conversation, contact, channelConnection);
    }
  } catch (error) {
    logger.error('instagram', 'Error in flow executor:', error);
    throw error;
  }
}

/**
 * Enhanced error handling utilities for Instagram service
 */

interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 30000, // 30 seconds
  backoffMultiplier: 2
};

/**
 * Determine if an error is retryable
 */
function isRetryableError(error: any): boolean {
  if (axios.isAxiosError(error)) {
    const status = error.response?.status;

    // Retry on server errors (5xx) and rate limiting (429)
    if (status && (status >= 500 || status === 429)) {
      return true;
    }

    // Retry on network errors
    if (error.code === 'ECONNRESET' || error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return true;
    }

    // Retry on timeout errors
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      return true;
    }
  }

  return false;
}

/**
 * Calculate delay for exponential backoff with jitter
 */
function calculateRetryDelay(attempt: number, config: RetryConfig): number {
  const exponentialDelay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt);
  const jitter = Math.random() * 0.1 * exponentialDelay; // Add 10% jitter
  return Math.min(exponentialDelay + jitter, config.maxDelay);
}

/**
 * Execute a function with retry logic and exponential backoff
 */
async function executeWithRetry<T>(
  operation: () => Promise<T>,
  operationName: string,
  connectionId?: number,
  config: RetryConfig = DEFAULT_RETRY_CONFIG
): Promise<T> {
  let lastError: any;

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      const result = await operation();

      // Update connection health on success
      if (connectionId && attempt > 0) {
        updateConnectionActivity(connectionId, true);
        logger.info('instagram', `${operationName} succeeded after ${attempt} retries`);
      }

      return result;
    } catch (error) {
      lastError = error;

      // Update connection health on failure
      if (connectionId) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        updateConnectionActivity(connectionId, false, errorMessage);
      }

      // Don't retry if this is the last attempt or error is not retryable
      if (attempt === config.maxRetries || !isRetryableError(error)) {
        break;
      }

      const delay = calculateRetryDelay(attempt, config);
      logger.warn('instagram', `${operationName} failed (attempt ${attempt + 1}/${config.maxRetries + 1}), retrying in ${delay}ms:`, error instanceof Error ? error.message : error);

      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  // All retries exhausted
  logger.error('instagram', `${operationName} failed after ${config.maxRetries + 1} attempts:`, lastError);
  throw lastError;
}

/**
 * Get connection health status
 */
export function getConnectionHealth(connectionId: number): {
  isActive: boolean;
  lastActivity: Date;
  errorCount: number;
  lastError: string | null;
  healthScore: number;
} {
  const state = getConnectionState(connectionId);
  const isActive = activeConnections.has(connectionId);


  let healthScore = 100;
  if (state.errorCount > 0) {
    healthScore = Math.max(0, 100 - (state.errorCount * 10));
  }

  const timeSinceActivity = Date.now() - state.lastActivity.getTime();
  if (timeSinceActivity > 300000) { // 5 minutes
    healthScore = Math.max(0, healthScore - 20);
  }

  return {
    isActive,
    lastActivity: state.lastActivity,
    errorCount: state.errorCount,
    lastError: state.lastError,
    healthScore
  };
}

const INSTAGRAM_API_VERSION = 'v22.0';
const INSTAGRAM_GRAPH_URL = 'https://graph.facebook.com';


export function verifyWebhookSignature(payload: string, signature: string, appSecret: string): boolean {
  if (!signature) {
    console.error('Error verifying Instagram webhook signature: No signature provided.');
    return false;
  }
  try {
    const hmac = crypto.createHmac('sha256', appSecret);
    hmac.update(payload, 'utf8');
    const expectedSignature = hmac.digest('hex');

    const providedSignature = signature.startsWith('sha256=')
      ? signature.substring('sha256='.length)
      : signature;

    if (expectedSignature.length !== providedSignature.length) {
        console.error('Error verifying Instagram webhook signature: Signature length mismatch.');
        return false;
    }

    return crypto.timingSafeEqual(
      Buffer.from(expectedSignature, 'hex'),
      Buffer.from(providedSignature, 'hex')
    );
  } catch (error) {
    console.error('Error verifying Instagram webhook signature:', error);
    return false;
  }
}

export async function connectToInstagram(connectionId: number, userId: number, companyId?: number): Promise<void> {
  let currentConnection: ChannelConnection | null = null;
  try {
    logger.info('instagram', `Connecting to Instagram for connection ${connectionId} by user ${userId} (company: ${companyId})`);

    const connectionResult = await storage.getChannelConnection(connectionId);
    if (!connectionResult) {
      throw new Error(`Connection with ID ${connectionId} not found`);
    }
    currentConnection = connectionResult as ChannelConnection;

    // Multi-tenant security: Verify company scoping if provided
    if (companyId && currentConnection.companyId && currentConnection.companyId !== companyId) {
      logger.error('instagram', `Company ID mismatch: Connection ${connectionId} belongs to company ${currentConnection.companyId}, but user is from company ${companyId}`);
      throw new Error(`Access denied: Connection does not belong to company ${companyId}`);
    }

    if (currentConnection.userId !== userId) {
      logger.error('instagram', `Unauthorized access attempt to connection ${connectionId} by user ${userId}`);
      throw new Error('Unauthorized access to channel connection');
    }


    const accessToken = currentConnection.accessToken;
    const instagramAccountId = (currentConnection.connectionData as InstagramConnectionData)?.instagramAccountId;

    if (!accessToken) {
      throw new Error('Instagram access token is missing for this connection.');
    }
    if (!instagramAccountId) {
      throw new Error('Instagram account ID is missing from connectionData.');
    }


    const connectionData = currentConnection.connectionData as InstagramConnectionData;
    const validationResult = await validateConnectionConfiguration(connectionData, accessToken);
    if (!validationResult.success) {
      await storage.updateChannelConnectionStatus(connectionId, 'error');
      updateConnectionActivity(connectionId, false, validationResult.error);

      eventEmitter.emit('connectionError', {
        connectionId,
        error: validationResult.error
      });

      throw new Error(`Connection validation failed: ${validationResult.error}`);
    }


    await storage.updateChannelConnectionStatus(connectionId, 'connected');

    const updatedConnectionData: InstagramConnectionData = {
      ...(connectionData || {}),
      accountInfo: validationResult.accountInfo,
      lastConnectedAt: new Date().toISOString(),
      lastValidatedAt: new Date().toISOString()
    };

    await storage.updateChannelConnection(connectionId, {
      connectionData: updatedConnectionData as Record<string, any>,
    });

    activeConnections.set(connectionId, true);
    updateConnectionActivity(connectionId, true);

    logger.info('instagram', `Connection ${connectionId} established successfully for account: ${validationResult.accountInfo?.username}`);

    eventEmitter.emit('connectionStatusUpdate', {
      connectionId,
      status: 'connected',
      accountInfo: validationResult.accountInfo
    } as ConnectionStatusUpdatePayload);
  } catch (error: unknown) {
    const baseMessage = `Error connecting to Instagram (ID: ${connectionId}):`;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('instagram', baseMessage, errorMessage);

    if (connectionId && (currentConnection || await storage.getChannelConnection(connectionId))) {
        await storage.updateChannelConnectionStatus(connectionId, 'error');
        updateConnectionActivity(connectionId, false, errorMessage);
    }
    if (error instanceof Error) throw error;
    throw new Error(`${baseMessage} ${errorMessage}`);
  }
}

export async function disconnectFromInstagram(connectionId: number, userId: number): Promise<boolean> {
  try {
    const connection = await storage.getChannelConnection(connectionId) as ChannelConnection | null;
    if (!connection) {
      logger.warn('instagram', `Connection ${connectionId} not found for disconnection`);
      return false;
    }


    if (connection.userId !== userId) {
      logger.error('instagram', `Unauthorized disconnect attempt to connection ${connectionId} by user ${userId}`);
      throw new Error('Unauthorized access to channel connection');
    }

    activeConnections.delete(connectionId);
    updateConnectionActivity(connectionId, true);
    await storage.updateChannelConnectionStatus(connectionId, 'disconnected');

    logger.info('instagram', `Instagram connection ${connectionId} disconnected successfully`);

    eventEmitter.emit('connectionStatusUpdate', {
      connectionId,
      status: 'disconnected'
    } as ConnectionStatusUpdatePayload);

    return true;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('instagram', `Error disconnecting from Instagram (ID: ${connectionId}):`, errorMessage);
    return false;
  }
}

export function isInstagramConnectionActive(connectionId: number): boolean {
  return activeConnections.has(connectionId);
}

export function getActiveInstagramConnections(): number[] {
  return Array.from(activeConnections.keys());
}

function getAxiosErrorDetails(error: AxiosError): string {
    const fbError = (error.response?.data as any)?.error;
    if (fbError) {
        return `Type: ${fbError.type}, Code: ${fbError.code}, Message: ${fbError.message}, User Title: ${fbError.error_user_title}, User Msg: ${fbError.error_user_msg}, Trace ID: ${fbError.fbtrace_id}`;
    }
    return error.message;
}


/**
 * Enhanced send message function that integrates with the inbox system
 * @param connectionId The channel connection ID
 * @param userId The user ID sending the message
 * @param companyId The company ID for multi-tenant security
 * @param to The recipient Instagram user ID (IGSID)
 * @param message The message content
 * @returns The saved message object
 */
export async function sendMessage(connectionId: number, userId: number, companyId: number, to: string, message: string) {
  try {
    if (!companyId) {
      throw new Error('Company ID is required for multi-tenant security');
    }

    const connection = await storage.getChannelConnection(connectionId) as ChannelConnection | null;
    if (!connection) {
      throw new Error(`Connection with ID ${connectionId} not found`);
    }

    // Multi-tenant security: Verify company scoping
    if (connection.companyId !== companyId) {
      throw new Error(`Access denied: Connection does not belong to company ${companyId}`);
    }

    const accessToken = connection.accessToken;
    const instagramAccountId = (connection.connectionData as InstagramConnectionData)?.instagramAccountId;

    if (!accessToken) {
      throw new Error('Instagram access token is missing');
    }

    if (!instagramAccountId) {
      throw new Error('Instagram account ID is missing');
    }

    // Send via Instagram API
    const response = await axios.post<InstagramMessageSendResponse>(
      `${INSTAGRAM_GRAPH_URL}/${INSTAGRAM_API_VERSION}/${instagramAccountId}/messages`,
      { recipient: { id: to }, message: { text: message } },
      {
        headers: { 'Authorization': `Bearer ${accessToken}`, 'Content-Type': 'application/json' },
        timeout: 10000
      }
    );

    if (response.status === 200 && response.data?.message_id) {
      const messageId = response.data.message_id;

      // Find or create conversation
      const conversation = await findOrCreateConversation(connectionId, to, companyId);

      // Save message to database
      const savedMessage = await storage.createMessage({
        conversationId: conversation.id,
        direction: 'outbound',
        type: 'text',
        content: message,
        senderId: userId,
        senderType: 'user',
        isFromBot: false,
        externalId: messageId || `instagram-${Date.now()}`,
        metadata: JSON.stringify({
          instagram_message_id: messageId,
          timestamp: new Date().toISOString(),
          instagram_account_id: instagramAccountId,
          recipient_id: to
        })
      });

      // Update conversation
      await storage.updateConversation(conversation.id, {
        lastMessageAt: new Date()
      });

      // Get the full conversation data
      const fullConversation = await storage.getConversation(conversation.id);

      // Emit event for real-time updates
      eventEmitter.emit('messageSent', {
        message: savedMessage,
        conversation: fullConversation
      });

      return savedMessage;
    } else {
      throw new Error('Failed to send message: Unknown error');
    }
  } catch (error: any) {
    logger.error('instagram', 'Error sending Instagram message:', error.response?.data || error.message);
    throw new Error(error.response?.data?.error?.message || error.message);
  }
}

/**
 * Send a text message via Instagram (legacy function for backward compatibility)
 * @param connectionId The ID of the channel connection
 * @param to The recipient Instagram user ID (IGSID)
 * @param message The message text to send
 * @param userId Optional user ID for authorization
 * @returns Promise with send result
 */
export async function sendInstagramMessage(
  connectionId: number,
  to: string,
  message: string,
  userId?: number
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    return await executeWithRetry(
      async () => {
        const connection = await storage.getChannelConnection(connectionId) as ChannelConnection | null;
        if (!connection) {
          throw new Error(`Connection with ID ${connectionId} not found`);
        }

        if (userId && connection.userId !== userId) {
          throw new Error('Unauthorized access to channel connection');
        }

        const accessToken = connection.accessToken;
        const instagramAccountId = (connection.connectionData as InstagramConnectionData)?.instagramAccountId;

        if (!accessToken) {
          throw new Error('Instagram access token is missing.');
        }
        if (!instagramAccountId) {
          throw new Error('Instagram account ID is missing from connectionData.');
        }

        const response = await axios.post<InstagramMessageSendResponse>(
          `${INSTAGRAM_GRAPH_URL}/${INSTAGRAM_API_VERSION}/${instagramAccountId}/messages`,
          { recipient: { id: to }, message: { text: message } },
          {
            headers: { 'Authorization': `Bearer ${accessToken}`, 'Content-Type': 'application/json' },
            timeout: 10000 // 10 second timeout
          }
        );

        if (response.status === 200 && response.data?.message_id) {
          return { success: true, messageId: response.data.message_id };
        } else {
          throw new Error(`Failed to send message: Status ${response.status}, Data: ${JSON.stringify(response.data)}`);
        }
      },
      'Instagram message sending',
      connectionId
    );
  } catch (error: unknown) {
    let errorMessage = 'Failed to send Instagram message.';
    if (axios.isAxiosError(error)) {
      errorMessage = getAxiosErrorDetails(error as AxiosError);
      logger.error('instagram', 'Axios error sending Instagram message:', errorMessage);
    } else if (error instanceof Error) {
      errorMessage = error.message;
      logger.error('instagram', 'Error sending Instagram message:', error.message);
    } else {
      logger.error('instagram', 'Unknown error sending Instagram message:', error);
    }
    return { success: false, error: errorMessage };
  }
}

// Note: sendMessage function is now the enhanced version above that integrates with unified inbox

/**
 * Upload media to Instagram for messaging
 */
export async function uploadInstagramMedia(
  connectionId: number,
  mediaUrl: string,
  mediaType: 'image' | 'video'
): Promise<{ success: boolean; mediaId?: string; error?: string }> {
  try {
    const connection = await storage.getChannelConnection(connectionId) as ChannelConnection | null;
    if (!connection) {
      return { success: false, error: `Connection with ID ${connectionId} not found` };
    }

    const accessToken = connection.accessToken;
    const instagramAccountId = (connection.connectionData as InstagramConnectionData)?.instagramAccountId;

    if (!accessToken || !instagramAccountId) {
      return { success: false, error: 'Instagram credentials missing' };
    }

    // Upload media to Instagram
    const uploadResponse = await axios.post(
      `${INSTAGRAM_GRAPH_URL}/${INSTAGRAM_API_VERSION}/${instagramAccountId}/media`,
      {
        image_url: mediaType === 'image' ? mediaUrl : undefined,
        video_url: mediaType === 'video' ? mediaUrl : undefined,
        media_type: mediaType.toUpperCase()
      },
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    );

    if (uploadResponse.status === 200 && uploadResponse.data?.id) {
      return {
        success: true,
        mediaId: uploadResponse.data.id
      };
    } else {
      return {
        success: false,
        error: 'Failed to upload media to Instagram'
      };
    }
  } catch (error: any) {
    logger.error('instagram', 'Error uploading media:', error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message || 'Media upload failed'
    };
  }
}

export async function sendInstagramMediaMessage(
  connectionId: number,
  to: string,
  mediaUrl: string,
  mediaType: 'image' | 'video',
  caption?: string,
  userId?: number
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    const connection = await storage.getChannelConnection(connectionId) as ChannelConnection | null;
    if (!connection) {
      return { success: false, error: `Connection with ID ${connectionId} not found` };
    }


    if (userId && connection.userId !== userId) {
      return { success: false, error: 'Unauthorized access to channel connection' };
    }

    const accessToken = connection.accessToken;
    const instagramAccountId = (connection.connectionData as InstagramConnectionData)?.instagramAccountId;

    if (!accessToken) {
      return { success: false, error: 'Instagram access token is missing.' };
    }
    if (!instagramAccountId) {
      return { success: false, error: 'Instagram account ID is missing from connectionData.' };
    }

    return await executeWithRetry(
      async () => {
        const mediaRequest: InstagramMediaMessageRequestBody = {
          recipient: { id: to },
          message: {
            attachment: { type: mediaType, payload: { url: mediaUrl, is_reusable: true } }
          }
        };
        if (caption) {
          mediaRequest.message.text = caption;
        }

        const response = await axios.post<InstagramMessageSendResponse>(
          `${INSTAGRAM_GRAPH_URL}/${INSTAGRAM_API_VERSION}/${instagramAccountId}/messages`,
          mediaRequest,
          {
            headers: { 'Authorization': `Bearer ${accessToken}`, 'Content-Type': 'application/json' },
            timeout: 15000 // 15 second timeout for media
          }
        );

        if (response.status === 200 && response.data?.message_id) {
          return { success: true, messageId: response.data.message_id };
        } else {
          throw new Error(`Failed to send media message: Status ${response.status}, Data: ${JSON.stringify(response.data)}`);
        }
      },
      'Instagram media message sending',
      connectionId
    );
  } catch (error: unknown) {
    let errorMessage = 'Failed to send Instagram media message.';
    if (axios.isAxiosError(error)) {
      errorMessage = getAxiosErrorDetails(error as AxiosError);
      logger.error('instagram', 'Axios error sending Instagram media message:', errorMessage);
    } else if (error instanceof Error) {
      errorMessage = error.message;
      logger.error('instagram', 'Error sending Instagram media message:', error.message);
    } else {
      logger.error('instagram', 'Unknown error sending Instagram media message:', error);
    }
    return { success: false, error: errorMessage };
  }
}

/**
 * Alias for sendInstagramMediaMessage to match API message service interface
 */
export const sendMedia = sendInstagramMediaMessage;

/**
 * Verify webhook signature against any configured Instagram connection
 * @param payload The raw payload from the webhook
 * @param signature The signature from the X-Hub-Signature-256 header
 * @returns True if signature is valid for any connection
 */
async function verifyWebhookSignatureForAnyConnection(payload: string, signature: string): Promise<boolean> {
  try {
    const connections = await storage.getChannelConnections(null) as ChannelConnection[];
    const instagramConnections = connections.filter(conn => conn.channelType === 'instagram');

    for (const connection of instagramConnections) {
      const connectionData = connection.connectionData as InstagramConnectionData;
      if (connectionData?.appSecret) {
        const isValid = verifyWebhookSignature(payload, signature, connectionData.appSecret);
        if (isValid) {
          logger.info('instagram', `Webhook signature verified for connection ${connection.id}`);
          return true;
        }
      }
    }

    logger.warn('instagram', 'Webhook signature could not be verified against any connection');
    return false;
  } catch (error) {
    logger.error('instagram', 'Error verifying webhook signature:', error);
    return false;
  }
}

export async function processWebhook(body: InstagramWebhookBody, signature?: string, companyId?: number): Promise<void> {
  try {
    logger.info('instagram', 'Processing Instagram webhook:', {
      hasSignature: !!signature,
      bodyType: typeof body,
      companyId: companyId || 'not_specified'
    });


    if (body['hub.mode'] === 'subscribe' && body['hub.verify_token']) {
      logger.info('instagram', 'Webhook verification request received');
      return;
    }


    if (signature && typeof body === 'string') {
      const isValidSignature = await verifyWebhookSignatureForAnyConnection(body, signature);
      if (!isValidSignature) {
        logger.error('instagram', 'Invalid webhook signature - rejecting request');
        throw new Error('Invalid webhook signature');
      }
    }

    if (body.entry && Array.isArray(body.entry)) {
      for (const entry of body.entry) {
        if (entry.messaging && Array.isArray(entry.messaging)) {
          for (const messagingEvent of entry.messaging) {
            await handleIncomingInstagramMessage(messagingEvent, entry.id, companyId);
          }
        } else if (entry.messaging) {
            logger.debug('instagram', 'Non-array messaging event received');
        }
      }
    } else {
        logger.debug('instagram', 'No entry array found in webhook body');
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('instagram', 'Error processing Instagram webhook:', errorMessage);
    throw error; // Re-throw to ensure proper HTTP error response
  }
}

function captionForAttachment(attachment: InstagramWebhookMessageAttachment): string {
    if (attachment.type === 'story_mention') return '[Mentioned you in a story]';
    if (attachment.type === 'image') return '[Image]';
    if (attachment.type === 'video') return '[Video]';
    if (attachment.type === 'audio') return '[Audio Message]';
    if (attachment.type === 'share' && attachment.title) return `[Shared Post: ${attachment.title}]`;
    if (attachment.type === 'fallback') return '[Unsupported Attachment]';
    return `[${attachment.type.toUpperCase()}]`;
}

/**
 * Find or create a conversation for a contact and channel
 * @param connectionId The channel connection ID
 * @param recipientId The recipient's Instagram user ID (IGSID)
 * @param companyId The company ID for multi-tenant security
 * @returns The conversation object
 */
async function findOrCreateConversation(connectionId: number, recipientId: string, companyId: number) {
  if (!companyId) {
    throw new Error('Company ID is required for multi-tenant security');
  }

  // Find or create contact
  let contact = await storage.getContactByPhone(recipientId, companyId);

  if (!contact) {
    const contactData: InsertContact = {
      companyId: companyId,
      name: `Instagram User ${recipientId.substring(0, 6)}...`,
      phone: recipientId,
      email: null,
      avatarUrl: null,
      identifier: recipientId,
      identifierType: 'instagram',
      source: 'instagram',
      notes: null
    };

    contact = await storage.createContact(contactData);
  }

  // Find or create conversation
  let conversation = await storage.getConversationByContactAndChannel(
    contact.id,
    connectionId
  );

  if (!conversation) {
    const conversationData: InsertConversation = {
      companyId: companyId,
      contactId: contact.id,
      channelId: connectionId,
      channelType: 'instagram',
      status: 'open',
      assignedToUserId: null,
      lastMessageAt: new Date(),
    };

    conversation = await storage.createConversation(conversationData);
  }

  return conversation;
}

async function handleIncomingInstagramMessage(messagingEvent: InstagramWebhookMessagingEvent, recipientIgAccountId: string, companyId?: number): Promise<void> {
  let connection: ChannelConnection | null = null;

  try {
    logger.debug('instagram', 'Processing incoming Instagram message event');

    const senderIgSid = messagingEvent.sender?.id;
    const message = messagingEvent.message;

    if (messagingEvent.reaction) {
        logger.debug('instagram', 'Skipping reaction event');
        return;
    }

    if (!senderIgSid || !message || !message.mid) {
      logger.warn('instagram', 'Missing required message data in event');
      return;
    }
    if (message.is_echo) {
        logger.debug('instagram', 'Skipping echo message');
        return;
    }

    // Use company-scoped connection lookup for better security
    const connections = await storage.getChannelConnections(null) as ChannelConnection[];
    connection = connections.find(conn => {
      const connectionData = conn.connectionData as InstagramConnectionData;
      return conn.channelType === 'instagram' && connectionData?.instagramAccountId === recipientIgAccountId;
    }) || null;

    if (!connection) {
      logger.warn('instagram', `No Instagram connection found for account ID: ${recipientIgAccountId}`);
      return;
    }

    // Multi-tenant security: Verify company scoping if provided
    if (companyId && connection.companyId !== companyId) {
      logger.warn('instagram', `Company ID mismatch for connection ${connection.id}: expected ${companyId}, got ${connection.companyId}`);
      return;
    }

    if (!connection.companyId) {
      logger.error('instagram', `Connection ${connection.id} missing companyId - security violation`);
      return;
    }

    let contact = await storage.getContactByPhone(senderIgSid, connection.companyId) as Contact | null;
    if (!contact) {
      const insertContactData: InsertContact = {
        companyId: connection.companyId,
        phone: senderIgSid,
        name: `Instagram User ${senderIgSid.substring(0, 6)}...`,
        source: 'instagram',
        identifier: senderIgSid,
        identifierType: 'instagram'
      };
      contact = await storage.createContact(insertContactData);
      logger.info('instagram', `Created new contact for Instagram user ${senderIgSid}`);
    }

    let conversation = await storage.getConversationByContactAndChannel(
      contact.id,
      connection.id
    ) as Conversation | null;

    const messageTimestamp = new Date(messagingEvent.timestamp || message.timestamp || Date.now());

    if (!conversation) {
      const insertConversationData: InsertConversation = {
        companyId: connection.companyId,
        contactId: contact.id,
        channelType: 'instagram',
        channelId: connection.id,
        status: 'open',
        lastMessageAt: messageTimestamp
      };
      conversation = await storage.createConversation(insertConversationData);
      
    }

    let messageText: string;
    let messageType = 'text';
    let mediaUrl: string | null = null;

    if (message.text) {
      messageText = message.text;
    } else if (message.attachments && message.attachments.length > 0) {
      const attachment = message.attachments[0];
      messageType = attachment.type || 'media';
      mediaUrl = attachment.payload?.url || attachment.url || null;
      messageText = captionForAttachment(attachment);
    } else {
      
      messageText = '[Unsupported or Empty Message Content]';
    }

    const insertMessageData: InsertMessage = {
      conversationId: conversation.id,
      content: messageText,
      type: messageType,
      direction: 'inbound',
      status: 'delivered',
      externalId: message.mid,
      mediaUrl: mediaUrl,
      metadata: {
        channelType: 'instagram',
        timestamp: messageTimestamp.getTime(),
        senderId: senderIgSid,
        recipientId: recipientIgAccountId
      }
    };

    const savedMessage = await storage.createMessage(insertMessageData);
    updateConnectionActivity(connection.id, true);

    const updatedConversationDataForEvent = {
        ...conversation,
        lastMessageAt: messageTimestamp,
        status: 'open' as const
    };
    await storage.updateConversation(conversation.id, {
      lastMessageAt: messageTimestamp,
      status: 'open'
    });

    logger.info('instagram', `Message received from ${senderIgSid} via connection ${connection.id}`);

    const eventPayload: MessageReceivedPayload = {
      message: savedMessage,
      conversation: updatedConversationDataForEvent as Conversation,
      contact: contact,
      connection: connection
    };
    eventEmitter.emit('messageReceived', eventPayload);


    if ((global as any).broadcastToCompany) {
      (global as any).broadcastToCompany({
        type: 'newMessage',
        data: savedMessage
      }, connection.companyId);

      (global as any).broadcastToCompany({
        type: 'conversationUpdated',
        data: eventPayload.conversation
      }, connection.companyId);
    }


    try {
      if (connection.companyId && !conversation.botDisabled) {
        logger.debug('instagram', `Message eligible for flow processing: conversation ${conversation.id}`);

        // Process message through flow executor
        await processMessageThroughFlowExecutor(savedMessage, updatedConversationDataForEvent, contact, connection);
      }
    } catch (flowError: any) {
      logger.error('instagram', `Error processing message through flows:`, flowError.message);
    }

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('instagram', `Error handling incoming Instagram message:`, errorMessage);
    if (connection?.id) {
      updateConnectionActivity(connection.id, false, errorMessage);
    }
  }
}

export function subscribeToInstagramEvents(
  eventType: 'connectionStatusUpdate',
  callback: (data: ConnectionStatusUpdatePayload) => void
): () => void;
export function subscribeToInstagramEvents(
  eventType: 'connectionError',
  callback: (data: ConnectionErrorPayload) => void
): () => void;
export function subscribeToInstagramEvents(
  eventType: 'messageReceived',
  callback: (data: MessageReceivedPayload) => void
): () => void;
export function subscribeToInstagramEvents(
  eventType: 'messageSent',
  callback: (data: any) => void
): () => void;
export function subscribeToInstagramEvents(
  eventType: string,
  callback: (data: any) => void
): () => void {
  eventEmitter.on(eventType, callback);
  return () => eventEmitter.off(eventType, callback);
}


/**
 * Test webhook configuration
 * @param webhookUrl The webhook URL to test
 * @param verifyToken The verify token to test
 * @returns Promise<boolean> indicating success
 */
export async function testWebhookConfiguration(
  webhookUrl: string,
  verifyToken: string
): Promise<{ success: boolean; error?: string }> {
  try {

    const url = new URL(webhookUrl);
    if (url.protocol !== 'https:') {
      return { success: false, error: 'Webhook URL must use HTTPS' };
    }

    if (!url.pathname.includes('/api/webhooks/instagram')) {
      return { success: false, error: 'Webhook URL must point to /api/webhooks/instagram endpoint' };
    }


    const testParams = new URLSearchParams({
      'hub.mode': 'subscribe',
      'hub.verify_token': verifyToken,
      'hub.challenge': 'test_challenge_' + Date.now()
    });

    const testResponse = await axios.get(`${webhookUrl}?${testParams.toString()}`, {
      timeout: 10000,
      validateStatus: (status) => status === 200 || status === 403
    });

    if (testResponse.status === 200) {
      return { success: true };
    } else {
      return { success: false, error: 'Webhook verification failed - check verify token configuration' };
    }
  } catch (error: any) {
    logger.error('instagram', 'Error testing webhook configuration:', error.message);
    return {
      success: false,
      error: error.code === 'ECONNREFUSED'
        ? 'Could not connect to webhook URL - check if server is accessible'
        : error.message || 'Webhook test failed'
    };
  }
}

/**
 * Refresh Instagram access token
 */
export async function refreshInstagramAccessToken(
  connectionId: number
): Promise<{ success: boolean; newToken?: string; expiresAt?: string; error?: string }> {
  try {
    const connection = await storage.getChannelConnection(connectionId) as ChannelConnection | null;
    if (!connection) {
      return { success: false, error: 'Connection not found' };
    }

    const connectionData = connection.connectionData as InstagramConnectionData;
    const appId = connectionData?.appId;
    const appSecret = connectionData?.appSecret;
    const currentToken = connection.accessToken;

    if (!appId || !appSecret || !currentToken) {
      return { success: false, error: 'Missing credentials for token refresh' };
    }

    // Refresh long-lived token
    const response = await axios.get(
      `${INSTAGRAM_GRAPH_URL}/${INSTAGRAM_API_VERSION}/oauth/access_token`,
      {
        params: {
          grant_type: 'fb_exchange_token',
          client_id: appId,
          client_secret: appSecret,
          fb_exchange_token: currentToken
        },
        timeout: 10000
      }
    );

    if (response.status === 200 && response.data?.access_token) {
      const newToken = response.data.access_token;
      const expiresIn = response.data.expires_in || 5184000; // Default 60 days
      const expiresAt = new Date(Date.now() + (expiresIn * 1000)).toISOString();

      // Update connection with new token
      const updatedConnectionData = {
        ...connectionData,
        accessTokenExpiresAt: expiresAt,
        lastTokenRefresh: new Date().toISOString()
      };

      await storage.updateChannelConnection(connectionId, {
        accessToken: newToken,
        connectionData: updatedConnectionData as Record<string, any>
      });

      logger.info('instagram', `Access token refreshed for connection ${connectionId}`);

      return {
        success: true,
        newToken,
        expiresAt
      };
    } else {
      return {
        success: false,
        error: 'Failed to refresh access token'
      };
    }
  } catch (error: any) {
    logger.error('instagram', 'Error refreshing access token:', error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message || 'Token refresh failed'
    };
  }
}

/**
 * Check if access token needs refresh (within 7 days of expiry)
 */
export async function checkTokenExpiry(connectionId: number): Promise<boolean> {
  try {
    const connection = await storage.getChannelConnection(connectionId) as ChannelConnection | null;
    if (!connection) return false;

    const connectionData = connection.connectionData as InstagramConnectionData;
    const expiresAt = connectionData?.accessTokenExpiresAt;

    if (!expiresAt) return false;

    const expiryDate = new Date(expiresAt);
    const sevenDaysFromNow = new Date(Date.now() + (7 * 24 * 60 * 60 * 1000));

    return expiryDate <= sevenDaysFromNow;
  } catch (error) {
    logger.error('instagram', 'Error checking token expiry:', error);
    return false;
  }
}

/**
 * Validate Instagram connection configuration
 * @param connectionData The connection configuration to validate
 * @returns Promise with validation result
 */
export async function validateConnectionConfiguration(
  connectionData: InstagramConnectionData,
  accessToken: string
): Promise<{ success: boolean; error?: string; accountInfo?: any }> {
  try {

    const response = await axios.get(
      `${INSTAGRAM_GRAPH_URL}/${INSTAGRAM_API_VERSION}/${connectionData.instagramAccountId}`,
      {
        params: {
          fields: 'id,username,name,profile_picture_url,followers_count'
        },
        headers: {
          'Authorization': `Bearer ${accessToken}`
        },
        timeout: 10000
      }
    );

    if (response.status === 200 && response.data) {
      return {
        success: true,
        accountInfo: response.data
      };
    } else {
      return {
        success: false,
        error: 'Failed to validate Instagram account access'
      };
    }
  } catch (error: any) {
    logger.error('instagram', 'Error validating Instagram connection:', error.response?.data || error.message);

    if (error.response?.status === 403) {
      return {
        success: false,
        error: 'Access denied - check Instagram account access token permissions'
      };
    } else if (error.response?.status === 404) {
      return {
        success: false,
        error: 'Instagram account not found - check account ID'
      };
    } else {
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message || 'Connection validation failed'
      };
    }
  }
}

export async function setupWebhookSubscription(
  connectionId: number,
  callbackUrl: string,
  verifyToken: string
): Promise<boolean> {
  try {
    const connection = await storage.getChannelConnection(connectionId) as ChannelConnection | null;
    if (!connection) {
      throw new Error(`Connection with ID ${connectionId} not found for webhook setup.`);
    }

    const pageAccessToken = connection.accessToken;
    const facebookAppId = (connection.connectionData as InstagramConnectionData)?.appId;

    if (!pageAccessToken) {
      throw new Error('Page Access Token is missing for webhook setup.');
    }
    if (!facebookAppId) {
      throw new Error('Facebook App ID is missing from connectionData for webhook setup.');
    }

    const fields = [
      'messages',
      'message_reactions',
    ].join(',');

    const response = await axios.post<InstagramSubscriptionResponse>(
      `${INSTAGRAM_GRAPH_URL}/${INSTAGRAM_API_VERSION}/${facebookAppId}/subscriptions`,
      {
        object: 'instagram',
        callback_url: callbackUrl,
        verify_token: verifyToken,
        fields: fields,
        access_token: pageAccessToken
      }
    );

    if (response.status === 200 && response.data?.success) {
        logger.info('instagram', `Webhook subscription set up successfully for connection ${connectionId}`);
        return true;
    } else {
        logger.error('instagram', `Failed to set up Instagram webhook subscription for App ${facebookAppId} (Conn: ${connectionId}):`, response.status, response.data);
        return false;
    }
  } catch (error: unknown) {
    let errorMessage = 'Error setting up Instagram webhook subscription';
     if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError<{error?: {message?: string, type?: string, code?: number, fbtrace_id?: string}}>;
        errorMessage = axiosError.response?.data?.error?.message || axiosError.message || errorMessage;
        logger.error('instagram', `Axios error setting up Instagram webhook subscription (Conn: ${connectionId}):`, errorMessage);
    } else if (error instanceof Error) {
        errorMessage = error.message;
        logger.error('instagram', `Error setting up Instagram webhook subscription (Conn: ${connectionId}):`, error.message);
    } else {
        logger.error('instagram', `Unknown error setting up Instagram webhook subscription (Conn: ${connectionId}):`, error);
    }
    return false;
  }
}

/**
 * Send Instagram message with quick replies
 */
export async function sendInstagramMessageWithQuickReplies(
  connectionId: number,
  to: string,
  message: string,
  quickReplies: InstagramQuickReply[],
  userId?: number
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    const connection = await storage.getChannelConnection(connectionId) as ChannelConnection | null;
    if (!connection) {
      return { success: false, error: `Connection with ID ${connectionId} not found` };
    }

    if (userId && connection.userId !== userId) {
      return { success: false, error: 'Unauthorized access to channel connection' };
    }

    const accessToken = connection.accessToken;
    const instagramAccountId = (connection.connectionData as InstagramConnectionData)?.instagramAccountId;

    if (!accessToken || !instagramAccountId) {
      return { success: false, error: 'Instagram credentials missing' };
    }

    const messageData = {
      recipient: { id: to },
      message: {
        text: message,
        quick_replies: quickReplies.slice(0, 13) // Instagram allows max 13 quick replies
      }
    };

    const response = await axios.post(
      `${INSTAGRAM_GRAPH_URL}/${INSTAGRAM_API_VERSION}/${instagramAccountId}/messages`,
      messageData,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      }
    );

    if (response.status === 200 && response.data?.message_id) {
      return {
        success: true,
        messageId: response.data.message_id
      };
    } else {
      return {
        success: false,
        error: 'Failed to send Instagram message with quick replies'
      };
    }
  } catch (error: any) {
    logger.error('instagram', 'Error sending message with quick replies:', error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data?.error?.message || error.message || 'Failed to send message'
    };
  }
}

/**
 * Get Instagram message templates for a connection
 */
export async function getInstagramMessageTemplates(connectionId: number): Promise<InstagramMessageTemplate[]> {
  try {
    const connection = await storage.getChannelConnection(connectionId) as ChannelConnection | null;
    if (!connection) {
      return [];
    }

    const connectionData = connection.connectionData as InstagramConnectionData;
    return connectionData?.settings?.messageTemplates || [];
  } catch (error) {
    logger.error('instagram', 'Error getting message templates:', error);
    return [];
  }
}

/**
 * Save Instagram message template
 */
export async function saveInstagramMessageTemplate(
  connectionId: number,
  template: InstagramMessageTemplate
): Promise<{ success: boolean; error?: string }> {
  try {
    const connection = await storage.getChannelConnection(connectionId) as ChannelConnection | null;
    if (!connection) {
      return { success: false, error: 'Connection not found' };
    }

    const connectionData = connection.connectionData as InstagramConnectionData || {};
    const settings = connectionData.settings || {};
    const templates = settings.messageTemplates || [];

    // Add or update template
    const existingIndex = templates.findIndex(t => t.id === template.id);
    if (existingIndex >= 0) {
      templates[existingIndex] = template;
    } else {
      templates.push(template);
    }

    const updatedConnectionData = {
      ...connectionData,
      settings: {
        ...settings,
        messageTemplates: templates
      }
    };

    await storage.updateChannelConnection(connectionId, {
      connectionData: updatedConnectionData as Record<string, any>
    });

    return { success: true };
  } catch (error: any) {
    logger.error('instagram', 'Error saving message template:', error);
    return { success: false, error: error.message };
  }
}

export default {
  connect: connectToInstagram,
  disconnect: disconnectFromInstagram,
  sendMessage: sendInstagramMessage,
  sendMedia: sendInstagramMediaMessage,
  sendMessageWithQuickReplies: sendInstagramMessageWithQuickReplies,
  uploadMedia: uploadInstagramMedia,
  getMessageTemplates: getInstagramMessageTemplates,
  saveMessageTemplate: saveInstagramMessageTemplate,
  refreshAccessToken: refreshInstagramAccessToken,
  checkTokenExpiry: checkTokenExpiry,
  isActive: isInstagramConnectionActive,
  getActiveConnections: getActiveInstagramConnections,
  subscribeToEvents: subscribeToInstagramEvents,
  processWebhook: processWebhook,
  setupWebhook: setupWebhookSubscription,
  verifyWebhookSignature,
  testWebhookConfiguration,
  validateConnectionConfiguration,
  getConnectionHealth
};