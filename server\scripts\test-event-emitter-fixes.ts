#!/usr/bin/env ts-node

/**
 * Test script to verify EventEmitter memory leak fixes
 * This script simulates multiple WebSocket connections and monitors EventEmitter behavior
 */

import { EventEmitter } from 'events';
import { eventEmitterMonitor, createMonitoredEventEmitter } from '../utils/event-emitter-monitor';

// Simulate the services
const testEmitters = {
  whatsapp: createMonitoredEventEmitter('test-whatsapp', 50),
  email: createMonitoredEventEmitter('test-email', 50),
  instagram: createMonitoredEventEmitter('test-instagram', 50),
  messenger: createMonitoredEventEmitter('test-messenger', 50),
  telegram: createMonitoredEventEmitter('test-telegram', 50)
};

interface TestConnection {
  id: string;
  unsubscribeFunctions: (() => void)[];
}

const activeConnections = new Map<string, TestConnection>();

/**
 * Simulate creating a WebSocket connection with event listeners
 */
function simulateWebSocketConnection(connectionId: string): TestConnection {
  const unsubscribeFunctions: (() => void)[] = [];

  // Simulate subscribing to events from all services
  Object.entries(testEmitters).forEach(([serviceName, emitter]) => {
    // Subscribe to messageReceived
    const unsubscribeMessageReceived = () => {
      emitter.removeListener('messageReceived', messageReceivedHandler);
    };
    const messageReceivedHandler = (data: any) => {
      console.log(`[${connectionId}] ${serviceName} messageReceived:`, data);
    };
    emitter.on('messageReceived', messageReceivedHandler);
    unsubscribeFunctions.push(unsubscribeMessageReceived);

    // Subscribe to connectionStatusUpdate
    const unsubscribeConnectionStatus = () => {
      emitter.removeListener('connectionStatusUpdate', connectionStatusHandler);
    };
    const connectionStatusHandler = (data: any) => {
      console.log(`[${connectionId}] ${serviceName} connectionStatusUpdate:`, data);
    };
    emitter.on('connectionStatusUpdate', connectionStatusHandler);
    unsubscribeFunctions.push(unsubscribeConnectionStatus);

    // Subscribe to connectionError
    const unsubscribeConnectionError = () => {
      emitter.removeListener('connectionError', connectionErrorHandler);
    };
    const connectionErrorHandler = (data: any) => {
      console.log(`[${connectionId}] ${serviceName} connectionError:`, data);
    };
    emitter.on('connectionError', connectionErrorHandler);
    unsubscribeFunctions.push(unsubscribeConnectionError);
  });

  const connection: TestConnection = {
    id: connectionId,
    unsubscribeFunctions
  };

  activeConnections.set(connectionId, connection);
  return connection;
}

/**
 * Simulate cleaning up a WebSocket connection
 */
function simulateWebSocketCleanup(connectionId: string): boolean {
  const connection = activeConnections.get(connectionId);
  if (!connection) {
    return false;
  }

  // Clean up all event listeners
  connection.unsubscribeFunctions.forEach(unsubscribe => {
    try {
      unsubscribe();
    } catch (error) {
      console.error(`Error cleaning up listener for ${connectionId}:`, error);
    }
  });

  activeConnections.delete(connectionId);
  console.log(`✅ Cleaned up connection: ${connectionId}`);
  return true;
}

/**
 * Emit test events to simulate real activity
 */
function emitTestEvents(): void {
  Object.entries(testEmitters).forEach(([serviceName, emitter]) => {
    emitter.emit('messageReceived', { service: serviceName, message: 'Test message', timestamp: Date.now() });
    emitter.emit('connectionStatusUpdate', { service: serviceName, status: 'connected' });
  });
}

/**
 * Run the memory leak test
 */
async function runMemoryLeakTest(): Promise<void> {
  console.log('🧪 Starting EventEmitter Memory Leak Test\n');

  // Start monitoring
  eventEmitterMonitor.startMonitoring();

  console.log('📊 Initial status:');
  eventEmitterMonitor.logDetailedStatus();

  // Test 1: Create multiple connections without cleanup (should show warnings)
  console.log('\n🔥 Test 1: Creating 15 connections without cleanup (should trigger warnings)');
  for (let i = 1; i <= 15; i++) {
    simulateWebSocketConnection(`test-conn-${i}`);
    if (i % 5 === 0) {
      console.log(`Created ${i} connections...`);
      eventEmitterMonitor.checkForLeaks();
    }
  }

  console.log('\n📊 Status after creating 15 connections:');
  eventEmitterMonitor.logDetailedStatus();

  // Emit some events
  console.log('\n📡 Emitting test events...');
  emitTestEvents();

  // Test 2: Clean up connections properly
  console.log('\n🧹 Test 2: Cleaning up connections properly');
  for (let i = 1; i <= 15; i++) {
    simulateWebSocketCleanup(`test-conn-${i}`);
  }

  console.log('\n📊 Status after cleanup:');
  eventEmitterMonitor.logDetailedStatus();

  // Test 3: Create and cleanup connections in a loop (normal operation)
  console.log('\n🔄 Test 3: Simulating normal connection lifecycle (10 cycles)');
  for (let cycle = 1; cycle <= 10; cycle++) {
    // Create 3 connections
    const connectionIds = [];
    for (let i = 1; i <= 3; i++) {
      const connId = `cycle-${cycle}-conn-${i}`;
      simulateWebSocketConnection(connId);
      connectionIds.push(connId);
    }

    // Emit some events
    emitTestEvents();

    // Clean up connections
    connectionIds.forEach(connId => {
      simulateWebSocketCleanup(connId);
    });

    if (cycle % 3 === 0) {
      console.log(`Completed ${cycle} cycles...`);
      eventEmitterMonitor.checkForLeaks();
    }
  }

  console.log('\n📊 Final status:');
  eventEmitterMonitor.logDetailedStatus();

  // Test 4: Get API summary
  console.log('\n📋 API Summary:');
  const summary = eventEmitterMonitor.getSummary();
  console.log(JSON.stringify(summary, null, 2));

  // Stop monitoring
  eventEmitterMonitor.stopMonitoring();

  console.log('\n✅ Memory leak test completed!');
  console.log('\n📝 Summary:');
  console.log('- EventEmitters should have maxListeners set to 50');
  console.log('- Warnings should appear when approaching listener limits');
  console.log('- Proper cleanup should remove all listeners');
  console.log('- Normal operation should maintain stable listener counts');
}

// Run the test if this script is executed directly
if (require.main === module) {
  runMemoryLeakTest().catch(console.error);
}

export { runMemoryLeakTest, simulateWebSocketConnection, simulateWebSocketCleanup };
