/**
 * WhatsApp Group Chat Filter Utility
 * 
 * This utility provides functions to identify and filter out WhatsApp group chat messages
 * from the inbox system to prevent group chat data from being displayed in the chat list.
 */

/**
 * Checks if a phone number/identifier matches WhatsApp group chat ID pattern
 * 
 * WhatsApp group chat IDs typically:
 * - Start with "120"
 * - Are 15+ digits long
 * - Contain only numeric characters
 * - Follow the format: 120[additional digits]
 * 
 * @param phoneNumber - The phone number or identifier to check
 * @returns true if it matches WhatsApp group chat ID pattern
 */
export function isWhatsAppGroupChatId(phoneNumber: string | null | undefined): boolean {
  if (!phoneNumber) {
    return false;
  }


  const cleanNumber = phoneNumber.replace(/[^\d]/g, '');
  

  return (
    cleanNumber.length >= 15 && // Must be at least 15 digits
    cleanNumber.startsWith('120') && // Must start with "120"
    /^\d+$/.test(cleanNumber) // Must contain only digits
  );
}

/**
 * Checks if a group JID matches WhatsApp group chat pattern
 * 
 * @param groupJid - The group JID to check (e.g., "<EMAIL>")
 * @returns true if it matches WhatsApp group chat pattern
 */
export function isWhatsAppGroupJid(groupJid: string | null | undefined): boolean {
  if (!groupJid) {
    return false;
  }


  const idPart = groupJid.split('@')[0];
  return isWhatsAppGroupChatId(idPart);
}

/**
 * Creates a SQL condition to filter out WhatsApp group chats from conversations
 * This can be used in database queries to exclude group chat conversations
 * 
 * @returns SQL condition string for filtering
 */
export function createGroupChatFilterCondition(): string {
  return `
    NOT (
      conversations.is_group = true 
      OR (
        contacts.phone IS NOT NULL 
        AND LENGTH(REGEXP_REPLACE(contacts.phone, '[^0-9]', '', 'g')) >= 15 
        AND REGEXP_REPLACE(contacts.phone, '[^0-9]', '', 'g') ~ '^120[0-9]+$'
      )
      OR (
        contacts.identifier IS NOT NULL 
        AND LENGTH(REGEXP_REPLACE(contacts.identifier, '[^0-9]', '', 'g')) >= 15 
        AND REGEXP_REPLACE(contacts.identifier, '[^0-9]', '', 'g') ~ '^120[0-9]+$'
      )
    )
  `;
}

/**
 * Filters an array of conversations to remove WhatsApp group chats
 * 
 * @param conversations - Array of conversation objects
 * @returns Filtered array without group chat conversations
 */
export function filterGroupChatsFromConversations(conversations: any[]): any[] {
  return conversations.filter(conversation => {

    if (conversation.is_group || conversation.isGroup) {
      return false;
    }


    if (conversation.group_jid || conversation.groupJid) {
      return false;
    }


    if (conversation.contact) {
      const phone = conversation.contact.phone || conversation.contact.identifier;
      if (isWhatsAppGroupChatId(phone)) {
        return false;
      }
    }


    if (conversation.phone && isWhatsAppGroupChatId(conversation.phone)) {
      return false;
    }


    if (conversation.identifier && isWhatsAppGroupChatId(conversation.identifier)) {
      return false;
    }

    return true;
  });
}

/**
 * Filters an array of contacts to remove WhatsApp group chat contacts
 * 
 * @param contacts - Array of contact objects
 * @returns Filtered array without group chat contacts
 */
export function filterGroupChatsFromContacts(contacts: any[]): any[] {
  return contacts.filter(contact => {

    if (isWhatsAppGroupChatId(contact.phone)) {
      return false;
    }


    if (isWhatsAppGroupChatId(contact.identifier)) {
      return false;
    }

    return true;
  });
}

/**
 * Logs when a group chat is filtered out (for debugging purposes)
 * 
 * @param identifier - The group chat identifier that was filtered
 * @param context - Context where the filtering occurred
 */
export function logGroupChatFiltered(identifier: string, context: string): void {
  console.log(`[WhatsApp Group Filter] Filtered group chat ${identifier} from ${context}`);
}

export default {
  isWhatsAppGroupChatId,
  isWhatsAppGroupJid,
  createGroupChatFilterCondition,
  filterGroupChatsFromConversations,
  filterGroupChatsFromContacts,
  logGroupChatFiltered
};
