import { useEffect } from 'react';
import Header from '@/components/layout/Header';
import Sidebar from '@/components/layout/Sidebar';
import ConversationList from '@/components/conversations/ConversationList';
import ConversationView from '@/components/conversations/ConversationView';
import { useConversations } from '@/context/ConversationContext';
import { MobileLayoutProvider, useMobileLayout } from '@/contexts/mobile-layout-context';
import { useQueryClient } from '@tanstack/react-query';
import { useInboxAutoRefresh } from '@/hooks/useInboxAutoRefresh';

function InboxContent() {
  const {
    conversations,
    activeConversationId,
    setActiveConversationId,
    setActiveChannelId,
    activeChannelId
  } = useConversations();

  const {
    isMobile,
    isTablet,
    isDesktop,
    isConversationListOpen,
    isContactDetailsOpen,
    closeAllPanels
  } = useMobileLayout();

  const queryClient = useQueryClient();

  // Initialize auto-refresh functionality
  useInboxAutoRefresh({
    enabled: true,
    intervalMs: 5 * 60 * 1000, // 5 minutes
    minHiddenTimeForRefresh: 10 * 60 * 1000, // 10 minutes
    maxRetries: 3,
    retryDelayMs: 2000
  });

  useEffect(() => {
    // Invalidate queries when component mounts to ensure fresh data
    queryClient.invalidateQueries({ queryKey: ['/api/conversations'] });
    queryClient.invalidateQueries({ queryKey: ['/api/contacts'] });
  }, [queryClient]);



  useEffect(() => {
    const selectedContactId = localStorage.getItem('selectedContactId');
    const selectedChannelType = localStorage.getItem('selectedChannelType');

    if (selectedContactId && selectedChannelType) {
      const conversation = conversations.find(conv =>
        conv.contactId === parseInt(selectedContactId) &&
        conv.channelType === selectedChannelType
      );

      if (conversation) {
        setActiveConversationId(conversation.id);
      }

      localStorage.removeItem('selectedContactId');
      localStorage.removeItem('selectedChannelType');
    }
  }, [conversations, setActiveConversationId]);

  return (
    <div className="h-screen flex flex-col overflow-hidden font-sans text-gray-800">
      <Header />

      <div className="flex flex-1 overflow-hidden relative">
        <div className={`${isMobile ? 'hidden' : 'flex'}`}>
          <Sidebar />
        </div>

        <div className={`
          ${isMobile
            ? `fixed inset-y-0 left-0 z-50 transform transition-transform duration-300 ease-in-out ${
                isConversationListOpen ? 'translate-x-0' : '-translate-x-full'
              }`
            : isTablet
            ? `${isConversationListOpen ? 'flex' : 'hidden'}`
            : 'flex'
          }
        `}>
          <ConversationList />
        </div>

        <div className="flex-1 flex flex-col overflow-hidden">
          <ConversationView />
        </div>

        {isMobile && (isConversationListOpen || isContactDetailsOpen) && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-30"
            onClick={(e) => {
              e.stopPropagation();
              closeAllPanels();
            }}
          />
        )}
      </div>
    </div>
  );
}

export default function Inbox() {
  return (
    <MobileLayoutProvider>
      <InboxContent />
    </MobileLayoutProvider>
  );
}
