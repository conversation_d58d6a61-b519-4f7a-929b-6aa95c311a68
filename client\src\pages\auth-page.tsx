import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useTranslation } from "@/hooks/use-translation";
import { useBranding } from "@/contexts/branding-context";
import { useSubdomain } from "@/contexts/subdomain-context";
import { Redirect } from "wouter";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Loader2, Mail, Lock, Eye, EyeOff } from "lucide-react";

const loginSchema = z.object({
  username: z.string().min(2, "Username or email must be at least 2 characters"),
  password: z.string().min(1, "Password is required"),
});

type LoginData = z.infer<typeof loginSchema>;



export default function AuthPage() {
  const { user, isLoading, loginMutation } = useAuth();
  const { t } = useTranslation();
  const { branding } = useBranding();
  const { subdomainInfo, isLoading: isSubdomainLoading } = useSubdomain();
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    if (isLoading) {
      const timeoutId = setTimeout(() => {
        document.cookie = "connect.sid=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
        window.location.reload();
      }, 3000);

      return () => clearTimeout(timeoutId);
    }
  }, [isLoading]);

  const loginForm = useForm<LoginData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  const onLoginSubmit = (data: LoginData) => {
    loginMutation.mutate(data);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (user) {
    return <Redirect to="/" />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-4">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100/30 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-100/30 rounded-full blur-3xl"></div>
      </div>



      {/* Main auth card */}
      <div className="relative z-10 w-full max-w-md">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">
          {/* Logo icon */}
          <div className="flex justify-center mb-6">
            <div className="w-auto h-12 flex items-center justify-center">
              {branding.logoUrl ? (
                <img src={branding.logoUrl} alt={branding.appName} className="h-12 w-auto" />
              ) : (
                <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">{branding.appName.charAt(0)}</span>
                </div>
              )}
            </div>
          </div>

          {/* Title and description */}
          <div className="text-center mb-8">
            {subdomainInfo?.isSubdomainMode && subdomainInfo?.company ? (
              <>
                <h1 className="text-2xl font-semibold text-gray-900 mb-2">
                  {t('auth.signin_company_title', 'Sign in to {{companyName}}', { companyName: subdomainInfo.company.name })}
                </h1>
                <p className="text-gray-500 text-sm">
                  {t('auth.signin_company_description', 'Access your company workspace on {{appName}}', { appName: branding.appName })}
                </p>
              </>
            ) : (
              <>
                <h1 className="text-2xl font-semibold text-gray-900 mb-2">
                  {t('auth.signin_title', 'Access like a shadow')}
                </h1>
                <p className="text-gray-500 text-sm">
                  {t('auth.signin_description', 'Unite your customer conversations across all channels in one powerful CRM.')}
                </p>
              </>
            )}
          </div>

          {/* Login form */}
          <Form {...loginForm}>
            <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
              <FormField
                control={loginForm.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        <Input
                          placeholder={t('auth.email_placeholder', 'Email')}
                          className="pl-10 h-12 bg-gray-50 border-gray-200 rounded-lg"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={loginForm.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="relative">
                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder={t('auth.password_placeholder', 'Password')}
                          className="pl-10 pr-10 h-12 bg-gray-50 border-gray-200 rounded-lg"
                          {...field}
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        >
                          {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Forgot password link */}
              <div className="flex justify-end">
                <a href="/forgot-password" className="text-sm text-blue-600 hover:text-blue-800 hover:underline">
                  {t('auth.forgot_password', 'Forgot password?')}
                </a>
              </div>

              {/* Submit button */}
              <Button
                type="submit"
                className="w-full h-12 btn-brand-primary text-white rounded-lg font-medium"
                disabled={loginMutation.isPending}
              >
                {loginMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('auth.logging_in', 'Logging in...')}
                  </>
                ) : (
                  t('auth.get_started', 'Login')
                )}
              </Button>
            </form>
          </Form>

          {/* Register link */}
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-600">
              {t('auth.new_to_platform', 'New to the platform?')}{' '}
              <a
                href="/register"
                className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
              >
                {t('auth.register_company', 'Register for a new Company')}
              </a>
            </p>
          </div>

          {/* Social login divider */}
          <div className="mt-6 mb-4">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">
                  {t('auth.or_sign_in_with', 'Or sign in with')}
                </span>
              </div>
            </div>
          </div>

          {/* Social login buttons */}
          <div className="flex justify-center space-x-4">
            <button
              onClick={() => window.location.href = '/api/auth/google'}
              className="w-12 h-12 bg-gray-50 hover:bg-gray-100 rounded-lg flex items-center justify-center border border-gray-200 transition-colors"
              title="Sign in with Google"
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
            </button>

            <button
              onClick={() => window.location.href = '/api/auth/facebook'}
              className="w-12 h-12 bg-gray-50 hover:bg-gray-100 rounded-lg flex items-center justify-center border border-gray-200 transition-colors"
              title="Sign in with Facebook"
            >
              <svg className="w-5 h-5" fill="#1877F2" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            </button>

            <button
              onClick={() => window.location.href = '/api/auth/apple'}
              className="w-12 h-12 bg-gray-50 hover:bg-gray-100 rounded-lg flex items-center justify-center border border-gray-200 transition-colors"
              title="Sign in with Apple"
            >
              <svg className="w-5 h-5" fill="#000000" viewBox="0 0 24 24">
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}