import { AIAssistantNode } from '@/components/flow-builder/AIAssistantNode';
import { BotDisableNode } from '@/components/flow-builder/BotDisableNode';
import { BotResetNode } from '@/components/flow-builder/BotResetNode';
import { DocumindNode } from '@/components/flow-builder/DocumindNode';
import { FlowiseNode } from '@/components/flow-builder/FlowiseNode';
import { GoogleSheetsNode } from '@/components/flow-builder/GoogleSheetsNode';
import { N8nNode } from '@/components/flow-builder/N8nNode';
import FollowUpNode from '@/components/flow-builder/nodes/FollowUpNode';

import { HTTPRequestNode } from '@/components/flow-builder/HTTPRequestNode';
import { ShopifyNode } from '@/components/flow-builder/ShopifyNode';
import { TranslationNode } from '@/components/flow-builder/TranslationNode';
import { TypebotNode } from '@/components/flow-builder/TypebotNode';
import UpdatePipelineStageNode from '@/components/flow-builder/UpdatePipelineStageNode';
import { WebhookNode } from '@/components/flow-builder/WebhookNode';
import { WooCommerceNode } from '@/components/flow-builder/WooCommerceNode';
import BotIcon from '@/components/ui/bot-icon';
import { Button } from '@/components/ui/button';
import { Calendar } from "@/components/ui/calendar";
import { FileUpload } from '@/components/ui/file-upload';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem, SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from '@/hooks/use-translation';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { getBrowserTimezone } from '@/utils/timezones';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  AlertCircle,
  ArrowRightCircle,
  Brain,
  Calendar as CalendarIcon,
  Clock,
  Copy,
  File,
  FileAudio, FileText, FileVideo,
  Globe,
  Image,
  Languages,
  LayoutGrid,
  ListOrdered,
  Loader2,
  MessageCircle,
  MessageSquare,
  Network,
  Plus,
  RefreshCw,
  Save,
  Search,
  Sheet,
  ShoppingBag, ShoppingCart,
  Trash2,
  UserCheck,
  Workflow,
  X
} from 'lucide-react';
import { nanoid } from 'nanoid';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import ReactFlow, {
  addEdge,
  Background,
  BaseEdge,
  Connection,
  Controls,
  Edge,
  EdgeLabelRenderer,
  EdgeProps,
  getSmoothStepPath,
  MiniMap,
  Node,
  NodeTypes,
  Panel,
  ReactFlowProvider,
  useEdgesState,
  useNodesState,
  useReactFlow
} from 'reactflow';
import 'reactflow/dist/style.css';
import { Link, useLocation, useRoute } from 'wouter';

const CalendarClock = (props: any) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h7.5" />
    <path d="M16 2v4" />
    <path d="M8 2v4" />
    <path d="M3 10h5" />
    <path d="M17.5 17.5 16 16.25V14" />
    <path d="M22 16a5 5 0 1 1-10 0 5 5 0 0 1 10 0Z" />
  </svg>
);

import { noHandleStyle, standardHandleStyle, yesHandleStyle } from '@/components/flow-builder/StyledHandle';
import { Handle, Position } from 'reactflow';

const AudioPreview = React.memo(({ url }: { url: string }) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [metadata, setMetadata] = useState<{ duration?: number }>({});

  React.useEffect(() => {
    if (url) {
      setIsLoading(true);
      setHasError(false);
      setMetadata({});
    }
  }, [url]);

  const handleLoadedMetadata = React.useCallback((e: React.SyntheticEvent<HTMLAudioElement>) => {
    const audio = e.currentTarget;
    setMetadata({ duration: audio.duration });
    setIsLoading(false);
    setHasError(false);
  }, []);

  const handleError = React.useCallback(() => {
    setIsLoading(false);
    setHasError(true);
  }, []);

  const formatDuration = React.useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const fileName = React.useMemo(() => {
    return url.split('/').pop() || 'audio file';
  }, [url]);

  if (!url) return null;

  return (
    <div className="mt-2 p-2 bg-secondary/20 rounded border">
      <div className="flex items-center gap-2 mb-2">
        <FileAudio className="h-4 w-4 text-purple-500" />
        <span className="text-xs font-medium truncate">{fileName}</span>
      </div>

      {isLoading && (
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-500"></div>
          <span className="ml-2 text-xs text-muted-foreground">{t('flow_builder.preview.loading_audio', 'Loading audio...')}</span>
        </div>
      )}

      {hasError && (
        <div className="flex items-center justify-center py-4 text-red-500">
          <span className="text-xs">{t('flow_builder.preview.failed_audio', 'Failed to load audio')}</span>
        </div>
      )}

      {!hasError && (
        <audio
          src={url}
          controls
          className="w-full h-8"
          onLoadedMetadata={handleLoadedMetadata}
          onError={handleError}
          preload="metadata"
        />
      )}

      {metadata.duration && (
        <div className="mt-1 text-xs text-muted-foreground">
          {t('flow_builder.preview.duration', 'Duration')}: {formatDuration(metadata.duration)}
        </div>
      )}
    </div>
  );
});

const VideoPreview = React.memo(({ url }: { url: string }) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [metadata, setMetadata] = useState<{ duration?: number; width?: number; height?: number }>({});

  React.useEffect(() => {
    if (url) {
      setIsLoading(true);
      setHasError(false);
      setMetadata({});
    }
  }, [url]);

  const handleLoadedMetadata = React.useCallback((e: React.SyntheticEvent<HTMLVideoElement>) => {
    const video = e.currentTarget;
    setMetadata({
      duration: video.duration,
      width: video.videoWidth,
      height: video.videoHeight
    });
    setIsLoading(false);
    setHasError(false);
  }, []);

  const handleError = React.useCallback(() => {
    setIsLoading(false);
    setHasError(true);
  }, []);

  const formatDuration = React.useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const fileName = React.useMemo(() => {
    return url.split('/').pop() || 'video file';
  }, [url]);

  if (!url) return null;

  return (
    <div className="mt-2 p-2 bg-secondary/20 rounded border">
      <div className="flex items-center gap-2 mb-2">
        <FileVideo className="h-4 w-4 text-red-500" />
        <span className="text-xs font-medium truncate">{fileName}</span>
      </div>

      {isLoading && (
        <div className="flex items-center justify-center py-8 bg-black/5 rounded">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
          <span className="ml-2 text-xs text-muted-foreground">{t('flow_builder.preview.loading_video', 'Loading video...')}</span>
        </div>
      )}

      {hasError && (
        <div className="flex items-center justify-center py-8 bg-black/5 rounded text-red-500">
          <FileVideo className="h-6 w-6 mr-2" />
          <span className="text-xs">{t('flow_builder.preview.failed_video', 'Failed to load video')}</span>
        </div>
      )}

      {!hasError && (
        <video
          src={url}
          controls
          className="w-full max-h-32 bg-black rounded"
          onLoadedMetadata={handleLoadedMetadata}
          onError={handleError}
          preload="metadata"
        />
      )}

      {metadata.duration && (
        <div className="mt-1 text-xs text-muted-foreground flex gap-4">
          <span>{t('flow_builder.preview.duration', 'Duration')}: {formatDuration(metadata.duration)}</span>
          {metadata.width && metadata.height && (
            <span>{t('flow_builder.preview.resolution', 'Resolution')}: {metadata.width}x{metadata.height}</span>
          )}
        </div>
      )}
    </div>
  );
});

const DocumentPreview = React.memo(({ url, fileName }: { url: string; fileName?: string }) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  React.useEffect(() => {
    if (url) {
      setIsLoading(true);
      setHasError(false);
    }
  }, [url]);

  const getFileExtension = React.useCallback((url: string) => {
    const name = fileName || url.split('/').pop() || '';
    return name.split('.').pop()?.toLowerCase() || '';
  }, [fileName, url]);

  const getFileIcon = React.useCallback((extension: string) => {
    switch (extension) {
      case 'pdf':
        return '📄';
      case 'doc':
      case 'docx':
        return '📝';
      case 'xls':
      case 'xlsx':
        return '📊';
      case 'ppt':
      case 'pptx':
        return '📽️';
      case 'txt':
        return '📃';
      case 'zip':
      case 'rar':
        return '🗜️';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return '🖼️';
      default:
        return '📁';
    }
  }, []);

  const getFileTypeLabel = React.useCallback((extension: string) => {
    switch (extension) {
      case 'pdf':
        return 'PDF Document';
      case 'doc':
      case 'docx':
        return 'Word Document';
      case 'xls':
      case 'xlsx':
        return 'Excel Spreadsheet';
      case 'ppt':
      case 'pptx':
        return 'PowerPoint Presentation';
      case 'txt':
        return 'Text File';
      case 'zip':
      case 'rar':
        return 'Archive File';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'Image File';
      default:
        return 'Document';
    }
  }, []);

  const displayFileName = React.useMemo(() => {
    return fileName || url.split('/').pop() || 'document';
  }, [fileName, url]);

  const extension = React.useMemo(() => getFileExtension(url), [getFileExtension, url]);
  const isImage = React.useMemo(() => ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension), [extension]);

  const handleImageLoad = React.useCallback(() => {
    setIsLoading(false);
    setHasError(false);
  }, []);

  const handleImageError = React.useCallback(() => {
    setIsLoading(false);
    setHasError(true);
  }, []);

  if (!url) return null;

  return (
    <div className="mt-2 p-2 bg-secondary/20 rounded border">
      <div className="flex items-center gap-2 mb-2">
        <span className="text-lg">{getFileIcon(extension)}</span>
        <div className="flex-1 min-w-0">
          <div className="text-xs font-medium truncate">{displayFileName}</div>
          <div className="text-xs text-muted-foreground">{getFileTypeLabel(extension)}</div>
        </div>
      </div>

      {isImage ? (
        <div className="relative">
          {isLoading && (
            <div className="flex items-center justify-center py-8 bg-black/5 rounded">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-amber-600"></div>
              <span className="ml-2 text-xs text-muted-foreground">{t('flow_builder.preview.loading_image', 'Loading image...')}</span>
            </div>
          )}
          {hasError && (
            <div className="flex items-center justify-center py-8 bg-black/5 rounded text-red-500">
              <span className="text-xs">{t('flow_builder.preview.failed_image', 'Failed to load image')}</span>
            </div>
          )}
          <img
            src={url}
            alt={displayFileName}
            className={`w-full max-h-32 object-cover rounded ${isLoading ? 'hidden' : ''}`}
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        </div>
      ) : (
        <div className="flex items-center justify-between p-2 bg-secondary/30 rounded">
          <span className="text-xs text-muted-foreground">{t('flow_builder.preview.click_to_download', 'Click to download/view')}</span>
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-xs px-2 py-1 bg-primary text-primary-foreground rounded hover:bg-primary/80"
          >
            {t('flow_builder.preview.open_document', 'Open')}
          </a>
        </div>
      )}
    </div>
  );
});

function NodeToolbar({ id, onDuplicate, onDelete }: { id: string; onDuplicate: (id: string) => void; onDelete: (id: string) => void }) {
  const { t } = useTranslation();

  return (
    <div className="absolute -top-8 -right-2 bg-background border rounded-md shadow-sm flex z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7"
              onClick={() => onDuplicate(id)}
            >
              <Copy className="h-3.5 w-3.5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="top">
            <p className="text-xs">{t('flow_builder.duplicate_node', 'Duplicate node')}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-destructive hover:text-destructive"
              onClick={() => onDelete(id)}
            >
              <Trash2 className="h-3.5 w-3.5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="top">
            <p className="text-xs">{t('flow_builder.delete_node', 'Delete node')}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}

const FlowContext = React.createContext<{
  onDeleteNode: (nodeId: string) => void;
  onDuplicateNode: (nodeId: string) => void;
} | null>(null);

function MessageNode({ data, isConnectable, id }: any) {
  const { t } = useTranslation();
  const [isEditing, setIsEditing] = useState(false);
  const [message, setMessage] = useState(data.message || t('flow_builder.default_message', "Hello! How can I help you?"));
  const { setNodes } = useReactFlow();
  const flowContext = useFlowContext();

  const availableVariables = [
    { name: "contact.name", description: t('flow_builder.var_contact_name', "Contact's name") },
    { name: "contact.phone", description: t('flow_builder.var_contact_phone', "Contact's phone number") },
    { name: "message.content", description: t('flow_builder.var_message_content', "Received message content") },
    { name: "date.today", description: t('flow_builder.var_date_today', "Current date") },
    { name: "time.now", description: t('flow_builder.var_time_now', "Current time") },
    { name: "availability", description: t('flow_builder.var_availability', "Google Calendar availability data from previous node") }
  ];

  const updateNodeData = useCallback((updates: any) => {
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              ...updates
            }
          };
        }
        return node;
      })
    );
  }, [id, setNodes]);

  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newMessage = e.target.value;
    setMessage(newMessage);
    updateNodeData({ message: newMessage });
  };

  const insertVariable = (variable: string) => {
    const textArea = document.getElementById(`message-textarea-${id}`) as HTMLTextAreaElement;
    if (!textArea) return;

    const cursorPos = textArea.selectionStart;
    const variableText = `{{${variable}}}`;
    const newMessage = message.substring(0, cursorPos) + variableText + message.substring(cursorPos);

    setMessage(newMessage);
    updateNodeData({ message: newMessage });

    setTimeout(() => {
      textArea.focus();
      textArea.setSelectionRange(cursorPos + variableText.length, cursorPos + variableText.length);
    }, 0);
  };

  const formatMessage = (message: string) => {
    const regex = /\{\{([^}]+)\}\}/g;

    if (!regex.test(message)) {
      return message;
    }

    const parts = [];
    let lastIndex = 0;
    let match;

    regex.lastIndex = 0;
    while ((match = regex.exec(message)) !== null) {
      if (match.index > lastIndex) {
        parts.push(message.substring(lastIndex, match.index));
      }

      parts.push(
        <span key={match.index} className="bg-primary/10 text-primary px-1 rounded">
          {match[0]}
        </span>
      );

      lastIndex = match.index + match[0].length;
    }

    if (lastIndex < message.length) {
      parts.push(message.substring(lastIndex));
    }

    return parts;
  };

  return (
    <div className="node-message p-3 rounded-lg bg-white border border-border shadow-sm max-w-[250px] group">
      {flowContext && (
        <NodeToolbar
          id={id}
          onDuplicate={flowContext.onDuplicateNode}
          onDelete={flowContext.onDeleteNode}
        />
      )}
      <div className="font-medium flex items-center gap-2 mb-2">
        <MessageSquare className="h-4 w-4 text-primary" />
        <span>{t('flow_builder.send_message', 'Send Message')}</span>
        <button
          className="ml-auto text-xs text-muted-foreground hover:text-foreground"
          onClick={() => setIsEditing(!isEditing)}
        >
          {isEditing ? t('common.done', 'Done') : t('common.edit', 'Edit')}
        </button>
      </div>

      {isEditing ? (
        <div className="space-y-2">
          <textarea
            id={`message-textarea-${id}`}
            className="w-full p-2 text-sm border rounded min-h-[80px] resize-none"
            value={message}
            onChange={handleMessageChange}
            placeholder={t('flow_builder.type_message_placeholder', 'Type your message here...')}
          />

          <div>
            <p className="text-xs font-medium mb-1">{t('flow_builder.insert_variable', 'Insert Variable:')}</p>
            <div className="flex flex-wrap gap-1">
              {availableVariables.map((variable) => (
                <button
                  key={variable.name}
                  className="text-xs px-2 py-1 bg-secondary rounded hover:bg-secondary/80"
                  title={variable.description}
                  onClick={() => insertVariable(variable.name)}
                >
                  {variable.name}
                </button>
              ))}
            </div>
          </div>

          <div className="text-[10px] text-muted-foreground">
            {t('flow_builder.variables_help', 'Variables will be replaced with actual values when message is sent.')}
          </div>
        </div>
      ) : (
        <div className="text-sm p-2 bg-secondary/40 rounded border border-border">
          {formatMessage(message)}
        </div>
      )}

      <Handle
        type="target"
        position={Position.Left}
        style={standardHandleStyle}
        isConnectable={isConnectable}
      />
    </div>
  );
}

const CONDITION_TYPES = {
  MESSAGE_CONTAINS: "Message Contains",
  EXACT_MATCH: "Exact Match",
  REGEX_MATCH: "Regex Match",
  MESSAGE_STARTS_WITH: "Message Starts With",
  MESSAGE_ENDS_WITH: "Message Ends With",
  HAS_MEDIA: "Has Media",
  MEDIA_TYPE: "Media Type Is",
  TIME_BASED: "Time Condition",
  CONTACT_ATTRIBUTE: "Contact Attribute",
  CUSTOM: "Custom Expression"
};

const MEDIA_TYPES = [
  "image",
  "video",
  "audio",
  "document",
  "sticker"
];

function ConditionNode({ data, isConnectable, id }: any) {
  const { t } = useTranslation();
  const [isEditing, setIsEditing] = useState(false);
  const [conditionType, setConditionType] = useState(data.conditionType || CONDITION_TYPES.MESSAGE_CONTAINS);
  const [conditionValue, setConditionValue] = useState(data.conditionValue || "");
  const [caseSensitive, setCaseSensitive] = useState(data.caseSensitive || false);
  const [mediaType, setMediaType] = useState(data.mediaType || MEDIA_TYPES[0]);
  const [timeOperator, setTimeOperator] = useState(data.timeOperator || "after");
  const [timeValue, setTimeValue] = useState(data.timeValue || "");
  const [contactAttribute, setContactAttribute] = useState(data.contactAttribute || "name");
  const [attributeValue, setAttributeValue] = useState(data.attributeValue || "");
  const [advancedMode, setAdvancedMode] = useState(data.advancedMode || false);
  const [customCondition, setCustomCondition] = useState(data.customCondition || "Contains('hello')");

  const [condition, setCondition] = useState(data.condition || "Contains('hello')");

  const { setNodes } = useReactFlow();
  const flowContext = useFlowContext();

  const updateNodeData = useCallback((updates: any) => {
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              ...updates
            }
          };
        }
        return node;
      })
    );
  }, [id, setNodes]);

  const updateConditionText = useCallback(() => {
    let newCondition = "";
    if (advancedMode) {
      newCondition = customCondition;
    } else {
      switch (conditionType) {
        case CONDITION_TYPES.MESSAGE_CONTAINS:
          newCondition = `Contains('${conditionValue}'${caseSensitive ? ', true' : ''})`;
          break;
        case CONDITION_TYPES.EXACT_MATCH:
          newCondition = `ExactMatch('${conditionValue}'${caseSensitive ? ', true' : ''})`;
          break;
        case CONDITION_TYPES.REGEX_MATCH:
          newCondition = `RegexMatch('${conditionValue}')`;
          break;
        case CONDITION_TYPES.MESSAGE_STARTS_WITH:
          newCondition = `StartsWith('${conditionValue}'${caseSensitive ? ', true' : ''})`;
          break;
        case CONDITION_TYPES.MESSAGE_ENDS_WITH:
          newCondition = `EndsWith('${conditionValue}'${caseSensitive ? ', true' : ''})`;
          break;
        case CONDITION_TYPES.HAS_MEDIA:
          newCondition = `HasMedia()`;
          break;
        case CONDITION_TYPES.MEDIA_TYPE:
          newCondition = `MediaType('${mediaType}')`;
          break;
        case CONDITION_TYPES.TIME_BASED:
          newCondition = `Time${timeOperator.charAt(0).toUpperCase() + timeOperator.slice(1)}('${timeValue}')`;
          break;
        case CONDITION_TYPES.CONTACT_ATTRIBUTE:
          newCondition = `Contact.${contactAttribute} == '${attributeValue}'`;
          break;
        default:
          newCondition = `Contains('${conditionValue}')`;
      }
    }

    setCondition(newCondition);
    updateNodeData({
      condition: newCondition,
      conditionType,
      conditionValue,
      caseSensitive,
      mediaType,
      timeOperator,
      timeValue,
      contactAttribute,
      attributeValue,
      advancedMode,
      customCondition
    });
  }, [
    advancedMode, conditionType, conditionValue, caseSensitive,
    mediaType, timeOperator, timeValue, contactAttribute,
    attributeValue, customCondition, updateNodeData
  ]);

  const handleConditionTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setConditionType(e.target.value);
  };

  const handleConditionValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConditionValue(e.target.value);
  };

  const handleTimeValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTimeValue(e.target.value);
  };

  const handleCustomConditionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomCondition(e.target.value);
  };

  const handleDoneClick = () => {
    updateConditionText();
    setIsEditing(false);
  };

  const renderConditionInputs = () => {
    if (advancedMode) {
      return (
        <div className="space-y-2">
          <input
            className="w-full p-2 text-sm border rounded"
            value={customCondition}
            onChange={handleCustomConditionChange}
            placeholder={t('flow_builder.enter_custom_condition', 'Enter custom condition')}
          />
          <div className="text-[10px] text-muted-foreground">
            {t('flow_builder.condition_examples', "Examples: Contains('help'), IsMedia(), ExactMatch('hello')")}
          </div>
        </div>
      );
    }

    switch (conditionType) {
      case CONDITION_TYPES.MESSAGE_CONTAINS:
      case CONDITION_TYPES.EXACT_MATCH:
      case CONDITION_TYPES.REGEX_MATCH:
      case CONDITION_TYPES.MESSAGE_STARTS_WITH:
      case CONDITION_TYPES.MESSAGE_ENDS_WITH:
        return (
          <div className="space-y-2">
            <input
              className="w-full p-2 text-sm border rounded"
              value={conditionValue}
              onChange={handleConditionValueChange}
              placeholder={t('flow_builder.enter_text_value', 'Enter text value')}
            />
            {conditionType !== CONDITION_TYPES.REGEX_MATCH && (
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id={`case-sensitive-${id}`}
                  checked={caseSensitive}
                  onChange={(e) => setCaseSensitive(e.target.checked)}
                />
                <label htmlFor={`case-sensitive-${id}`} className="text-xs">
                  {t('flow_builder.case_sensitive', 'Case sensitive')}
                </label>
              </div>
            )}
          </div>
        );

      case CONDITION_TYPES.HAS_MEDIA:
        return (
          <div className="text-xs text-muted-foreground">
            {t('flow_builder.has_media_desc', 'Checks if the message has any attached media.')}
          </div>
        );

      case CONDITION_TYPES.MEDIA_TYPE:
        return (
          <div className="space-y-2">
            <select
              className="w-full p-2 text-sm border rounded"
              value={mediaType}
              onChange={(e) => setMediaType(e.target.value)}
            >
              {MEDIA_TYPES.map(type => (
                <option key={type} value={type}>{type.charAt(0).toUpperCase() + type.slice(1)}</option>
              ))}
            </select>
            <div className="text-[10px] text-muted-foreground">
              {t('flow_builder.media_type_desc', 'Checks if the message contains media of the selected type.')}
            </div>
          </div>
        );

      case CONDITION_TYPES.TIME_BASED:
        return (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <select
                className="flex-1 p-2 text-sm border rounded"
                value={timeOperator}
                onChange={(e) => setTimeOperator(e.target.value)}
              >
                <option value="before">{t('flow_builder.before', 'Before')}</option>
                <option value="after">{t('flow_builder.after', 'After')}</option>
                <option value="between">{t('flow_builder.between', 'Between')}</option>
              </select>
            </div>
            <input
              className="w-full p-2 text-sm border rounded"
              value={timeValue}
              onChange={handleTimeValueChange}
              placeholder={timeOperator === "between" ? "09:00,17:00" : "09:00"}
              type="text"
            />
            <div className="text-[10px] text-muted-foreground">
              {timeOperator === "between"
                ? t('flow_builder.time_format_between', 'Use format: HH:MM,HH:MM (24h)')
                : t('flow_builder.time_format_single', 'Use format: HH:MM (24h)')
              }
            </div>
          </div>
        );

      case CONDITION_TYPES.CONTACT_ATTRIBUTE:
        return (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <select
                className="flex-1 p-2 text-sm border rounded"
                value={contactAttribute}
                onChange={(e) => setContactAttribute(e.target.value)}
              >
                <option value="name">{t('flow_builder.name', 'Name')}</option>
                <option value="phone">{t('flow_builder.phone', 'Phone')}</option>
                <option value="email">{t('flow_builder.email', 'Email')}</option>
                <option value="tags">{t('flow_builder.tags', 'Tags')}</option>
              </select>
            </div>
            <input
              className="w-full p-2 text-sm border rounded"
              value={attributeValue}
              onChange={(e) => setAttributeValue(e.target.value)}
              placeholder={t('flow_builder.attribute_value', 'Attribute value')}
            />
          </div>
        );

      default:
        return null;
    }
  };

  const formatConditionDisplay = () => {
    if (advancedMode) {
      return customCondition;
    }

    switch (conditionType) {
      case CONDITION_TYPES.MESSAGE_CONTAINS:
        return `Message contains: "${conditionValue}"${caseSensitive ? " (case sensitive)" : ""}`;
      case CONDITION_TYPES.EXACT_MATCH:
        return `Message exactly matches: "${conditionValue}"${caseSensitive ? " (case sensitive)" : ""}`;
      case CONDITION_TYPES.REGEX_MATCH:
        return `Message matches regex: "${conditionValue}"`;
      case CONDITION_TYPES.MESSAGE_STARTS_WITH:
        return `Message starts with: "${conditionValue}"${caseSensitive ? " (case sensitive)" : ""}`;
      case CONDITION_TYPES.MESSAGE_ENDS_WITH:
        return `Message ends with: "${conditionValue}"${caseSensitive ? " (case sensitive)" : ""}`;
      case CONDITION_TYPES.HAS_MEDIA:
        return "Message has media attachment";
      case CONDITION_TYPES.MEDIA_TYPE:
        return `Media type is: ${mediaType}`;
      case CONDITION_TYPES.TIME_BASED:
        return `Time is ${timeOperator}: ${timeValue}`;
      case CONDITION_TYPES.CONTACT_ATTRIBUTE:
        return `Contact ${contactAttribute} is: "${attributeValue}"`;
      default:
        return condition;
    }
  };

  return (
    <div className="node-condition p-3 rounded-lg bg-white border border-border shadow-sm max-w-[280px] group">
      {flowContext && (
        <NodeToolbar
          id={id}
          onDuplicate={flowContext.onDuplicateNode}
          onDelete={flowContext.onDeleteNode}
        />
      )}
      <div className="font-medium flex items-center gap-2 mb-2">
        <AlertCircle className="h-4 w-4 text-amber-500" />
        <span>{t('flow_builder.condition', 'Condition')}</span>
        <button
          className="ml-auto text-xs text-muted-foreground hover:text-foreground"
          onClick={() => isEditing ? handleDoneClick() : setIsEditing(true)}
        >
          {isEditing ? t('common.done', 'Done') : t('common.edit', 'Edit')}
        </button>
      </div>

      {isEditing ? (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-xs font-medium">{t('flow_builder.condition_type', 'Condition Type:')}</label>
            <div className="flex items-center gap-2">
              <span className="text-xs">{t('flow_builder.advanced', 'Advanced')}</span>
              <Switch
                checked={advancedMode}
                onCheckedChange={setAdvancedMode}
                className="scale-75"
              />
            </div>
          </div>

          {!advancedMode && (
            <select
              className="w-full p-2 text-sm border rounded"
              value={conditionType}
              onChange={handleConditionTypeChange}
            >
              {Object.entries(CONDITION_TYPES).map(([key, value]) => (
                <option key={key} value={value}>{value}</option>
              ))}
            </select>
          )}

          {renderConditionInputs()}
        </div>
      ) : (
        <div className="text-sm p-2 bg-secondary/40 rounded border border-border">
          {formatConditionDisplay()}
        </div>
      )}

      <div className="flex mt-2 text-xs justify-between">
        <div className="text-green-600">{t('flow_builder.yes', 'Yes')} →</div>
        <div className="text-red-500">{t('flow_builder.no', 'No')} →</div>
      </div>

      <Handle
        type="target"
        position={Position.Top}
        style={standardHandleStyle}
        isConnectable={isConnectable}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="yes"
        style={yesHandleStyle}
        isConnectable={isConnectable}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="no"
        style={noHandleStyle}
        isConnectable={isConnectable}
      />
    </div>
  );
}

function ImageNode({ data, isConnectable, id }: any) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [imageUrl, setImageUrl] = useState(data.mediaUrl || "");
  const [caption, setCaption] = useState(data.caption || "");
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const { setNodes } = useReactFlow();
  const flowContext = useFlowContext();

  const availableVariables = [
    { name: "contact.name", description: "Contact's name" },
    { name: "contact.phone", description: "Contact's phone number" },
    { name: "date.today", description: "Current date" },
    { name: "time.now", description: "Current time" }
  ];

  const updateNodeData = useCallback((updates: any) => {
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              ...updates
            }
          };
        }
        return node;
      })
    );
  }, [id, setNodes]);

  const handleImageUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newUrl = e.target.value;
    setImageUrl(newUrl);
    updateNodeData({
      mediaUrl: newUrl,
      originalName: '',
      mimetype: '',
      size: 0
    });
  };

  const handleCaptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newCaption = e.target.value;
    setCaption(newCaption);
    updateNodeData({ caption: newCaption });
  };

  const handleRemoveImage = () => {
    setImageUrl("");
    updateNodeData({
      mediaUrl: "",
      originalName: "",
      mimetype: "",
      size: 0
    });
    toast({
      title: t('flow_builder.success.image_removed', 'Image removed'),
      description: t('flow_builder.success.image_removed_desc', 'Image has been removed from the node')
    });
  };

  const insertVariable = (variable: string) => {
    const textArea = document.getElementById(`caption-textarea-${id}`) as HTMLTextAreaElement;
    if (!textArea) return;

    const cursorPos = textArea.selectionStart;
    const variableText = `{{${variable}}}`;
    const newCaption = caption.substring(0, cursorPos) + variableText + caption.substring(cursorPos);

    setCaption(newCaption);
    updateNodeData({ caption: newCaption });

    setTimeout(() => {
      textArea.focus();
      textArea.setSelectionRange(cursorPos + variableText.length, cursorPos + variableText.length);
    }, 0);
  };

  const formatText = (text: string) => {
    const regex = /\{\{([^}]+)\}\}/g;

    if (!regex.test(text)) {
      return text;
    }

    const parts = [];
    let lastIndex = 0;
    let match;

    regex.lastIndex = 0;
    while ((match = regex.exec(text)) !== null) {
      if (match.index > lastIndex) {
        parts.push(text.substring(lastIndex, match.index));
      }

      parts.push(
        <span key={match.index} className="bg-primary/10 text-primary px-1 rounded">
          {match[0]}
        </span>
      );

      lastIndex = match.index + match[0].length;
    }

    if (lastIndex < text.length) {
      parts.push(text.substring(lastIndex));
    }

    return parts;
  };

  const handleFileUpload = async (file: File) => {
    if (!file) {
      toast({
        title: t('flow_builder.error.no_file', 'No file selected'),
        description: t('flow_builder.error.no_file_desc', 'Please select a file to upload'),
        variant: 'destructive'
      });
      return;
    }

    const validImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
    if (!validImageTypes.includes(file.type)) {
      toast({
        title: t('flow_builder.error.invalid_file_type', 'Invalid file type'),
        description: t('flow_builder.error.invalid_image_type', 'Please select a valid image file (JPEG, PNG, GIF, WebP, SVG)'),
        variant: 'destructive'
      });
      return;
    }

    const maxSize = 30 * 1024 * 1024;
    if (file.size > maxSize) {
      toast({
        title: t('flow_builder.error.file_too_large', 'File too large'),
        description: t('flow_builder.error.max_file_size', 'Maximum file size is 30MB'),
        variant: 'destructive'
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'image');

      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          setUploadProgress(progress);
        }
      });

      xhr.addEventListener('load', () => {
        setIsUploading(false);
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            if (response.url) {
              setImageUrl(response.url);
              updateNodeData({
                mediaUrl: response.url,
                originalName: response.originalName,
                mimetype: response.mimetype,
                size: response.size
              });
              toast({
                title: t('flow_builder.success.upload_complete', 'Upload complete'),
                description: t('flow_builder.success.image_uploaded', 'Image uploaded successfully')
              });
            } else {
              throw new Error('Invalid response: missing URL');
            }
          } catch (parseError) {
            console.error('Error parsing response:', parseError);
            throw new Error('Invalid server response');
          }
        } else {
          let errorMessage = 'Upload failed';
          try {
            const errorResponse = JSON.parse(xhr.responseText);
            errorMessage = errorResponse.error || errorResponse.message || errorMessage;
          } catch (e) {
          }
          throw new Error(errorMessage);
        }
      });

      xhr.addEventListener('error', () => {
        setIsUploading(false);
        throw new Error('Network error during upload');
      });

      xhr.addEventListener('timeout', () => {
        setIsUploading(false);
        throw new Error('Upload timeout');
      });

      xhr.timeout = 60000;
      xhr.open('POST', '/api/upload');
      xhr.send(formData);
    } catch (error) {
      console.error('Error uploading file:', error);
      setIsUploading(false);
      toast({
        title: t('flow_builder.error.upload_failed', 'Upload failed'),
        description: error instanceof Error ? error.message : t('flow_builder.error.upload_error', 'An error occurred while uploading the file'),
        variant: 'destructive'
      });
    }
  };

  return (
    <div className="node-image p-3 rounded-lg bg-white border border-border shadow-sm max-w-[250px] relative group">
      {flowContext && (
        <NodeToolbar
          id={id}
          onDuplicate={flowContext.onDuplicateNode}
          onDelete={flowContext.onDeleteNode}
        />
      )}

      <div className="font-medium flex items-center gap-2 mb-2">
        <Image className="h-4 w-4 text-blue-500" />
        <span>{t('flow_builder.send_image', 'Send Image')}</span>
        <button
          className="ml-auto text-xs text-muted-foreground hover:text-foreground"
          onClick={() => setIsEditing(!isEditing)}
        >
          {isEditing ? t('common.done', 'Done') : t('common.edit', 'Edit')}
        </button>
      </div>

      {isEditing ? (
        <div className="space-y-2">
          <div>
            <label className="text-xs font-medium mb-1 block">{t('flow_builder.image_label', 'Image:')}</label>
            <div className="space-y-2">
              <FileUpload
                onFileSelected={handleFileUpload}
                fileType="image/*"
                maxSize={30}
                className="w-full"
                showProgress={isUploading}
                progress={uploadProgress}
              />

              <div className="text-[10px] text-muted-foreground mt-1">
                {t('flow_builder.or_enter_image_url', 'Or enter image URL:')}
              </div>

              <input
                className="w-full p-2 text-xs border rounded"
                value={imageUrl}
                onChange={handleImageUrlChange}
                placeholder={t('flow_builder.enter_image_url', 'Enter image URL or path')}
              />

              {imageUrl && (
                <button
                  className="w-full mt-2 px-3 py-1 text-xs bg-red-100 text-red-700 border border-red-300 rounded hover:bg-red-200 transition-colors"
                  onClick={handleRemoveImage}
                >
                  {t('flow_builder.remove_image', 'Remove Image')}
                </button>
              )}
            </div>
          </div>

          <div>
            <label className="text-xs font-medium mb-1 block">{t('flow_builder.caption_optional', 'Caption (optional):')}</label>
            <textarea
              id={`caption-textarea-${id}`}
              className="w-full p-2 text-sm border rounded min-h-[60px] resize-none"
              value={caption}
              onChange={handleCaptionChange}
              placeholder={t('flow_builder.add_caption_image', 'Add a caption to your image...')}
            />
          </div>

          <div>
            <p className="text-xs font-medium mb-1">{t('flow_builder.insert_variable_caption', 'Insert Variable in Caption:')}</p>
            <div className="flex flex-wrap gap-1">
              {availableVariables.map((variable) => (
                <button
                  key={variable.name}
                  className="text-xs px-2 py-1 bg-secondary rounded hover:bg-secondary/80"
                  title={variable.description}
                  onClick={() => insertVariable(variable.name)}
                >
                  {variable.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-2">
          {imageUrl ? (
            <div className="relative aspect-video bg-secondary/40 rounded overflow-hidden flex items-center justify-center">
              <img
                src={imageUrl}
                alt="Message attachment"
                className="max-w-full max-h-full object-contain"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'https://placehold.co/400x300?text=Invalid+Image+URL';
                }}
              />
            </div>
          ) : (
            <div className="aspect-video bg-secondary/40 rounded flex items-center justify-center text-muted-foreground">
              <div className="text-center text-xs">{t('flow_builder.no_image_provided', 'No image provided')}</div>
            </div>
          )}

          {caption && (
            <>
              <div className="text-xs text-muted-foreground">{t('flow_builder.caption_label', 'Caption:')}</div>
              <div className="text-sm p-2 bg-secondary/40 rounded border border-border">
                {formatText(caption)}
              </div>
            </>
          )}
        </div>
      )}

      <Handle
        type="target"
        position={Position.Left}
        style={standardHandleStyle}
        isConnectable={isConnectable}
      />
    </div>
  );
}

function VideoNode({ data, isConnectable, id }: any) {
  const { t } = useTranslation();
  const [isEditing, setIsEditing] = useState(false);
  const [videoUrl, setVideoUrl] = useState(data.mediaUrl || "");
  const [caption, setCaption] = useState(data.caption || t('flow_builder.default_video_caption', 'Watch this video!'));
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showPreview, setShowPreview] = useState(false);
  const { setNodes } = useReactFlow();
  const flowContext = useFlowContext();

  const availableVariables = [
    { name: "contact.name", description: "Contact's name" },
    { name: "contact.phone", description: "Contact's phone number" },
    { name: "date.today", description: "Current date" },
    { name: "time.now", description: "Current time" }
  ];

  const updateNodeData = useCallback((updates: any) => {
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              ...updates
            }
          };
        }
        return node;
      })
    );
  }, [id, setNodes]);

  const handleVideoUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newUrl = e.target.value;
    setVideoUrl(newUrl);
    updateNodeData({ mediaUrl: newUrl });
  };



  const handleCaptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newCaption = e.target.value;
    setCaption(newCaption);
    updateNodeData({ caption: newCaption });
  };

  const insertVariable = (variable: string) => {
    const textArea = document.getElementById(`video-caption-textarea-${id}`) as HTMLTextAreaElement;
    if (!textArea) return;

    const cursorPos = textArea.selectionStart;
    const variableText = `{{${variable}}}`;
    const newCaption = caption.substring(0, cursorPos) + variableText + caption.substring(cursorPos);

    setCaption(newCaption);
    updateNodeData({ caption: newCaption });

    setTimeout(() => {
      textArea.focus();
      textArea.setSelectionRange(cursorPos + variableText.length, cursorPos + variableText.length);
    }, 0);
  };

  const formatText = (text: string) => {
    const regex = /\{\{([^}]+)\}\}/g;

    if (!regex.test(text)) {
      return text;
    }

    const parts = [];
    let lastIndex = 0;
    let match;

    regex.lastIndex = 0;
    while ((match = regex.exec(text)) !== null) {
      if (match.index > lastIndex) {
        parts.push(text.substring(lastIndex, match.index));
      }

      parts.push(
        <span key={match.index} className="bg-primary/10 text-primary px-1 rounded">
          {match[0]}
        </span>
      );

      lastIndex = match.index + match[0].length;
    }

    if (lastIndex < text.length) {
      parts.push(text.substring(lastIndex));
    }

    return parts;
  };

  const handleFileUpload = async (file: File) => {
    if (!file) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'video');

      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          setUploadProgress(progress);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          const response = JSON.parse(xhr.responseText);
          setVideoUrl(response.url);
          updateNodeData({ mediaUrl: response.url });
          setIsUploading(false);
        } else {
          throw new Error('Upload failed');
        }
      });

      xhr.addEventListener('error', () => {
        throw new Error('Upload failed');
      });

      xhr.open('POST', '/api/upload');
      xhr.send(formData);
    } catch (error) {
      console.error('Error uploading file:', error);
      setIsUploading(false);
    }
  };

  return (
    <div className="node-video p-3 rounded-lg bg-white border border-border shadow-sm max-w-[250px] relative group">
      {flowContext && (
        <NodeToolbar
          id={id}
          onDuplicate={flowContext.onDuplicateNode}
          onDelete={flowContext.onDeleteNode}
        />
      )}

      <div className="font-medium flex items-center gap-2 mb-2">
        <FileVideo className="h-4 w-4 text-red-500" />
        <span>{t('flow_builder.send_video', 'Send Video')}</span>
        <button
          className="ml-auto text-xs text-muted-foreground hover:text-foreground"
          onClick={() => setIsEditing(!isEditing)}
        >
          {isEditing ? t('common.done', 'Done') : t('common.edit', 'Edit')}
        </button>
      </div>

      {isEditing ? (
        <div className="space-y-2">
          <div>
            <label className="text-xs font-medium mb-1 block">{t('flow_builder.video_url_label', 'Video URL:')}</label>
            <input
              className="w-full p-2 text-sm border rounded"
              value={videoUrl}
              onChange={handleVideoUrlChange}
              placeholder={t('flow_builder.enter_video_url', 'Enter video URL or path')}
            />
            <div className="mt-2">
              <FileUpload
                onFileSelected={handleFileUpload}
                fileType="video/*"
                maxSize={30}
                className="w-full"
                showProgress={isUploading}
                progress={uploadProgress}
              />
            </div>
            {videoUrl && (
              <button
                className="w-full mt-2 px-3 py-2 text-xs bg-red-100 text-red-700 border border-red-300 rounded hover:bg-red-200 transition-colors flex items-center justify-center gap-2"
                onClick={() => setShowPreview(!showPreview)}
              >
                <FileVideo className="h-3 w-3" />
                {showPreview ? t('flow_builder.hide_preview', 'Hide Preview') : t('flow_builder.preview_video', 'Preview Video')}
              </button>
            )}
            {videoUrl && showPreview && <VideoPreview url={videoUrl} />}
          </div>

          <div>
            <label className="text-xs font-medium mb-1 block">{t('flow_builder.caption_optional', 'Caption (optional):')}</label>
            <textarea
              id={`video-caption-textarea-${id}`}
              className="w-full p-2 text-sm border rounded min-h-[60px] resize-none"
              value={caption}
              onChange={handleCaptionChange}
              placeholder={t('flow_builder.add_caption_video', 'Add a caption to your video...')}
            />
          </div>

          <div>
            <p className="text-xs font-medium mb-1">{t('flow_builder.insert_variable_caption', 'Insert Variable in Caption:')}</p>
            <div className="flex flex-wrap gap-1">
              {availableVariables.map((variable) => (
                <button
                  key={variable.name}
                  className="text-xs px-2 py-1 bg-secondary rounded hover:bg-secondary/80"
                  title={variable.description}
                  onClick={() => insertVariable(variable.name)}
                >
                  {variable.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-2">
          <div className="text-xs text-muted-foreground">{t('flow_builder.video_url_label', 'Video URL:')}</div>
          <div className="text-sm p-2 bg-secondary/40 rounded border border-border truncate">
            {videoUrl || t('flow_builder.no_url_provided', 'No URL provided')}
          </div>
          {videoUrl && (
            <button
              className="w-full mt-2 px-3 py-2 text-xs bg-red-100 text-red-700 border border-red-300 rounded hover:bg-red-200 transition-colors flex items-center justify-center gap-2"
              onClick={() => setShowPreview(!showPreview)}
            >
              <FileVideo className="h-3 w-3" />
              {showPreview ? t('flow_builder.hide_preview', 'Hide Preview') : t('flow_builder.preview_video', 'Preview Video')}
            </button>
          )}
          {videoUrl && showPreview && <VideoPreview url={videoUrl} />}

          {caption && (
            <>
              <div className="text-xs text-muted-foreground">{t('flow_builder.caption_label', 'Caption:')}</div>
              <div className="text-sm p-2 bg-secondary/40 rounded border border-border">
                {formatText(caption)}
              </div>
            </>
          )}
        </div>
      )}

      <Handle
        type="target"
        position={Position.Left}
        style={standardHandleStyle}
        isConnectable={isConnectable}
      />
    </div>
  );
}

function AudioNode({ data, isConnectable, id }: any) {
  const { t } = useTranslation();
  const [isEditing, setIsEditing] = useState(false);
  const [audioUrl, setAudioUrl] = useState(data.mediaUrl || "");
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showPreview, setShowPreview] = useState(false);
  const { setNodes } = useReactFlow();
  const flowContext = useFlowContext();

  const updateNodeData = useCallback((updates: any) => {
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              ...updates
            }
          };
        }
        return node;
      })
    );
  }, [id, setNodes]);

  const handleAudioUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newUrl = e.target.value;
    setAudioUrl(newUrl);
    updateNodeData({ mediaUrl: newUrl });
  };





  const handleFileUpload = async (file: File) => {
    if (!file) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'audio');

      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          setUploadProgress(progress);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          const response = JSON.parse(xhr.responseText);
          setAudioUrl(response.url);
          updateNodeData({ mediaUrl: response.url });
          setIsUploading(false);
        } else {
          throw new Error('Upload failed');
        }
      });

      xhr.addEventListener('error', () => {
        throw new Error('Upload failed');
      });

      xhr.open('POST', '/api/upload');
      xhr.send(formData);
    } catch (error) {
      console.error('Error uploading file:', error);
      setIsUploading(false);
    }
  };

  return (
    <div className="node-audio p-3 rounded-lg bg-white border border-border shadow-sm max-w-[250px] relative group">
      {flowContext && (
        <NodeToolbar
          id={id}
          onDuplicate={flowContext.onDuplicateNode}
          onDelete={flowContext.onDeleteNode}
        />
      )}

      <div className="font-medium flex items-center gap-2 mb-2">
        <FileAudio className="h-4 w-4 text-purple-500" />
        <span>{t('flow_builder.send_audio', 'Send Audio')}</span>
        <button
          className="ml-auto text-xs text-muted-foreground hover:text-foreground"
          onClick={() => setIsEditing(!isEditing)}
        >
          {isEditing ? t('common.done', 'Done') : t('common.edit', 'Edit')}
        </button>
      </div>

      {isEditing ? (
        <div className="space-y-2">
          <div>
            <label className="text-xs font-medium mb-1 block">{t('flow_builder.audio_url_label', 'Audio URL:')}</label>
            <input
              className="w-full p-2 text-sm border rounded"
              value={audioUrl}
              onChange={handleAudioUrlChange}
              placeholder={t('flow_builder.enter_audio_url', 'Enter audio URL or path')}
            />
            <div className="mt-2">
              <FileUpload
                onFileSelected={handleFileUpload}
                fileType="audio/*"
                maxSize={30}
                className="w-full"
                showProgress={isUploading}
                progress={uploadProgress}
              />
            </div>
            {audioUrl && (
              <button
                className="w-full mt-2 px-3 py-2 text-xs bg-purple-100 text-purple-700 border border-purple-300 rounded hover:bg-purple-200 transition-colors flex items-center justify-center gap-2"
                onClick={() => setShowPreview(!showPreview)}
              >
                <FileAudio className="h-3 w-3" />
                {showPreview ? t('flow_builder.hide_preview', 'Hide Preview') : t('flow_builder.preview_audio', 'Preview Audio')}
              </button>
            )}
            {audioUrl && showPreview && <AudioPreview url={audioUrl} />}
          </div>
        </div>
      ) : (
        <div className="space-y-2">
          <div className="text-xs text-muted-foreground">{t('flow_builder.audio_url_label', 'Audio URL:')}</div>
          <div className="text-sm p-2 bg-secondary/40 rounded border border-border truncate">
            {audioUrl || t('flow_builder.no_url_provided', 'No URL provided')}
          </div>
          {audioUrl && (
            <button
              className="w-full mt-2 px-3 py-2 text-xs bg-purple-100 text-purple-700 border border-purple-300 rounded hover:bg-purple-200 transition-colors flex items-center justify-center gap-2"
              onClick={() => setShowPreview(!showPreview)}
            >
              <FileAudio className="h-3 w-3" />
              {showPreview ? t('flow_builder.hide_preview', 'Hide Preview') : t('flow_builder.preview_audio', 'Preview Audio')}
            </button>
          )}
          {audioUrl && showPreview && <AudioPreview url={audioUrl} />}
        </div>
      )}

      <Handle
        type="target"
        position={Position.Left}
        style={standardHandleStyle}
        isConnectable={isConnectable}
      />
    </div>
  );
}

function DocumentNode({ data, isConnectable, id }: any) {
  const { t } = useTranslation();
  const [isEditing, setIsEditing] = useState(false);
  const [documentUrl, setDocumentUrl] = useState(data.mediaUrl || "");
  const [fileName, setFileName] = useState(data.fileName || "");
  const [caption, setCaption] = useState(data.caption || t('flow_builder.default_document_caption', 'Check out this document!'));
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showPreview, setShowPreview] = useState(false);
  const { setNodes } = useReactFlow();
  const flowContext = useFlowContext();

  const availableVariables = [
    { name: "contact.name", description: "Contact's name" },
    { name: "contact.phone", description: "Contact's phone number" },
    { name: "date.today", description: "Current date" },
    { name: "time.now", description: "Current time" }
  ];

  const updateNodeData = useCallback((updates: any) => {
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              ...updates
            }
          };
        }
        return node;
      })
    );
  }, [id, setNodes]);

  const handleDocumentUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newUrl = e.target.value;
    setDocumentUrl(newUrl);
    updateNodeData({ mediaUrl: newUrl });
  };



  const handleFileNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setFileName(newName);
    updateNodeData({ fileName: newName });
  };

  const handleCaptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newCaption = e.target.value;
    setCaption(newCaption);
    updateNodeData({ caption: newCaption });
  };

  const insertVariable = (variable: string) => {
    const textArea = document.getElementById(`doc-caption-textarea-${id}`) as HTMLTextAreaElement;
    if (!textArea) return;

    const cursorPos = textArea.selectionStart;
    const variableText = `{{${variable}}}`;
    const newCaption = caption.substring(0, cursorPos) + variableText + caption.substring(cursorPos);

    setCaption(newCaption);
    updateNodeData({ caption: newCaption });

    setTimeout(() => {
      textArea.focus();
      textArea.setSelectionRange(cursorPos + variableText.length, cursorPos + variableText.length);
    }, 0);
  };

  const formatText = (text: string) => {
    const regex = /\{\{([^}]+)\}\}/g;
    if (!regex.test(text)) return text;

    const parts = [];
    let lastIndex = 0;
    let match;

    regex.lastIndex = 0;
    while ((match = regex.exec(text)) !== null) {
      if (match.index > lastIndex) {
        parts.push(text.substring(lastIndex, match.index));
      }

      parts.push(
        <span key={match.index} className="bg-primary/10 text-primary px-1 rounded">
          {match[0]}
        </span>
      );

      lastIndex = match.index + match[0].length;
    }

    if (lastIndex < text.length) {
      parts.push(text.substring(lastIndex));
    }

    return parts;
  };

  const handleFileUpload = async (file: File) => {
    if (!file) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'document');

      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          setUploadProgress(progress);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          const response = JSON.parse(xhr.responseText);
          setDocumentUrl(response.url);
          updateNodeData({ mediaUrl: response.url });

          if (!fileName) {
            const urlParts = response.url.split('/');
            const extractedFileName = urlParts[urlParts.length - 1];
            if (extractedFileName) {
              setFileName(extractedFileName);
              updateNodeData({ fileName: extractedFileName });
            }
          }

          setIsUploading(false);
        } else {
          throw new Error('Upload failed');
        }
      });

      xhr.addEventListener('error', () => {
        throw new Error('Upload failed');
      });

      xhr.open('POST', '/api/upload');
      xhr.send(formData);
    } catch (error) {
      console.error('Error uploading file:', error);
      setIsUploading(false);
    }
  };

  return (
    <div className="node-document p-3 rounded-lg bg-white border border-border shadow-sm max-w-[250px] relative group">
      {flowContext && (
        <NodeToolbar
          id={id}
          onDuplicate={flowContext.onDuplicateNode}
          onDelete={flowContext.onDeleteNode}
        />
      )}

      <div className="font-medium flex items-center gap-2 mb-2">
        <File className="h-4 w-4 text-amber-600" />
        <span>{t('flow_builder.send_document', 'Send Document')}</span>
        <button
          className="ml-auto text-xs text-muted-foreground hover:text-foreground"
          onClick={() => setIsEditing(!isEditing)}
        >
          {isEditing ? t('common.done', 'Done') : t('common.edit', 'Edit')}
        </button>
      </div>

      {isEditing ? (
        <div className="space-y-2">
          <div>
            <label className="text-xs font-medium mb-1 block">{t('flow_builder.document_url_label', 'Document URL:')}</label>
            <input
              className="w-full p-2 text-sm border rounded"
              value={documentUrl}
              onChange={handleDocumentUrlChange}
              placeholder={t('flow_builder.enter_document_url', 'Enter document URL or path')}
            />
            <div className="mt-2">
              <FileUpload
                onFileSelected={handleFileUpload}
                fileType="application/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
                maxSize={30}
                className="w-full"
                showProgress={isUploading}
                progress={uploadProgress}
              />
            </div>
            {documentUrl && (
              <button
                className="w-full mt-2 px-3 py-2 text-xs bg-amber-100 text-amber-700 border border-amber-300 rounded hover:bg-amber-200 transition-colors flex items-center justify-center gap-2"
                onClick={() => setShowPreview(!showPreview)}
              >
                <File className="h-3 w-3" />
                {showPreview ? t('flow_builder.hide_preview', 'Hide Preview') : t('flow_builder.preview_document', 'Preview Document')}
              </button>
            )}
            {documentUrl && showPreview && <DocumentPreview url={documentUrl} fileName={fileName} />}
          </div>

          <div>
            <label className="text-xs font-medium mb-1 block">{t('flow_builder.file_name_optional', 'File Name (optional):')}</label>
            <input
              className="w-full p-2 text-sm border rounded"
              value={fileName}
              onChange={handleFileNameChange}
              placeholder={t('flow_builder.enter_file_name', 'Enter file name (e.g. report.pdf)')}
            />
          </div>

          <div>
            <label className="text-xs font-medium mb-1 block">{t('flow_builder.caption_optional', 'Caption (optional):')}</label>
            <textarea
              id={`doc-caption-textarea-${id}`}
              className="w-full p-2 text-sm border rounded min-h-[60px] resize-none"
              value={caption}
              onChange={handleCaptionChange}
              placeholder={t('flow_builder.add_caption_document', 'Add a caption to your document...')}
            />
          </div>

          <div>
            <p className="text-xs font-medium mb-1">{t('flow_builder.insert_variable_caption', 'Insert Variable in Caption:')}</p>
            <div className="flex flex-wrap gap-1">
              {availableVariables.map((variable) => (
                <button
                  key={variable.name}
                  className="text-xs px-2 py-1 bg-secondary rounded hover:bg-secondary/80"
                  title={variable.description}
                  onClick={() => insertVariable(variable.name)}
                >
                  {variable.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-2">
          <div className="text-xs text-muted-foreground">{t('flow_builder.document_url_label', 'Document URL:')}</div>
          <div className="text-sm p-2 bg-secondary/40 rounded border border-border truncate">
            {documentUrl || t('flow_builder.no_url_provided', 'No URL provided')}
          </div>
          {documentUrl && (
            <button
              className="w-full mt-2 px-3 py-2 text-xs bg-amber-100 text-amber-700 border border-amber-300 rounded hover:bg-amber-200 transition-colors flex items-center justify-center gap-2"
              onClick={() => setShowPreview(!showPreview)}
            >
              <File className="h-3 w-3" />
              {showPreview ? t('flow_builder.hide_preview', 'Hide Preview') : t('flow_builder.preview_document', 'Preview Document')}
            </button>
          )}
          {documentUrl && showPreview && <DocumentPreview url={documentUrl} fileName={fileName} />}

          {fileName && (
            <>
              <div className="text-xs text-muted-foreground">{t('flow_builder.file_name_label', 'File Name:')}</div>
              <div className="text-sm p-2 bg-secondary/40 rounded border border-border truncate">
                {fileName}
              </div>
            </>
          )}

          {caption && (
            <>
              <div className="text-xs text-muted-foreground">{t('flow_builder.caption_label', 'Caption:')}</div>
              <div className="text-sm p-2 bg-secondary/40 rounded border border-border">
                {formatText(caption)}
              </div>
            </>
          )}
        </div>
      )}

      <Handle
        type="target"
        position={Position.Left}
        style={standardHandleStyle}
        isConnectable={isConnectable}
      />
    </div>
  );
}

function TriggerNode({ data, isConnectable, id }: any) {
  const { t } = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);
  const [localConditionType, setLocalConditionType] = useState(data.conditionType || 'any');
  const [localConditionValue, setLocalConditionValue] = useState(data.conditionValue || '');
  const [hardResetKeyword, setHardResetKeyword] = useState(data.hardResetKeyword || '');
  const [hardResetConfirmationMessage, setHardResetConfirmationMessage] = useState(
    data.hardResetConfirmationMessage || 'Bot has been reactivated. Starting fresh conversation...'
  );
  const { setNodes } = useReactFlow();
  const flowContext = useFlowContext();

  const conditionTypes = [
    { value: 'any', label: t('flow_builder.any_message', 'Any Message') },
    { value: 'contains', label: t('flow_builder.contains_word', 'Contains Word') },
    { value: 'exact', label: t('flow_builder.exact_match', 'Exact Match') },
    { value: 'regex', label: t('flow_builder.regex_pattern', 'Regex Pattern') },
    { value: 'media', label: t('flow_builder.has_media', 'Has Media') }
  ];

  const updateNodeData = useCallback((updates: any) => {
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              ...updates
            }
          };
        }
        return node;
      })
    );
  }, [id, setNodes]);

  const handleConditionTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newType = e.target.value;
    setLocalConditionType(newType);
    if (newType === 'media' || localConditionType === 'media') {
      setLocalConditionValue('');
      updateNodeData({ conditionType: newType, conditionValue: '' });
    } else {
      updateNodeData({ conditionType: newType });
    }
  };

  const handleConditionValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLocalConditionValue(newValue);
    updateNodeData({ conditionValue: newValue });
  };

  const handleHardResetKeywordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setHardResetKeyword(newValue);
    updateNodeData({ hardResetKeyword: newValue });
  };

  const handleHardResetConfirmationMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setHardResetConfirmationMessage(newValue);
    updateNodeData({ hardResetConfirmationMessage: newValue });
  };

  return (
    <div className="node-trigger p-3 rounded-lg bg-white border border-border shadow-sm max-w-[250px] group">
      {flowContext && (
        <div className="absolute -top-8 -right-2 bg-background border rounded-md shadow-sm flex z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 text-destructive hover:text-destructive"
                  onClick={() => flowContext.onDeleteNode(id)}
                >
                  <Trash2 className="h-3.5 w-3.5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">
                <p className="text-xs">Delete node</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      )}
      <div className="font-medium flex items-center gap-2 mb-2">
        <MessageSquare className="h-4 w-4 text-green-500" />
        <span>{t('flow_builder.message_received', 'Message Received')}</span>
        <button
          className="ml-auto text-xs text-muted-foreground hover:text-foreground"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? t('common.hide', 'Hide') : t('common.edit', 'Edit')}
        </button>
      </div>


      

      <div className="text-sm p-2 bg-secondary/40 rounded border border-border">
        <div className="text-xs text-muted-foreground mb-1">{t('flow_builder.when', 'When')}</div>

        <div className="flex items-center gap-1">
          <span className="font-medium">{data.channel || "WhatsApp"}</span> {t('flow_builder.message_lowercase', 'message')}
        </div>

        {data.conditionType !== 'any' && (
          <div className="mt-1 text-xs flex flex-wrap gap-1">
            <span>{t('flow_builder.that', 'that')} {getConditionLabel(data.conditionType)}</span>
            {data.conditionValue && (
              <span className="font-medium bg-primary/10 rounded px-1">
                "{data.conditionValue}"
              </span>
            )}
          </div>
        )}

        {data.hardResetKeyword && (
          <div className="mt-1 text-xs flex flex-wrap gap-1">
            <span className="text-orange-600">{t('flow_builder.hard_reset_label', 'Hard Reset')}:</span>
            <span className="font-medium bg-orange-100 text-orange-700 rounded px-1">
              "{data.hardResetKeyword}"
            </span>
          </div>
        )}
      </div>

      {isExpanded && (
        <div className="mt-3 text-xs space-y-2 p-2 border rounded bg-secondary/10">
          <div>
            <label className="block mb-1 font-medium">Condition Type</label>
            <select
              className="w-full p-1 border rounded bg-background text-xs"
              value={localConditionType}
              onChange={handleConditionTypeChange}
            >
              {conditionTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          {localConditionType !== 'any' && localConditionType !== 'media' && (
            <div>
              <label className="block mb-1 font-medium">
                {localConditionType === 'contains' ? 'Word or Phrase' :
                 localConditionType === 'exact' ? 'Exact Text' : 'Pattern'}
              </label>
              <input
                className="w-full p-1 border rounded bg-background text-xs"
                placeholder={getConditionPlaceholder(localConditionType)}
                value={localConditionValue}
                onChange={handleConditionValueChange}
              />
            </div>
          )}

          <div className="border-t pt-2 mt-2">
            <label className="block mb-1 font-medium text-orange-600">
              {t('flow_builder.hard_reset_keyword', 'Hard Reset Keyword')}
            </label>
            <input
              className="w-full p-1 border rounded bg-background text-xs"
              placeholder={t('flow_builder.hard_reset_keyword_placeholder', 'reset, restart, newchat, etc.')}
              value={hardResetKeyword}
              onChange={handleHardResetKeywordChange}
            />
            <div className="text-[9px] text-muted-foreground mt-1">
              {t('flow_builder.hard_reset_keyword_help', 'When bot is disabled, users can type this keyword to re-enable the bot and start fresh')}
            </div>
          </div>

          {hardResetKeyword && (
            <div>
              <label className="block mb-1 font-medium text-orange-600">
                {t('flow_builder.hard_reset_confirmation_message', 'Reset Confirmation Message')}
              </label>
              <textarea
                className="w-full p-1 border rounded bg-background text-xs h-12 resize-none"
                placeholder={t('flow_builder.hard_reset_confirmation_placeholder', 'Bot has been reactivated. Starting fresh conversation...')}
                value={hardResetConfirmationMessage}
                onChange={handleHardResetConfirmationMessageChange}
              />
              <div className="text-[9px] text-muted-foreground mt-1">
                {t('flow_builder.hard_reset_confirmation_help', 'Message sent to user when hard reset is triggered')}
              </div>
            </div>
          )}

          <div className="text-[10px] text-muted-foreground mt-2">
            {t('flow_builder.changes_saved_automatically', 'Changes are saved automatically when you save the flow.')}
          </div>
        </div>
      )}

      <Handle
        type="source"
        position={Position.Bottom}
        style={standardHandleStyle}
        isConnectable={isConnectable}
      />
    </div>
  );
}

function getConditionLabel(conditionType: string): string {
  switch (conditionType) {
    case 'contains': return 'contains';
    case 'exact': return 'exactly matches';
    case 'regex': return 'matches pattern';
    case 'media': return 'has media attachment';
    default: return '';
  }
}

function getConditionPlaceholder(conditionType: string): string {
  switch (conditionType) {
    case 'contains': return 'help, support, etc.';
    case 'exact': return 'Hello world';
    case 'regex': return '\\b\\w+\\b';
    default: return '';
  }
}


function WaitNode({ data, isConnectable, id }: any) {
  const [isEditing, setIsEditing] = useState(false);
  const { setNodes } = useReactFlow();
  const { onDeleteNode, onDuplicateNode } = useFlowContext();

  const [waitMode, setWaitMode] = useState(data.waitMode || 'duration');

  const [timeValue, setTimeValue] = useState(data.timeValue || 5);
  const [timeUnit, setTimeUnit] = useState(data.timeUnit || 'minutes');

  const [waitDate, setWaitDate] = useState<Date | undefined>(
    data.waitDate ? new Date(data.waitDate) : undefined
  );
  const [waitTime, setWaitTime] = useState(data.waitTime || '12:00');
  const [timezone, setTimezone] = useState(data.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone);

  const updateNodeData = useCallback((updates: any) => {
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              ...updates
            }
          };
        }
        return node;
      })
    );
  }, [id, setNodes]);

  const handleModeChange = (newMode: string) => {
    setWaitMode(newMode);
    updateNodeData({ waitMode: newMode });
  };

  const handleTimeValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || 1;
    setTimeValue(value);
    updateNodeData({ timeValue: value });
  };

  const handleTimeUnitChange = (value: string) => {
    setTimeUnit(value);
    updateNodeData({ timeUnit: value });
  };

  const handleDateChange = (date: Date | undefined) => {
    setWaitDate(date);
    updateNodeData({ waitDate: date ? date.toISOString() : null });
  };

  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setWaitTime(e.target.value);
    updateNodeData({ waitTime: e.target.value });
  };

  const handleTimezoneChange = (value: string) => {
    setTimezone(value);
    updateNodeData({ timezone: value });
  };

  const formatDate = (date: Date | undefined): string => {
    if (!date) return 'No date selected';
    return date.toLocaleDateString();
  };

  const getWaitDescription = (): string => {
    if (waitMode === 'duration') {
      return `Wait for ${timeValue} ${timeUnit}`;
    } else {
      if (!waitDate) return 'Schedule not set';
      return `Scheduled for ${formatDate(waitDate)} at ${waitTime} (${timezone.split('/').pop()?.replace('_', ' ') || timezone})`;
    }
  };

  return (
    <div className="node-wait p-3 rounded-lg bg-white border border-border shadow-sm max-w-[250px] group">
      <NodeToolbar id={id} onDuplicate={onDuplicateNode} onDelete={onDeleteNode} />

      <div className="font-medium flex items-center gap-2 mb-2">
        <Clock className="h-4 w-4 text-orange-500" />
        <span>Wait</span>
        <button
          className="ml-auto text-xs text-muted-foreground hover:text-foreground"
          onClick={() => setIsEditing(!isEditing)}
        >
          {isEditing ? 'Done' : 'Edit'}
        </button>
      </div>

      {isEditing ? (
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-2">
            <Button
              type="button"
              size="sm"
              variant={waitMode === 'duration' ? 'default' : 'outline'}
              onClick={() => handleModeChange('duration')}
              className="text-xs"
            >
              <Clock className="h-3 w-3 mr-1" />
              Duration
            </Button>
            <Button
              type="button"
              size="sm"
              variant={waitMode === 'datetime' ? 'default' : 'outline'}
              onClick={() => handleModeChange('datetime')}
              className="text-xs"
            >
              <CalendarClock className="h-3 w-3 mr-1" />
              Schedule
            </Button>
          </div>

          {waitMode === 'duration' ? (
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="text-xs text-muted-foreground">Duration</label>
                <input
                  type="number"
                  min="1"
                  className="w-full p-1 border rounded bg-background text-xs"
                  value={timeValue}
                  onChange={handleTimeValueChange}
                />
              </div>
              <div>
                <label className="text-xs text-muted-foreground">Unit</label>
                <Select value={timeUnit} onValueChange={handleTimeUnitChange}>
                  <SelectTrigger className="w-full h-8 text-xs">
                    <SelectValue placeholder="Select unit" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="seconds">Seconds</SelectItem>
                    <SelectItem value="minutes">Minutes</SelectItem>
                    <SelectItem value="hours">Hours</SelectItem>
                    <SelectItem value="days">Days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div>
                <label className="text-xs text-muted-foreground">Date</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="brand"
                      size="sm"
                      className="w-full justify-start text-left font-normal h-8 text-xs"
                    >
                      <CalendarIcon className="mr-2 h-3 w-3" />
                      {waitDate ? formatDate(waitDate) : "Select date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={waitDate}
                      onSelect={handleDateChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div>
                <label className="text-xs text-muted-foreground">Time</label>
                <input
                  type="time"
                  className="w-full h-8 p-1 border rounded bg-background text-xs"
                  value={waitTime}
                  onChange={handleTimeChange}
                />
              </div>
              <div>
                <label className="text-xs text-muted-foreground">Timezone</label>
                <Select value={timezone} onValueChange={handleTimezoneChange}>
                  <SelectTrigger className="w-full h-8 text-xs">
                    <SelectValue placeholder="Select timezone" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[160px]">
                    <SelectGroup>
                      {Intl.supportedValuesOf("timeZone").map((tz) => (
                        <SelectItem key={tz} value={tz} className="text-xs">
                          {tz.replace('_', ' ').split('/').pop() || tz}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="text-sm p-2 bg-secondary/40 rounded border border-border">
          {getWaitDescription()}
        </div>
      )}

      <Handle
        type="target"
        position={Position.Top}
        style={standardHandleStyle}
        isConnectable={isConnectable}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        style={standardHandleStyle}
        isConnectable={isConnectable}
      />
    </div>
  );
}

interface QuickReplyOption {
  text: string;
  value: string;
}

function QuickReplyNode({ data, isConnectable, id }: any) {
  const [isEditing, setIsEditing] = useState(false);
  const [message, setMessage] = useState(data.message || "Please select an option to continue:");
  const [options, setOptions] = useState<QuickReplyOption[]>(
    data.options || [
      { text: "I have a question about my order.", value: "order" },
      { text: "I have a question about a product.", value: "product" },
      { text: "I have another question.", value: "other" }
    ]
  );
  const { setNodes } = useReactFlow();
  const flowContext = useFlowContext();

  const availableVariables = [
    { name: "contact.name", description: "Contact's name" },
    { name: "contact.phone", description: "Contact's phone number" },
    { name: "date.today", description: "Current date" },
    { name: "time.now", description: "Current time" }
  ];

  const updateNodeData = useCallback((updates: any) => {
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              ...updates
            }
          };
        }
        return node;
      })
    );
  }, [id, setNodes]);

  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newMessage = e.target.value;
    setMessage(newMessage);
    updateNodeData({ message: newMessage });
  };

  const handleOptionTextChange = (index: number, text: string) => {
    const newOptions = [...options];
    newOptions[index] = { ...newOptions[index], text };
    setOptions(newOptions);
    updateNodeData({ options: newOptions });
  };

  const handleOptionValueChange = (index: number, value: string) => {
    const newOptions = [...options];
    newOptions[index] = { ...newOptions[index], value };
    setOptions(newOptions);
    updateNodeData({ options: newOptions });
  };

  const addOption = () => {
    const newOptions = [...options, { text: "New option", value: `option${options.length + 1}` }];
    setOptions(newOptions);
    updateNodeData({ options: newOptions });
  };

  const removeOption = (index: number) => {
    const newOptions = options.filter((_, i) => i !== index);
    setOptions(newOptions);
    updateNodeData({ options: newOptions });
  };

  const insertVariable = (variable: string) => {
    const textArea = document.getElementById(`quickreply-textarea-${id}`) as HTMLTextAreaElement;
    if (!textArea) return;

    const cursorPos = textArea.selectionStart;
    const variableText = `{{${variable}}}`;
    const newMessage = message.substring(0, cursorPos) + variableText + message.substring(cursorPos);

    setMessage(newMessage);
    updateNodeData({ message: newMessage });

    setTimeout(() => {
      textArea.focus();
      textArea.setSelectionRange(cursorPos + variableText.length, cursorPos + variableText.length);
    }, 0);
  };

  const formatMessage = (text: string) => {
    const regex = /\{\{([^}]+)\}\}/g;

    if (!regex.test(text)) {
      return text;
    }

    const parts = [];
    let lastIndex = 0;
    let match;

    regex.lastIndex = 0;
    while ((match = regex.exec(text)) !== null) {
      if (match.index > lastIndex) {
        parts.push(text.substring(lastIndex, match.index));
      }

      parts.push(
        <span key={match.index} className="bg-primary/10 text-primary px-1 rounded">
          {match[0]}
        </span>
      );

      lastIndex = match.index + match[0].length;
    }

    if (lastIndex < text.length) {
      parts.push(text.substring(lastIndex));
    }

    return parts;
  };

  return (
    <div className="node-quickreply p-3 rounded-lg bg-white border border-border shadow-sm max-w-[280px] group">
      {flowContext && (
        <NodeToolbar
          id={id}
          onDuplicate={flowContext.onDuplicateNode}
          onDelete={flowContext.onDeleteNode}
        />
      )}

      <div className="font-medium flex items-center gap-2 mb-2">
        <ListOrdered className="h-4 w-4 text-blue-500" />
        <span>Quick Reply Options</span>
        <button
          className="ml-auto text-xs text-muted-foreground hover:text-foreground"
          onClick={() => setIsEditing(!isEditing)}
        >
          {isEditing ? 'Done' : 'Edit'}
        </button>
      </div>

      {isEditing ? (
        <div className="space-y-3">
          <div>
            <label className="text-xs font-medium mb-1 block">Message:</label>
            <textarea
              id={`quickreply-textarea-${id}`}
              className="w-full p-2 text-sm border rounded min-h-[80px] resize-none"
              value={message}
              onChange={handleMessageChange}
              placeholder="Type your message here..."
            />

            <div className="mt-2">
              <p className="text-xs font-medium mb-1">Insert Variable:</p>
              <div className="flex flex-wrap gap-1">
                {availableVariables.map((variable) => (
                  <button
                    key={variable.name}
                    className="text-xs px-2 py-1 bg-secondary rounded hover:bg-secondary/80"
                    title={variable.description}
                    onClick={() => insertVariable(variable.name)}
                  >
                    {variable.name}
                  </button>
                ))}
              </div>
            </div>
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-xs font-medium">Options:</label>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 text-xs"
                onClick={addOption}
                disabled={options.length >= 10}
              >
                <Plus className="h-3 w-3 mr-1" />
                Add Option
              </Button>
            </div>

            <div className="space-y-4 max-h-[300px] overflow-y-auto pr-1">
              {options.map((option, index) => (
                <div key={index} className="space-y-2 relative">
                  <div className="flex items-center gap-2">
                    <div className="flex-shrink-0 w-6 h-6 rounded-md bg-blue-500 text-white flex items-center justify-center text-xs font-medium">
                      {index + 1}
                    </div>
                    <div className="flex-1 font-medium text-xs">Option {index + 1}</div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-7 w-7 text-muted-foreground hover:text-destructive"
                      onClick={() => removeOption(index)}
                      disabled={options.length <= 1}
                    >
                      <X className="h-3.5 w-3.5" />
                    </Button>
                  </div>

                  <div className="pl-8 space-y-2">
                    <div>
                      <label className="text-xs text-muted-foreground block mb-1">Display Text:</label>
                      <input
                        className="w-full p-2 text-sm border rounded"
                        value={option.text}
                        onChange={(e) => handleOptionTextChange(index, e.target.value)}
                        placeholder="Text to display"
                      />
                    </div>

                    <div>
                      <label className="text-xs text-muted-foreground block mb-1">Response Value:</label>
                      <input
                        className="w-full p-2 text-sm border rounded"
                        value={option.value}
                        onChange={(e) => handleOptionValueChange(index, e.target.value)}
                        placeholder="Value to match"
                      />
                      <div className="text-[10px] text-muted-foreground mt-1">
                        User can respond with this value to select this option
                      </div>
                    </div>
                  </div>

                  <Handle
                    type="source"
                    position={Position.Right}
                    id={`option-${index + 1}`}
                    style={{
                      ...standardHandleStyle,
                      top: '30px',
                      right: '-12px'
                    }}
                    isConnectable={isConnectable}
                  />
                </div>
              ))}
            </div>

            <div className="text-[10px] text-muted-foreground mt-2">
              Each option will have its own output connection.
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-2">
          <div className="text-sm p-2 bg-secondary/40 rounded border border-border">
            {formatMessage(message)}
          </div>

          <div className="space-y-1.5 mt-3">
            {options.map((option, index) => (
              <div key={index} className="flex items-center gap-2 relative">
                <div className="flex-shrink-0 w-6 h-6 rounded-md bg-blue-500 text-white flex items-center justify-center text-xs font-medium">
                  {index + 1}
                </div>
                <div className="text-sm flex-1 pr-6">
                  <div>{option.text}</div>
                  <div className="text-xs text-muted-foreground">
                    Responds to: "{option.value}"
                  </div>
                </div>
                <Handle
                  type="source"
                  position={Position.Right}
                  id={`option-${index + 1}`}
                  style={{
                    ...standardHandleStyle,
                    top: '50%',
                    right: '-12px'
                  }}
                  isConnectable={isConnectable}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      <Handle
        type="target"
        position={Position.Left}
        style={standardHandleStyle}
        isConnectable={isConnectable}
      />
    </div>
  );
}

const nodeTypes: NodeTypes = {
  message: MessageNode,
  condition: ConditionNode,
  trigger: TriggerNode,
  image: ImageNode,
  video: VideoNode,
  audio: AudioNode,
  document: DocumentNode,
  wait: WaitNode,
  quickreply: QuickReplyNode,
  follow_up: FollowUpNode,

  ai_assistant: AIAssistantNode,
  translation: TranslationNode,
  update_pipeline_stage: UpdatePipelineStageNode,
  webhook: WebhookNode,
  http_request: HTTPRequestNode,
  shopify: ShopifyNode,
  woocommerce: WooCommerceNode,
  typebot: TypebotNode,
  flowise: FlowiseNode,
  n8n: N8nNode,
  google_sheets: GoogleSheetsNode,
  documind: DocumindNode,
  bot_disable: BotDisableNode,
  bot_reset: BotResetNode
};

const CustomEdge = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
  selected,
}: EdgeProps) => {
  const { setEdges } = useReactFlow();
  const [edgePath, labelX, labelY] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  const handleDelete = () => {
    setEdges((edges) => edges.filter((edge) => edge.id !== id));
  };

  return (
    <>
      <BaseEdge path={edgePath} markerEnd={markerEnd} style={style} />
      {selected && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              fontSize: 12,
              pointerEvents: 'all',
            }}
            className="nodrag nopan"
          >
            <button
              className="flex items-center justify-center w-6 h-6 rounded-full bg-white border border-red-500 text-red-500 hover:bg-red-50 transition-colors"
              onClick={handleDelete}
            >
              <Trash2 size={12} />
            </button>
          </div>
        </EdgeLabelRenderer>
      )}
    </>
  );
};

const edgeTypes = {
  custom: CustomEdge,
  smoothstep: CustomEdge
};

function NodeSelector({ onAdd, nodes }: { onAdd: (type: string) => void; nodes: Node[] }) {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');

  const hasTypebotNode = nodes.some(node => node.type === 'typebot');
  const hasFlowiseNode = nodes.some(node => node.type === 'flowise');
  const hasN8nNode = nodes.some(node => node.type === 'n8n');
  const hasGoogleSheetsNode = nodes.some(node => node.type === 'google_sheets');
  const hasDocumindNode = nodes.some(node => node.type === 'documind');



  const getSingletonNodeState = (nodeType: string) => {
    switch (nodeType) {
      
      case 'typebot':
        return {
          disabled: hasTypebotNode,
          tooltip: hasTypebotNode ? t('flow_builder.typebot_node_exists', 'Only one Typebot node allowed per flow') : ''
        };
      case 'flowise':
        return {
          disabled: hasFlowiseNode,
          tooltip: hasFlowiseNode ? t('flow_builder.flowise_node_exists', 'Only one Flowise node allowed per flow') : ''
        };
      case 'n8n':
        return {
          disabled: hasN8nNode,
          tooltip: hasN8nNode ? t('flow_builder.n8n_node_exists', 'Only one n8n node allowed per flow') : ''
        };
      case 'documind':
        return {
          disabled: hasDocumindNode,
          tooltip: hasDocumindNode ? t('flow_builder.documind_node_exists', 'Only one Documind node allowed per flow') : ''
        };

      default:
        return { disabled: false, tooltip: '' };
    }
  };

  const allNodes = [
    {
      type: 'trigger',
      name: 'WhatsApp Message',
      section: 'Triggers',
      icon: MessageSquare,
      color: 'text-green-500',
      ...getSingletonNodeState('trigger')
    },

    { type: 'message', name: 'Text Message', section: 'Messages', icon: MessageSquare, color: 'text-secondry', disabled: false },
    { type: 'quickreply', name: 'Quick Reply Options', section: 'Messages', icon: ListOrdered, color: 'text-blue-500', disabled: false },
    { type: 'follow_up', name: 'Follow-up Message', section: 'Messages', icon: Clock, color: 'text-orange-500', disabled: false },
    { type: 'image', name: 'Image Message', section: 'Messages', icon: Image, color: 'text-blue-500', disabled: false },
    { type: 'video', name: 'Video Message', section: 'Messages', icon: FileVideo, color: 'text-red-500', disabled: false },
    { type: 'audio', name: 'Audio Message', section: 'Messages', icon: FileAudio, color: 'text-purple-500', disabled: false },
    { type: 'document', name: 'Document Message', section: 'Messages', icon: File, color: 'text-amber-600', disabled: false },


    
    { type: 'condition', name: 'Condition', section: 'Flow Control', icon: AlertCircle, color: 'text-amber-500', disabled: false },
    { type: 'wait', name: 'Wait', section: 'Flow Control', icon: Clock, color: 'text-orange-500', disabled: false },
    { type: 'ai_assistant', name: 'AI Assistant', section: 'Flow Control', icon: ({ className }: { className?: string }) => <BotIcon className={className} size={16} />, color: 'text-violet-500', ...getSingletonNodeState('ai_assistant') },
    { type: 'translation', name: 'Translation', section: 'Flow Control', icon: Languages, color: 'text-blue-600', disabled: false },
    { type: 'update_pipeline_stage', name: 'Pipeline', section: 'Flow Control', icon: ArrowRightCircle, color: 'text-teal-500', disabled: false },
    { type: 'bot_disable', name: 'Agent Handoff', section: 'Flow Control', icon: UserCheck, color: 'text-orange-600', disabled: false },
    { type: 'n8n', name: 'n8n', section: 'Integrations', icon: Workflow, color: 'text-orange-600', ...getSingletonNodeState('n8n') },
    { type: 'http_request', name: 'HTTP Request', section: 'Integrations', icon: Network, color: 'text-purple-500', disabled: false },
    { type: 'google_sheets', name: 'Google Sheets', section: 'Integrations', icon: Sheet, color: 'text-green-600', ...getSingletonNodeState('google_sheets') },
    { type: 'webhook', name: 'Webhook', section: 'Integrations', icon: Globe, color: 'text-blue-500', disabled: false },
    { type: 'shopify', name: 'Shopify', section: 'Integrations', icon: ShoppingBag, color: 'text-green-500', disabled: false },
    { type: 'woocommerce', name: 'WooCommerce', section: 'Integrations', icon: ShoppingCart, color: 'text-purple-500', disabled: false },
    { type: 'typebot', name: 'Typebot', section: 'Integrations', icon: MessageCircle, color: 'text-blue-600', ...getSingletonNodeState('typebot') },
    { type: 'flowise', name: 'Flowise', section: 'Integrations', icon: Brain, color: 'text-purple-600', ...getSingletonNodeState('flowise') },
    { type: 'documind', name: 'Documind PDF Chat', section: 'Integrations', icon: FileText, color: 'text-orange-600', ...getSingletonNodeState('documind') },
  ];

  const filteredNodes = searchTerm.trim() === '' ? allNodes : allNodes.filter(node => {
    const searchLower = searchTerm.toLowerCase();
    return (
      node.name.toLowerCase().includes(searchLower) ||
      node.section.toLowerCase().includes(searchLower) ||
      node.type.toLowerCase().includes(searchLower)
    );
  });

  const groupedNodes = filteredNodes.reduce((acc, node) => {
    if (!acc[node.section]) {
      acc[node.section] = [];
    }
    acc[node.section].push(node);
    return acc;
  }, {} as Record<string, typeof allNodes>);


  const clearSearch = () => {
    setSearchTerm('');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      clearSearch();
    } else if (e.key === 'Enter' && filteredNodes.length === 1) {
      onAdd(filteredNodes[0].type);
    }
  };


  return (
    <div className="w-full flex flex-col h-full">
      <div className="relative mb-3 flex-shrink-0">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          type="text"
          placeholder={t('flow_builder.search_nodes', 'Search nodes...')}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onKeyDown={handleKeyDown}
          className="pl-10 pr-10 h-9 text-sm"
        />
        {searchTerm && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-muted"
            onClick={clearSearch}
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>

      <h3 className="font-medium mb-3 flex-shrink-0">{t('flow_builder.add_node', 'Add Node')}</h3>

      <div
        className="flex-1 overflow-y-auto custom-scrollbar"
        style={{
          scrollbarWidth: 'thin',
          scrollbarColor: '#cbd5e1 #f1f5f9',
          maxHeight: 'calc(100vh - 200px)',
          overflowY: 'auto',
          paddingRight: '4px'
        }}
      >
        {searchTerm && filteredNodes.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">{t('flow_builder.no_nodes_found', 'No nodes found')}</p>
            <p className="text-xs">{t('flow_builder.try_different_search', 'Try a different search term')}</p>
          </div>
        )}

        {filteredNodes.length > 0 && (
          <div className="grid gap-4 pr-2">
            {Object.entries(groupedNodes).map(([sectionName, sectionNodes]) => (
              <div key={sectionName}>
                <h4 className="text-xs font-medium text-muted-foreground mb-2">{sectionName}</h4>

                <div className={sectionName === 'Triggers' ? 'w-full' : 'grid gap-2'}>
                  {sectionNodes.map((node) => {
                    const IconComponent = node.icon;
                    const getTooltipText = () => {
                      if ((node as any).tooltip) return (node as any).tooltip;
                      return "";
                    };

                    return (
                      <Button
                        key={node.type}
                        variant="outline"
                        className={`${sectionName === 'Triggers' ? 'justify-start w-full' : 'justify-start'} ${
                          node.disabled ? 'opacity-50 cursor-not-allowed' : ''
                        }`}
                        onClick={() => onAdd(node.type)}
                        disabled={node.disabled}
                        title={getTooltipText()}
                      >
                        <IconComponent className={`h-4 w-4 mr-2 ${node.disabled ? 'text-muted-foreground' : node.color}`} />
                        {node.name}
                      </Button>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

function FlowEditor() {
  const { t } = useTranslation();
  const [match, params] = useRoute('/flows/:id');
  const flowId = match ? parseInt(params.id) : null;
  const isEditMode = flowId !== null;

  const { toast } = useToast();
  const [, navigate] = useLocation();
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const reactFlowInstance = useReactFlow();

  const initialNodes: Node[] = !isEditMode ? [
    {
      id: 'trigger-node',
      type: 'trigger',
      position: { x: 250, y: 50 },
      data: {
        label: 'WhatsApp Message Trigger',
        channel: 'WhatsApp',
        conditionType: 'any',
        conditionValue: ''
      }
    }
  ] : [];

  const initialEdges: Edge[] = [];

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const [isAutoArranging, setIsAutoArranging] = useState(false);
  const [previousNodePositions, setPreviousNodePositions] = useState<Node[]>([]);

  const { data: flowData, isLoading: isLoadingFlow } = useQuery({
    queryKey: ['/api/flows', flowId],
    queryFn: async () => {
      if (!flowId) return null;
      const res = await fetch(`/api/flows/${flowId}`);
      if (!res.ok) throw new Error('Failed to load flow');
      return res.json();
    },
    enabled: isEditMode
  });

  useEffect(() => {
    if (flowData) {
      setName(flowData.name);
      try {
        const parsedNodes = typeof flowData.nodes === 'string'
          ? JSON.parse(flowData.nodes)
          : flowData.nodes;

        const parsedEdges = typeof flowData.edges === 'string'
          ? JSON.parse(flowData.edges)
          : flowData.edges;

        setNodes(parsedNodes || []);
        setEdges(parsedEdges || []);
      } catch (error) {
        toast({
          title: t('flow_builder.error_loading_flow', 'Error loading flow'),
          description: t('flow_builder.could_not_parse_flow_data', 'Could not parse flow data'),
          variant: 'destructive'
        });
      }
    }
  }, [flowData, setNodes, setEdges, toast]);

  const createFlowMutation = useMutation({
    mutationFn: async (flowData: any) => {
      const response = await apiRequest('POST', '/api/flows', flowData);
      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/flows'] });
      navigate(`/flows/${data.id}`);
      toast({
        title: t('flow_builder.flow_created', 'Flow created'),
        description: t('flow_builder.flow_created_successfully', 'Your flow has been created successfully.')
      });
    },
    onError: (error: any) => {
      toast({
        title: t('flow_builder.error_creating_flow', 'Error creating flow'),
        description: error.message || t('flow_builder.something_went_wrong', 'Something went wrong'),
        variant: 'destructive'
      });
    }
  });

  const updateFlowMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: any }) => {
      const response = await apiRequest('PATCH', `/api/flows/${id}`, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/flows', flowId] });
      toast({
        title: t('flow_builder.flow_updated', 'Flow updated'),
        description: t('flow_builder.flow_updated_successfully', 'Your flow has been updated successfully.')
      });
    },
    onError: (error: any) => {
      toast({
        title: t('flow_builder.error_updating_flow', 'Error updating flow'),
        description: error.message || t('flow_builder.something_went_wrong', 'Something went wrong'),
        variant: 'destructive'
      });
    }
  });

  const onConnect = useCallback(
    (connection: Connection) => {
      setEdges((eds) => addEdge({
        ...connection,
        animated: true,
        type: 'smoothstep'
      }, eds));
    },
    [setEdges]
  );

  const onDeleteNode = useCallback(
    (nodeId: string) => {
      setNodes((nds) => nds.filter((node) => node.id !== nodeId));

      setEdges((eds) => eds.filter(
        (edge) => edge.source !== nodeId && edge.target !== nodeId
      ));

      toast({
        title: t('flow_builder.node_deleted', 'Node deleted'),
        description: t('flow_builder.node_connections_removed', 'Node and its connections have been removed.')
      });
    },
    [setNodes, setEdges, toast]
  );

  const onDuplicateNode = useCallback(
    (nodeId: string) => {
      const nodeToDuplicate = nodes.find((node) => node.id === nodeId);
      if (!nodeToDuplicate) return;

      const singletonNodes = [ 'typebot', 'flowise'];
      if (singletonNodes.includes(nodeToDuplicate.type || '')) {
        const nodeTypeNames: Record<string, string> = {
          typebot: t('flow_builder.typebot', 'Typebot'),
          flowise: t('flow_builder.flowise', 'Flowise')
        };

        const nodeTypeName = nodeTypeNames[nodeToDuplicate.type || ''] || 'This';

        toast({
          title: t('flow_builder.cannot_duplicate_singleton', 'Cannot Duplicate Node'),
          description: t('flow_builder.singleton_node_unique', `${nodeTypeName} nodes cannot be duplicated. Only one instance is allowed per flow.`),
          variant: 'destructive'
        });
        return;
      }

      const newNodeId = `node_${nanoid()}`;

      const duplicateNode: Node = {
        ...nodeToDuplicate,
        id: newNodeId,
        position: {
          x: nodeToDuplicate.position.x + 30,
          y: nodeToDuplicate.position.y + 30
        }
      };

      setNodes((nds) => nds.concat(duplicateNode));

      toast({
        title: t('flow_builder.node_duplicated', 'Node duplicated'),
        description: t('flow_builder.node_copy_created', 'A copy of the node has been created.')
      });
    },
    [nodes, setNodes, toast]
  );

  const onAddNode = useCallback(
    (type: string) => {
      if (!reactFlowWrapper.current) return;

      const singletonNodes = [ 'typebot', 'flowise'];
      if (singletonNodes.includes(type)) {
        const existingNode = nodes.find(node => node.type === type);
        if (existingNode) {
          const nodeTypeNames: Record<string, string> = {
            typebot: t('flow_builder.typebot', 'Typebot'),
            flowise: t('flow_builder.flowise', 'Flowise')
          };

          const nodeTypeName = nodeTypeNames[type] || 'This';

          toast({
            title: t('flow_builder.singleton_node_exists_title', `${nodeTypeName} Already Exists`),
            description: t('flow_builder.singleton_node_exists_description', `Only one ${nodeTypeName} node is allowed per flow. Please use the existing node.`),
            variant: 'destructive'
          });
          return;
        }
      }

      const triggerNode = nodes.find(node => node.type === 'trigger');
      const triggerNodeId = triggerNode?.id || '';
      const hasExistingConnection = edges.some(edge => edge.source === triggerNodeId);

      const newNodeId = `node_${nanoid()}`;

      let nodeData: any = { label: `${type.charAt(0).toUpperCase() + type.slice(1)} Node` };

      switch (type) {
        case 'message':
          nodeData = { ...nodeData, message: 'Hello! How can I help you?' };
          break;
        case 'quickreply':
          nodeData = {
            ...nodeData,
            message: 'Please select an option to continue:',
            options: [
              'I have a question about my order.',
              'I have a question about a product.',
              'I have another question.'
            ]
          };
          break;
        case 'follow_up':
          nodeData = {
            ...nodeData,
            messageType: 'text',
            messageContent: 'Thank you for your interest! How can we help you further?',
            mediaUrl: '',
            caption: '',
            templateId: undefined,
            triggerEvent: 'conversation_start',
            delayAmount: 24,
            delayUnit: 'hours',
            specificDatetime: '',
            maxRetries: 3
          };
          break;
        case 'condition':
          nodeData = { ...nodeData, condition: "Contains('help')" };
          break;
        case 'action':
          nodeData = { ...nodeData, action: 'Create ticket' };
          break;
        case 'image':
          nodeData = { ...nodeData, mediaUrl: '', caption: t('flow_builder.default_image_caption', 'Check out this image!') };
          break;
        case 'video':
          nodeData = { ...nodeData, mediaUrl: '', caption: t('flow_builder.default_video_caption', 'Watch this video!') };
          break;
        case 'audio':
          nodeData = { ...nodeData, mediaUrl: '', caption: t('flow_builder.default_audio_caption', 'Listen to this audio!') };
          break;
        case 'document':
          nodeData = { ...nodeData, mediaUrl: '', fileName: 'document.pdf', caption: t('flow_builder.default_document_caption_full', 'Here is the document you requested.') };
          break;
        case 'wait':
          nodeData = { ...nodeData, timeValue: 5, timeUnit: 'minutes' };
          break;

        case 'ai_assistant':
          nodeData = {
            ...nodeData,
            provider: 'openai',
            model: 'gpt-4o',
            apiKey: '',
            prompt: 'You are a helpful assistant. Answer user questions concisely and accurately.\n\nWhen users request calendar-related tasks, you can:\n- Book appointments and meetings\n- Check availability for scheduling\n- Update or modify appointments\n- Cancel appointments when needed\n\nFor appointment booking:\n1. First check availability using the check_availability function\n2. Collect necessary details (title, date, time, attendees, location)\n3. Confirm all details with the user before booking\n4. Use book_appointment function to create the calendar event\n5. Provide confirmation with event details\n\nAlways be professional and ensure you have all required information before making calendar changes. Also make sure to ask the user about their email if they wish to know the previous appointments. So that we can fetch the previous appointments from the  calendar. Also make sure to not share any sensitive information with the user like appointemnts made by other users etc. Only give info to the user if they are the owner of the appointment.',
            enableHistory: true,
            enableAudio: false,
            enableTaskExecution: false,
            tasks: [],
            enableGoogleCalendar: false,
            calendarBusinessHours: { start: '09:00', end: '17:00' },
            calendarDefaultDuration: 60,

            calendarTimeZone: getBrowserTimezone(),
            calendarFunctions: [],
            onDeleteNode: onDeleteNode,
            onDuplicateNode: onDuplicateNode
          };
          break;
        case 'update_pipeline_stage':
          nodeData = {
            ...nodeData,
            stageId: null,
            dealIdVariable: "{{contact.id}}",
            type: "update_pipeline_stage"
          };
          break;
        case 'webhook':
          nodeData = {
            ...nodeData,
            url: '',
            method: 'POST',
            headers: [],
            body: '{"message": "{{message.content}}", "contact": "{{contact.name}}"}',
            authType: 'none',
            authToken: '',
            authUsername: '',
            authPassword: '',
            authApiKey: '',
            authApiKeyHeader: 'X-API-Key',
            timeout: 30,
            followRedirects: true,
            onDeleteNode: onDeleteNode,
            onDuplicateNode: onDuplicateNode
          };
          break;
        case 'http_request':
          nodeData = {
            ...nodeData,
            url: '',
            method: 'GET',
            headers: [{ key: 'Accept', value: 'application/json' }],
            body: '',
            authType: 'none',
            authToken: '',
            authUsername: '',
            authPassword: '',
            authApiKey: '',
            authApiKeyHeader: 'X-API-Key',
            timeout: 30,
            followRedirects: true,
            responseType: 'auto',
            retryCount: 0,
            retryDelay: 1000,
            variableMappings: [],
            onDeleteNode: onDeleteNode,
            onDuplicateNode: onDuplicateNode
          };
          break;
        case 'shopify':
          nodeData = {
            ...nodeData,
            shopDomain: '',
            apiKey: '',
            apiPassword: '',
            resource: 'orders',
            action: 'get',
            filters: { limit: '10' },
            variableMappings: [],
            rateLimitDelay: 500,
            onDeleteNode: onDeleteNode,
            onDuplicateNode: onDuplicateNode
          };
          break;
        case 'woocommerce':
          nodeData = {
            ...nodeData,
            siteUrl: '',
            consumerKey: '',
            consumerSecret: '',
            resource: 'orders',
            action: 'get',
            filters: { per_page: '10' },
            variableMappings: [],
            apiVersion: 'v3',
            onDeleteNode: onDeleteNode,
            onDuplicateNode: onDuplicateNode
          };
          break;
        case 'typebot':
          nodeData = {
            ...nodeData,
            apiToken: '',
            workspaceId: '',
            typebotId: '',
            botName: '',
            operation: 'start_conversation',
            config: {},
            variableMappings: [],
            sessionTimeout: 3600,
            onDeleteNode: onDeleteNode,
            onDuplicateNode: onDuplicateNode
          };
          break;
        case 'flowise':
          nodeData = {
            ...nodeData,
            instanceUrl: '',
            apiKey: '',
            chatflowId: '',
            chatflowName: '',
            operation: 'start_chatflow',
            config: {},
            variableMappings: [],
            sessionTimeout: 3600,
            onDeleteNode: onDeleteNode,
            onDuplicateNode: onDuplicateNode
          };
          break;
        case 'n8n':
          nodeData = {
            ...nodeData,
            instanceUrl: '',
            apiKey: '',
            webhookUrl: '',
            workflowId: '',
            workflowName: '',
            operation: 'webhook_trigger',
            config: {},
            variableMappings: [],
            timeout: 30,
            onDeleteNode: onDeleteNode,
            onDuplicateNode: onDuplicateNode
          };
          break;
        case 'bot_disable':
          nodeData = {
            ...nodeData,
            disableDuration: '30',
            customDuration: 60,
            customDurationUnit: 'minutes',
            triggerMethod: 'always',
            keyword: 'agent',
            caseSensitive: false,
            assignToAgent: 'auto',
            notifyAgent: true,
            handoffMessage: 'A customer is requesting human assistance.'
          };
          break;
        case 'translation':
          nodeData = {
            ...nodeData,
            enabled: true,
            apiKey: '',
            targetLanguage: 'en',
            translationMode: 'separate',
            detectLanguage: true,
            onDeleteNode: onDeleteNode,
            onDuplicateNode: onDuplicateNode
          };
          break;
        case 'bot_reset':
          nodeData = {
            ...nodeData,
            resetScope: 'bot_only',
            confirmationMessage: 'Bot assistance has been re-enabled. How can I help you?',
            sendConfirmation: true,
            clearVariables: false,
            resetFlowPosition: false,
            notifyAgent: true,
            autoReassign: false
          };
          break;
      }

      const newNode: Node = {
        id: newNodeId,
        type,
        position: {
          x: triggerNode ? triggerNode.position.x : 250,
          y: triggerNode ? triggerNode.position.y + 150 : 150
        },
        data: nodeData
      };

      setNodes((nds) => nds.concat(newNode));

      if (triggerNode && !hasExistingConnection && type !== 'trigger') {
        const newEdge: Edge = {
          id: `edge-${triggerNodeId}-${newNodeId}`,
          source: triggerNodeId,
          target: newNodeId,
          animated: true,
          type: 'smoothstep'
        };

        setEdges((eds) => eds.concat(newEdge));
      }

      setTimeout(() => {
        try {
          reactFlowInstance.fitView({
            nodes: [{ id: newNodeId }],
            duration: 800,
            padding: 0.3,
            maxZoom: 1.2,
            minZoom: 0.5
          });
        } catch (error) {
          
        }
      }, 100);
    },
    [nodes, edges, setNodes, setEdges, reactFlowInstance]
  );

  const autoArrangeNodes = useCallback(() => {
    if (nodes.length === 0) return;

    setPreviousNodePositions([...nodes]);
    setIsAutoArranging(true);

    const nodeCount = nodes.length;

    // Use generous spacing to ensure no overlaps
    const baseHorizontalSpacing = 400; 
    const baseVerticalSpacing = 200;   

    // For large flows, still maintain good spacing
    const horizontalSpacing = nodeCount > 20 ? baseHorizontalSpacing * 0.9 : baseHorizontalSpacing;
    const verticalSpacing = nodeCount > 20 ? baseVerticalSpacing * 0.9 : baseVerticalSpacing;

    const startX = 100;
    const startY = 100;

    const triggerNode = nodes.find(node => node.type === 'trigger');

    const nodeConnections = new Map<string, string[]>();
    const incomingConnections = new Map<string, string[]>();

    nodes.forEach(node => {
      nodeConnections.set(node.id, []);
      incomingConnections.set(node.id, []);
    });

    edges.forEach(edge => {
      const sourceConnections = nodeConnections.get(edge.source) || [];
      sourceConnections.push(edge.target);
      nodeConnections.set(edge.source, sourceConnections);

      const targetIncoming = incomingConnections.get(edge.target) || [];
      targetIncoming.push(edge.source);
      incomingConnections.set(edge.target, targetIncoming);
    });

    const levels = new Map<string, number>();
    const nodesAtLevel = new Map<number, string[]>();
    const visited = new Set<string>();

    const assignLevels = (startNodeId: string) => {
      const queue: Array<{nodeId: string, level: number}> = [{nodeId: startNodeId, level: 0}];

      while (queue.length > 0) {
        const {nodeId, level} = queue.shift()!;

        if (visited.has(nodeId)) continue;
        visited.add(nodeId);

        levels.set(nodeId, level);

        if (!nodesAtLevel.has(level)) {
          nodesAtLevel.set(level, []);
        }
        nodesAtLevel.get(level)!.push(nodeId);

        const connections = nodeConnections.get(nodeId) || [];
        connections.forEach(targetId => {
          if (!visited.has(targetId)) {
            queue.push({nodeId: targetId, level: level + 1});
          }
        });
      }
    };

    const startNode = triggerNode || nodes[0];
    assignLevels(startNode.id);

    let disconnectedLevel = 0;
    nodes.forEach(node => {
      if (!visited.has(node.id)) {
        const hasOutgoing = (nodeConnections.get(node.id) || []).length > 0;
        const hasIncoming = (incomingConnections.get(node.id) || []).length > 0;

        let nodeLevel = 0;
        if (!hasIncoming && hasOutgoing) {
          nodeLevel = 0;
        } else if (hasIncoming && !hasOutgoing) {
          nodeLevel = Math.max(...Array.from(levels.values())) + 1;
        } else {
          nodeLevel = disconnectedLevel;
          disconnectedLevel++;
        }

        levels.set(node.id, nodeLevel);
        if (!nodesAtLevel.has(nodeLevel)) {
          nodesAtLevel.set(nodeLevel, []);
        }
        nodesAtLevel.get(nodeLevel)!.push(node.id);
      }
    });

    // Debug: Log level assignments
    console.log('Level assignments:', Object.fromEntries(levels));
    console.log('Nodes at each level:', Object.fromEntries(nodesAtLevel));

    // Calculate positions with guaranteed no overlap
    const updatedNodes = nodes.map(node => {
      const level = levels.get(node.id) || 0;
      const levelNodes = nodesAtLevel.get(level) || [];
      const indexInLevel = levelNodes.indexOf(node.id);
      const totalNodesInLevel = levelNodes.length;

      // Calculate X position (horizontal spacing between levels)
      const x = startX + (level * horizontalSpacing);

      // Calculate Y position with guaranteed spacing based on node type
      let y: number;

      // Different node types have different heights - use generous estimates
      const getNodeHeight = (nodeType: string) => {
        switch (nodeType) {
          case 'ai_assistant': return 250; // AI Assistant nodes are taller
          case 'quick_reply': return 220;  // Quick reply nodes are tall
          case 'condition': return 200;    // Condition nodes are medium
          case 'message': return 180;      // Message nodes are standard
          case 'trigger': return 160;      // Trigger nodes are compact
          default: return 200;             // Default safe height
        }
      };

      const nodeHeight = getNodeHeight(node.type || 'default');
      const minVerticalGap = 100; // Very generous gap between nodes
      const requiredSpacing = nodeHeight + minVerticalGap;

      if (totalNodesInLevel === 1) {
        // Single node - place it at a reasonable center position
        y = startY + 200;
      } else {
        // Multiple nodes - stack them with guaranteed spacing
        // Start from a base position and add generous spacing for each node
        const baseY = startY + 50;
        y = baseY + (indexInLevel * requiredSpacing);

        // Debug logging
        console.log(`Node ${node.id} (${node.type}) at level ${level}, index ${indexInLevel}/${totalNodesInLevel}, height=${nodeHeight}, Y=${y}`);
      }

      return {
        ...node,
        position: { x, y }
      };
    });

    setNodes(updatedNodes);

    setTimeout(() => {
      if (reactFlowInstance) {
        reactFlowInstance.fitView({
          padding: 0.1,
          includeHiddenNodes: false,
          minZoom: 0.5,
          maxZoom: 1.5,
          duration: 800
        });
      }
    }, 100);

    // Show success message with layout details
    const levelCount = Math.max(...Array.from(levels.values())) + 1;
    toast({
      title: 'Nodes Auto-Arranged',
      description: `${nodes.length} nodes organized across ${levelCount} levels with proper spacing. No overlaps guaranteed!`,
    });

    setTimeout(() => {
      setIsAutoArranging(false);
    }, 500);
  }, [nodes, edges, setNodes, toast]);

  const undoAutoArrange = useCallback(() => {
    if (previousNodePositions.length > 0) {
      setNodes(previousNodePositions);
      setPreviousNodePositions([]);
      toast({
        title: 'Auto-Arrange Undone',
        description: 'Nodes have been restored to their previous positions.',
      });
    }
  }, [previousNodePositions, setNodes, toast]);

  const handleSave = async () => {
    if (!name.trim()) {
      toast({
        title: t('flow_builder.name_required', 'Name required'),
        description: t('flow_builder.provide_flow_name', 'Please provide a name for your flow'),
        variant: 'destructive'
      });
      return;
    }

    setLoading(true);
    try {
      const flowToSave = {
        name,
        nodes: JSON.stringify(nodes),
        edges: JSON.stringify(edges),
        status: 'draft'
      };

      if (isEditMode && flowId) {
        await updateFlowMutation.mutateAsync({ id: flowId, data: flowToSave });
      } else {
        await createFlowMutation.mutateAsync(flowToSave);
      }
    } finally {
      setLoading(false);
    }
  };

  const [sidebarOpen, setSidebarOpen] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setSidebarOpen(true);
      } else {
        setSidebarOpen(false);
      }
    };

    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'A') {
        event.preventDefault();
        if (!isAutoArranging && nodes.length > 0) {
          autoArrangeNodes();
        }
      }
      if ((event.ctrlKey || event.metaKey) && event.key === 'z' && previousNodePositions.length > 0) {
        event.preventDefault();
        undoAutoArrange();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [autoArrangeNodes, undoAutoArrange, isAutoArranging, nodes.length, previousNodePositions.length]);

  if (isEditMode && isLoadingFlow) {
    return (
      <div className="flex justify-center items-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="flow-builder h-full flex flex-col">
      <div className="flow-header p-4 border-b flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex flex-col sm:flex-row w-full sm:w-auto gap-4 items-start sm:items-center">
          <Input
            placeholder={t('flow_builder.flow_name', 'Flow name')}
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="w-full sm:w-64"
          />
          <div className="flex gap-2 w-full sm:w-auto">
            <Button
              variant="outline"
              onClick={handleSave}
              disabled={loading}
              className="flex-1 sm:flex-none btn-brand-primary"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              {t('common.save', 'Save')}
            </Button>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    onClick={autoArrangeNodes}
                    disabled={isAutoArranging || nodes.length === 0}
                    className="flex-1 sm:flex-none"
                  >
                    {isAutoArranging ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <LayoutGrid className="h-4 w-4 mr-2" />
                    )}
                    {isAutoArranging ? 'Arranging...' : 'Auto-Arrange'}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Automatically organize all nodes in a clean hierarchical layout</p>
                  <p className="text-xs text-muted-foreground mt-1">Shortcut: Ctrl+Shift+A</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            {previousNodePositions.length > 0 && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      onClick={undoAutoArrange}
                      disabled={isAutoArranging}
                      className="flex-1 sm:flex-none"
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Undo
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Restore nodes to their previous positions</p>
                    <p className="text-xs text-muted-foreground mt-1">Shortcut: Ctrl+Z</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            <Link href="/flows" className="flex-1 sm:flex-none">
              <Button variant="outline" className="w-full">{t('common.cancel', 'Cancel')}</Button>
            </Link>
            <Button
              variant="outline"
              size="icon"
              className="md:hidden"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              {sidebarOpen ? <ArrowRightCircle /> : <MessageSquare />}
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 flex relative min-h-0">
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black/50 z-10 md:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        <div className={`flow-sidebar border-r bg-background shadow-lg md:shadow-none z-20 transition-all duration-300 ease-in-out ${
          sidebarOpen
            ? 'absolute md:relative h-full w-full sm:w-80 md:w-auto md:min-w-[280px] md:max-w-[320px] lg:min-w-[300px] lg:max-w-[350px]'
            : 'hidden md:flex md:min-w-[280px] md:max-w-[320px] lg:min-w-[300px] lg:max-w-[350px]'
        }`}>
          <div className="flex justify-between items-center p-4 border-b md:hidden">
            <h3 className="font-medium">{t('flow_builder.node_selection', 'Node Selection')}</h3>
            <Button
              variant="outline"
              size="icon"
              onClick={() => setSidebarOpen(false)}
            >
              <ArrowRightCircle />
            </Button>
          </div>

          <div className="flex-1 overflow-y-auto p-4">
            <NodeSelector
              nodes={nodes}
              onAdd={(type) => {
                onAddNode(type);
                if (window.innerWidth < 768) {
                  setSidebarOpen(false);
                }
              }}
            />
          </div>
        </div>

        <div
          className={`flow-container flex-1 relative ${
            sidebarOpen ? 'hidden md:flex' : 'flex'
          }`}
          ref={reactFlowWrapper}
          style={{ minHeight: '90vh' }}
        >
          <FlowProvider onDeleteNode={onDeleteNode} onDuplicateNode={onDuplicateNode}>
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              nodeTypes={nodeTypes}
              edgeTypes={edgeTypes}
              proOptions={{ hideAttribution: true }}
              defaultEdgeOptions={{
          animated: true,
          type: 'smoothstep',
          style: { stroke: '#64748b' }
              }}
            >
              <Background />
              <Controls />
              <MiniMap />
              <Panel position="top-right" className="bg-background p-2 rounded-md shadow-sm border">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="text-xs flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${
              isEditMode ? (
                flowData?.status === 'active' ? 'bg-green-500' : 'bg-amber-500'
              ) : 'bg-blue-500'
            }`} />
            {isEditMode ? (
              flowData?.status === 'active' ? t('flow_builder.active', 'Active') : t('flow_builder.draft', 'Draft')
            ) : t('flow_builder.creating_new_flow', 'Creating New Flow')}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{t('flow_builder.current_flow_status', 'Current flow status')}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
              </Panel>
            </ReactFlow>
          </FlowProvider>
        </div>
      </div>
    </div>
  );
}

function FlowProvider({ children, onDeleteNode, onDuplicateNode }: {
  children: React.ReactNode;
  onDeleteNode: (nodeId: string) => void;
  onDuplicateNode: (nodeId: string) => void;
}) {
  return (
    <FlowContext.Provider value={{ onDeleteNode, onDuplicateNode }}>
      {children}
    </FlowContext.Provider>
  );
}

export function useFlowContext() {
  const context = useContext(FlowContext);
  if (!context) {
    throw new Error('useFlowContext must be used within a FlowProvider');
  }
  return context;
}

export default function FlowBuilderPage() {
  return (
    <ReactFlowProvider>
      <FlowEditor />
    </ReactFlowProvider>
  );
}