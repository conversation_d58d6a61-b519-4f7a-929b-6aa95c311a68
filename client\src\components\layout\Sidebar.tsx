import { Link, useLocation } from 'wouter';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useConversations } from '@/context/ConversationContext';
import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { usePermissions, PermissionGate } from '@/hooks/usePermissions';
import { useTranslation } from '@/hooks/use-translation';
import useSocket from '@/hooks/useSocket';
import TrialStatus from '@/components/TrialStatus';

export default function Sidebar() {
  const [location, setLocation] = useLocation();
  const { setActiveChannelId, activeChannelId } = useConversations();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [, setIsMobile] = useState(false);
  const { company } = useAuth();
  const { t } = useTranslation();
  const {
    PERMISSIONS
  } = usePermissions();

  const queryClient = useQueryClient();

  const { onMessage } = useSocket('/ws');

  useEffect(() => {
    const unsubscribeChannelCreated = onMessage('channelConnectionCreated', (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/channel-connections', company?.id] });
    });

    const unsubscribeChannelUpdated = onMessage('channelConnectionUpdated', (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/channel-connections', company?.id] });
    });

    const unsubscribeChannelDeleted = onMessage('channelConnectionDeleted', (data) => {
      if (data.data?.id === activeChannelId) {
        setActiveChannelId(null);
      }
      queryClient.invalidateQueries({ queryKey: ['/api/channel-connections', company?.id] });
    });

    const unsubscribeWhatsAppStatus = onMessage('whatsappConnectionStatus', (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/channel-connections', company?.id] });
    });

    const unsubscribeInstagramStatus = onMessage('instagramConnectionStatus', (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/channel-connections', company?.id] });
    });

    const unsubscribeMessengerStatus = onMessage('messengerConnectionStatus', (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/channel-connections', company?.id] });
    });

    return () => {
      unsubscribeChannelCreated();
      unsubscribeChannelUpdated();
      unsubscribeChannelDeleted();
      unsubscribeWhatsAppStatus();
      unsubscribeInstagramStatus();
      unsubscribeMessengerStatus();
    };
  }, [onMessage, queryClient, company?.id, activeChannelId, setActiveChannelId]);

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setIsCollapsed(true);
      }
    };

    checkIfMobile();

    window.addEventListener('resize', checkIfMobile);

    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const { data: channelConnections = [] } = useQuery<any[]>({
    queryKey: ['/api/channel-connections', company?.id],
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    staleTime: 1000 * 60 * 2,
    enabled: !!company
  });

  const handleChannelClick = (channelId: number) => {
    if (activeChannelId === channelId) {
      setActiveChannelId(null);
    } else {
      setActiveChannelId(channelId);
    }

    if (location !== '/inbox') {
      setLocation('/inbox');
    }
  };

  const companyStyle = company ? {
    sidebarBg: { backgroundColor: adjustColor(company.primaryColor ?? '#1f2937', -40) },
    sidebarHover: { backgroundColor: adjustColor(company.primaryColor ?? '#1f2937', -30) },
    activeItem: { backgroundColor: adjustColor(company.primaryColor ?? '#1f2937', -20) },
    toggleButton: { backgroundColor: adjustColor(company.primaryColor ?? '#1f2937', -30) },
    toggleButtonHover: { backgroundColor: adjustColor(company.primaryColor ?? '#1f2937', -20) },
    toggleButtonBorder: { borderColor: adjustColor(company.primaryColor ?? '#1f2937', -10) }
  } : {
    sidebarBg: {},
    sidebarHover: {},
    activeItem: {},
    toggleButton: {},
    toggleButtonHover: {},
    toggleButtonBorder: {}
  };

  function adjustColor(color: string, amount: number): string {
    try {
      color = color.replace('#', '');

      let r = parseInt(color.substring(0, 2), 16);
      let g = parseInt(color.substring(2, 4), 16);
      let b = parseInt(color.substring(4, 6), 16);

      r = Math.max(0, Math.min(255, r + amount));
      g = Math.max(0, Math.min(255, g + amount));
      b = Math.max(0, Math.min(255, b + amount));

      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    } catch (error) {
      return '#1f2937';
    }
  }

  return (
    <nav
      className={`text-white ${isCollapsed ? 'w-16' : 'w-64'} transition-all duration-300 ease-in-out flex-shrink-0 flex flex-col relative`}
      style={companyStyle.sidebarBg || { backgroundColor: '#1f2937' }}
    >
      <button
        onClick={toggleSidebar}
        className="absolute -right-3 top-6 text-white p-1 rounded-full border z-50 shadow-lg transition-colors"
        style={{
          ...companyStyle.toggleButton,
          ...companyStyle.toggleButtonBorder
        }}
        aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
      >
        <i className={`ri-${isCollapsed ? 'arrow-right' : 'arrow-left'}-s-line text-sm`}></i>
      </button>

      <div className="px-4 py-4 flex flex-col overflow-y-auto">
        <div className="flex flex-col space-y-1">
          <Link
            href="/inbox"
            className={`flex items-center px-2 py-2 rounded-lg ${location === '/inbox' ? 'text-white' : 'text-gray-400 hover:text-white'}`}
            style={location === '/inbox' ? companyStyle.activeItem : {}}
          >
            <i className="ri-inbox-line text-xl"></i>
            <span className={`ml-3 ${isCollapsed ? 'hidden' : 'block'}`}>{t('nav.inbox', 'Inbox')}</span>
          </Link>

          <PermissionGate permissions={[PERMISSIONS.VIEW_FLOWS, PERMISSIONS.MANAGE_FLOWS]}>
            <Link
              href="/flows"
              className={`flex items-center px-2 py-2 rounded-lg ${location === '/flows' ? 'text-white' : 'text-gray-400 hover:text-white'}`}
              style={location === '/flows' ? companyStyle.activeItem : {}}
            >
              <i className="ri-flow-chart text-xl"></i>
              <span className={`ml-3 ${isCollapsed ? 'hidden' : 'block'}`}>{t('nav.flow_builder', 'Flow Builder')}</span>
            </Link>
          </PermissionGate>

          <PermissionGate permissions={[PERMISSIONS.VIEW_CONTACTS, PERMISSIONS.MANAGE_CONTACTS]}>
            <Link
              href="/contacts"
              className={`flex items-center px-2 py-2 rounded-lg ${location === '/contacts' ? 'text-white' : 'text-gray-400 hover:text-white'}`}
              style={location === '/contacts' ? companyStyle.activeItem : {}}
            >
              <i className="ri-contacts-line text-xl"></i>
              <span className={`ml-3 ${isCollapsed ? 'hidden' : 'block'}`}>{t('nav.contacts', 'Contacts')}</span>
            </Link>
          </PermissionGate>

          <PermissionGate permissions={[PERMISSIONS.VIEW_PIPELINE, PERMISSIONS.MANAGE_PIPELINE]}>
            <Link
              href="/pipeline"
              className={`flex items-center px-2 py-2 rounded-lg ${location === '/pipeline' ? 'text-white' : 'text-gray-400 hover:text-white'}`}
              style={location === '/pipeline' ? companyStyle.activeItem : {}}
            >
              <i className="ri-stack-line text-xl"></i>
              <span className={`ml-3 ${isCollapsed ? 'hidden' : 'block'}`}>{t('nav.pipeline', 'Pipeline')}</span>
            </Link>
          </PermissionGate>

          <PermissionGate permissions={[PERMISSIONS.VIEW_CALENDAR, PERMISSIONS.MANAGE_CALENDAR]}>
            <Link
              href="/calendar"
              className={`flex items-center px-2 py-2 rounded-lg ${location === '/calendar' ? 'text-white' : 'text-gray-400 hover:text-white'}`}
              style={location === '/calendar' ? companyStyle.activeItem : {}}
            >
              <i className="ri-calendar-line text-xl"></i>
              <span className={`ml-3 ${isCollapsed ? 'hidden' : 'block'}`}>{t('nav.calendar', 'Calendar')}</span>
            </Link>
          </PermissionGate>

          <PermissionGate permissions={[
            PERMISSIONS.VIEW_CAMPAIGNS,
            PERMISSIONS.CREATE_CAMPAIGNS,
            PERMISSIONS.EDIT_CAMPAIGNS,
            PERMISSIONS.DELETE_CAMPAIGNS,
            PERMISSIONS.MANAGE_TEMPLATES,
            PERMISSIONS.MANAGE_SEGMENTS,
            PERMISSIONS.VIEW_CAMPAIGN_ANALYTICS,
            PERMISSIONS.MANAGE_WHATSAPP_ACCOUNTS,
            PERMISSIONS.CONFIGURE_CHANNELS
          ]}>
            <Link
              href="/campaigns"
              className={`flex items-center px-2 py-2 rounded-lg ${location.startsWith('/campaigns') ? 'text-white' : 'text-gray-400 hover:text-white'}`}
              style={location.startsWith('/campaigns') ? companyStyle.activeItem : {}}
            >
              <i className="ri-megaphone-line text-xl"></i>
              <span className={`ml-3 ${isCollapsed ? 'hidden' : 'block'}`}>{t('nav.campaigns', 'Campaigns')}</span>
            </Link>
          </PermissionGate>


          <PermissionGate permissions={[PERMISSIONS.VIEW_ANALYTICS, PERMISSIONS.VIEW_DETAILED_ANALYTICS]}>
            <Link
              href="/analytics"
              className={`flex items-center px-2 py-2 rounded-lg ${location === '/analytics' ? 'text-white' : 'text-gray-400 hover:text-white'}`}
              style={location === '/analytics' ? companyStyle.activeItem : {}}
            >
              <i className="ri-bar-chart-line text-xl"></i>
              <span className={`ml-3 ${isCollapsed ? 'hidden' : 'block'}`}>{t('nav.analytics', 'Analytics')}</span>
            </Link>
          </PermissionGate>
        </div>

        <PermissionGate permissions={[PERMISSIONS.VIEW_CHANNELS, PERMISSIONS.MANAGE_CHANNELS]}>
          <div className="mt-8 pt-4 border-t border-gray-700">
            <h3 className={`text-xs uppercase tracking-wide text-gray-500 mb-2 ${isCollapsed ? 'hidden' : 'block'}`}>{t('nav.channels', 'Channels')}</h3>
            <div className="flex flex-col space-y-1">
              {channelConnections.map((connection: any) => {
              let icon;
              let color;

              switch(connection.channelType) {
                case 'whatsapp_official':
                  icon = "ri-whatsapp-line";
                  color = "#25D366";
                  break;
                case 'whatsapp_unofficial':
                  icon = "ri-whatsapp-line";
                  color = "#25D366";
                  break;
                case 'messenger':
                  icon = "ri-messenger-line";
                  color = "#1877F2";
                  break;
                case 'instagram':
                  icon = "ri-instagram-line";
                  color = "#E4405F";
                  break;
                default:
                  icon = "ri-chat-1-line";
                  color = "#333235";
              }

              const isActive = activeChannelId === connection.id;

              return (
                <button
                  key={connection.id}
                  className={`flex items-center px-2 py-2 rounded-lg w-full text-left ${isActive ? 'bg-gray-800 text-white' : 'text-gray-400 hover:bg-gray-800 hover:text-white'}`}
                  onClick={() => handleChannelClick(connection.id)}
                >
                  <i className={icon + " text-xl"} style={{ color: isActive ? 'white' : color }}></i>
                  <span className={`ml-3 ${isCollapsed ? 'hidden' : 'block'} truncate`}>
                    {connection.accountName}
                  </span>
                </button>
              );
              })}
            </div>
          </div>
        </PermissionGate>

        <div className="mt-auto pt-4">
          <TrialStatus isCollapsed={isCollapsed} />

          <PermissionGate permissions={[PERMISSIONS.VIEW_PAGES, PERMISSIONS.MANAGE_PAGES]}>
            <Link
              href="/pages"
              className={`flex items-center px-2 py-2 rounded-lg ${location === '/pages' ? 'text-white' : 'text-gray-400 hover:text-white'}`}
              style={location === '/pages' ? companyStyle.activeItem : {}}
            >
              <i className="ri-file-text-line text-xl"></i>
              <span className={`ml-3 ${isCollapsed ? 'hidden' : 'block'}`}>{t('nav.pages', 'Pages')}</span>
            </Link>
          </PermissionGate>

          <PermissionGate permissions={[PERMISSIONS.VIEW_SETTINGS, PERMISSIONS.MANAGE_SETTINGS]}>
            <Link
              href="/settings"
              className={`flex items-center px-2 py-2 rounded-lg ${location === '/settings' ? 'text-white' : 'text-gray-400 hover:text-white'}`}
              style={location === '/settings' ? companyStyle.activeItem : {}}
            >
              <i className="ri-settings-line text-xl"></i>
              <span className={`ml-3 ${isCollapsed ? 'hidden' : 'block'}`}>{t('nav.settings', 'Settings')}</span>
            </Link>
          </PermissionGate>
            <a
            href={`https://docs.${window.location.hostname.replace(/^www\./, '')}`}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center px-2 py-2 rounded-lg w-full text-left text-gray-400 hover:text-white"
            >
            <i className="ri-question-line text-xl"></i>
            <span className={`ml-3 ${isCollapsed ? 'hidden' : 'block'}`}>{t('nav.help_support', 'Help & Support')}</span>
            </a>

            <Link
              href="/settings?tab=billing"
              className="flex items-center px-2 py-2 rounded-lg w-full text-left text-gray-400 hover:text-white"
            >
              <i className="ri-bank-card-line text-xl"></i>
              <span className={`ml-3 ${isCollapsed ? 'hidden' : 'block'}`}>{t('nav.billing', 'Billing & Subscription')}</span>
            </Link>

          {company && (
            <div className={`mt-4 pt-4 border-t border-gray-700 text-xs text-gray-500 ${isCollapsed ? 'hidden' : 'block'}`}>
              <div className="px-2 space-y-1">
                <div>{t('nav.company', 'Company')}: {company.name}</div>
                <div>{t('nav.plan', 'Plan')}: <span className="capitalize">{company.plan}</span></div>
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
}
