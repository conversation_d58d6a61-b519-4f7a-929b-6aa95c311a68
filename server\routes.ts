import {
  contacts,
  conversations,
  insertChannelConnectionSchema,
  InsertContact,
  insertContactSchema,
  InsertConversation,
  insertConversationSchema,
  insertFlowAssignmentSchema,
  insertFlowSchema,
  insertMessageSchema,
  insertNoteSchema,
  invitationStatusTypes,
  messages,
  PERMISSIONS
} from "@shared/schema";
import crypto, { randomBytes, scrypt, timingSafeEqual } from "crypto";
import { eq } from "drizzle-orm";
import type { Express, Request, Response } from "express";
import express from "express";
import axios from "axios";
import fs from "fs";
import fsExtra from "fs-extra";
import { createServer, type Server } from "http";
import multer from "multer";
import path from "path";
import { promisify } from "util";
import { WebSocket, WebSocketServer } from "ws";
import { z } from "zod";
import { registerAdminRoutes } from "./admin-routes";
import { setupAuth } from "./auth";
import { setupSocialAuth } from "./social-auth";
import { db } from "./db";
import { setupLanguageRoutes } from "./language-routes";
import { ensureAuthenticated, getUserPermissions, requireAnyPermission, requirePermission } from "./middleware";

import { affiliateTrackingMiddleware } from "./middleware/affiliate-tracking";
import { requireSubdomainAuth, subdomainMiddleware } from "./middleware/subdomain";
import { isWhatsAppGroupChatId } from "./utils/whatsapp-group-filter";
import { insertCompanyPageSchema } from "@shared/schema";
import { registerPaymentRoutes } from "./payment-routes";
import { registerPlanRoutes } from "./plan-routes";
import { setupTrialRoutes } from "./trial-routes";
import campaignRoutes from "./routes/campaigns";
import templateMediaRoutes from "./routes/template-media";
import autoUpdateRoutes from "./routes/auto-update";
import followUpRoutes from "./routes/follow-ups";
import emailTemplateRoutes from "./routes/email-templates";
import emailSignatureRoutes from "./routes/email-signatures";
import enhancedSubscriptionRoutes from "./routes/enhanced-subscription";
import instagramService from "./services/channels/instagram";
import telegramService from "./services/channels/telegram";
import messengerService from "./services/channels/messenger";
import emailService from "./services/channels/email";
import whatsAppService, { downloadAndSaveMedia, getConnection as getWhatsAppConnection } from "./services/channels/whatsapp";
import whatsAppOfficialService from "./services/channels/whatsapp-official";
import whatsAppTwilioService from "./services/channels/whatsapp-twilio";
import whatsApp360DialogService from "./services/channels/whatsapp-360dialog";
import whatsApp360DialogPartnerService from "./services/channels/whatsapp-360dialog-partner";
import whatsAppMetaPartnerService from "./services/channels/whatsapp-meta-partner";
import { generateApiKey, hashApiKey } from "./middleware/api-auth";
import apiV1Routes from "./routes/api-v1";
import channelManager from "./services/channel-manager";
import {
  sendTeamInvitation,
  testSmtpConfig,
  type SmtpConfig
} from "./services/email";
import flowExecutor from "./services/flow-executor";
import googleCalendarService from "./services/google-calendar";
import googleSheetsService from "./services/google-sheets";
import { storage } from "./storage";
import { logger } from "./utils/logger";
import { eventEmitterMonitor } from "./utils/event-emitter-monitor";

const scryptAsync = promisify(scrypt);

async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

async function comparePasswords(supplied: string, stored: string) {
  const [hashed, salt] = stored.split(".");
  const hashedBuf = Buffer.from(hashed, "hex");
  const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;
  return timingSafeEqual(hashedBuf, suppliedBuf);
}

const validateBody = (schema: any, body: any) => {
  const result = schema.safeParse(body);
  if (!result.success) {
    throw new Error(`Validation error: ${result.error.message}`);
  }
  return result.data;
};

export async function registerRoutes(app: Express): Promise<Server> {
  await setupAuth(app);
  setupSocialAuth(app);

  app.get('/public/branding', async (req, res) => {

    try {
      const settings = await storage.getAllAppSettings();


      const brandingSettings = settings.filter(s =>
        s.key === 'branding' ||
        s.key === 'branding_logo' ||
        s.key === 'branding_favicon'
      );


      res.set('Cache-Control', 'public, max-age=300');
      res.json(brandingSettings);

    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch branding settings' });
    }
  });

  // Public API endpoint to fetch website data by slug
  app.get('/api/public/website/:slug', async (req, res) => {
    const { slug } = req.params;

    try {
      const website = await storage.getWebsiteBySlug(slug);

      if (!website || website.status !== 'published') {
        return res.status(404).json({ error: 'Website not found' });
      }

      // Return the website data as JSON
      res.json(website);
    } catch (error) {
      console.error('Error fetching website:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  // Public website display route (legacy - serves the first published website)
  app.get('/website', async (req, res) => {
    try {
      const publishedWebsite = await storage.getPublishedWebsite();

      if (!publishedWebsite) {
        return res.status(404).send(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Website Not Found</title>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
            </head>
            <body>
              <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <h1>Website Not Found</h1>
                <p>No published website is currently available.</p>
              </div>
            </body>
          </html>
        `);
      }

      // Build the complete HTML page
      const html = `
        <!DOCTYPE html>
        <html>
          <head>
            <title>${publishedWebsite.metaTitle || publishedWebsite.title}</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <meta name="description" content="${publishedWebsite.metaDescription || publishedWebsite.description || ''}">
            <meta name="keywords" content="${publishedWebsite.metaKeywords || ''}">
            ${publishedWebsite.favicon ? `<link rel="icon" href="${publishedWebsite.favicon}">` : ''}
            ${publishedWebsite.googleAnalyticsId ? `
              <!-- Google Analytics -->
              <script async src="https://www.googletagmanager.com/gtag/js?id=${publishedWebsite.googleAnalyticsId}"></script>
              <script>
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${publishedWebsite.googleAnalyticsId}');
              </script>
            ` : ''}
            ${publishedWebsite.facebookPixelId ? `
              <!-- Facebook Pixel -->
              <script>
                !function(f,b,e,v,n,t,s)
                {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
                n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
                n.queue=[];t=b.createElement(e);t.async=!0;
                t.src=v;s=b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t,s)}(window, document,'script',
                'https://connect.facebook.net/en_US/fbevents.js');
                fbq('init', '${publishedWebsite.facebookPixelId}');
                fbq('track', 'PageView');
              </script>
              <noscript><img height="1" width="1" style="display:none"
                src="https://www.facebook.com/tr?id=${publishedWebsite.facebookPixelId}&ev=PageView&noscript=1"
              /></noscript>
            ` : ''}
            <style>
              ${publishedWebsite.grapesCss || ''}
              ${publishedWebsite.customCss || ''}
            </style>
            ${publishedWebsite.customHead || ''}
          </head>
          <body>
            ${publishedWebsite.grapesHtml || ''}
            <script>
              ${publishedWebsite.grapesJs || ''}
              ${publishedWebsite.customJs || ''}
            </script>
          </body>
        </html>
      `;

      res.set('Cache-Control', 'public, max-age=300');
      res.send(html);
    } catch (error) {
      console.error('Error serving published website:', error);
      res.status(500).send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Error</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
          </head>
          <body>
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
              <h1>Error</h1>
              <p>An error occurred while loading the website.</p>
            </div>
          </body>
        </html>
      `);
    }
  });






  app.use(subdomainMiddleware);


  app.use(affiliateTrackingMiddleware);

  registerAdminRoutes(app);

  app.use('/api/v1', apiV1Routes);

  registerPlanRoutes(app);

  setupTrialRoutes(app);

  registerPaymentRoutes(app);

  setupLanguageRoutes(app);

  app.use('/api/campaigns', requireSubdomainAuth, ensureAuthenticated, campaignRoutes);

  app.use('/api/templates', templateMediaRoutes);

  app.use('/api/auto-update', autoUpdateRoutes);

  app.use('/api/follow-ups', ensureAuthenticated, followUpRoutes);

  app.use('/api/email-templates', ensureAuthenticated, emailTemplateRoutes);

  app.use('/api/email-signatures', ensureAuthenticated, emailSignatureRoutes);

  app.use('/api/enhanced-subscription', enhancedSubscriptionRoutes);



  const ensureSuperAdmin = (req: Request, res: Response, next: any) => {
    if (req.isAuthenticated() && req.user && (req.user as any).isSuperAdmin) {
      return next();
    }
    res.status(403).json({ message: 'Super admin access required' });
  };

  app.get('/api/branding', ensureAuthenticated, async (req, res) => {
    try {
      const settings = await storage.getAllAppSettings();

      const brandingSettings = settings.filter(s =>
        s.key === 'branding' ||
        s.key === 'branding_logo' ||
        s.key === 'branding_favicon'
      );

      res.json(brandingSettings);
    } catch (error) {
      console.error('Error fetching branding settings:', error);
      res.status(500).json({ error: 'Failed to fetch branding settings' });
    }
  });

  app.get('/api/settings/api-keys', ensureAuthenticated, async (req: any, res) => {
    try {
      const apiKeys = await storage.getApiKeysByCompanyId(req.user.companyId);

      const sanitizedKeys = apiKeys.map(key => ({
        id: key.id,
        name: key.name,
        keyPrefix: key.keyPrefix,
        permissions: key.permissions,
        isActive: key.isActive,
        lastUsedAt: key.lastUsedAt,
        createdAt: key.createdAt,
        expiresAt: key.expiresAt,
        rateLimitPerMinute: key.rateLimitPerMinute,
        rateLimitPerHour: key.rateLimitPerHour,
        rateLimitPerDay: key.rateLimitPerDay
      }));

      res.json(sanitizedKeys);
    } catch (error) {
      console.error('Error fetching API keys:', error);
      res.status(500).json({ error: 'Failed to fetch API keys' });
    }
  });

  app.post('/api/settings/api-keys', ensureAuthenticated, async (req: any, res) => {
    try {
      const { name } = req.body;

      if (!name || typeof name !== 'string' || name.trim().length === 0) {
        return res.status(400).json({ error: 'API key name is required' });
      }

      const { key, hash, prefix } = generateApiKey();

      const apiKeyData = {
        companyId: req.user.companyId,
        userId: req.user.id,
        name: name.trim(),
        keyHash: hash,
        keyPrefix: prefix,
        permissions: ['messages:send', 'channels:read', 'messages:read', 'media:upload'],
        isActive: true,
        rateLimitPerMinute: 60,
        rateLimitPerHour: 1000,
        rateLimitPerDay: 10000,
        allowedIps: [],
        metadata: {}
      };

      const createdKey = await storage.createApiKey(apiKeyData);

      res.status(201).json({
        id: createdKey.id,
        key: key,
        name: createdKey.name,
        keyPrefix: createdKey.keyPrefix,
        permissions: createdKey.permissions,
        isActive: createdKey.isActive,
        createdAt: createdKey.createdAt
      });
    } catch (error) {
      console.error('Error creating API key:', error);
      res.status(500).json({ error: 'Failed to create API key' });
    }
  });

  app.patch('/api/settings/api-keys/:id', ensureAuthenticated, async (req: any, res) => {
    try {
      const keyId = parseInt(req.params.id);
      const { isActive, name } = req.body;

      if (isNaN(keyId)) {
        return res.status(400).json({ error: 'Invalid API key ID' });
      }

      const existingKey = await storage.getApiKeysByCompanyId(req.user.companyId);
      const keyToUpdate = existingKey.find(k => k.id === keyId);

      if (!keyToUpdate) {
        return res.status(404).json({ error: 'API key not found' });
      }

      const updateData: any = {};
      if (typeof isActive === 'boolean') updateData.isActive = isActive;
      if (typeof name === 'string' && name.trim().length > 0) updateData.name = name.trim();

      const updatedKey = await storage.updateApiKey(keyId, updateData);

      res.json({
        id: updatedKey.id,
        name: updatedKey.name,
        keyPrefix: updatedKey.keyPrefix,
        permissions: updatedKey.permissions,
        isActive: updatedKey.isActive,
        lastUsedAt: updatedKey.lastUsedAt,
        createdAt: updatedKey.createdAt,
        updatedAt: updatedKey.updatedAt
      });
    } catch (error) {
      console.error('Error updating API key:', error);
      res.status(500).json({ error: 'Failed to update API key' });
    }
  });

  app.delete('/api/settings/api-keys/:id', ensureAuthenticated, async (req: any, res) => {
    try {
      const keyId = parseInt(req.params.id);

      if (isNaN(keyId)) {
        return res.status(400).json({ error: 'Invalid API key ID' });
      }

      const existingKeys = await storage.getApiKeysByCompanyId(req.user.companyId);
      const keyToDelete = existingKeys.find(k => k.id === keyId);

      if (!keyToDelete) {
        return res.status(404).json({ error: 'API key not found' });
      }

      await storage.deleteApiKey(keyId);

      res.json({ message: 'API key deleted successfully' });
    } catch (error) {
      console.error('Error deleting API key:', error);
      res.status(500).json({ error: 'Failed to delete API key' });
    }
  });

  app.get('/api/settings/api-usage-stats', ensureAuthenticated, async (req: any, res) => {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);

      const stats = await storage.getApiUsageStats(req.user.companyId, startDate, endDate);

      res.json(stats);
    } catch (error) {
      console.error('Error fetching API usage stats:', error);
      res.status(500).json({ error: 'Failed to fetch API usage statistics' });
    }
  });

  app.get('/api/users/me', (req, res) => {
   

    if (!req.isAuthenticated()) {
      
      return res.status(401).json({ message: 'Unauthorized' });
    }

    
    res.json(req.user);
  });

  app.get('/api/users/permissions', ensureAuthenticated, async (req, res) => {
    try {
      const user = req.user as any;
      const permissions = await getUserPermissions(user);
      res.json(permissions);
    } catch (error) {
      console.error('Error fetching user permissions:', error);
      res.status(500).json({ error: 'Failed to fetch permissions' });
    }
  });

  app.get('/api/agents', ensureAuthenticated, async (req, res) => {
    try {
      const user = req.user as any;
      if (!user?.companyId) {
        return res.status(400).json({ error: 'User not associated with a company' });
      }

      const agents = await storage.getUsersByCompany(user.companyId);


      const availableAgents = agents.filter(agent =>
        (agent.role === 'agent' || agent.role === 'admin') && !agent.isSuperAdmin
      );


      res.json(availableAgents);
    } catch (error) {
      console.error('Error fetching agents:', error);
      res.status(500).json({ error: 'Failed to fetch agents' });
    }
  });

  app.post('/api/conversations/:id/assign', ensureAuthenticated, requirePermission(PERMISSIONS.ASSIGN_CONVERSATIONS), async (req, res) => {
    try {
      const conversationId = parseInt(req.params.id);
      const { agentId } = req.body;
      const user = req.user as any;

      if (isNaN(conversationId)) {
        return res.status(400).json({ error: 'Invalid conversation ID' });
      }

      const conversation = await storage.getConversation(conversationId);
      if (!conversation) {
        return res.status(404).json({ error: 'Conversation not found' });
      }

      if (conversation.companyId !== null && conversation.companyId !== user.companyId) {
        return res.status(403).json({ error: 'Access denied' });
      }

      if (conversation.companyId === null) {
        await storage.updateConversation(conversationId, {
          companyId: user.companyId
        });
      }

      if (agentId) {
        const agent = await storage.getUser(agentId);
        if (!agent || agent.companyId !== user.companyId) {
          return res.status(400).json({ error: 'Invalid agent or agent not in same company' });
        }
      }

      const updatedConversation = await storage.updateConversation(conversationId, {
        assignedToUserId: agentId || null
      });

      broadcastToAll({
        type: 'conversationAssigned',
        data: {
          conversationId,
          agentId: agentId || null,
          assignedBy: user.id,
          conversation: updatedConversation
        }
      });

      res.json(updatedConversation);
    } catch (error) {
      console.error('Error assigning conversation:', error);
      res.status(500).json({ error: 'Failed to assign conversation' });
    }
  });

  app.delete('/api/conversations/:id/assign', ensureAuthenticated, requirePermission(PERMISSIONS.ASSIGN_CONVERSATIONS), async (req, res) => {
    try {
      const conversationId = parseInt(req.params.id);
      const user = req.user as any;

      if (isNaN(conversationId)) {
        return res.status(400).json({ error: 'Invalid conversation ID' });
      }

      const conversation = await storage.getConversation(conversationId);
      if (!conversation) {
        return res.status(404).json({ error: 'Conversation not found' });
      }

      if (conversation.companyId !== null && conversation.companyId !== user.companyId) {
        return res.status(403).json({ error: 'Access denied' });
      }

      if (conversation.companyId === null) {
        await storage.updateConversation(conversationId, {
          companyId: user.companyId
        });
      }

      const updatedConversation = await storage.updateConversation(conversationId, {
        assignedToUserId: null
      });

      broadcastToAll({
        type: 'conversationUnassigned',
        data: {
          conversationId,
          unassignedBy: user.id,
          conversation: updatedConversation
        }
      });

      res.json(updatedConversation);
    } catch (error) {
      console.error('Error unassigning conversation:', error);
      res.status(500).json({ error: 'Failed to unassign conversation' });
    }
  });


  app.get('/api/admin/companies/:id/users', ensureSuperAdmin, async (req, res) => {
    try {
      const companyId = parseInt(req.params.id);
      if (isNaN(companyId)) {
        return res.status(400).json({ error: 'Invalid company ID' });
      }

      const users = await storage.getUsersByCompany(companyId);
      res.json(users);
    } catch (error) {
      console.error('Error fetching company users:', error);
      res.status(500).json({ error: 'Failed to fetch company users' });
    }
  });

  app.patch('/api/users/me', ensureAuthenticated, async (req, res) => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ message: 'Unauthorized' });
      }

      const { fullName, email, username, avatarUrl } = req.body;
      const updates: any = {};

      if (fullName) updates.fullName = fullName;
      if (email) updates.email = email;
      if (username) {
        const existingUser = await storage.getUserByUsernameCaseInsensitive(username);
        if (existingUser && existingUser.id !== userId) {
          return res.status(400).json({ message: 'Username already taken' });
        }
        updates.username = username;
      }
      if (avatarUrl !== undefined) updates.avatarUrl = avatarUrl;

      const updatedUser = await storage.updateUser(userId, updates);

      if (req.session && (req.session as any)['passport']) {
        (req.session as any).passport.user = updatedUser.id;
      }

      res.json(updatedUser);
    } catch (error: any) {
      console.error('Error updating user profile:', error);
      res.status(500).json({ message: error.message || 'Error updating profile' });
    }
  });

  app.post('/api/users/change-password', ensureAuthenticated, async (req, res) => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ message: 'Unauthorized' });
      }

      const { currentPassword, newPassword } = req.body;
      if (!currentPassword || !newPassword) {
        return res.status(400).json({ message: 'Current password and new password are required' });
      }

      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      const isPasswordValid = await comparePasswords(currentPassword, user.password);
      if (!isPasswordValid) {
        return res.status(400).json({ message: 'Current password is incorrect' });
      }

      const hashedPassword = await hashPassword(newPassword);

      await storage.updateUserPassword(userId, hashedPassword);

      res.json({ message: 'Password updated successfully' });
    } catch (error: any) {
      console.error('Error changing password:', error);
      res.status(500).json({ message: error.message || 'Error changing password' });
    }
  });

  const UPLOAD_DIR = path.join(process.cwd(), 'uploads');
  fsExtra.ensureDirSync(UPLOAD_DIR);

  const BRANDING_DIR = path.join(UPLOAD_DIR, 'branding');
  fsExtra.ensureDirSync(BRANDING_DIR);

  const FLOW_MEDIA_DIR = path.join(UPLOAD_DIR, 'flow-media');
  fsExtra.ensureDirSync(FLOW_MEDIA_DIR);



  const flowMediaStorage = multer.diskStorage({
    destination: function (req, file, cb) {
      cb(null, FLOW_MEDIA_DIR)
    },
    filename: function (req, file, cb) {
      const uniqueId = crypto.randomBytes(16).toString('hex');
      const fileExt = path.extname(file.originalname);
      cb(null, `${uniqueId}${fileExt}`);
    }
  });

  const createSecureUpload = (options: {
    maxFileSize?: number,
    allowedMimeTypes?: string[],
    allowedExtensions?: string[],
    destination?: string
  } = {}) => {
    const {
      maxFileSize = 10 * 1024 * 1024,
      allowedMimeTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
        'video/mp4', 'video/webm', 'video/quicktime', 'video/avi',
        'audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp3', 'audio/webm', 'audio/aac', 'audio/mp4',
        'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain', 'text/csv', 'application/zip', 'application/x-zip-compressed'
      ],
      allowedExtensions = [
        '.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg',
        '.mp4', '.webm', '.mov', '.avi',
        '.mp3', '.wav', '.ogg',
        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
        '.txt', '.csv', '.zip'
      ],
      destination = UPLOAD_DIR
    } = options;

    return multer({
      storage: multer.diskStorage({
        destination: function (req, file, cb) {
          cb(null, destination);
        },
        filename: function (req, file, cb) {
          const uniqueId = crypto.randomBytes(16).toString('hex');
          const fileExt = path.extname(file.originalname).toLowerCase();
          const timestamp = Date.now();
          cb(null, `${timestamp}-${uniqueId}${fileExt}`);
        }
      }),
      limits: {
        fileSize: maxFileSize,
        files: 5,
        fields: 10,
        fieldNameSize: 100,
        fieldSize: 1024 * 1024
      },
      fileFilter: function (req, file, cb) {
       

        const fileExt = path.extname(file.originalname).toLowerCase();
        if (!allowedExtensions.includes(fileExt)) {
          
          return cb(new Error(`File type not allowed. Allowed extensions: ${allowedExtensions.join(', ')}`));
        }

        if (!allowedMimeTypes.includes(file.mimetype)) {
          
          return cb(new Error(`File type not allowed. Allowed types: ${allowedMimeTypes.join(', ')}`));
        }

        const filename = file.originalname.toLowerCase();

        const dangerousPatterns = [
          /\.exe$/i, /\.bat$/i, /\.cmd$/i, /\.com$/i, /\.pif$/i, /\.scr$/i,
          /\.vbs$/i, /\.js$/i, /\.jar$/i, /\.php$/i, /\.asp$/i, /\.jsp$/i
        ];

        if (dangerousPatterns.some(pattern => pattern.test(filename))) {
          
          return cb(new Error('File type not allowed for security reasons'));
        }

        cb(null, true);
      }
    });
  };

  const upload = createSecureUpload();



  app.post('/api/messages/:id/download-media', ensureAuthenticated, async (req, res) => {
    try {
      const messageId = parseInt(req.params.id);
      if (isNaN(messageId)) {
        return res.status(400).json({ error: 'Invalid message ID' });
      }

      const message = await db.query.messages.findFirst({
        where: eq(messages.id, messageId)
      });

      if (!message) {
        return res.status(404).json({ error: 'Message not found' });
      }

      if (message.mediaUrl) {
        return res.status(200).json({ mediaUrl: message.mediaUrl });
      }

      if (!message.metadata) {
        const simulatedMediaUrl = `/media/placeholder-${message.type || 'image'}.svg`;

        await db.update(messages)
          .set({ mediaUrl: simulatedMediaUrl })
          .where(eq(messages.id, messageId));

        return res.status(200).json({
          mediaUrl: simulatedMediaUrl,
          simulated: true
        });
      }

      let metadata;
      try {
        metadata = typeof message.metadata === 'string'
          ? JSON.parse(message.metadata)
          : message.metadata;
      } catch (error) {
        console.error('Error parsing message metadata:', error);

        const simulatedMediaUrl = `/media/placeholder-${message.type || 'image'}.svg`;

        await db.update(messages)
          .set({ mediaUrl: simulatedMediaUrl })
          .where(eq(messages.id, messageId));

        return res.status(200).json({
          mediaUrl: simulatedMediaUrl,
          simulated: true
        });
      }

      const waMessage = metadata.waMessage ||
        metadata.message ||
        (metadata.messageData && metadata.messageData.message);

      if (!waMessage) {
        

        const simulatedMediaUrl = `/media/placeholder-${message.type || 'image'}.svg`;

        await db.update(messages)
          .set({ mediaUrl: simulatedMediaUrl })
          .where(eq(messages.id, messageId));

        return res.status(200).json({
          mediaUrl: simulatedMediaUrl,
          simulated: true
        });
      }

      const conversation = await db.query.conversations.findFirst({
        where: eq(conversations.id, message.conversationId)
      });

      if (!conversation || !conversation.channelId) {
        return res.status(400).json({ error: 'Conversation not found or has no channel ID' });
      }

      if (conversation.channelType !== 'whatsapp' && conversation.channelType !== 'whatsapp_unofficial') {
        return res.status(400).json({ error: 'Media download only supported for WhatsApp channels' });
      }

      const sock = getWhatsAppConnection(conversation.channelId);

      if (!sock) {
        return res.status(400).json({ error: 'WhatsApp connection not active' });
      }

      const messageObj = metadata.waMessage ||
        metadata.message ||
        (metadata.messageData && metadata.messageData.message);

      const mediaUrl = await downloadAndSaveMedia(messageObj, sock);

      if (!mediaUrl) {
        const simulatedMediaUrl = `/media/placeholder-${message.type || 'image'}.svg`;

        await db.update(messages)
          .set({ mediaUrl: simulatedMediaUrl })
          .where(eq(messages.id, messageId));

        return res.status(200).json({
          mediaUrl: simulatedMediaUrl,
          simulated: true
        });
      }

      await db.update(messages)
        .set({ mediaUrl })
        .where(eq(messages.id, messageId));

      return res.status(200).json({ mediaUrl });
    } catch (error) {
      console.error('Error downloading media:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  });


  app.use('/uploads', (req, res, next) => {
    express.static(UPLOAD_DIR, {
      setHeaders: (res, path) => {
        if (path.includes('/branding/')) {
          res.setHeader('Cache-Control', 'public, max-age=3600');
        }
      }
    })(req, res, next);
  });



  app.post('/api/webhooks/twilio-whatsapp', express.urlencoded({ extended: true }), async (req, res) => {
    try {
      

      const payload = req.body;

      if (!payload.EventType) {
        
        return res.status(400).send('Bad Request');
      }

      await whatsAppTwilioService.processWebhook(payload);

      res.status(200).send('OK');
    } catch (error) {
      console.error('Error processing Twilio WhatsApp webhook:', error);
      res.status(500).send('Internal Server Error');
    }
  });

  app.post('/api/webhooks/360dialog-partner', express.json(), async (req, res) => {
    try {
      

      const payload = req.body;

      if (!payload.id || !payload.event) {
        
        return res.status(400).send('Bad Request');
      }

      await whatsApp360DialogPartnerService.processPartnerWebhook(payload);

      res.status(200).send('OK');
    } catch (error) {
      console.error('Error processing 360Dialog Partner webhook:', error);
      res.status(500).send('Internal Server Error');
    }
  });

  app.post('/api/webhooks/360dialog-messaging', express.json(), async (req, res) => {
    try {
      

      const payload = req.body;

      if (!payload.object || !payload.entry) {
        
        return res.status(400).send('Bad Request');
      }

      await whatsApp360DialogPartnerService.processMessagingWebhook(payload);

      res.status(200).send('OK');
    } catch (error) {
      console.error('Error processing 360Dialog Messaging webhook:', error);
      res.status(500).send('Internal Server Error');
    }
  });

  app.post('/api/360dialog/onboarding-callback', ensureAuthenticated, async (req: Request, res: Response) => {
    try {
      

      const { clientId, channels } = req.body;
      const user = req.user as any;

      if (!user?.id || !user?.companyId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const userId = user.id;
      const companyId = user.companyId;

      if (!clientId || !channels || !Array.isArray(channels)) {
        return res.status(400).json({ error: 'Missing required onboarding data' });
      }

      const success = await whatsApp360DialogPartnerService.processOnboardingCallback(companyId, {
        clientId,
        channels
      });

      if (success) {
        res.json({ success: true, message: 'Onboarding processed successfully' });
      } else {
        res.status(500).json({ error: 'Failed to process onboarding callback' });
      }
    } catch (error: any) {
      console.error('Error processing 360Dialog onboarding callback:', error);
      res.status(500).json({ error: error.message || 'Failed to process onboarding callback' });
    }
  });

  app.post('/api/channel-connections/whatsapp-embedded-signup', ensureAuthenticated, async (req: Request, res: Response) => {
    try {
      const { code } = req.body;
      const user = req.user as any;

      if (!code) {
        return res.status(400).json({ message: 'Authorization code is required' });
      }

      if (!user?.id) {
        return res.status(401).json({ message: 'User not authenticated' });
      }

      

      const tokenResponse = await fetch('https://graph.facebook.com/v22.0/oauth/access_token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: process.env.FACEBOOK_APP_ID || '',
          client_secret: process.env.FACEBOOK_APP_SECRET || '',
          code: code,
        }),
      });

      if (!tokenResponse.ok) {
        const errorData = await tokenResponse.json();
        console.error('Token exchange failed:', errorData);
        return res.status(400).json({
          message: 'Failed to exchange authorization code for access token',
          error: errorData
        });
      }

      const tokenData = await tokenResponse.json();
      const accessToken = tokenData.access_token;

      if (!accessToken) {
        return res.status(400).json({ message: 'No access token received' });
      }

      const wabaResponse = await fetch(`https://graph.facebook.com/v22.0/me/businesses?access_token=${accessToken}`);

      if (!wabaResponse.ok) {
        const errorData = await wabaResponse.json();
        console.error('Failed to get business accounts:', errorData);
        return res.status(400).json({
          message: 'Failed to retrieve business account information',
          error: errorData
        });
      }

      const wabaData = await wabaResponse.json();

      if (!wabaData.data || wabaData.data.length === 0) {
        return res.status(400).json({ message: 'No WhatsApp Business accounts found' });
      }

      const businessAccount = wabaData.data[0];
      const wabaId = businessAccount.id;

      const phoneNumbersResponse = await fetch(
        `https://graph.facebook.com/v22.0/${wabaId}/phone_numbers?access_token=${accessToken}`
      );

      if (!phoneNumbersResponse.ok) {
        const errorData = await phoneNumbersResponse.json();
        console.error('Failed to get phone numbers:', errorData);
        return res.status(400).json({
          message: 'Failed to retrieve phone numbers',
          error: errorData
        });
      }

      const phoneNumbersData = await phoneNumbersResponse.json();

      if (!phoneNumbersData.data || phoneNumbersData.data.length === 0) {
        return res.status(400).json({ message: 'No phone numbers found for this WhatsApp Business account' });
      }

      const phoneNumber = phoneNumbersData.data[0];
      const phoneNumberId = phoneNumber.id;


      if (!user.companyId) {
        return res.status(400).json({ message: 'Company ID is required for multi-tenant security' });
      }

      const connection = await storage.createChannelConnection({
        userId: user.id,
        companyId: user.companyId, // Add companyId for multi-tenant security
        channelType: 'whatsapp_official',
        accountId: wabaId,
        accountName: `WhatsApp Business - ${phoneNumber.display_phone_number}`,
        status: 'connected',
        connectionData: {
          phoneNumberId: phoneNumberId,
          wabaId: wabaId,
          accessToken: accessToken,
          phoneNumber: phoneNumber.display_phone_number,
          verifiedName: phoneNumber.verified_name || businessAccount.name,
          businessAccountId: businessAccount.id,
          businessAccountName: businessAccount.name
        }
      });

 

      try {

        if (!req.user || !req.user.companyId) {
          throw new Error('Company ID is required for multi-tenant security');
        }

        await whatsAppOfficialService.initializeConnection(connection.id, req.user.companyId, {
          accessToken,
          phoneNumberId,
          wabaId,
          webhookVerifyToken: process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN || 'default_verify_token'
        });
      } catch (initError) {
        console.error('Failed to initialize WhatsApp Official service:', initError);
      }

      broadcastToCompany({
        type: 'channelConnectionCreated',
        data: connection
      }, user.companyId);

      res.status(201).json(connection);
    } catch (error) {
      console.error('Error processing WhatsApp Business API signup:', error);
      res.status(500).json({
        message: 'Failed to process WhatsApp Business API signup',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.post('/api/channel-connections/meta-whatsapp-embedded-signup', ensureAuthenticated, async (req: Request, res: Response) => {
    try {
      const { connectionName, signupData } = req.body;
      const user = req.user as any;

      if (!connectionName || !signupData) {
        return res.status(400).json({ message: 'Connection name and signup data are required' });
      }

      if (!user?.companyId) {
        return res.status(401).json({ message: 'User not authenticated or missing company' });
      }

      

      const result = await whatsAppMetaPartnerService.processEmbeddedSignupCallback(
        user.companyId,
        signupData
      );

      res.status(201).json(result);
    } catch (error) {
      console.error('Error processing Meta WhatsApp embedded signup:', error);
      res.status(500).json({
        message: 'Failed to process Meta WhatsApp embedded signup',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.get('/api/whatsapp/behavior-config', ensureAuthenticated, async (req: Request, res: Response) => {
    try {
      const typingConfig = whatsAppService.getTypingConfiguration();
      const messageSplittingConfig = whatsAppService.getMessageSplittingConfiguration();
      const messageDebouncingConfig = whatsAppService.getMessageDebouncingConfiguration();

      res.json({
        typing: typingConfig,
        messageSplitting: messageSplittingConfig,
        messageDebouncing: messageDebouncingConfig
      });
    } catch (error) {
      console.error('Error getting WhatsApp behavior configuration:', error);
      res.status(500).json({
        message: 'Failed to get WhatsApp behavior configuration',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.post('/api/whatsapp/behavior-config', ensureAuthenticated, async (req: Request, res: Response) => {
    try {
      const { typing, messageSplitting, messageDebouncing } = req.body;

      if (typing) {
        whatsAppService.configureTypingBehavior(typing);
      }

      if (messageSplitting) {
        whatsAppService.configureMessageSplitting(messageSplitting);
      }

      if (messageDebouncing) {
        whatsAppService.configureMessageDebouncing(messageDebouncing);
      }

      res.json({
        message: 'WhatsApp behavior configuration updated successfully',
        typing: whatsAppService.getTypingConfiguration(),
        messageSplitting: whatsAppService.getMessageSplittingConfiguration(),
        messageDebouncing: whatsAppService.getMessageDebouncingConfiguration()
      });
    } catch (error) {
      console.error('Error updating WhatsApp behavior configuration:', error);
      res.status(500).json({
        message: 'Failed to update WhatsApp behavior configuration',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.get('/api/whatsapp/debouncing-status', ensureAuthenticated, async (req: Request, res: Response) => {
    try {
      const status = whatsAppService.getDebouncingStatus();
      res.json(status);
    } catch (error) {
      console.error('Error getting WhatsApp debouncing status:', error);
      res.status(500).json({
        message: 'Failed to get WhatsApp debouncing status',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.post('/api/whatsapp/test-splitting-debug', ensureAuthenticated, async (req: Request, res: Response) => {
    try {
      const { message, phoneNumber } = req.body;
      const testMessage = message || "This is a test message that should be long enough to trigger message splitting functionality. It contains multiple sentences to test the splitting algorithm. The system should split this into multiple chunks and deliver them with appropriate delays. Each chunk should be delivered in sequence to ensure the user receives the complete message.";
      const testPhone = phoneNumber || "+1234567890";

      const result = whatsAppService.testMessageSplitting(testMessage);
      const queueStatus = whatsAppService.getQueueStatus(testPhone, 1);

      res.json({
        message: 'Message splitting test completed',
        testMessage,
        result,
        queueStatus,
        config: whatsAppService.getMessageSplittingConfiguration()
      });
    } catch (error) {
      console.error('Error testing message splitting:', error);
      res.status(500).json({
        message: 'Failed to test message splitting',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  const httpServer = createServer(app);

  const wss = new WebSocketServer({ server: httpServer, path: '/ws' });

  const clients = new Map<string, {
    socket: WebSocket,
    userId?: number,
    companyId?: number,
    isAuthenticated: boolean,
    lastActivity: Date,
    authTimeout?: NodeJS.Timeout,
    pingInterval?: NodeJS.Timeout,
    unsubscribeFunctions?: (() => void)[]
  }>();

  const CONNECTION_CLEANUP_INTERVAL = 30000;
  const CONNECTION_TIMEOUT = 300000;
  const PING_INTERVAL = 30000;

  const cleanupClient = (clientId: string) => {
    const client = clients.get(clientId);
    if (client) {
      // Clean up event listeners first
      if (client.unsubscribeFunctions) {
        client.unsubscribeFunctions.forEach(unsubscribe => {
          try {
            unsubscribe();
          } catch (error) {
            logger.error('websocket', `Error cleaning up event listener for client ${clientId}:`, error);
          }
        });
        client.unsubscribeFunctions = [];
      }

      if (client.authTimeout) {
        clearTimeout(client.authTimeout);
      }
      if (client.pingInterval) {
        clearInterval(client.pingInterval);
      }

      if (client.socket.readyState === WebSocket.OPEN) {
        client.socket.close();
      }

      clients.delete(clientId);
      logger.websocket(`Cleaned up client: ${clientId}`);
    }
  };

  const cleanupInterval = setInterval(() => {
    const now = new Date();
    const clientsToCleanup: string[] = [];

    clients.forEach((client, clientId) => {
      const timeSinceLastActivity = now.getTime() - client.lastActivity.getTime();

      if (timeSinceLastActivity > CONNECTION_TIMEOUT ||
        client.socket.readyState === WebSocket.CLOSED ||
        client.socket.readyState === WebSocket.CLOSING) {
        clientsToCleanup.push(clientId);
      }
    });

    clientsToCleanup.forEach(clientId => {
      logger.websocket(`Cleaning up inactive client: ${clientId}`);
      cleanupClient(clientId);
    });

    if (clientsToCleanup.length > 0) {
      logger.websocket(`Cleaned up ${clientsToCleanup.length} inactive connections. Active: ${clients.size}`);
    }
  }, CONNECTION_CLEANUP_INTERVAL);

  wss.on('connection', (ws) => {
    const clientId = Math.random().toString(36).substring(2, 15);
    logger.websocket(`Client connected: ${clientId}`);

    const pingInterval = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.ping();
      }
    }, PING_INTERVAL);

    const authTimeout = setTimeout(() => {
      const client = clients.get(clientId);
      if (client && !client.isAuthenticated) {
        logger.websocket(`Authentication timeout for client: ${clientId}`);
        ws.send(JSON.stringify({
          type: 'authError',
          message: 'Authentication timeout'
        }));
        cleanupClient(clientId);
        ws.close();
      }
    }, 10000);

    clients.set(clientId, {
      socket: ws,
      isAuthenticated: false,
      lastActivity: new Date(),
      authTimeout,
      pingInterval
    });

    const handleAuthentication = async (userId: number) => {
      try {
        const user = await storage.getUser(userId);
        if (!user) {
          ws.send(JSON.stringify({
            type: 'authError',
            message: 'User not found'
          }));
          cleanupClient(clientId);
          ws.close();
          return;
        }

        const client = clients.get(clientId);
        if (client) {
          client.isAuthenticated = true;
          client.userId = userId;
          client.companyId = user.companyId === null ? undefined : user.companyId;
          client.lastActivity = new Date();

          if (client.authTimeout) {
            clearTimeout(client.authTimeout);
            client.authTimeout = undefined;
          }
        }


        const conversations = await storage.getConversations();
        ws.send(JSON.stringify({
          type: 'authenticated',
          message: 'Successfully authenticated'
        }));

        ws.send(JSON.stringify({
          type: 'conversations',
          data: conversations
        }));

        const unsubscribeQrCode = whatsAppService.subscribeToEvents('qrCode', (data) => {
          if (data && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'whatsappQrCode',
              connectionId: data.connectionId,
              qrCode: data.qrCode
            }));
          }
        });

        const unsubscribeConnectionStatus = whatsAppService.subscribeToEvents('connectionStatusUpdate', (data) => {
          if (data && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'whatsappConnectionStatus',
              connectionId: data.connectionId,
              status: data.status
            }));
          }
        });

        const unsubscribeConnectionError = whatsAppService.subscribeToEvents('connectionError', (data) => {
          if (data && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'whatsappConnectionError',
              connectionId: data.connectionId,
              error: data.error
            }));
          }
        });

        const unsubscribeMessageReceived = whatsAppService.subscribeToEvents('messageReceived', (data) => {
          if (data && ws.readyState === WebSocket.OPEN) {
            broadcastToAll({
              type: 'newMessage',
              data: data.message
            });

            broadcastToAll({
              type: 'conversationUpdated',
              data: data.conversation
            });
          }
        });

        const unsubscribeWhatsAppOfficialConnectionStatus = whatsAppOfficialService.subscribeToEvents('connectionStatusUpdate', (data) => {
          if (data && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'whatsappOfficialConnectionStatus',
              connectionId: data.connectionId,
              status: data.status
            }));
          }
        });

        const unsubscribeWhatsAppOfficialConnectionError = whatsAppOfficialService.subscribeToEvents('connectionError', (data) => {
          if (data && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'whatsappOfficialConnectionError',
              connectionId: data.connectionId,
              error: data.error
            }));
          }
        });

        const unsubscribeWhatsAppOfficialMessageReceived = whatsAppOfficialService.subscribeToEvents('newMessage', (data) => {
          if (data && ws.readyState === WebSocket.OPEN) {
            broadcastToAll({
              type: 'newMessage',
              data: data.message
            });

            broadcastToAll({
              type: 'conversationUpdated',
              data: data.conversation
            });
          }
        });

        const unsubscribeInstagramConnectionStatus = instagramService.subscribeToEvents('connectionStatusUpdate', (data) => {
          if (data && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'instagramConnectionStatus',
              connectionId: data.connectionId,
              status: data.status
            }));
          }
        });

        const unsubscribeInstagramConnectionError = instagramService.subscribeToEvents('connectionError', (data) => {
          if (data && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'instagramConnectionError',
              connectionId: data.connectionId,
              error: data.error
            }));
          }
        });

        const unsubscribeInstagramMessageReceived = instagramService.subscribeToEvents('messageReceived', (data) => {
          if (data && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'newMessage',
              data: data.message
            }));

            ws.send(JSON.stringify({
              type: 'conversationUpdated',
              data: data.conversation
            }));
          }
        });

        const unsubscribeMessengerConnectionStatus = messengerService.subscribeToEvents('connectionStatusUpdate', (data) => {
          if (data && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'messengerConnectionStatus',
              connectionId: data.connectionId,
              status: data.status
            }));
          }
        });

        const unsubscribeMessengerConnectionError = messengerService.subscribeToEvents('connectionError', (data) => {
          if (data && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'messengerConnectionError',
              connectionId: data.connectionId,
              error: data.error
            }));
          }
        });

        const unsubscribeMessengerMessageReceived = messengerService.subscribeToEvents('messageReceived', (data) => {
          if (data && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'newMessage',
              data: data.message
            }));

            ws.send(JSON.stringify({
              type: 'conversationUpdated',
              data: data.conversation
            }));
          }
        });

        const unsubscribeEmailMessageReceived = emailService.subscribeToEvents('messageReceived', (data) => {
          if (data && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'newMessage',
              data: data.message
            }));

            ws.send(JSON.stringify({
              type: 'conversationUpdated',
              data: data.conversation
            }));
          }
        });

        // Store unsubscribe functions in the client for proper cleanup
        const clientForCleanup = clients.get(clientId);
        if (clientForCleanup) {
          clientForCleanup.unsubscribeFunctions = [
            unsubscribeQrCode,
            unsubscribeConnectionStatus,
            unsubscribeConnectionError,
            unsubscribeMessageReceived,
            unsubscribeWhatsAppOfficialConnectionStatus,
            unsubscribeWhatsAppOfficialConnectionError,
            unsubscribeWhatsAppOfficialMessageReceived,
            unsubscribeInstagramConnectionStatus,
            unsubscribeInstagramConnectionError,
            unsubscribeInstagramMessageReceived,
            unsubscribeMessengerConnectionStatus,
            unsubscribeMessengerConnectionError,
            unsubscribeMessengerMessageReceived,
            unsubscribeEmailMessageReceived
          ];
        }

        ws.on('close', () => {
          // Event listeners will be cleaned up by cleanupClient
          cleanupClient(clientId);
        });
      } catch (error) {
        console.error('Authentication error:', error);
        ws.send(JSON.stringify({
          type: 'authError',
          message: 'Authentication failed'
        }));
        ws.close();
      }
    };

    ws.on('message', async (message) => {
      try {
        const data = JSON.parse(message.toString());

        const client = clients.get(clientId);
        if (client) {
          client.lastActivity = new Date();
        }

        if (data.type === 'authenticate') {
          const userId = data.userId;

          if (!userId) {
            ws.send(JSON.stringify({
              type: 'authError',
              message: 'Missing userId in authentication request'
            }));
            return;
          }

          await handleAuthentication(userId);
        } else if (data.type === 'ping') {
          ws.send(JSON.stringify({ type: 'pong' }));
          return;
        }

        if (data.type === 'ping') {
          ws.send(JSON.stringify({ type: 'pong' }));
          return;
        }

        if (!client || !client.isAuthenticated) {
          ws.send(JSON.stringify({
            type: 'authError',
            message: 'Not authenticated'
          }));
          return;
        }

        if (data.type === 'sendMessage') {
          const { conversationId, content, isFromBot = false } = data.message;
          const userId = client.userId;

          if (!userId) {
            ws.send(JSON.stringify({
              type: 'error',
              message: 'User ID not available'
            }));
            return;
          }

          try {
            const conversation = await storage.getConversation(conversationId);
            if (!conversation) {
              throw new Error(`Conversation with ID ${conversationId} not found`);
            }

            let messageContent = content;
            if (!isFromBot) {
              try {
                const user = await storage.getUser(userId);
                if (user && user.fullName) {
                  messageContent = `> *${user.fullName}*\n\n${content}`;
                }
              } catch (userError) {
                console.error('Error fetching user for signature:', userError);
              }
            }

            let savedMessage = null;

            if (conversation.channelType === 'whatsapp' || conversation.channelType === 'whatsapp_unofficial') {
              let recipient: string;

              if (conversation.isGroup) {
                if (!conversation.groupJid) {
                  throw new Error('Group conversation missing group JID');
                }
                recipient = conversation.groupJid;
                
              } else {
                if (!conversation.contactId) {
                  throw new Error('Individual conversation missing contact ID');
                }
                const contact = await storage.getContact(conversation.contactId);
                if (!contact) {
                  throw new Error(`Contact with ID ${conversation.contactId} not found`);
                }

                const phoneNumber = contact.identifier || contact.phone;
                if (!phoneNumber) {
                  throw new Error('No phone number found for contact');
                }
                recipient = phoneNumber;
                
              }

              savedMessage = await whatsAppService.sendMessage(
                conversation.channelId,
                userId,
                recipient,
                messageContent,
                false,
                conversationId
              );

              if (!savedMessage) {
                throw new Error('Failed to send WhatsApp message');
              }
            } else if (conversation.channelType === 'whatsapp_official') {
              if (!conversation.contactId) {
                throw new Error('Individual conversation missing contact ID');
              }
              const contact = await storage.getContact(conversation.contactId);
              if (!contact) {
                throw new Error(`Contact with ID ${conversation.contactId} not found`);
              }

              const phoneNumber = contact.identifier || contact.phone;
              if (!phoneNumber) {
                throw new Error('No phone number found for contact');
              }


              const user = await storage.getUser(userId);
              if (!user || !user.companyId) {
                throw new Error('Company ID is required for multi-tenant security');
              }

              savedMessage = await whatsAppOfficialService.sendMessage(
                conversation.channelId,
                userId,
                user.companyId,
                phoneNumber,
                messageContent
              );

              if (!savedMessage) {
                throw new Error('Failed to send WhatsApp Business API message');
              }
            } else {
              savedMessage = await storage.createMessage({
                conversationId,
                direction: 'outbound',
                type: 'text',
                content: messageContent,
                senderId: userId,
                senderType: 'user',
                isFromBot,
                externalId: `msg-${Date.now()}`,
                metadata: { timestamp: new Date().toISOString() }
              });

              if (conversation.channelType === 'instagram') {
                if (!conversation.contactId) {
                  throw new Error('Individual conversation missing contact ID');
                }
                const contact = await storage.getContact(conversation.contactId);
                if (!contact) {
                  throw new Error(`Contact with ID ${conversation.contactId} not found`);
                }

                const instagramId = contact.identifier;
                if (instagramId) {
                  
                  instagramService.sendMessage(
                    conversation.channelId,
                    instagramId,
                    messageContent
                  ).catch(err => console.error('Error sending Instagram message:', err));
                }
              } else if (conversation.channelType === 'messenger') {
                if (!conversation.contactId) {
                  throw new Error('Individual conversation missing contact ID');
                }
                const contact = await storage.getContact(conversation.contactId);
                if (!contact) {
                  throw new Error(`Contact with ID ${conversation.contactId} not found`);
                }

                const messengerId = contact.identifier;
                if (messengerId) {
                  
                  messengerService.sendMessage(
                    conversation.channelId,
                    messengerId,
                    messageContent
                  ).catch(err => console.error('Error sending Messenger message:', err));
                }
              }
            }

            if (savedMessage) {
              if (conversation.channelType !== 'whatsapp' && conversation.channelType !== 'whatsapp_unofficial' && conversation.channelType !== 'whatsapp_official') {
                broadcastToAll({
                  type: 'newMessage',
                  data: savedMessage
                });

                await storage.updateConversation(conversationId, {
                  lastMessageAt: new Date()
                });

                const updatedConversation = await storage.getConversation(conversationId);
                if (updatedConversation) {
                  broadcastToAll({
                    type: 'conversationUpdated',
                    data: updatedConversation
                  });
                }
              }
            }
          } catch (error) {
            console.error('Error processing message:', error);
            ws.send(JSON.stringify({
              type: 'error',
              message: 'Failed to send message'
            }));
          }
        }
      } catch (err) {
        console.error('Error processing WebSocket message:', err);
      }
    });

    ws.on('close', (code, reason) => {
      logger.websocket(`Client disconnected: ${clientId}, code: ${code}, reason: ${reason}`);
      cleanupClient(clientId);
    });

    ws.on('error', (error) => {
      logger.error('websocket', `Error for client ${clientId}`, error);
      cleanupClient(clientId);
    });
  });

  function broadcastToAll(data: any, companyId?: number) {
    const message = JSON.stringify(data);
    let sentCount = 0;

    clients.forEach((client, clientId) => {
      if (client.isAuthenticated && client.userId && client.socket.readyState === WebSocket.OPEN) {
        if (companyId && client.companyId !== companyId) {
          return;
        }

        try {
          client.socket.send(message);
          sentCount++;
        } catch (error) {
          console.error(`Error sending message to client ${clientId}:`, error);
          cleanupClient(clientId);
        }
      }
    });

    logger.verbose('websocket', `Broadcasted message to ${sentCount} clients${companyId ? ` (company ${companyId})` : ''}`);
  }

  function broadcastToCompany(data: any, companyId: number) {
    broadcastToAll(data, companyId);
  }

  (global as any).broadcastToAllClients = broadcastToAll;
  (global as any).broadcastToCompany = broadcastToCompany;


  let globalUnsubscribeFunctions: (() => void)[] = [];


  if ((global as any).globalWhatsAppListenersCleanup) {
    (global as any).globalWhatsAppListenersCleanup();
  }


  const unsubscribeWhatsAppMessageSent = whatsAppService.subscribeToEvents('messageSent', (data) => {
    if (data) {
      broadcastToAll({
        type: 'newMessage',
        data: data.message
      });

      broadcastToAll({
        type: 'conversationUpdated',
        data: data.conversation
      });
    }
  });

  const unsubscribeWhatsAppOfficialNewMessage = whatsAppOfficialService.subscribeToEvents('newMessage', (data) => {
    if (data) {
      broadcastToAll({
        type: 'newMessage',
        data: data.message
      });

      broadcastToAll({
        type: 'conversationUpdated',
        data: data.conversation
      });
    }
  });

  const unsubscribeEmailMessageReceived = emailService.subscribeToEvents('messageReceived', (data) => {
    if (data) {
      broadcastToAll({
        type: 'newMessage',
        data: data.message
      });

      broadcastToAll({
        type: 'conversationUpdated',
        data: data.conversation
      });
    }
  });

  const unsubscribeMessengerMessageReceived = messengerService.subscribeToEvents('messageReceived', (data: any) => {
    if (data) {
      broadcastToAll({
        type: 'newMessage',
        data: data.message
      });

      broadcastToAll({
        type: 'conversationUpdated',
        data: data.conversation
      });
    }
  });

  const unsubscribeMessengerMessageSent = messengerService.subscribeToEvents('messageSent', (data: any) => {
    if (data) {
      broadcastToAll({
        type: 'newMessage',
        data: data.message
      });

      broadcastToAll({
        type: 'conversationUpdated',
        data: data.conversation
      });
    }
  });

  const unsubscribeInstagramMessageReceived = instagramService.subscribeToEvents('messageReceived', (data: any) => {
    if (data) {
      broadcastToAll({
        type: 'newMessage',
        data: data.message
      });

      broadcastToAll({
        type: 'conversationUpdated',
        data: data.conversation
      });
    }
  });

  const unsubscribeInstagramMessageSent = instagramService.subscribeToEvents('messageSent', (data: any) => {
    if (data) {
      broadcastToAll({
        type: 'newMessage',
        data: data.message
      });

      broadcastToAll({
        type: 'conversationUpdated',
        data: data.conversation
      });
    }
  });

  globalUnsubscribeFunctions = [
    unsubscribeWhatsAppMessageSent,
    unsubscribeWhatsAppOfficialNewMessage,
    unsubscribeEmailMessageReceived,
    unsubscribeMessengerMessageReceived,
    unsubscribeMessengerMessageSent,
    unsubscribeInstagramMessageReceived,
    unsubscribeInstagramMessageSent
  ];


  (global as any).globalWhatsAppListenersCleanup = () => {
    globalUnsubscribeFunctions.forEach(unsubscribe => {
      try {
        unsubscribe();
      } catch (error) {
        console.error('Error cleaning up global WhatsApp listener:', error);
      }
    });
    globalUnsubscribeFunctions = [];
  };

  // Start EventEmitter monitoring
  eventEmitterMonitor.startMonitoring();
  logger.info('websocket', 'EventEmitter monitoring started');

  flowExecutor.setWebSocketClients(clients);

  const cleanupAllConnections = () => {
    logger.info('websocket', `Cleaning up ${clients.size} WebSocket connections...`);

    if (cleanupInterval) {
      clearInterval(cleanupInterval);
    }

    clients.forEach((client, clientId) => {
      cleanupClient(clientId);
    });


    if ((global as any).globalWhatsAppListenersCleanup) {
      (global as any).globalWhatsAppListenersCleanup();
    }

    // Stop EventEmitter monitoring
    eventEmitterMonitor.stopMonitoring();

    logger.info('websocket', 'All WebSocket connections and global listeners cleaned up');
  };

  process.on('SIGTERM', cleanupAllConnections);
  process.on('SIGINT', cleanupAllConnections);



  app.get('/api/channel-connections', ensureAuthenticated, async (req: any, res) => {
    try {
      // Ensure company ID is available for multi-tenant security
      if (!req.user || !req.user.companyId) {
        return res.status(400).json({ error: 'Company ID is required for multi-tenant security' });
      }

      const connections = await storage.getChannelConnections(req.user.id, req.user.companyId);
      res.json(connections);
    } catch (error) {
      console.error('Error fetching channel connections:', error);
      res.status(500).json({ error: 'Failed to fetch channel connections' });
    }
  });

  // DEPRECATED: Use /api/channel-connections instead
  app.get('/api/channels', ensureAuthenticated, async (req: any, res) => {
    try {
      // Add deprecation warning header
      res.set('X-Deprecated', 'This endpoint is deprecated. Use /api/channel-connections instead.');

      // Ensure company ID is available for multi-tenant security
      if (!req.user || !req.user.companyId) {
        return res.status(400).json({ error: 'Company ID is required for multi-tenant security' });
      }

      const connections = await storage.getChannelConnections(req.user.id, req.user.companyId);
      res.json(connections);
    } catch (error) {
      console.error('Error fetching channels:', error);
      res.status(500).json({ error: 'Failed to fetch channels' });
    }
  });

  app.patch('/api/channel-connections/:id', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.id);

      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ error: 'Channel connection not found' });
      }

      if (connection.userId !== req.user.id) {
        return res.status(403).json({ error: 'Not authorized to update this connection' });
      }

      if (req.body.accountName) {
        const updatedConnection = await storage.updateChannelConnectionName(connectionId, req.body.accountName);

        broadcastToCompany({
          type: 'channelConnectionUpdated',
          data: updatedConnection
        }, req.user.companyId);

        return res.json(updatedConnection);
      }

      res.status(400).json({ error: 'Missing required fields' });
    } catch (error) {
      console.error('Error updating channel connection:', error);
      res.status(500).json({ error: 'Failed to update channel connection' });
    }
  });


  app.put('/api/channel-connections/:id', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.id);

      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ error: 'Channel connection not found' });
      }


      if (!req.user || !req.user.companyId) {
        return res.status(400).json({ error: 'Company ID is required for multi-tenant security' });
      }


      if (connection.companyId !== req.user.companyId) {
        return res.status(403).json({ error: 'Access denied: Connection does not belong to your company' });
      }

      if (connection.userId !== req.user.id) {
        return res.status(403).json({ error: 'Not authorized to update this connection' });
      }


      const updateData: any = {};

      if (req.body.accountName) {
        updateData.accountName = req.body.accountName;
      }

      if (req.body.accessToken) {
        updateData.accessToken = req.body.accessToken;
      }

      if (req.body.connectionData) {

        updateData.connectionData = {
          ...(connection.connectionData || {}),
          ...req.body.connectionData
        };
      }


      const updatedConnection = await storage.updateChannelConnection(connectionId, updateData);


      if (connection.channelType === 'whatsapp_official' && (req.body.accessToken || req.body.connectionData)) {
        try {

          if (!req.user || !req.user.companyId) {
            throw new Error('Company ID is required for multi-tenant security');
          }

          const connectionData = updatedConnection.connectionData as any;
          await whatsAppOfficialService.initializeConnection(connectionId, req.user.companyId, {
            accessToken: req.body.accessToken || connection.accessToken || connectionData.accessToken,
            phoneNumberId: connectionData.phoneNumberId,
            wabaId: connectionData.wabaId || connectionData.businessAccountId,
            webhookVerifyToken: process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN || 'default_verify_token'
          });
        } catch (initError) {
          console.error('Failed to reinitialize WhatsApp Official service:', initError);

        }
      }

      broadcastToCompany({
        type: 'channelConnectionUpdated',
        data: updatedConnection
      }, req.user.companyId);

      res.json(updatedConnection);
    } catch (error) {
      console.error('Error updating channel connection:', error);
      res.status(500).json({ error: 'Failed to update channel connection' });
    }
  });


  app.get('/api/channel-connections/:id', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.id);

      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ error: 'Channel connection not found' });
      }

      if (connection.userId !== req.user.id) {
        return res.status(403).json({ error: 'Not authorized to access this connection' });
      }


      const sanitizedConnection = {
        ...connection,
        accessToken: undefined, // Don't send access token
        connectionData: {
          ...(connection.connectionData || {}),
          accessToken: undefined, // Don't send access token from connectionData
          appSecret: undefined // Don't send app secret
        }
      };

      res.json(sanitizedConnection);
    } catch (error) {
      console.error('Error retrieving channel connection:', error);
      res.status(500).json({ error: 'Failed to retrieve channel connection' });
    }
  });

  app.post('/api/channel-connections', ensureAuthenticated, async (req: any, res) => {
    try {

      if (!req.user || !req.user.companyId) {
        return res.status(400).json({ message: 'Company ID is required for multi-tenant security' });
      }

      const connectionData = validateBody(insertChannelConnectionSchema, {
        ...req.body,
        userId: req.user.id,
        companyId: req.user.companyId // Add companyId for multi-tenant security
      });

      const connection = await storage.createChannelConnection(connectionData);

      broadcastToCompany({
        type: 'channelConnectionCreated',
        data: connection
      }, req.user.companyId);

      if (connection.channelType === 'whatsapp_unofficial') {
        try {
          whatsAppService.connect(connection.id, req.user.id)
            .catch(err => console.error('Error connecting to WhatsApp:', err));

          res.status(201).json(connection);
        } catch (err: any) {
          console.error('Error initiating WhatsApp connection:', err);
          res.status(201).json(connection);
        }
      } else if (connection.channelType === 'whatsapp_official') {
        try {

          if (!req.user || !req.user.companyId) {
            return res.status(400).json({ message: 'Company ID is required for multi-tenant security' });
          }

          whatsAppOfficialService.connect(connection.id, req.user.id, req.user.companyId)
            .catch(err => console.error('Error connecting to WhatsApp Business API:', err));

          res.status(201).json(connection);
        } catch (err: any) {
          console.error('Error initiating WhatsApp Business API connection:', err);
          res.status(201).json(connection);
        }
      } else if (connection.channelType === 'whatsapp_twilio') {
        try {
          const connectionData = connection.connectionData as any;
          await whatsAppTwilioService.initializeConnection(connection.id, {
            accountSid: connectionData.accountSid,
            authToken: connectionData.authToken,
            conversationServiceSid: connectionData.conversationServiceSid,
            whatsappNumber: connectionData.whatsappNumber
          });

          res.status(201).json(connection);
        } catch (err: any) {
          console.error('Error initiating Twilio WhatsApp connection:', err);
          res.status(201).json(connection);
        }
      } else if (connection.channelType === 'whatsapp_360dialog') {
        try {
          const connectionData = connection.connectionData as any;

          if (connectionData.clientId && connectionData.channels && connection.companyId) {
            await whatsApp360DialogPartnerService.processOnboardingCallback(connection.companyId, {
              clientId: connectionData.clientId,
              channels: connectionData.channels
            });
          }

          await whatsApp360DialogPartnerService.connect(connection.id);

          res.status(201).json(connection);
        } catch (err: any) {
          console.error('Error initiating 360Dialog Partner WhatsApp connection:', err);
          res.status(201).json(connection);
        }
      } else if (connection.channelType === 'instagram') {
        try {
          instagramService.connect(connection.id, req.user.id, req.user.companyId)
            .catch(err => console.error('Error connecting to Instagram:', err));

          res.status(201).json(connection);
        } catch (err: any) {
          console.error('Error initiating Instagram connection:', err);
          res.status(201).json(connection);
        }
      } else if (connection.channelType === 'messenger') {
        try {
          messengerService.connect(connection.id, req.user.id)
            .catch(err => console.error('Error connecting to Messenger:', err));

          res.status(201).json(connection);
        } catch (err: any) {
          console.error('Error initiating Messenger connection:', err);
          res.status(201).json(connection);
        }
      } else {
        res.status(201).json(connection);
      }
    } catch (err: any) {
      res.status(400).json({ message: err.message });
    }
  });

  app.delete('/api/channel-connections/:id', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.id);
      const connection = await storage.getChannelConnection(connectionId);

      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }


      if (!req.user || !req.user.companyId) {
        return res.status(400).json({ message: 'Company ID is required for multi-tenant security' });
      }


      if (connection.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Access denied: Connection does not belong to your company' });
      }

      if (connection.userId !== req.user.id) {
        return res.status(403).json({ message: 'You do not have permission to delete this connection' });
      }

      if (connection.channelType === 'whatsapp_unofficial' || connection.channelType === 'whatsapp') {
        await whatsAppService.disconnect(connectionId, req.user.id);
      } else if (connection.channelType === 'whatsapp_official') {

        if (!req.user || !req.user.companyId) {
          return res.status(400).json({ message: 'Company ID is required for multi-tenant security' });
        }

        await whatsAppOfficialService.disconnect(connectionId, req.user.id, req.user.companyId);
      } else if (connection.channelType === 'whatsapp_twilio') {
        await whatsAppTwilioService.disconnect(connectionId);
      } else if (connection.channelType === 'whatsapp_360dialog') {
        await whatsApp360DialogPartnerService.disconnect(connectionId);
      } else if (connection.channelType === 'instagram') {
        await instagramService.disconnect(connectionId, req.user.id);
      } else if (connection.channelType === 'messenger') {
        await messengerService.disconnect(connectionId, req.user.id);
      }

      const deleted = await storage.deleteChannelConnection(connectionId);

      if (!deleted) {
        return res.status(500).json({ message: 'Failed to delete the connection' });
      }

      broadcastToCompany({
        type: 'channelConnectionDeleted',
        data: { id: connectionId }
      }, req.user.companyId);

      
      res.status(200).json({ message: 'Connection deleted successfully' });
    } catch (err: any) {
      console.error('Error deleting channel connection:', err);
      res.status(400).json({ message: err.message });
    }
  });

  app.post('/api/channel-connections/:id/reconnect', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.id);

      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ error: 'Connection not found' });
      }


      if (connection.channelType !== 'whatsapp' && connection.channelType !== 'whatsapp_unofficial' && connection.channelType !== 'whatsapp_official') {
        return res.status(400).json({ error: 'Only WhatsApp connections can be reconnected' });
      }

      try {
        await whatsAppService.disconnect(connectionId, req.user.id);
        
      } catch (disconnectErr) {
        console.error('Error during disconnect phase:', disconnectErr);
      }

      setTimeout(async () => {
        try {
          await whatsAppService.connect(connectionId, req.user.id);
          
        } catch (connectErr) {
          console.error('Error during reconnection:', connectErr);
        }
      }, 1000);

      res.status(200).json({ message: 'Reconnection initiated' });
    } catch (err: any) {
      console.error('Error reconnecting to WhatsApp:', err);
      res.status(500).json({ message: err.message });
    }
  });

  app.get('/api/whatsapp/status/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const connection = await storage.getChannelConnection(connectionId);

      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

     

      const isActive = whatsAppService.isConnectionActive(connectionId);

      res.json({
        connectionId,
        status: connection.status,
        isActive
      });
    } catch (err: any) {
      res.status(400).json({ message: err.message });
    }
  });

  app.put('/api/debug/connection-status/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      if (process.env.NODE_ENV === 'production') {
        return res.status(403).json({ message: 'Debug endpoints not available in production' });
      }

      const connectionId = parseInt(req.params.connectionId);
      const { status } = req.body;

      if (!status) {
        return res.status(400).json({ message: 'Status is required' });
      }

      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

    

      await storage.updateChannelConnectionStatus(connectionId, status);

      broadcastToCompany({
        type: 'connectionStatusUpdate',
        data: {
          connectionId,
          status
        }
      }, req.user.companyId);

      res.json({
        message: 'Connection status updated successfully',
        connectionId,
        oldStatus: connection.status,
        newStatus: status
      });
    } catch (err: any) {
      console.error('Error updating connection status:', err);
      res.status(500).json({ message: err.message });
    }
  });

  app.get('/api/whatsapp/diagnostics/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const connection = await storage.getChannelConnection(connectionId);

      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

  

      const diagnostics = whatsAppService.getConnectionDiagnostics(connectionId);

      res.json({
        connectionId,
        diagnostics,
        timestamp: new Date().toISOString()
      });
    } catch (err: any) {
      res.status(400).json({ message: err.message });
    }
  });

  app.post("/api/whatsapp/recover-connections", ensureAuthenticated, async (req: any, res) => {
    try {
      

      const { checkAndRecoverConnections } = await import('./services/channels/whatsapp');
      await checkAndRecoverConnections();

      res.json({
        message: "Connection recovery check completed",
        timestamp: new Date().toISOString()
      });
    } catch (err: any) {
      console.error('Error during manual connection recovery:', err);
      res.status(500).json({ message: err.message });
    }
  });

  app.get('/api/whatsapp/profile-picture/:connectionId/:phoneNumber', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const { phoneNumber } = req.params;

      const connection = await storage.getChannelConnection(connectionId);

      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      const isActive = whatsAppService.isConnectionActive(connectionId);
      if (!isActive) {
        return res.status(400).json({ message: 'WhatsApp connection is not active' });
      }

      const profilePictureUrl = await whatsAppService.fetchProfilePicture(connectionId, phoneNumber, true);

      if (profilePictureUrl) {
        res.json({ success: true, url: profilePictureUrl });
      } else {
        res.status(404).json({ message: 'No profile picture found for this contact' });
      }
    } catch (err: any) {
      console.error('Error fetching WhatsApp profile picture:', err);
      res.status(500).json({ message: err.message || 'Failed to fetch profile picture' });
    }
  });

  app.get('/api/whatsapp/profile-picture-url/:connectionId/:phoneNumber', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const { phoneNumber } = req.params;

      const connection = await storage.getChannelConnection(connectionId);

      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      

      const isActive = whatsAppService.isConnectionActive(connectionId);
      if (!isActive) {
        return res.status(400).json({ message: 'WhatsApp connection is not active' });
      }

      const profilePictureUrl = await whatsAppService.getProfilePictureUrl(connectionId, phoneNumber);

      if (profilePictureUrl) {
        res.json({ success: true, url: profilePictureUrl, direct: true });
      } else {
        res.status(404).json({ message: 'No profile picture found for this contact' });
      }
    } catch (err: any) {
      console.error('Error fetching WhatsApp profile picture URL:', err);
      res.status(500).json({ message: err.message || 'Failed to fetch profile picture URL' });
    }
  });

  app.get('/api/whatsapp/group-picture/:connectionId/:groupJid', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const { groupJid } = req.params;

      const connection = await storage.getChannelConnection(connectionId);

      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      

      const isActive = whatsAppService.isConnectionActive(connectionId);
      if (!isActive) {
        return res.status(400).json({ message: 'WhatsApp connection is not active' });
      }

      const { fetchGroupProfilePicture } = await import('./services/channels/whatsapp');
      const groupPictureUrl = await fetchGroupProfilePicture(connectionId, groupJid, false);

      if (groupPictureUrl) {
        res.json({ success: true, url: groupPictureUrl, direct: true });
      } else {
        res.status(404).json({ message: 'No group profile picture found' });
      }
    } catch (err: any) {
      console.error('Error fetching group profile picture:', err);
      res.status(500).json({ message: err.message || 'Failed to fetch group profile picture' });
    }
  });

  app.get('/api/whatsapp/participant-picture/:connectionId/:participantJid', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const { participantJid } = req.params;

      const connection = await storage.getChannelConnection(connectionId);

      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      

      const isActive = whatsAppService.isConnectionActive(connectionId);
      if (!isActive) {
        return res.status(400).json({ message: 'WhatsApp connection is not active' });
      }

      const { getParticipantProfilePictureUrl } = await import('./services/channels/whatsapp');
      const participantPictureUrl = await getParticipantProfilePictureUrl(connectionId, participantJid);

      if (participantPictureUrl) {
        res.json({ success: true, url: participantPictureUrl, direct: true });
      } else {
        res.status(404).json({ message: 'No participant profile picture found' });
      }
    } catch (err: any) {
      console.error('Error fetching participant profile picture:', err);
      res.status(500).json({ message: err.message || 'Failed to fetch participant profile picture' });
    }
  });

  app.post('/api/whatsapp/participants-pictures/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const { participantJids } = req.body;

      if (!Array.isArray(participantJids) || participantJids.length === 0) {
        return res.status(400).json({ message: 'participantJids array is required' });
      }

      const connection = await storage.getChannelConnection(connectionId);

      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      

      const isActive = whatsAppService.isConnectionActive(connectionId);
      if (!isActive) {
        return res.status(400).json({ message: 'WhatsApp connection is not active' });
      }

      const { fetchParticipantProfilePictures } = await import('./services/channels/whatsapp');
      const participantPictures = await fetchParticipantProfilePictures(connectionId, participantJids, false);

      const result = Object.fromEntries(participantPictures);

      res.json({
        success: true,
        participants: result,
        total: participantJids.length,
        found: Object.values(result).filter(url => url !== null).length
      });
    } catch (err: any) {
      console.error('Error fetching participant profile pictures:', err);
      res.status(500).json({ message: err.message || 'Failed to fetch participant profile pictures' });
    }
  });

  app.get('/api/whatsapp/validate-phone/:phoneNumber', ensureAuthenticated, async (req: any, res) => {
    try {
      const { phoneNumber } = req.params;

      const cleanPhoneNumber = phoneNumber.replace(/\D/g, '');

      const jid = `${cleanPhoneNumber}@s.whatsapp.net`;

      res.json({
        original: phoneNumber,
        cleaned: cleanPhoneNumber,
        jid: jid,
        valid: cleanPhoneNumber.length >= 10 && cleanPhoneNumber.length <= 15
      });
    } catch (err: any) {
      console.error('Error validating phone number:', err);
      res.status(500).json({ message: err.message || 'Failed to validate phone number' });
    }
  });

  app.post('/api/whatsapp/connect/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const connection = await storage.getChannelConnection(connectionId);

      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      

      await storage.updateChannelConnectionStatus(connectionId, 'connecting');

      try {
        await whatsAppService.connect(connectionId, req.user.id);
        
      } catch (err) {
        console.error('Error connecting to WhatsApp:', err);
        await storage.updateChannelConnectionStatus(connectionId, 'error');
      }

      res.json({ message: 'WhatsApp connection initiated' });
    } catch (err: any) {
      console.error('Error in connect endpoint:', err);
      res.status(400).json({ message: err.message });
    }
  });

  app.post('/api/whatsapp/disconnect/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const connection = await storage.getChannelConnection(connectionId);

      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      

      let success = false;

      if (connection.channelType === 'whatsapp_unofficial') {
        success = await whatsAppService.disconnect(connectionId, req.user.id);
      }
      else if (connection.channelType === 'whatsapp_official') {

        if (!req.user || !req.user.companyId) {
          return res.status(400).json({ message: 'Company ID is required for multi-tenant security' });
        }

        success = await whatsAppOfficialService.disconnect(connectionId, req.user.id, req.user.companyId);
      }
      else {
        return res.status(400).json({ message: 'Connection type not supported for disconnect' });
      }

      if (success) {
        res.json({ message: 'WhatsApp disconnected successfully' });
      } else {
        res.status(500).json({ message: 'Failed to disconnect from WhatsApp' });
      }
    } catch (err: any) {
      res.status(400).json({ message: err.message });
    }
  });

  app.post('/api/whatsapp/reconnect/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      if (isNaN(connectionId)) {
        return res.status(400).json({ error: 'Invalid connection ID' });
      }

      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ error: 'Connection not found' });
      }


      if (connection.channelType === 'whatsapp_unofficial') {
        try {
          await whatsAppService.disconnect(connectionId, req.user.id);
          
        } catch (disconnectErr) {
          console.error('Error during disconnect phase:', disconnectErr);
        }

        await storage.updateChannelConnectionStatus(connectionId, 'reconnecting');

        setTimeout(async () => {
          try {
            await whatsAppService.connect(connectionId, req.user.id);
            
          } catch (connectErr) {
            console.error('Error during reconnection:', connectErr);
            await storage.updateChannelConnectionStatus(connectionId, 'error');
          }
        }, 1000);
      }
      else if (connection.channelType === 'whatsapp_official') {
        try {

          if (!req.user || !req.user.companyId) {
            return res.status(400).json({ message: 'Company ID is required for multi-tenant security' });
          }

          await whatsAppOfficialService.connect(connectionId, req.user.id, req.user.companyId);

        } catch (connectErr) {
          console.error('Error during WhatsApp Business API reconnect:', connectErr);
          await storage.updateChannelConnectionStatus(connectionId, 'error');
          return res.status(500).json({ error: connectErr instanceof Error ? connectErr.message : String(connectErr) });
        }
      }
      else {
        return res.status(400).json({ error: 'Only WhatsApp connections can be reconnected' });
      }

      res.status(200).json({ message: 'Reconnection initiated' });
    } catch (err: any) {
      console.error('Error reconnecting to WhatsApp:', err);
      res.status(500).json({ message: err.message });
    }
  });

  app.post('/api/whatsapp/send/:connectionId', ensureAuthenticated, requireAnyPermission([PERMISSIONS.MANAGE_CHANNELS, PERMISSIONS.MANAGE_CONVERSATIONS]), async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const { to, message } = req.body;

      

      if (!to || !message) {
        return res.status(400).json({ message: 'Missing required parameters' });
      }

      const connection = await storage.getChannelConnection(connectionId);

      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      
      let messageContent = message;
      try {
        const user = await storage.getUser(req.user.id);
        if (user && user.fullName) {
          messageContent = `> *${user.fullName}*\n\n${message}`;
        }
      } catch (userError) {
        console.error('Error fetching user for signature in WhatsApp send:', userError);
      }

      let sentMessage;

      if (connection.channelType === 'whatsapp_unofficial') {
        sentMessage = await whatsAppService.sendMessage(connectionId, req.user.id, to, messageContent);
      }
      else if (connection.channelType === 'whatsapp_official') {
        try {

          if (!req.user || !req.user.companyId) {
            return res.status(400).json({ message: 'Company ID is required for multi-tenant security' });
          }

          sentMessage = await whatsAppOfficialService.sendMessage(connectionId, req.user.id, req.user.companyId, to, messageContent);
        } catch (error: any) {
          console.error(`Failed to send WhatsApp Business API message:`, error);
          return res.status(500).json({ message: error.message || 'Failed to send message' });
        }
      }
      else if (connection.channelType === 'whatsapp_twilio') {
        sentMessage = await whatsAppTwilioService.sendMessage(connectionId, req.user.id, to, messageContent);
      }
      else if (connection.channelType === 'whatsapp_360dialog') {
        sentMessage = await whatsApp360DialogPartnerService.sendMessage(connectionId, req.user.id, to, messageContent);
      }
      else {
        return res.status(400).json({ message: 'Unsupported channel type for sending messages' });
      }

      if (sentMessage) {
        
        res.status(201).json(sentMessage);
      } else {
        console.error(`Failed to send WhatsApp message to ${to}`);
        res.status(500).json({ message: 'Failed to send WhatsApp message' });
      }
    } catch (err: any) {
      console.error('Error sending WhatsApp message:', err);
      res.status(400).json({ message: err.message });
    }
  });


  app.post('/api/whatsapp/test-template/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const { phoneNumber, templateName = 'hello_world', languageCode = 'en_US' } = req.body;

      if (!phoneNumber) {
        return res.status(400).json({ error: 'Phone number is required' });
      }


      if (!req.user || !req.user.companyId) {
        return res.status(400).json({ error: 'Company ID is required for multi-tenant security' });
      }

      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ error: 'Connection not found' });
      }


      if (connection.companyId !== req.user.companyId) {
        return res.status(403).json({ error: 'Access denied: Connection does not belong to your company' });
      }

      if (connection.userId !== req.user.id) {
        return res.status(403).json({ error: 'Not authorized to use this connection' });
      }

      if (connection.channelType !== 'whatsapp_official') {
        return res.status(400).json({ error: 'Template testing only supported for WhatsApp Business API connections' });
      }

      const result = await whatsAppOfficialService.sendWhatsAppTestTemplate(
        connectionId,
        req.user.companyId,
        phoneNumber,
        templateName,
        languageCode
      );

      if (result.success) {
        res.json({
          success: true,
          message: 'Template message sent successfully',
          messageId: result.messageId
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error || 'Failed to send template message'
        });
      }
    } catch (err: any) {
      console.error('Error testing WhatsApp template:', err);
      res.status(500).json({ error: err.message || 'Internal server error' });
    }
  });

  app.post('/api/conversations/:id/upload-media-old', ensureAuthenticated, upload.single('file'), async (req: any, res) => {
    try {
      const conversationId = parseInt(req.params.id);
      if (isNaN(conversationId)) {
        return res.status(400).json({ error: 'Invalid conversation ID' });
      }

      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      const { caption = '', mediaType = 'auto' } = req.body;

      const conversation = await db.query.conversations.findFirst({
        where: eq(conversations.id, conversationId)
      });

      if (!conversation) {
        await fsExtra.unlink(req.file.path);
        return res.status(404).json({ error: 'Conversation not found' });
      }

      if (!conversation.channelId) {
        await fsExtra.unlink(req.file.path);
        return res.status(400).json({ error: 'Conversation has no channel ID' });
      }

      if (conversation.channelType !== 'whatsapp' && conversation.channelType !== 'whatsapp_unofficial' && conversation.channelType !== 'whatsapp_official' && conversation.channelType !== 'whatsapp_twilio' && conversation.channelType !== 'whatsapp_360dialog') {
        await fsExtra.unlink(req.file.path);
        return res.status(400).json({ error: 'Media upload only supported for WhatsApp channels' });
      }

      let determinedMediaType: 'image' | 'video' | 'audio' | 'document' = 'document';
      if (mediaType === 'auto') {
        if (req.file.mimetype.startsWith('image/')) {
          determinedMediaType = 'image';
        } else if (req.file.mimetype.startsWith('video/')) {
          determinedMediaType = 'video';
        } else if (req.file.mimetype.startsWith('audio/')) {
          determinedMediaType = 'audio';
        } else {
          determinedMediaType = 'document';
        }
      } else {
        if (['image', 'video', 'audio', 'document'].includes(mediaType)) {
          determinedMediaType = mediaType as 'image' | 'video' | 'audio' | 'document';
        }
      }

      if (!conversation.contactId) {
        await fsExtra.unlink(req.file.path);
        return res.status(400).json({ error: 'Individual conversation missing contact ID' });
      }

      const contact = await db.query.contacts.findFirst({
        where: eq(contacts.id, conversation.contactId)
      });

      if (!contact) {
        await fsExtra.unlink(req.file.path);
        return res.status(404).json({ error: 'Contact not found' });
      }

      let message;

      if (conversation.channelType === 'whatsapp_official') {

        if (!req.user || !req.user.companyId) {
          await fsExtra.unlink(req.file.path);
          return res.status(400).json({ error: 'Company ID is required for multi-tenant security' });
        }

        const publicUrl = `${req.protocol}://${req.get('host')}/uploads/${path.basename(req.file.path)}`;

        const publicPath = path.join(process.cwd(), 'uploads', path.basename(req.file.path));
        await fsExtra.copy(req.file.path, publicPath);

        message = await whatsAppOfficialService.sendMedia(
          conversation.channelId,
          req.user.id,
          req.user.companyId,
          contact.identifier || contact.phone || '',
          determinedMediaType,
          publicUrl,
          caption,
          req.file.originalname,
          undefined, // originalMimeType
          false // isFromBot = false for user-sent media messages
        );
      } else if (conversation.channelType === 'whatsapp_twilio') {
        const publicUrl = `${req.protocol}://${req.get('host')}/uploads/${path.basename(req.file.path)}`;

        const publicPath = path.join(process.cwd(), 'uploads', path.basename(req.file.path));
        await fsExtra.copy(req.file.path, publicPath);

        message = await whatsAppTwilioService.sendMedia(
          conversation.channelId,
          req.user.id,
          contact.identifier || contact.phone || '',
          determinedMediaType,
          publicUrl,
          caption,
          req.file.originalname
        );
      } else if (conversation.channelType === 'whatsapp_360dialog') {
        const publicUrl = `${req.protocol}://${req.get('host')}/uploads/${path.basename(req.file.path)}`;

        const publicPath = path.join(process.cwd(), 'uploads', path.basename(req.file.path));
        await fsExtra.copy(req.file.path, publicPath);

        message = await whatsApp360DialogPartnerService.sendMedia(
          conversation.channelId,
          req.user.id,
          contact.identifier || contact.phone || '',
          determinedMediaType,
          publicUrl,
          caption,
          req.file.originalname
        );
      } else {
        message = await whatsAppService.sendMedia(
          conversation.channelId,
          req.user.id,
          contact.identifier || contact.phone || '',
          determinedMediaType,
          req.file.path,
          caption,
          req.file.originalname,
          false,
          conversationId
        );
      }

      if (!message) {
        await fsExtra.unlink(req.file.path);
        return res.status(500).json({ error: 'Failed to send media message' });
      }

      await fsExtra.unlink(req.file.path);

      return res.status(201).json(message);
    } catch (error: any) {
      console.error('Error uploading media:', error);

      if (req.file && req.file.path) {
        try {
          await fsExtra.unlink(req.file.path);
        } catch (unlinkError) {
          console.error('Error deleting uploaded file:', unlinkError);
        }
      }

      return res.status(500).json({
        error: 'Internal server error',
        message: error.message
      });
    }
  });

  app.get('/api/contacts', ensureAuthenticated, async (req, res) => {
    try {
      const user = req.user as any;

      const page = req.query.page ? parseInt(req.query.page as string) : 1;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
      const search = req.query.search as string || undefined;
      const channel = req.query.channel as string || undefined;

      const companyId = user.isSuperAdmin ? undefined : user.companyId;

      const result = await storage.getContacts({ page, limit, search, channel, companyId });

      res.json(result);
    } catch (error) {
      console.error('Error fetching contacts:', error);
      res.status(500).json({ message: 'Failed to fetch contacts' });
    }
  });

  app.get('/api/contacts/csv-template', ensureAuthenticated, (req, res) => {
    try {
      const headers = ['name', 'phone', 'email', 'company', 'tags', 'notes'];
      const sampleData = [
        'John Doe,+1234567890,<EMAIL>,Acme Corp,"vip,customer",Important client',
        'Jane Smith,+0987654321,<EMAIL>,Tech Inc,"lead,prospect",New lead from website',
        'Bob Johnson,+1122334455,<EMAIL>,StartupXYZ,"customer",Regular customer'
      ];

      const csvContent = [headers.join(','), ...sampleData].join('\n');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename="contact_import_template.csv"');
      res.send(csvContent);
    } catch (error) {
      console.error('Error generating CSV template:', error);
      res.status(500).json({ error: 'Failed to generate CSV template' });
    }
  });

  app.get('/api/contacts/:id', ensureAuthenticated, async (req, res) => {
    const id = parseInt(req.params.id);
    const contact = await storage.getContact(id);

    if (!contact) {
      return res.status(404).json({ message: 'Contact not found' });
    }


    if (isWhatsAppGroupChatId(contact.phone) || isWhatsAppGroupChatId(contact.identifier)) {
      return res.status(404).json({ message: 'Contact not found' });
    }

    res.json(contact);
  });

  app.patch('/api/contacts/:id', ensureAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);

      const contact = await storage.getContact(id);
      if (!contact) {
        return res.status(404).json({ message: 'Contact not found' });
      }

      const updateData = req.body;
      const updatedContact = await storage.updateContact(id, updateData);

      if ((global as any).broadcastToAllClients) {
        (global as any).broadcastToAllClients({
          type: 'contactUpdated',
          data: updatedContact
        });
      }

      res.json(updatedContact);
    } catch (error) {
      console.error('Error updating contact:', error);
      res.status(500).json({ message: 'Failed to update contact' });
    }
  });

  app.delete('/api/contacts/bulk', ensureAuthenticated, async (req: any, res) => {
    try {
      const { contactIds } = req.body;

      if (!Array.isArray(contactIds) || contactIds.length === 0) {
        return res.status(400).json({ error: 'Contact IDs array is required' });
      }

      const validIds = contactIds.filter(id => {
        const numId = Number(id);
        return Number.isInteger(numId) && numId > 0;
      });

      if (validIds.length !== contactIds.length) {
        const invalidIds = contactIds.filter(id => {
          const numId = Number(id);
          return !Number.isInteger(numId) || numId <= 0;
        });
        console.error('Invalid contact IDs:', invalidIds);
        return res.status(400).json({
          error: `Invalid contact IDs: ${invalidIds.join(', ')}. All contact IDs must be valid positive integers.`
        });
      }

      const results = {
        successful: [] as number[],
        failed: [] as { id: number; error: string }[],
        total: contactIds.length
      };

      const batchSize = 10;
      for (let i = 0; i < contactIds.length; i += batchSize) {
        const batch = contactIds.slice(i, i + batchSize);

        await Promise.all(batch.map(async (contactId: any) => {
          try {
            const numericContactId = Number(contactId);

            const contact = await storage.getContact(numericContactId);
            if (!contact) {
              results.failed.push({ id: numericContactId, error: 'Contact not found' });
              return;
            }

            if (contact.companyId !== req.user.companyId) {
              results.failed.push({ id: numericContactId, error: 'Access denied' });
              return;
            }

            const success = await storage.deleteContact(numericContactId);
            if (success) {
              results.successful.push(numericContactId);
            } else {
              results.failed.push({ id: numericContactId, error: 'Failed to delete contact' });
            }
          } catch (error) {
            console.error(`Error deleting contact ${contactId}:`, error);
            results.failed.push({
              id: Number(contactId),
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }));
      }

      if (results.successful.length > 0) {
        broadcastToAll({
          type: 'contactsBulkDeleted',
          data: {
            deletedIds: results.successful,
            companyId: req.user.companyId
          }
        });
      }

      res.json(results);

    } catch (error) {
      console.error('Error in bulk delete contacts:', error);
      res.status(500).json({
        error: error instanceof Error ? error.message : 'Failed to delete contacts'
      });
    }
  });

  app.delete('/api/contacts/:id', ensureAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);

      const contact = await storage.getContact(id);
      if (!contact) {
        return res.status(404).json({ message: 'Contact not found' });
      }

      const success = await storage.deleteContact(id);

      if (success) {
        res.json({ message: 'Contact deleted successfully' });
      } else {
        res.status(500).json({ message: 'Failed to delete contact' });
      }
    } catch (error) {
      console.error('Error deleting contact:', error);
      res.status(500).json({ message: 'Server error' });
    }
  });

  app.post('/api/contacts', ensureAuthenticated, async (req: any, res) => {
    try {
      const contactData = validateBody(insertContactSchema, {
        ...req.body,
        companyId: req.user.companyId
      });


      if (contactData.phone && isWhatsAppGroupChatId(contactData.phone)) {
        return res.status(400).json({
          message: 'Cannot create contacts with WhatsApp group chat IDs'
        });
      }

      if (contactData.identifier && isWhatsAppGroupChatId(contactData.identifier)) {
        return res.status(400).json({
          message: 'Cannot create contacts with WhatsApp group chat IDs'
        });
      }

      const contact = await storage.createContact(contactData);
      res.status(201).json(contact);
    } catch (err: any) {
      res.status(400).json({ message: err.message });
    }
  });

  const csvUploadStorage = multer.diskStorage({
    destination: (req, file, cb) => {
      const uploadPath = path.join(process.cwd(), 'uploads', 'csv');
      if (!fs.existsSync(uploadPath)) {
        fs.mkdirSync(uploadPath, { recursive: true });
      }
      cb(null, uploadPath);
    },
    filename: (req, file, cb) => {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `contacts-import-${timestamp}-${file.originalname}`;
      cb(null, filename);
    }
  });

  const csvUpload = multer({
    storage: csvUploadStorage,
    limits: {
      fileSize: 10 * 1024 * 1024,
      files: 1
    },
    fileFilter: (req, file, cb) => {
      if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
        cb(null, true);
      } else {
        cb(new Error('Only CSV files are allowed.'));
      }
    }
  });

  app.post('/api/contacts/import-for-segment', ensureAuthenticated, csvUpload.single('csvFile'), async (req: any, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No CSV file provided' });
      }

      const { duplicateHandling = 'skip', columnMapping } = req.body;
      const companyId = req.user.companyId;
      let mapping: Record<string, string> = {};

      try {
        mapping = columnMapping ? JSON.parse(columnMapping) : {};
      } catch (error) {
        console.error('Error parsing column mapping:', error);
      }

      const csvData = fs.readFileSync(req.file.path, 'utf8');
      const lines = csvData.split('\n').filter(line => line.trim());

      if (lines.length < 2) {
        return res.status(400).json({ error: 'CSV file must contain at least a header row and one data row' });
      }

      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));


      const nameColumn = Object.keys(mapping).find(key => mapping[key] === 'name');
      if (!nameColumn) {
        return res.status(400).json({
          error: 'Name field must be mapped to import contacts'
        });
      }

      let successful = 0;
      let failed = 0;
      const errors: string[] = [];
      const importedContacts: any[] = [];

      for (let i = 1; i < lines.length; i++) {
        try {
          const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
          const contactData: any = { companyId };

          headers.forEach((header, index) => {
            const mappedField = mapping[header];
            const value = values[index] || '';

            if (mappedField && mappedField !== '__skip__' && value) {
              switch (mappedField) {
                case 'name':
                  contactData.name = value;
                  break;
                case 'email':
                  contactData.email = value;
                  break;
                case 'phone':
                  contactData.phone = value;
                  break;
                case 'company':
                  contactData.company = value;
                  break;
                case 'notes':
                  contactData.notes = value;
                  break;
                case 'tags':
                  contactData.tags = value.split(',').map(t => t.trim()).filter(Boolean);
                  break;
              }
            }
          });

          contactData.source = 'csv_import';
          contactData.isActive = true;

          if (!contactData.name || contactData.name.trim() === '') {
            errors.push(`Row ${i + 1}: Name is required`);
            failed++;
            continue;
          }

          if (contactData.phone) {
            const phoneDigits = contactData.phone.replace(/[^0-9]/g, '');
            if (phoneDigits.length > 14) {
              errors.push(`Row ${i + 1}: Phone number too long (max 14 digits)`);
              failed++;
              continue;
            }
          }

          if (contactData.email && duplicateHandling !== 'create') {
            const existingContact = await storage.getContactByEmail(contactData.email, companyId);

            if (existingContact) {
              if (duplicateHandling === 'skip') {
                continue;
              } else if (duplicateHandling === 'update') {
                await storage.updateContact(existingContact.id, contactData);
                importedContacts.push(existingContact);
                successful++;
                continue;
              }
            }
          }

          const newContact = await storage.createContact(contactData);
          importedContacts.push(newContact);
          successful++;

        } catch (error) {
          console.error(`Error processing row ${i + 1}:`, error);
          errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          failed++;
        }
      }

      try {
        fs.unlinkSync(req.file.path);
      } catch (cleanupError) {
        console.error('Error cleaning up CSV file:', cleanupError);
      }

      res.json({
        successful,
        failed,
        errors: errors.slice(0, 10),
        importedContacts: importedContacts.slice(0, 50)
      });

    } catch (error) {
      console.error('Error importing contacts for segment:', error);

      if (req.file && req.file.path) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (cleanupError) {
          console.error('Error cleaning up uploaded file:', cleanupError);
        }
      }

      res.status(500).json({
        error: error instanceof Error ? error.message : 'Failed to import contacts'
      });
    }
  });



  app.post('/api/contacts/import', ensureAuthenticated, csvUpload.single('csvFile'), async (req: any, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No CSV file provided' });
      }

      const { duplicateHandling = 'skip' } = req.body;
      const companyId = req.user.companyId;

      const csvData = fs.readFileSync(req.file.path, 'utf8');
      const lines = csvData.split('\n').filter(line => line.trim());

      if (lines.length < 2) {
        return res.status(400).json({ error: 'CSV file must contain at least a header row and one data row' });
      }

      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
      const requiredHeaders = ['name'];
      const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));

      if (missingHeaders.length > 0) {
        return res.status(400).json({
          error: `Missing required headers: ${missingHeaders.join(', ')}`
        });
      }

      let successful = 0;
      let failed = 0;
      const errors: string[] = [];

      for (let i = 1; i < lines.length; i++) {
        try {
          const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
          const contactData: any = { companyId };

          headers.forEach((header, index) => {
            const value = values[index] || '';
            switch (header.toLowerCase()) {
              case 'name':
                contactData.name = value;
                break;
              case 'email':
                contactData.email = value || null;
                break;
              case 'phone':
                contactData.phone = value || null;
                break;
              case 'company':
                contactData.company = value || null;
                break;
              case 'identifiertype':
              case 'identifier_type':
                contactData.identifierType = value || null;
                break;
              case 'identifier':
                contactData.identifier = value || null;
                break;
              case 'notes':
                contactData.notes = value || null;
                break;
              case 'tags':
                contactData.tags = value ? value.split(',').map(t => t.trim()).filter(Boolean) : null;
                break;
              case 'source':
                contactData.source = value || 'csv_import';
                break;
            }
          });

          contactData.source = contactData.source || 'csv_import';
          contactData.isActive = true;

          if (!contactData.name || contactData.name.trim() === '') {
            errors.push(`Row ${i + 1}: Name is required`);
            failed++;
            continue;
          }

          if (contactData.email && duplicateHandling !== 'create') {
            const existingContact = await storage.getContactByEmail(contactData.email, companyId);

            if (existingContact) {
              if (duplicateHandling === 'skip') {
                continue;
              } else if (duplicateHandling === 'update') {
                await storage.updateContact(existingContact.id, contactData);
                successful++;
                continue;
              }
            }
          }

          await storage.createContact(contactData);
          successful++;

        } catch (error) {
          console.error(`Error processing row ${i + 1}:`, error);
          errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          failed++;
        }
      }

      try {
        fs.unlinkSync(req.file.path);
      } catch (cleanupError) {
        console.error('Error cleaning up CSV file:', cleanupError);
      }

      res.json({
        successful,
        failed,
        errors: errors.slice(0, 10)
      });

    } catch (error) {
      console.error('Error importing contacts:', error);

      if (req.file && req.file.path) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (cleanupError) {
          console.error('Error cleaning up uploaded file:', cleanupError);
        }
      }

      res.status(500).json({
        error: error instanceof Error ? error.message : 'Failed to import contacts'
      });
    }
  });

  app.post('/api/contacts/:id/avatar', ensureAuthenticated, upload.single('avatar'), async (req: any, res) => {
    try {
      const contactId = parseInt(req.params.id);

      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }

      const contact = await storage.getContact(contactId);
      if (!contact) {
        await fsExtra.unlink(req.file.path);
        return res.status(404).json({ message: 'Contact not found' });
      }

      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(req.file.mimetype)) {
        await fsExtra.unlink(req.file.path);
        return res.status(400).json({ message: 'Invalid file type. Only images are allowed.' });
      }

      const avatarUrl = `/uploads/${req.file.filename}`;

      const updatedContact = await storage.updateContact(contactId, {
        avatarUrl
      });

      if ((global as any).broadcastToAllClients) {
        (global as any).broadcastToAllClients({
          type: 'contactUpdated',
          data: updatedContact
        });
      }

      res.json({
        success: true,
        contact: updatedContact,
        avatarUrl
      });
    } catch (err: any) {
      console.error('Error uploading contact avatar:', err);
      if (req.file) {
        await fsExtra.unlink(req.file.path).catch(() => { });
      }
      res.status(500).json({ message: err.message });
    }
  });

  app.post('/api/contacts/:id/update-profile-picture', ensureAuthenticated, async (req: any, res) => {
    try {
      const contactId = parseInt(req.params.id);
      const { connectionId } = req.body;

      if (!connectionId) {
        return res.status(400).json({ message: 'WhatsApp connection ID is required' });
      }

      const contact = await storage.getContact(contactId);
      if (!contact) {
        return res.status(404).json({ message: 'Contact not found' });
      }

      if (!contact.identifier || contact.identifierType !== 'whatsapp') {
        return res.status(400).json({
          message: 'Contact does not have a WhatsApp identifier'
        });
      }

      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

   

      const isActive = whatsAppService.isConnectionActive(connectionId);
      if (!isActive) {
        return res.status(400).json({ message: 'WhatsApp connection is not active' });
      }

      const profilePictureUrl = await whatsAppService.fetchProfilePicture(
        connectionId,
        contact.identifier
      );

      if (!profilePictureUrl) {
        return res.status(404).json({ message: 'No profile picture found for this contact' });
      }

      const updatedContact = await storage.updateContact(contactId, {
        avatarUrl: profilePictureUrl
      });

      if ((global as any).broadcastToAllClients) {
        (global as any).broadcastToAllClients({
          type: 'contactUpdated',
          data: updatedContact
        });
      }

      res.json({
        success: true,
        contact: updatedContact,
        profilePictureUrl
      });
    } catch (err: any) {
      console.error('Error updating group profile picture:', err);
      res.status(500).json({ message: err.message || 'Failed to update group profile picture' });
    }
  });

  app.post('/api/conversations/:id/update-group-picture', ensureAuthenticated, async (req: any, res) => {
    try {
      const conversationId = parseInt(req.params.id);
      const { connectionId } = req.body;

      if (!connectionId) {
        return res.status(400).json({ message: 'WhatsApp connection ID is required' });
      }

      const conversation = await storage.getConversation(conversationId);
      if (!conversation) {
        return res.status(404).json({ message: 'Conversation not found' });
      }

      if (!conversation.isGroup || !conversation.groupJid) {
        return res.status(400).json({
          message: 'This conversation is not a group chat'
        });
      }

      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }



      const isActive = whatsAppService.isConnectionActive(connectionId);
      if (!isActive) {
        return res.status(400).json({ message: 'WhatsApp connection is not active' });
      }

      

      const groupPictureUrl = await whatsAppService.fetchGroupProfilePicture(
        connectionId,
        conversation.groupJid
      );

      

      if (!groupPictureUrl) {
        return res.status(404).json({ message: 'No group profile picture found' });
      }

      const updatedGroupMetadata = {
        ...(conversation.groupMetadata || {}),
        profilePictureUrl: groupPictureUrl
      };

      const updatedConversation = await storage.updateConversation(conversationId, {
        groupMetadata: updatedGroupMetadata
      });

      if ((global as any).broadcastToAllClients) {
        (global as any).broadcastToAllClients({
          type: 'conversationUpdated',
          data: updatedConversation
        });
      }

      res.json({
        success: true,
        conversation: updatedConversation,
        groupPictureUrl
      });
    } catch (err: any) {
      console.error('Error updating group profile picture:', err);
      res.status(500).json({ message: err.message || 'Failed to update group profile picture' });
    }
  });



  app.get('/api/conversations', ensureAuthenticated, requireAnyPermission([PERMISSIONS.VIEW_ALL_CONVERSATIONS, PERMISSIONS.VIEW_ASSIGNED_CONVERSATIONS]), async (req, res) => {
    try {
      const user = req.user as any;
      const userPermissions = (req as any).userPermissions;

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 50;
      const search = req.query.search as string;

      const companyId = user.isSuperAdmin ? undefined : user.companyId;
      const { conversations, total } = await storage.getConversations({
        companyId,
        page,
        limit,
        search
      });

      let filteredConversations = conversations;
      if (userPermissions && !userPermissions[PERMISSIONS.VIEW_ALL_CONVERSATIONS]) {
        filteredConversations = conversations.filter(conversation =>
          conversation.assignedToUserId === user.id
        );
      }

      const conversationsWithContacts = await Promise.all(
        filteredConversations.map(async (conversation) => {
          const contact = conversation.contactId ? await storage.getContact(conversation.contactId) : null;
          return {
            ...conversation,
            contact
          };
        })
      );

      res.json({
        conversations: conversationsWithContacts,
        total: filteredConversations.length,
        page,
        limit,
        totalPages: Math.ceil(filteredConversations.length / limit)
      });
    } catch (error) {
      console.error('Error fetching conversations:', error);
      res.status(500).json({ error: 'Failed to fetch conversations' });
    }
  });

  app.get('/api/conversations/:id', ensureAuthenticated, requireAnyPermission([PERMISSIONS.VIEW_ALL_CONVERSATIONS, PERMISSIONS.VIEW_ASSIGNED_CONVERSATIONS]), async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const user = req.user as any;
      const userPermissions = (req as any).userPermissions;

      const conversation = await storage.getConversation(id);

      if (!conversation) {
        return res.status(404).json({ message: 'Conversation not found' });
      }


      if (conversation.isGroup || conversation.groupJid) {
        return res.status(404).json({ message: 'Conversation not found' });
      }


      if (conversation.contactId) {
        const contact = await storage.getContact(conversation.contactId);
        if (contact && (isWhatsAppGroupChatId(contact.phone) || isWhatsAppGroupChatId(contact.identifier))) {
          return res.status(404).json({ message: 'Conversation not found' });
        }
      }

      if (!user.isSuperAdmin) {
        if (user.companyId && conversation.companyId !== user.companyId && conversation.companyId !== null) {
          return res.status(403).json({ message: 'Access denied' });
        }

        if (userPermissions && !userPermissions[PERMISSIONS.VIEW_ALL_CONVERSATIONS]) {
          if (conversation.assignedToUserId !== user.id) {
            return res.status(403).json({ message: 'Access denied' });
          }
        }
      }

      const contact = conversation.contactId ? await storage.getContact(conversation.contactId) : null;

      res.json({
        ...conversation,
        contact
      });
    } catch (error) {
      console.error('Error fetching conversation:', error);
      res.status(500).json({ error: 'Failed to fetch conversation' });
    }
  });

  app.post('/api/conversations/:id/mark-read', ensureAuthenticated, async (req: any, res) => {
    try {
      const conversationId = parseInt(req.params.id);
      const user = req.user as any;

      const conversation = await storage.getConversation(conversationId);
      if (!conversation) {
        return res.status(404).json({ message: 'Conversation not found' });
      }

      if (!user.isSuperAdmin && user.companyId && conversation.companyId !== user.companyId && conversation.companyId !== null) {
        return res.status(403).json({ message: 'Access denied' });
      }

      await storage.markConversationAsRead(conversationId);

      broadcastToAll({
        type: 'unreadCountUpdated',
        data: {
          conversationId,
          unreadCount: 0
        }
      });

      res.json({
        success: true,
        conversationId,
        unreadCount: 0
      });
    } catch (err: any) {
      console.error('Error marking conversation as read:', err);
      res.status(500).json({ message: err.message || 'Failed to mark conversation as read' });
    }
  });

  app.get('/api/conversations/unread-counts', ensureAuthenticated, async (req: any, res) => {
    try {
      const user = req.user as any;
      const unreadCounts = await storage.getAllUnreadCounts(user.id);
      res.json(unreadCounts);
    } catch (err: any) {
      console.error('Error fetching unread counts:', err);
      res.status(500).json({ message: err.message || 'Failed to fetch unread counts' });
    }
  });

  app.get('/api/conversations/:id/unread-count', ensureAuthenticated, async (req: any, res) => {
    try {
      const conversationId = parseInt(req.params.id);
      const user = req.user as any;

      const conversation = await storage.getConversation(conversationId);
      if (!conversation) {
        return res.status(404).json({ message: 'Conversation not found' });
      }

      if (!user.isSuperAdmin && user.companyId && conversation.companyId !== user.companyId && conversation.companyId !== null) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const unreadCount = await storage.getUnreadCount(conversationId);
      res.json({ unreadCount });
    } catch (err: any) {
      console.error('Error fetching unread count:', err);
      res.status(500).json({ message: err.message || 'Failed to fetch unread count' });
    }
  });

  app.post('/api/conversations/whatsapp/initiate', ensureAuthenticated, async (req: any, res) => {
    try {
      const { name, phoneNumber, channelConnectionId, initialMessage } = req.body;

      if (!name || !phoneNumber || !channelConnectionId) {
        return res.status(400).json({
          message: 'Name, phone number, and channel connection ID are required'
        });
      }

      const cleanPhoneNumber = phoneNumber.replace(/\D/g, '');
      if (cleanPhoneNumber.length < 10) {
        return res.status(400).json({
          message: 'Please enter a valid phone number with at least 10 digits'
        });
      }


      if (isWhatsAppGroupChatId(cleanPhoneNumber)) {
        return res.status(400).json({
          message: 'Cannot initiate conversations with WhatsApp group chat IDs'
        });
      }

      const channelConnection = await storage.getChannelConnection(channelConnectionId);
      if (!channelConnection) {
        return res.status(404).json({ message: 'Channel connection not found' });
      }


      if (!req.user || !req.user.companyId) {
        return res.status(400).json({ message: 'Company ID is required for multi-tenant security' });
      }


      if (channelConnection.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Access denied: Connection does not belong to your company' });
      }


      const supportedChannelTypes = ['whatsapp_unofficial', 'whatsapp', 'whatsapp_official'];
      if (!supportedChannelTypes.includes(channelConnection.channelType)) {
        return res.status(400).json({
          message: `Unsupported channel type: ${channelConnection.channelType}`
        });
      }

      if (channelConnection.status !== 'active') {
        return res.status(400).json({
          message: 'WhatsApp connection is not active. Please reconnect in Settings.'
        });
      }

      let contact = await storage.getContactByIdentifier(cleanPhoneNumber, 'whatsapp');

      if (!contact) {
        const contactData: InsertContact = {
          companyId: req.user.companyId,
          name,
          phone: cleanPhoneNumber,
          identifier: cleanPhoneNumber,
          identifierType: 'whatsapp',
          source: 'manual',
          email: null,
          avatarUrl: null,
          company: null,
          tags: null,
          isActive: true,
          notes: null
        };

        contact = await storage.createContact(contactData);
      }

      let conversation = await storage.getConversationByContactAndChannel(
        contact.id,
        channelConnectionId
      );

      if (!conversation) {
        const conversationData: InsertConversation = {
          companyId: req.user.companyId,
          contactId: contact.id,
          channelId: channelConnectionId,
          channelType: channelConnection.channelType, // Use the actual channel type from connection
          status: 'open',
          assignedToUserId: req.user.id,
          lastMessageAt: new Date()
        };

        conversation = await storage.createConversation(conversationData);
      }

      if (initialMessage && initialMessage.trim()) {
        try {
          let sentMessage = null;

          if (channelConnection.channelType === 'whatsapp_official') {

            const whatsAppOfficialService = await import('./services/channels/whatsapp-official');
            sentMessage = await whatsAppOfficialService.sendMessage(
              channelConnectionId,
              req.user.id,
              req.user.companyId,
              cleanPhoneNumber,
              initialMessage.trim()
            );
          } else {

            const { sendWhatsAppMessage } = await import('./services/channels/whatsapp');
            sentMessage = await sendWhatsAppMessage(
              channelConnectionId,
              req.user.id,
              cleanPhoneNumber,
              initialMessage.trim()
            );
          }

          if (!sentMessage) {
            console.warn('Initial message was not sent successfully');
          }
        } catch (messageError) {
          console.error('Error sending initial message:', messageError);
        }
      }

      broadcastToAll({
        type: 'newConversation',
        data: {
          ...conversation,
          contact
        }
      });

      res.status(201).json({
        conversation,
        contact,
        success: true,
        message: 'Conversation initiated successfully'
      });

    } catch (err: any) {
      console.error('Error initiating WhatsApp conversation:', err);
      res.status(500).json({ message: err.message || 'Failed to initiate conversation' });
    }
  });

  app.post('/api/conversations', ensureAuthenticated, async (req: any, res) => {
    try {
      const conversationData = validateBody(insertConversationSchema, {
        ...req.body,
        assignedToUserId: req.user.id
      });
      const conversation = await storage.createConversation(conversationData);

      broadcastToAll({
        type: 'newConversation',
        data: conversation
      });

      res.status(201).json(conversation);
    } catch (err: any) {
      res.status(400).json({ message: err.message });
    }
  });

  app.patch('/api/conversations/:id', ensureAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updates = req.body;

      const conversation = await storage.updateConversation(id, updates);

      broadcastToAll({
        type: 'conversationUpdated',
        data: conversation
      });

      res.json(conversation);
    } catch (err: any) {
      res.status(400).json({ message: err.message });
    }
  });

  app.get('/api/conversations/:id/messages', ensureAuthenticated, async (req, res) => {
    try {
      const conversationId = parseInt(req.params.id);
      const user = req.user as any;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 25;
      const offset = (page - 1) * limit;


      const conversation = await storage.getConversation(conversationId);

      if (!conversation) {
        return res.status(404).json({ error: 'Conversation not found' });
      }


      if (conversation.isGroup || conversation.groupJid) {
        return res.status(404).json({ error: 'Conversation not found' });
      }


      if (conversation.contactId) {
        const contact = await storage.getContact(conversation.contactId);
        if (contact && (isWhatsAppGroupChatId(contact.phone) || isWhatsAppGroupChatId(contact.identifier))) {
          return res.status(404).json({ error: 'Conversation not found' });
        }
      }

      if (!user.isSuperAdmin) {

        if (!user.companyId) {
          return res.status(400).json({ error: 'User must be associated with a company' });
        }

        if (conversation.companyId !== user.companyId && conversation.companyId !== null) {
          return res.status(403).json({ error: 'Access denied: You can only access conversations from your company' });
        }
      }


      let totalMessages: number;
      let messages: any[];

      if (user.isSuperAdmin) {

        totalMessages = await storage.getMessagesCountByConversation(conversationId);
        messages = await storage.getMessagesByConversationPaginated(conversationId, limit, offset);
      } else {

        totalMessages = await storage.getMessagesCountByConversationWithCompanyValidation(conversationId, user.companyId);
        messages = await storage.getMessagesByConversationPaginatedWithCompanyValidation(conversationId, user.companyId, limit, offset);
      }

      res.json({
        messages: messages.reverse(),
        pagination: {
          page,
          limit,
          total: totalMessages,
          totalPages: Math.ceil(totalMessages / limit),
          hasMore: offset + messages.length < totalMessages
        }
      });
    } catch (error) {
      console.error('Error fetching messages:', error);
      res.status(500).json({ error: 'Failed to fetch messages' });
    }
  });

  app.get('/api/messages/:externalId', ensureAuthenticated, async (req: any, res) => {
    try {
      const externalId = req.params.externalId;
      const user = req.user;

      const message = await storage.getMessageByExternalId(externalId, user.companyId);

      if (!message) {
        return res.status(404).json({ error: 'Message not found' });
      }

      res.json(message);
    } catch (error: any) {
      console.error('Error fetching message by external ID:', error);
      res.status(500).json({ error: error.message });
    }
  });

  app.delete('/api/messages/:id', ensureAuthenticated, async (req: any, res) => {
    try {
      const messageId = parseInt(req.params.id);

      const message = await db
        .select()
        .from(messages)
        .where(eq(messages.id, messageId))
        .limit(1)
        .then(rows => rows[0]);

      if (!message) {
        return res.status(404).json({ error: 'Message not found' });
      }

      const conversation = await storage.getConversation(message.conversationId);
      if (!conversation) {
        return res.status(404).json({ error: 'Conversation not found' });
      }

      const user = req.user;

      let canDelete = false;

      if (user.isSuperAdmin || user.isAdmin) {
        canDelete = true;
      }
      else if (message.direction === 'outbound' && message.senderId === user.id) {
        canDelete = true;
      }
      else if (conversation.isGroup && !user.isSuperAdmin) {
        if (user.companyId && conversation.companyId !== user.companyId) {
          return res.status(403).json({ error: 'You do not have permission to access this conversation' });
        }
      }

      if (!canDelete) {
        return res.status(403).json({
          error: conversation.isGroup
            ? 'You do not have permission to delete this group message'
            : 'You do not have permission to delete this message'
        });
      }

      

      // Pass company ID for multi-tenant security
      const result = await channelManager.deleteMessage(messageId, user.id, user.companyId);

      if (!result.success) {
        return res.status(400).json({ error: result.error || 'Failed to delete message' });
      }

      res.json({
        success: true,
        message: 'Message deleted successfully',
        isGroupMessage: conversation.isGroup
      });
    } catch (error) {
      console.error('Error deleting message:', error);
      res.status(500).json({ error: 'Failed to delete message' });
    }
  });

  app.delete('/api/conversations/:id/history', ensureAuthenticated, async (req: any, res) => {
    try {
      const conversationId = parseInt(req.params.id);
      const user = req.user;

      const conversation = await storage.getConversation(conversationId);
      if (!conversation) {
        return res.status(404).json({ error: 'Conversation not found' });
      }

      let canClear = false;

      if (user.isSuperAdmin || user.isAdmin) {
        canClear = true;
      }
      else if (conversation.isGroup) {
        if (user.companyId && conversation.companyId === user.companyId) {
          canClear = true;
        }
      }
      else if (!conversation.isGroup) {
        if (conversation.assignedToUserId === user.id ||
            (user.companyId && conversation.companyId === user.companyId)) {
          canClear = true;
        }
      }

      if (!canClear) {
        return res.status(403).json({
          error: conversation.isGroup
            ? 'You do not have permission to clear this group chat history'
            : 'You do not have permission to clear this chat history'
        });
      }

      

      const clearResult = await storage.clearConversationHistory(conversationId);

      if (!clearResult.success) {
        return res.status(500).json({ error: 'Failed to clear conversation history' });
      }

      const { mediaCleanupService } = await import('./services/media-cleanup');
      const mediaCleanup = await mediaCleanupService.cleanupConversationMedia(clearResult.mediaFiles);

      if ((global as any).broadcastToAllClients) {
        (global as any).broadcastToAllClients({
          type: 'conversationHistoryCleared',
          data: {
            conversationId,
            deletedMessageCount: clearResult.deletedCount,
            deletedMediaCount: mediaCleanup.deletedFiles.length
          }
        });
      }

      res.json({
        success: true,
        message: 'Chat history cleared successfully',
        deletedMessageCount: clearResult.deletedCount,
        deletedMediaCount: mediaCleanup.deletedFiles.length,
        failedMediaCount: mediaCleanup.failedFiles.length,
        isGroupChat: conversation.isGroup,
        mediaCleanupErrors: mediaCleanup.errors
      });
    } catch (error) {
      console.error('Error clearing conversation history:', error);
      res.status(500).json({ error: 'Failed to clear conversation history' });
    }
  });

  app.post('/api/messages/:id/reply', ensureAuthenticated, async (req: any, res) => {
    try {
      const originalMessageId = parseInt(req.params.id);
      const { content } = req.body;
      const user = req.user;

      if (!content || !content.trim()) {
        return res.status(400).json({ error: 'Message content is required' });
      }

      const originalMessage = await storage.getMessageById(originalMessageId);
      if (!originalMessage) {
        return res.status(404).json({ error: 'Original message not found' });
      }

      const conversation = await storage.getConversation(originalMessage.conversationId);
      if (!conversation) {
        return res.status(404).json({ error: 'Conversation not found' });
      }

      if (!user.isSuperAdmin && conversation.companyId !== user.companyId) {
        return res.status(403).json({ error: 'You do not have permission to reply in this conversation' });
      }

      const contact = conversation.contactId ? await storage.getContact(conversation.contactId) : null;

      let quotedMessage = null;

      if (originalMessage.metadata) {
        try {
          let metadata;

          if (typeof originalMessage.metadata === 'string') {
            if (originalMessage.metadata.startsWith('{') || originalMessage.metadata.startsWith('[')) {
              metadata = JSON.parse(originalMessage.metadata);
            } else {
              throw new Error('Invalid metadata format');
            }
          } else if (typeof originalMessage.metadata === 'object') {
            metadata = originalMessage.metadata;
          } else {
            throw new Error('Unexpected metadata type: ' + typeof originalMessage.metadata);
          }

          if (metadata.whatsappMessage) {
            quotedMessage = metadata.whatsappMessage;
          }
          else if (metadata.messageId && metadata.remoteJid) {
            quotedMessage = {
              key: {
                id: metadata.messageId,
                remoteJid: metadata.remoteJid,
                fromMe: metadata.fromMe || false
              },
              message: {
                conversation: originalMessage.content || 'Media message'
              },
              messageTimestamp: Date.now()
            };
          }
        } catch (e) {
          if (originalMessage.externalId) {
            quotedMessage = {
              key: {
                id: originalMessage.externalId,
                remoteJid: contact?.identifier ? `${contact.identifier}@s.whatsapp.net` : '<EMAIL>',
                fromMe: originalMessage.direction === 'outbound'
              },
              message: {
                conversation: originalMessage.content || 'Message'
              },
              messageTimestamp: originalMessage.sentAt ? new Date(originalMessage.sentAt).getTime() : Date.now()
            };
          }
        }
      }

      const replyOptions = {
        originalMessageId: originalMessageId.toString(),
        originalContent: originalMessage.content || 'Media message',
        originalSender: originalMessage.direction === 'inbound'
          ? (contact?.name || 'Contact')
          : 'You',
        quotedMessage: quotedMessage
      };

      // Pass company ID for multi-tenant security
      const result = await channelManager.sendReply(
        originalMessage.conversationId,
        content.trim(),
        replyOptions,
        user.id,
        user.companyId
      );

      if (!result.success) {
        return res.status(400).json({ error: result.error || 'Failed to send reply' });
      }

      res.json({
        success: true,
        message: 'Reply sent successfully',
        messageId: result.messageId,
        data: result.data
      });
    } catch (error) {
      console.error('Error sending reply:', error);
      res.status(500).json({ error: 'Failed to send reply' });
    }
  });

  app.get('/api/conversations/:id/capabilities', ensureAuthenticated, async (req: any, res) => {
    try {
      const conversationId = parseInt(req.params.id);
      const user = req.user;

      const conversation = await storage.getConversation(conversationId);
      if (!conversation) {
        return res.status(404).json({ error: 'Conversation not found' });
      }

      if (!user.isSuperAdmin && conversation.companyId !== user.companyId) {
        return res.status(403).json({ error: 'You do not have permission to view this conversation' });
      }

      const capabilities = channelManager.getCapabilities(conversation.channelType);

      res.json({
        success: true,
        capabilities,
        channelType: conversation.channelType
      });
    } catch (error) {
      console.error('Error getting channel capabilities:', error);
      res.status(500).json({ error: 'Failed to get channel capabilities' });
    }
  });

  app.get('/api/conversations/:id/bot-status', ensureAuthenticated, async (req: any, res) => {
    try {
      const conversationId = parseInt(req.params.id);
      const user = req.user;

      if (isNaN(conversationId)) {
        return res.status(400).json({ error: 'Invalid conversation ID' });
      }

      const conversation = await storage.getConversation(conversationId);
      if (!conversation) {
        return res.status(404).json({ error: 'Conversation not found' });
      }

      if (!user.isSuperAdmin && conversation.companyId !== user.companyId) {
        return res.status(403).json({ error: 'You do not have permission to view this conversation' });
      }

      let botDisabled = conversation.botDisabled || false;
      if (botDisabled && conversation.disableDuration && conversation.disabledAt) {
        const disabledAt = new Date(conversation.disabledAt);
        const expiresAt = new Date(disabledAt.getTime() + (conversation.disableDuration * 60 * 1000));
        const now = new Date();

        if (now > expiresAt) {
          await storage.updateConversation(conversationId, {
            botDisabled: false,
            disabledAt: null,
            disableDuration: null,
            disableReason: null
          });
          botDisabled = false;
        }
      }

      res.json({
        conversationId,
        botDisabled,
        disabledAt: conversation.disabledAt,
        disableDuration: conversation.disableDuration,
        disableReason: conversation.disableReason
      });
    } catch (error) {
      console.error('Error getting bot status:', error);
      res.status(500).json({ error: 'Failed to get bot status' });
    }
  });

  app.patch('/api/conversations/:id/bot-status', ensureAuthenticated, async (req: any, res) => {
    try {
      const conversationId = parseInt(req.params.id);
      const user = req.user;
      const { botDisabled, disableDuration, disableReason } = req.body;

      if (isNaN(conversationId)) {
        return res.status(400).json({ error: 'Invalid conversation ID' });
      }

      if (typeof botDisabled !== 'boolean') {
        return res.status(400).json({ error: 'botDisabled must be a boolean value' });
      }

      const conversation = await storage.getConversation(conversationId);
      if (!conversation) {
        return res.status(404).json({ error: 'Conversation not found' });
      }

      if (!user.isSuperAdmin && conversation.companyId !== user.companyId) {
        return res.status(403).json({ error: 'You do not have permission to modify this conversation' });
      }

      const updateData: any = {
        botDisabled
      };

      if (botDisabled) {
        updateData.disabledAt = new Date();
        updateData.disableDuration = disableDuration || null;
        updateData.disableReason = disableReason || null;
      } else {
        updateData.disabledAt = null;
        updateData.disableDuration = null;
        updateData.disableReason = null;
      }

      const updatedConversation = await storage.updateConversation(conversationId, updateData);

      broadcastToAll({
        type: 'conversationBotStatusUpdated',
        data: {
          conversationId,
          botDisabled,
          disabledAt: updateData.disabledAt,
          disableDuration: updateData.disableDuration,
          disableReason: updateData.disableReason
        }
      });

      res.json({
        conversationId,
        botDisabled,
        disabledAt: updateData.disabledAt,
        disableDuration: updateData.disableDuration,
        disableReason: updateData.disableReason
      });
    } catch (error) {
      console.error('Error updating bot status:', error);
      res.status(500).json({ error: 'Failed to update bot status' });
    }
  });

  app.post('/api/conversations/:id/messages', ensureAuthenticated, async (req: any, res) => {
    try {
      const conversationId = parseInt(req.params.id);
      const user = req.user;


      const conversation = await storage.getConversation(conversationId);

      if (!conversation) {
        return res.status(404).json({ error: 'Conversation not found' });
      }


      if (!user.isSuperAdmin) {
        if (!user.companyId) {
          return res.status(400).json({ error: 'User must be associated with a company' });
        }

        if (conversation.companyId !== user.companyId && conversation.companyId !== null) {
          return res.status(403).json({ error: 'Access denied: You can only create messages in conversations from your company' });
        }
      }

      const messageData = validateBody(insertMessageSchema, {
        ...req.body,
        conversationId,
        senderId: req.user.id,
        senderType: 'user',
        direction: 'outbound',
        isFromBot: req.body.isFromBot || false
      });

      const message = await storage.createMessage(messageData);

      await storage.updateConversation(conversationId, {
        lastMessageAt: new Date()
      });

      broadcastToAll({
        type: 'newMessage',
        data: message
      });

      const updatedConversation = await storage.getConversation(conversationId);
      if (updatedConversation) {
        broadcastToAll({
          type: 'conversationUpdated',
          data: updatedConversation
        });
      }

      res.status(201).json(message);
    } catch (err: any) {
      res.status(400).json({ message: err.message });
    }
  });

  app.post('/api/conversations/:id/upload-media', ensureAuthenticated, requireAnyPermission([PERMISSIONS.MANAGE_CHANNELS, PERMISSIONS.MANAGE_CONVERSATIONS]), (req, res, next) => {


    const simpleUpload = multer({
      storage: multer.diskStorage({
        destination: function (req, file, cb) {
          cb(null, UPLOAD_DIR);
        },
        filename: function (req, file, cb) {
          const uniqueId = crypto.randomBytes(16).toString('hex');
          const fileExt = path.extname(file.originalname) || '';
          cb(null, `${uniqueId}${fileExt}`);
        }
      }),
      limits: { fileSize: 10 * 1024 * 1024 }
    });

    simpleUpload.single('file')(req, res, (err) => {
      if (err) {
        console.error('Multer error:', err);
        simpleUpload.single('media')(req, res, (err2) => {
          if (err2) {
            console.error('Multer error (fallback):', err2);
            simpleUpload.any()(req, res, (err3) => {
              if (err3) {
                console.error('Multer error (last resort):', err3);
                return res.status(400).json({ error: 'Failed to process file upload' });
              }

              if (req.files && Array.isArray(req.files) && req.files.length > 0) {
                req.file = req.files[0];
                
                next();
              } else {
                return res.status(400).json({ error: 'No file found in the request' });
              }
            });
          } else {
            
            next();
          }
        });
      } else {
        
        next();
      }
    });
  }, async (req: any, res) => {
    try {
      const conversationId = parseInt(req.params.id);
      const caption = req.body.caption || '';

      if (!req.file) {
        return res.status(400).json({ error: 'No media file uploaded' });
      }

      const conversation = await storage.getConversation(conversationId);

      if (!conversation) {
        await fsExtra.unlink(req.file.path);
        return res.status(404).json({ error: 'Conversation not found' });
      }

      let determinedMediaType: 'image' | 'video' | 'audio' | 'document' = 'document';
      const mimeType = req.file.mimetype;

      if (mimeType.startsWith('image/')) {
        determinedMediaType = 'image';
      } else if (mimeType.startsWith('video/')) {
        determinedMediaType = 'video';
      } else if (mimeType.startsWith('audio/')) {
        determinedMediaType = 'audio';
      } else {
        determinedMediaType = 'document';
      }

      if (!conversation.contactId) {
        await fsExtra.unlink(req.file.path);
        return res.status(400).json({ error: 'Individual conversation missing contact ID' });
      }

      const contact = await storage.getContact(conversation.contactId);

      if (!contact) {
        await fsExtra.unlink(req.file.path);
        return res.status(404).json({ error: 'Contact not found' });
      }

      const channelConnection = await storage.getChannelConnection(conversation.channelId);

      if (!channelConnection) {
        await fsExtra.unlink(req.file.path);
        return res.status(404).json({ error: 'Channel connection not found' });
      }

      const user = req.user as any;

      const { checkConnectionPermission } = await import('./services/channels/whatsapp');

      const hasConnectionAccess = await checkConnectionPermission(
        user,
        channelConnection,
        conversationId,
        conversation.channelId
      );

      if (!hasConnectionAccess) {
        await fsExtra.unlink(req.file.path);
        return res.status(403).json({ error: 'You do not have permission to access this connection' });
      }

      // Check connection status based on channel type
      let isConnectionActive = false;
      if (conversation.channelType === 'whatsapp_official') {
        isConnectionActive = whatsAppOfficialService.isActive?.(conversation.channelId) ?? false;

        // If connection is not active, try to initialize it
        if (!isConnectionActive && channelConnection.connectionData) {
          try {
            const connectionData = channelConnection.connectionData as any;
            if (connectionData.accessToken && connectionData.phoneNumberId && connectionData.wabaId) {
              await whatsAppOfficialService.initializeConnection(conversation.channelId, user.companyId, {
                accessToken: connectionData.accessToken,
                phoneNumberId: connectionData.phoneNumberId,
                wabaId: connectionData.wabaId || connectionData.businessAccountId,
                webhookVerifyToken: process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN || 'default_verify_token'
              });
              isConnectionActive = whatsAppOfficialService.isActive?.(conversation.channelId) ?? false;
            }
          } catch (initError) {
            console.error('Failed to initialize WhatsApp Official connection:', initError);
          }
        }
      } else {
        isConnectionActive = whatsAppService.isConnectionActive?.(conversation.channelId) ?? false;
      }

      if (!isConnectionActive) {
        await fsExtra.unlink(req.file.path);
        return res.status(500).json({
          error: 'WhatsApp connection not active',
          message: 'The WhatsApp connection is not currently active. Please refresh the connection or try again later.'
        });
      }

      let message = null;
      let convertedFilePath: string | null = null; // Track converted files for cleanup

      try {
        // Route to appropriate service based on channel type
        if (conversation.channelType === 'whatsapp_official') {
          if (!req.user || !req.user.companyId) {
            await fsExtra.unlink(req.file.path);
            return res.status(400).json({ error: 'Company ID is required for multi-tenant security' });
          }

          // Force HTTP for localhost in development to avoid SSL issues
          const host = req.get('host') || 'localhost:9000';
          const protocol = host.includes('localhost') ? 'http' : req.protocol;
          let publicUrl = `${protocol}://${host}/uploads/${path.basename(req.file.path)}`;
          const publicPath = path.join(process.cwd(), 'uploads', path.basename(req.file.path));

          // Only copy if source and destination are different
          if (path.resolve(req.file.path) !== path.resolve(publicPath)) {
            await fsExtra.copy(req.file.path, publicPath);
          }

          let finalMimeType = req.file.mimetype;
          let finalFilename = req.file.originalname;

          // For WhatsApp Official, convert audio to supported format if needed
          if (determinedMediaType === 'audio') {
            const supportedAudioTypes = [
              'audio/aac', 'audio/mp4', 'audio/mpeg', 'audio/amr', 'audio/ogg', 'audio/opus'
            ];

            if (!supportedAudioTypes.includes(req.file.mimetype)) {
              // Convert to OGG format which is supported
              const { convertAudioForWhatsAppWithFallback } = await import('./utils/audio-converter');
              const tempDir = path.join(process.cwd(), 'temp', 'audio');
              await fsExtra.ensureDir(tempDir);

              try {
                const conversionResult = await convertAudioForWhatsAppWithFallback(
                  req.file.path,
                  tempDir,
                  req.file.originalname
                );

                // Copy converted file to uploads directory
                const convertedBasename = path.basename(conversionResult.outputPath);
                const convertedPublicPath = path.join(process.cwd(), 'uploads', convertedBasename);
                await fsExtra.copy(conversionResult.outputPath, convertedPublicPath);

                // Track converted file for cleanup
                convertedFilePath = convertedPublicPath;

                // Update URL and MIME type
                publicUrl = `${protocol}://${host}/uploads/${convertedBasename}`;
                finalMimeType = conversionResult.mimeType;
                finalFilename = convertedBasename;

                // Clean up temp file
                await fsExtra.unlink(conversionResult.outputPath);
              } catch (conversionError) {
                console.error('Audio conversion failed for WhatsApp Official:', conversionError);
                // Fall back to original file
              }
            }
          }

          message = await whatsAppOfficialService.sendMedia(
            conversation.channelId,
            req.user.id,
            req.user.companyId,
            contact.identifier || contact.phone || '',
            determinedMediaType,
            publicUrl,
            caption,
            finalFilename,
            finalMimeType,
            false // isFromBot = false for user-sent media messages
          );
        } else if (conversation.channelType === 'whatsapp_twilio') {
          // Force HTTP for localhost in development to avoid SSL issues
          const host = req.get('host') || 'localhost:9000';
          const protocol = host.includes('localhost') ? 'http' : req.protocol;
          const publicUrl = `${protocol}://${host}/uploads/${path.basename(req.file.path)}`;
          const publicPath = path.join(process.cwd(), 'uploads', path.basename(req.file.path));

          // Only copy if source and destination are different
          if (path.resolve(req.file.path) !== path.resolve(publicPath)) {
            await fsExtra.copy(req.file.path, publicPath);
          }

          message = await whatsAppTwilioService.sendMedia(
            conversation.channelId,
            req.user.id,
            contact.identifier || contact.phone || '',
            determinedMediaType,
            publicUrl,
            caption,
            req.file.originalname
          );
        } else if (conversation.channelType === 'whatsapp_360dialog') {
          // Force HTTP for localhost in development to avoid SSL issues
          const host = req.get('host') || 'localhost:9000';
          const protocol = host.includes('localhost') ? 'http' : req.protocol;
          const publicUrl = `${protocol}://${host}/uploads/${path.basename(req.file.path)}`;
          const publicPath = path.join(process.cwd(), 'uploads', path.basename(req.file.path));

          // Only copy if source and destination are different
          if (path.resolve(req.file.path) !== path.resolve(publicPath)) {
            await fsExtra.copy(req.file.path, publicPath);
          }

          message = await whatsApp360DialogPartnerService.sendMedia(
            conversation.channelId,
            req.user.id,
            contact.identifier || contact.phone || '',
            determinedMediaType,
            publicUrl,
            caption,
            req.file.originalname
          );
        } else {
          // Default to unofficial WhatsApp
          message = await whatsAppService.sendMedia(
            conversation.channelId,
            req.user.id,
            contact.identifier || contact.phone || '',
            determinedMediaType,
            req.file.path,
            caption,
            req.file.originalname,
            false,
            conversationId
          );
        }

        if (!message) {
          await fsExtra.unlink(req.file.path);
          return res.status(500).json({ error: 'Failed to send media message' });
        }
      } catch (sendError) {
        console.error('Media send error:', sendError);

        await fsExtra.unlink(req.file.path);

        // Clean up converted file if it was created
        if (convertedFilePath) {
          try {
            await fsExtra.unlink(convertedFilePath);
          } catch (cleanupError) {
            console.error('Error cleaning up converted audio file:', cleanupError);
          }
        }

        return res.status(500).json({
          error: 'Failed to send WhatsApp media message',
          message: sendError instanceof Error ? sendError.message : 'Unknown error'
        });
      }

      // Clean up uploaded file
      await fsExtra.unlink(req.file.path);

      // Clean up converted file if it was created (WhatsApp Official audio conversion)
      if (convertedFilePath) {
        try {
          await fsExtra.unlink(convertedFilePath);
        } catch (cleanupError) {
          console.error('Error cleaning up converted audio file:', cleanupError);
        }
      }

      // Return the message result
      return res.status(201).json(message);
    } catch (error: any) {
      console.error('Error uploading media:', error);

      if (req.file && req.file.path) {
        try {
          await fsExtra.unlink(req.file.path);
        } catch (unlinkError) {
          console.error('Error deleting uploaded file:', unlinkError);
        }
      }

      return res.status(500).json({
        error: 'Internal server error',
        message: error.message
      });
    }
  });

  app.get('/api/contacts/:id/notes', ensureAuthenticated, async (req, res) => {
    const contactId = parseInt(req.params.id);
    const notes = await storage.getNotesByContact(contactId);
    res.json(notes);
  });

  app.post('/api/contacts/:id/notes', ensureAuthenticated, async (req: any, res) => {
    try {
      const contactId = parseInt(req.params.id);
      const noteData = validateBody(insertNoteSchema, {
        ...req.body,
        contactId,
        userId: req.user.id
      });

      const note = await storage.createNote(noteData);
      res.status(201).json(note);
    } catch (err: any) {
      res.status(400).json({ message: err.message });
    }
  });

  app.post('/api/upload', ensureAuthenticated, async (req, res) => {
    const flowMediaUpload = multer({
      storage: flowMediaStorage,
      limits: { fileSize: 30 * 1024 * 1024 }
    }).single('file');

    flowMediaUpload(req, res, async (err) => {
      if (err) {
        console.error('Error uploading file:', err);
        return res.status(400).json({ error: 'File upload failed', message: err.message });
      }

      if (!req.file) {
        return res.status(400).json({ error: 'No file found in the request' });
      }

      try {
        

        const filename = path.basename(req.file.path);
        const fileUrl = `/media/flow-media/${filename}`;

        return res.status(200).json({
          url: fileUrl,
          originalName: req.file.originalname,
          mimetype: req.file.mimetype,
          size: req.file.size
        });
      } catch (error: any) {
        console.error('Error processing uploaded file:', error);

        if (req.file && req.file.path) {
          try {
            await fsExtra.unlink(req.file.path);
          } catch (unlinkError) {
            console.error('Error deleting uploaded file:', unlinkError);
          }
        }

        return res.status(500).json({ error: 'Server error', message: error.message });
      }
    });
  });

  app.get('/api/flows', ensureAuthenticated, async (req: any, res) => {
    try {
      const flows = await storage.getFlows(req.user.id);
      res.json(flows);
    } catch (err: any) {
      res.status(500).json({ message: err.message });
    }
  });

  app.get('/api/user/plan-info', ensureAuthenticated, async (req: any, res) => {
    try {
      const user = req.user;
      if (!user.companyId) {
        return res.status(403).json({ message: 'User is not associated with a company' });
      }

      const company = await storage.getCompany(user.companyId);
      if (!company) {
        return res.status(403).json({ message: 'Company not found' });
      }

      let companyPlan;

      // First try to get plan by planId (more accurate after upgrades)
      if (company.planId) {
        companyPlan = await storage.getPlan(company.planId);
      }

      // Fallback to plan name lookup if planId lookup fails
      if (!companyPlan && company.plan) {
        const allPlans = await storage.getAllPlans();
        companyPlan = allPlans.find(p => p.name.toLowerCase() === company.plan!.toLowerCase());
      }

      if (!companyPlan) {
        return res.status(403).json({ message: 'Company plan not found' });
      }

      const companyFlows = await storage.getFlowsByCompany(company.id);

      res.json({
        plan: companyPlan,
        company: company,
        currentFlowCount: companyFlows.length,
        remainingFlows: Math.max(0, companyPlan.maxFlows - companyFlows.length)
      });
    } catch (err: any) {
      res.status(500).json({ message: err.message });
    }
  });

  app.get('/api/flows/:id', ensureAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const flow = await storage.getFlow(id);

      if (!flow) {
        return res.status(404).json({ message: 'Flow not found' });
      }

      res.json(flow);
    } catch (err: any) {
      res.status(500).json({ message: err.message });
    }
  });

  app.post('/api/flows', ensureAuthenticated, async (req: any, res) => {
    try {
      const user = req.user;
      if (!user.companyId) {
        return res.status(403).json({ message: 'User is not associated with a company' });
      }

      const company = await storage.getCompany(user.companyId);
      if (!company) {
        return res.status(403).json({ message: 'Company not found' });
      }

      let companyPlan;

      // First try to get plan by planId (more accurate after upgrades)
      if (company.planId) {
        companyPlan = await storage.getPlan(company.planId);
      }

      // Fallback to plan name lookup if planId lookup fails
      if (!companyPlan && company.plan) {
        const allPlans = await storage.getAllPlans();
        companyPlan = allPlans.find(p => p.name.toLowerCase() === company.plan!.toLowerCase());
      }

      if (!companyPlan) {
        return res.status(403).json({ message: 'Company plan not found' });
      }

      const companyFlows = await storage.getFlowsByCompany(company.id);

      if (companyFlows.length >= companyPlan.maxFlows) {
        return res.status(403).json({
          message: `You've reached your plan's flow limit. The ${companyPlan.name} plan allows ${companyPlan.maxFlows} flow${companyPlan.maxFlows === 1 ? '' : 's'}.`,
          planLimit: companyPlan.maxFlows,
          currentCount: companyFlows.length,
          planName: companyPlan.name,
          upgradeRequired: true
        });
      }

      const flowData = validateBody(insertFlowSchema, {
        ...req.body,
        userId: typeof req.user?.id === 'number' ? req.user.id : (() => { res.status(400).json({ message: 'User ID is required' }); throw new Error('User ID is required'); })(),
        companyId: company.id,
        status: req.body.status || 'draft'
      });

      const flow = await storage.createFlow(flowData);
      res.status(201).json(flow);

      broadcastToAll({
        type: 'flowCreated',
        data: flow
      });
    } catch (err: any) {
      res.status(400).json({ message: err.message });
    }
  });

  app.patch('/api/flows/:id', ensureAuthenticated, async (req: any, res) => {
    try {
      const id = parseInt(req.params.id);
      const flow = await storage.getFlow(id);

      if (!flow) {
        return res.status(404).json({ message: 'Flow not found' });
      }

      if (flow.userId !== req.user.id) {
        return res.status(403).json({ message: 'You do not have permission to update this flow' });
      }

      const updatedData = {
        ...req.body
      };

      const updatedFlow = await storage.updateFlow(id, updatedData);
      res.json(updatedFlow);

      broadcastToAll({
        type: 'flowUpdated',
        data: updatedFlow
      });
    } catch (err: any) {
      res.status(400).json({ message: err.message });
    }
  });

  app.delete('/api/flows/:id', ensureAuthenticated, async (req: any, res) => {
    try {
      const id = parseInt(req.params.id);
      const flow = await storage.getFlow(id);

      if (!flow) {
        return res.status(404).json({ message: 'Flow not found' });
      }

      if (flow.userId !== req.user.id) {
        return res.status(403).json({ message: 'You do not have permission to delete this flow' });
      }

      const deleted = await storage.deleteFlow(id);
      if (!deleted) {
        return res.status(500).json({ message: 'Failed to delete flow' });
      }

      res.status(204).end();

      broadcastToAll({
        type: 'flowDeleted',
        data: { id }
      });
    } catch (err: any) {
      res.status(500).json({ message: err.message });
    }
  });

  app.post('/api/flows/:id/duplicate', ensureAuthenticated, async (req: any, res) => {
    try {
      const id = parseInt(req.params.id);
      const originalFlow = await storage.getFlow(id);

      if (!originalFlow) {
        return res.status(404).json({ message: 'Flow not found' });
      }

      if (originalFlow.userId !== req.user.id) {
        return res.status(403).json({ message: 'You do not have permission to duplicate this flow' });
      }

      const user = req.user;
      if (!user.companyId) {
        return res.status(403).json({ message: 'User is not associated with a company' });
      }

      const company = await storage.getCompany(user.companyId);
      if (!company) {
        return res.status(403).json({ message: 'Company not found' });
      }

      let companyPlan;

      // First try to get plan by planId (more accurate after upgrades)
      if (company.planId) {
        companyPlan = await storage.getPlan(company.planId);
      }

      // Fallback to plan name lookup if planId lookup fails
      if (!companyPlan && company.plan) {
        const allPlans = await storage.getAllPlans();
        companyPlan = allPlans.find(p => p.name.toLowerCase() === company.plan!.toLowerCase());
      }

      if (!companyPlan) {
        return res.status(403).json({ message: 'Company plan not found' });
      }

      const companyFlows = await storage.getFlowsByCompany(company.id);

      if (companyFlows.length >= companyPlan.maxFlows) {
        return res.status(403).json({
          message: `You've reached your plan's flow limit. The ${companyPlan.name} plan allows ${companyPlan.maxFlows} flow${companyPlan.maxFlows === 1 ? '' : 's'}.`,
          planLimit: companyPlan.maxFlows,
          currentCount: companyFlows.length,
          planName: companyPlan.name,
          upgradeRequired: true
        });
      }

      const duplicateFlowData = validateBody(insertFlowSchema, {
        userId: originalFlow.userId,
        companyId: originalFlow.companyId,
        name: `${originalFlow.name} (Copy)`,
        description: originalFlow.description,
        status: 'draft',
        nodes: originalFlow.nodes,
        edges: originalFlow.edges,
        version: 1
      });

      const duplicatedFlow = await storage.createFlow(duplicateFlowData);
      res.status(201).json(duplicatedFlow);

      broadcastToAll({
        type: 'flowCreated',
        data: duplicatedFlow
      });
    } catch (err: any) {
      res.status(400).json({ message: err.message });
    }
  });

  app.get('/api/flow-assignments', ensureAuthenticated, async (req, res) => {
    try {
      const { channelId, flowId } = req.query;
      const channelIdNum = channelId ? parseInt(channelId as string) : undefined;
      const flowIdNum = flowId ? parseInt(flowId as string) : undefined;

      const assignments = await storage.getFlowAssignments(channelIdNum, flowIdNum);
      res.json(assignments);
    } catch (err: any) {
      res.status(500).json({ message: err.message });
    }
  });

  app.post('/api/flow-assignments', ensureAuthenticated, async (req: any, res) => {
    try {
      const assignmentData = validateBody(insertFlowAssignmentSchema, {
        ...req.body,
        isActive: req.body.isActive || false
      });

      const channelConnection = await storage.getChannelConnection(assignmentData.channelId);
      if (!channelConnection) {
        return res.status(404).json({ message: 'Channel connection not found' });
      }

      if (channelConnection.userId !== req.user.id) {
        return res.status(403).json({ message: 'You do not have permission to assign flows to this channel' });
      }

      const assignment = await storage.createFlowAssignment(assignmentData);
      res.status(201).json(assignment);

      broadcastToAll({
        type: 'flowAssignmentCreated',
        data: assignment
      });
    } catch (err: any) {
      res.status(400).json({ message: err.message });
    }
  });

  app.patch('/api/flow-assignments/:id/status', ensureAuthenticated, async (req: any, res) => {
    try {
      const id = parseInt(req.params.id);
      const { isActive } = req.body;

      if (isActive === undefined) {
        return res.status(400).json({ message: 'isActive field is required' });
      }

      const assignment = await storage.getFlowAssignment(id);
      if (!assignment) {
        return res.status(404).json({ message: 'Flow assignment not found' });
      }

      const channelConnection = await storage.getChannelConnection(assignment.channelId);
      if (!channelConnection || channelConnection.userId !== req.user.id) {
        return res.status(403).json({ message: 'You do not have permission to update this assignment' });
      }

      const updatedAssignment = await storage.updateFlowAssignmentStatus(id, isActive);
      res.json(updatedAssignment);

      broadcastToAll({
        type: 'flowAssignmentUpdated',
        data: updatedAssignment
      });
    } catch (err: any) {
      res.status(400).json({ message: err.message });
    }
  });

  app.delete('/api/flow-assignments/:id', ensureAuthenticated, async (req: any, res) => {
    try {
      const id = parseInt(req.params.id);

      const assignment = await storage.getFlowAssignment(id);
      if (!assignment) {
        return res.status(404).json({ message: 'Flow assignment not found' });
      }

      const channelConnection = await storage.getChannelConnection(assignment.channelId);
      if (!channelConnection || channelConnection.userId !== req.user.id) {
        return res.status(403).json({ message: 'You do not have permission to delete this assignment' });
      }

      const deleted = await storage.deleteFlowAssignment(id);
      if (!deleted) {
        return res.status(500).json({ message: 'Failed to delete flow assignment' });
      }

      res.status(204).end();

      broadcastToAll({
        type: 'flowAssignmentDeleted',
        data: { id }
      });
    } catch (err: any) {
      res.status(500).json({ message: err.message });
    }
  });

  app.get('/api/google/auth', ensureAuthenticated, async (req: any, res) => {
    try {
      const authUrl = await googleCalendarService.getAuthUrl(req.user.id, req.user.companyId);

      if (!authUrl) {
        return res.status(400).json({
          error: 'Google Calendar integration not configured by platform administrator. Please contact support.'
        });
      }

      res.json({ authUrl });
    } catch (error) {
      console.error('Error generating Google auth URL:', error);
      res.status(500).json({ error: 'Failed to generate authentication URL' });
    }
  });

  app.get('/api/google/calendar/callback', async (req, res) => {
    await googleCalendarService.handleAuthCallback(req, res);
  });


  app.get('/api/google/sheets/auth', ensureAuthenticated, async (req: any, res) => {
    try {
      const authUrl = await googleSheetsService.getAuthUrl(req.user.id, req.user.companyId);

      if (!authUrl) {
        return res.status(400).json({
          error: 'Google Sheets integration not configured by platform administrator. Please contact support.'
        });
      }

      res.json({ authUrl });
    } catch (error) {
      console.error('Error generating Google Sheets auth URL:', error);
      res.status(500).json({ error: 'Failed to generate authentication URL' });
    }
  });

  app.get('/api/google/sheets/callback', async (req, res) => {
    await googleSheetsService.handleAuthCallback(req, res);
  });

  app.get('/api/google/sheets/status', ensureAuthenticated, async (req: any, res) => {
    try {
      const status = await googleSheetsService.checkUserAuthentication(req.user.id, req.user.companyId);
      res.json(status);
    } catch (error) {
      console.error('Error checking Google Sheets status:', error);
      res.status(500).json({
        connected: false,
        message: 'Error checking connection status'
      });
    }
  });

  app.delete('/api/google/sheets', ensureAuthenticated, async (req: any, res) => {
    try {
      await storage.deleteGoogleTokens(req.user.id, req.user.companyId);
      res.json({
        success: true,
        message: 'Google Sheets disconnected successfully'
      });
    } catch (error) {
      console.error('Error disconnecting Google Sheets:', error);
      res.status(500).json({
        success: false,
        message: 'Error disconnecting Google Sheets'
      });
    }
  });


  app.get('/api/google/sheets/list', ensureAuthenticated, async (req: any, res) => {
    try {
      const sheets = await googleSheetsService.listUserSheets(req.user.id, req.user.companyId);
      res.json(sheets);
    } catch (error) {
      console.error('Error fetching Google Sheets:', error);
      res.status(500).json({
        success: false,
        error: 'Error fetching Google Sheets'
      });
    }
  });


  app.post('/api/google/sheets/sheet-names', ensureAuthenticated, async (req: any, res) => {
    try {
      const { spreadsheetId } = req.body;

      if (!spreadsheetId) {
        return res.status(400).json({
          success: false,
          error: 'Spreadsheet ID is required'
        });
      }

      const result = await googleSheetsService.getSheetNames(req.user.id, req.user.companyId, spreadsheetId);
      res.json(result);
    } catch (error) {
      console.error('Error fetching sheet names:', error);
      res.status(500).json({
        success: false,
        error: 'Error fetching sheet names'
      });
    }
  });

  app.get('/api/google/credentials', ensureAuthenticated, ensureAdmin, async (req: any, res) => {
    try {

      const adminOAuthConfig = await storage.getAppSetting('google_calendar_oauth');

      if (adminOAuthConfig && (adminOAuthConfig.value as any)?.enabled) {
        return res.json({
          configured: true,
          clientId: 'Configured by platform administrator',
          clientSecret: '••••••••••••••••',
          redirectUri: `${process.env.BASE_URL || 'http://localhost:5000'}/api/google/calendar/callback`
        });
      }


      const credentials = await storage.getGoogleCalendarCredentials(req.user.companyId);

      if (!credentials) {
        return res.json({
          configured: false,
          clientId: '',
          clientSecret: '',
          redirectUri: ''
        });
      }

      return res.json({
        configured: true,
        clientId: credentials.clientId || '',
        clientSecret: credentials.clientSecret ? '••••••••••••••••' : '',
        redirectUri: credentials.redirectUri || `${process.env.BASE_URL || 'http://localhost:9000'}/api/google/calendar/callback`
      });
    } catch (error) {
      console.error('Error getting Google Calendar credentials:', error);
      return res.status(500).json({
        success: false,
        message: 'Error getting Google Calendar credentials'
      });
    }
  });

  app.post('/api/google/credentials', ensureAuthenticated, ensureAdmin, async (req: any, res) => {
    try {

      const adminOAuthConfig = await storage.getAppSetting('google_calendar_oauth');

      if (adminOAuthConfig && (adminOAuthConfig.value as any)?.enabled) {
        return res.status(400).json({
          success: false,
          message: 'Google Calendar is now configured at the platform level. Individual company credentials are no longer supported. Contact your platform administrator for configuration changes.'
        });
      }


      const { clientId, clientSecret, redirectUri } = req.body;

      if (!clientId || !clientSecret) {
        return res.status(400).json({
          success: false,
          message: 'Client ID and Client Secret are required'
        });
      }

      const credentials = {
        clientId,
        clientSecret,
        redirectUri: redirectUri || `${process.env.BASE_URL || 'http://localhost:5000'}/api/google/callback`
      };

      const success = await storage.saveGoogleCalendarCredentials(req.user.companyId, credentials);

      if (!success) {
        return res.status(500).json({
          success: false,
          message: 'Failed to save Google Calendar credentials'
        });
      }

      await storage.deleteGoogleTokens(req.user.id, req.user.companyId);

      return res.json({
        success: true,
        message: 'Google Calendar credentials updated successfully'
      });
    } catch (error) {
      console.error('Error updating Google Calendar credentials:', error);
      return res.status(500).json({
        success: false,
        message: 'Error updating Google Calendar credentials'
      });
    }
  });

  app.get('/api/google/calendar/status', ensureAuthenticated, async (req: any, res) => {
    try {
      const status = await googleCalendarService.checkCalendarConnectionStatus(req.user.id, req.user.companyId);
      return res.json(status);
    } catch (error) {
      console.error('Error checking Google Calendar status:', error);
      return res.status(500).json({
        connected: false,
        message: 'Error checking Google Calendar status'
      });
    }
  });

  app.delete('/api/google/calendar', ensureAuthenticated, async (req: any, res) => {
    try {
      await storage.deleteGoogleTokens(req.user.id, req.user.companyId);
      return res.json({ success: true });
    } catch (error) {
      console.error('Error unlinking Google Calendar:', error);
      return res.status(500).json({ error: 'Error unlinking Google Calendar' });
    }
  });

  app.post('/api/google/calendar/disconnect', ensureAuthenticated, async (req: any, res) => {
    try {
      await storage.deleteGoogleTokens(req.user.id, req.user.companyId);
      return res.json({ success: true });
    } catch (error) {
      console.error('Error disconnecting Google Calendar:', error);
      return res.status(500).json({ error: 'Error disconnecting Google Calendar' });
    }
  });

  app.post('/api/google/calendar/events', ensureAuthenticated, async (req: any, res) => {
    try {
      console.log('Google Calendar API: Create event request received:', {
        userId: req.user.id,
        companyId: req.user.companyId,
        body: req.body
      });

      const { summary, description, location, startDateTime, endDateTime, attendees } = req.body;

      if (!summary || !startDateTime || !endDateTime) {
        console.log('Google Calendar API: Missing required fields:', { summary, startDateTime, endDateTime });
        return res.status(400).json({ error: 'Missing required fields' });
      }

      const eventData = {
        summary,
        description: description || '',
        location: location || '',
        start: {
          dateTime: startDateTime,
          timeZone: 'UTC'
        },
        end: {
          dateTime: endDateTime,
          timeZone: 'UTC'
        },
        attendees: attendees || []
      };

      console.log('Google Calendar API: Creating event with data:', eventData);

      const result = await googleCalendarService.createCalendarEvent(
        req.user.id,
        req.user.companyId,
        eventData
      );

      console.log('Google Calendar API: Create event result:', result);

      if (!result.success) {
        console.error('Google Calendar API: Create event failed:', result.error);
        return res.status(400).json({ error: result.error });
      }

      return res.json(result);
    } catch (error) {
      console.error('Error creating calendar event:', error);
      return res.status(500).json({ error: 'Error creating calendar event' });
    }
  });

  app.get('/api/google/calendar/events', ensureAuthenticated, async (req: any, res) => {
    try {
      const timeMin = req.query.timeMin as string;
      const timeMax = req.query.timeMax as string;
      const maxResults = parseInt(req.query.maxResults as string) || 10;

      if (!timeMin || !timeMax) {
        return res.status(400).json({ error: 'timeMin and timeMax are required' });
      }

      const result = await googleCalendarService.listCalendarEvents(
        req.user.id,
        req.user.companyId,
        timeMin,
        timeMax,
        maxResults
      );

      if (!result.success) {
        return res.status(400).json({ error: result.error });
      }

      return res.json(result);
    } catch (error) {
      console.error('Error listing calendar events:', error);
      return res.status(500).json({ error: 'Error listing calendar events' });
    }
  });

  app.patch('/api/google/calendar/events/:eventId', ensureAuthenticated, async (req: any, res) => {
    try {
      const eventId = req.params.eventId;
      const { summary, description, location, startDateTime, endDateTime, attendees } = req.body;

      if (!eventId || !summary || !startDateTime || !endDateTime) {
        return res.status(400).json({ error: 'Missing required fields' });
      }

      const eventData = {
        summary,
        description: description || '',
        location: location || '',
        start: {
          dateTime: startDateTime,
          timeZone: 'UTC'
        },
        end: {
          dateTime: endDateTime,
          timeZone: 'UTC'
        },
        attendees: Array.isArray(attendees) ? attendees : []
      };

      const result = await googleCalendarService.updateCalendarEvent(
        req.user.id,
        req.user.companyId,
        eventId,
        eventData
      );

      if (!result.success) {
        return res.status(400).json({ error: result.error });
      }

      return res.json(result);
    } catch (error) {
      console.error('Error updating calendar event:', error);
      return res.status(500).json({ error: 'Error updating calendar event' });
    }
  });

  app.delete('/api/google/calendar/events/:eventId', ensureAuthenticated, async (req: any, res) => {
    try {
      const eventId = req.params.eventId;

      if (!eventId) {
        return res.status(400).json({ error: 'Event ID is required' });
      }

      const result = await googleCalendarService.deleteCalendarEvent(
        req.user.id,
        req.user.companyId,
        eventId
      );

      if (!result.success) {
        return res.status(400).json({ error: result.error });
      }

      return res.json({ success: true });
    } catch (error) {
      console.error('Error deleting calendar event:', error);
      return res.status(500).json({ error: 'Error deleting calendar event' });
    }
  });

  app.get('/api/google/calendar/availability', ensureAuthenticated, async (req: any, res) => {
    try {
      console.log('Google Calendar API: Availability check request received:', {
        userId: req.user.id,
        companyId: req.user.companyId,
        query: req.query
      });

      const date = req.query.date as string;
      const durationMinutes = parseInt(req.query.duration as string) || 30;

      if (!date) {
        console.log('Google Calendar API: Missing date parameter');
        return res.status(400).json({ error: 'Date is required (YYYY-MM-DD format)' });
      }

      console.log('Google Calendar API: Checking availability for:', { date, durationMinutes });

      const result = await googleCalendarService.getAvailableTimeSlots(
        req.user.id,
        req.user.companyId,
        date,
        durationMinutes
      );

      console.log('Google Calendar API: Availability check result:', result);

      if (!result.success) {
        console.error('Google Calendar API: Availability check failed:', result.error);
        return res.status(400).json({ error: result.error });
      }

      return res.json(result);
    } catch (error) {
      console.error('Error getting available time slots:', error);
      return res.status(500).json({ error: 'Error getting available time slots' });
    }
  });

  app.post('/api/n8n/test-connection', ensureAuthenticated, async (req: Request, res: Response) => {
    try {
      const {
        instanceUrl,
        apiKey,
        webhookUrl,
        chatWebhookUrl,
        operation,
        workflowName,
        enableMediaSupport,
        supportedMediaTypes,
        includeFileMetadata
      } = req.body;

      if (!instanceUrl) {
        return res.status(400).json({
          success: false,
          error: 'Instance URL is required'
        });
      }

      try {
        new URL(instanceUrl);
      } catch {
        return res.status(400).json({
          success: false,
          error: 'Invalid URL format'
        });
      }

    

      let testUrl = '';
      let testData = {};
      let method = 'POST';

      if (chatWebhookUrl && chatWebhookUrl.trim()) {
        testUrl = chatWebhookUrl.trim();


        const baseTestData: any = {
          chatInput: 'Test message from PowerChatPlus',
          sessionId: 'test-session-powerchatplus',
          messageType: 'text',
          isMediaMessage: false,

          message: {
            content: 'Test message from PowerChatPlus',
            type: 'text',
            timestamp: new Date().toISOString()
          },

          contact: {
            id: 'test-contact-123',
            name: 'Test User',
            phone: '+1234567890',
            email: '<EMAIL>'
          },

          conversation: {
            id: 'test-conversation-456',
            channelType: 'whatsapp'
          }
        };


        if (enableMediaSupport) {
          baseTestData.mediaSupport = {
            enabled: true,
            supportedTypes: supportedMediaTypes || ['image', 'video', 'audio', 'document'],
            includeMetadata: includeFileMetadata !== false
          };


          baseTestData.sampleMediaMessage = {
            messageType: 'image',
            isMediaMessage: true,
            message: {
              content: 'Test image with caption',
              type: 'image',
              timestamp: new Date().toISOString()
            },
            media: {
              url: 'https://via.placeholder.com/300x200.png?text=Test+Image',
              type: 'image',
              metadata: includeFileMetadata ? {
                filename: 'test-image.png',
                mimeType: 'image/png',
                fileSize: 12345,
                originalName: 'test-image.png'
              } : undefined
            }
          };
        }

        testData = baseTestData;

        method = 'POST';


      }
      else if (operation === 'webhook_trigger' && webhookUrl) {
        testUrl = webhookUrl;
        testData = {
          test: true,
          timestamp: new Date().toISOString(),
          source: 'app_Test',
          message: 'Test execution from app'
        };
        method = 'POST';
      } else if (workflowName && apiKey) {
        const cleanWorkflowName = workflowName.replace(/^#/, '');



        let executionError = null;
        let executionSuccess = false;
        let executionResult = null;

        testData = {
          test: true,
          timestamp: new Date().toISOString(),
          source: 'PowerChatPlus_Test',
          message: 'Test execution from PowerChatPlus'
        };



        const directEndpoints = [
          `/api/v1/workflows/${encodeURIComponent(cleanWorkflowName)}/run`,
          `/api/v1/workflows/${encodeURIComponent(cleanWorkflowName)}/execute`,
          `/api/v1/workflows/${encodeURIComponent(cleanWorkflowName)}/activate`,
          `/webhook/${encodeURIComponent(cleanWorkflowName)}`
        ];

        let firstError: any = null;

        for (const endpoint of directEndpoints) {
          try {
            testUrl = `${instanceUrl}${endpoint}`;

            const response = await axios({
              method: 'POST',
              url: testUrl,
              data: testData,
              headers: {
                'X-N8N-API-KEY': apiKey,
                'Content-Type': 'application/json'
              },
              timeout: 30000
            });

            executionSuccess = true;
            executionResult = response.data;
            break;
          } catch (endpointError: any) {
            if (!firstError) firstError = endpointError;
          }
        }

        if (!executionSuccess) {
          executionError = firstError;

          if (firstError.response?.status === 404) {

            try {
              const listResponse = await axios.get(`${instanceUrl}/api/v1/workflows`, {
                headers: {
                  'X-N8N-API-KEY': apiKey,
                  'Content-Type': 'application/json'
                },
                timeout: 30000
              });

              const workflows = listResponse.data?.data || [];

              const matchingWorkflow = workflows.find((w: any) =>
                w.id === cleanWorkflowName ||
                w.name === cleanWorkflowName ||
                w.id?.toString() === cleanWorkflowName
              );

              if (matchingWorkflow) {


                const endpoints = [
                  `/api/v1/workflows/${matchingWorkflow.id}/run`,
                  `/api/v1/workflows/${matchingWorkflow.id}/execute`,
                  `/api/v1/workflows/${matchingWorkflow.id}/activate`,
                  `/webhook/${matchingWorkflow.id}`
                ];

                for (const endpoint of endpoints) {
                  try {
                    const retryUrl = `${instanceUrl}${endpoint}`;
                    const retryResponse = await axios({
                      method: 'POST',
                      url: retryUrl,
                      data: testData,
                      headers: {
                        'X-N8N-API-KEY': apiKey,
                        'Content-Type': 'application/json'
                      },
                      timeout: 30000
                    });

                    executionSuccess = true;
                    executionResult = retryResponse.data;
                    break;
                  } catch (endpointError: any) {
                  }
                }

              }

            } catch (listError: any) {
            }
          }
        }

        if (executionSuccess) {
          return res.json({
            success: true,
            data: executionResult,
            message: 'Workflow executed successfully'
          });
        } else {
          throw executionError;
        }
      } else {
        return res.status(400).json({
          success: false,
          error: 'Either webhook URL or (workflow name + API key) is required to execute the workflow'
        });
      }

      if (webhookUrl) {
        const headers: any = {
          'Content-Type': 'application/json'
        };

        const response = await axios({
          method,
          url: testUrl,
          headers,
          data: testData,
          timeout: 30000
        });

        let successMessage = 'Workflow executed successfully via webhook';
        let executionData = response.data;

        return res.json({
          success: true,
          message: successMessage,
          data: executionData,
          status: response.status,
          executedAt: new Date().toISOString()
        });
      }

    } catch (error: any) {
      console.error('Error executing n8n workflow:', error);

      let errorMessage = 'Workflow execution failed';

      if (error.response) {
        const status = error.response.status;
        const responseData = error.response.data;

        if (status === 404) {
          if (req.body.workflowName && req.body.apiKey) {
            errorMessage = `Workflow not found: "${req.body.workflowName}" does not exist or is not accessible.`;
          } else {
            errorMessage = 'Webhook URL not found. Please check the webhook URL.';
          }
        } else if (status === 401) {
          errorMessage = 'Authentication failed. Please provide a valid API key.';
        } else if (status === 403) {
          errorMessage = 'Access forbidden. Please check your API key permissions for workflow execution.';
        } else if (status === 400) {
          errorMessage = `Bad request: ${responseData?.message || 'Invalid workflow configuration'}`;
        } else if (status === 500) {
          errorMessage = `Workflow execution error: ${responseData?.message || 'Internal server error in n8n'}`;
        } else {
          errorMessage = `Execution failed: ${status} ${error.response.statusText}`;
        }
      } else if (error.code === 'ECONNREFUSED') {
        errorMessage = 'Connection refused: n8n instance is not accessible. Please check if the instance is running.';
      } else if (error.code === 'ENOTFOUND') {
        errorMessage = 'Host not found: Please check the instance URL format.';
      } else if (error.code === 'ETIMEDOUT') {
        errorMessage = 'Execution timeout: Workflow is taking too long to execute or n8n is not responding.';
      } else if (error.message) {
        errorMessage = `Execution failed: ${error.message}`;
      }

      return res.status(500).json({
        success: false,
        error: errorMessage
      });
    }
  });

  app.post('/api/n8n/list-workflows', async (req, res) => {
    try {
      const { instanceUrl, apiKey } = req.body;

      if (!instanceUrl || !apiKey) {
        return res.status(400).json({
          success: false,
          error: 'Instance URL and API key are required'
        });
      }

      try {
        new URL(instanceUrl);
      } catch {
        return res.status(400).json({
          success: false,
          error: 'Invalid URL format'
        });
      }

  

      const listUrl = `${instanceUrl}/api/v1/workflows`;

      const response = await axios.get(listUrl, {
        headers: {
          'X-N8N-API-KEY': apiKey,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      });


      return res.json({
        success: true,
        workflows: response.data?.data || [],
        message: `Found ${response.data?.data?.length || 0} workflows`
      });

    } catch (error: any) {
      console.error('N8n list workflows error:', error);
      return res.status(500).json({
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to list workflows'
      });
    }
  });

  app.post('/api/google/sheets/test-connection', ensureAuthenticated, async (req: any, res: Response) => {
    try {
      const { serviceAccountJson, spreadsheetId, sheetName, useOAuth } = req.body;


      if (useOAuth || !serviceAccountJson) {

        try {
          const result = await googleSheetsService.testConnectionWithOAuth(
            req.user.id,
            req.user.companyId,
            spreadsheetId,
            sheetName || 'Sheet1'
          );
          return res.json(result);
        } catch (oauthError) {
          if (!serviceAccountJson) {
            return res.status(400).json({
              success: false,
              error: 'Please connect your Google Sheets account using OAuth authentication above'
            });
          }

        }
      }


      if (!serviceAccountJson || !spreadsheetId) {
        return res.status(400).json({
          success: false,
          error: 'Service Account JSON and Spreadsheet ID are required for legacy authentication'
        });
      }

      const config = {
        serviceAccountJson,
        spreadsheetId,
        sheetName: sheetName || 'Sheet1'
      };

      const result = await googleSheetsService.testConnection(config);
      return res.json(result);
    } catch (error) {
      console.error('Error testing Google Sheets connection:', error);
      return res.status(500).json({
        success: false,
        error: 'Error testing Google Sheets connection'
      });
    }
  });

  app.post('/api/google/sheets/add-test-data', ensureAuthenticated, async (req: Request, res: Response) => {
    try {
      const { serviceAccountJson, spreadsheetId, sheetName } = req.body;

      if (!serviceAccountJson || !spreadsheetId) {
        return res.status(400).json({
          success: false,
          error: 'Service Account JSON and Spreadsheet ID are required'
        });
      }

      const config = {
        serviceAccountJson,
        spreadsheetId,
        sheetName: sheetName || 'Sheet1'
      };

      const result = await googleSheetsService.addTestData(config);
      return res.json(result);
    } catch (error) {
      console.error('Error adding test data to Google Sheets:', error);
      return res.status(500).json({
        success: false,
        error: 'Error adding test data to Google Sheets'
      });
    }
  });

  app.post('/api/google/sheets/get-info', ensureAuthenticated, async (req: Request, res: Response) => {
    try {
      const { serviceAccountJson, spreadsheetId, sheetName } = req.body;

      if (!serviceAccountJson || !spreadsheetId) {
        return res.status(400).json({
          success: false,
          error: 'Service Account JSON and Spreadsheet ID are required'
        });
      }

      const config = {
        serviceAccountJson,
        spreadsheetId,
        sheetName: sheetName || 'Sheet1'
      };

      const result = await googleSheetsService.getSheetInfo(config);
      return res.json(result);
    } catch (error) {
      console.error('Error getting Google Sheets info:', error);
      return res.status(500).json({
        success: false,
        error: 'Error getting Google Sheets info'
      });
    }
  });

  app.post('/api/google/sheets/append-row', ensureAuthenticated, async (req: Request, res: Response) => {
    try {
      const { serviceAccountJson, spreadsheetId, sheetName, columnMappings } = req.body;

      if (!serviceAccountJson || !spreadsheetId || !columnMappings) {
        return res.status(400).json({
          success: false,
          error: 'Service Account JSON, Spreadsheet ID, and Column Mappings are required'
        });
      }

      const config = {
        serviceAccountJson,
        spreadsheetId,
        sheetName: sheetName || 'Sheet1'
      };

      const result = await googleSheetsService.appendRow(config, { columnMappings });
      return res.json(result);
    } catch (error) {
      console.error('Error appending row to Google Sheets:', error);
      return res.status(500).json({
        success: false,
        error: 'Error appending row to Google Sheets'
      });
    }
  });

  app.post('/api/google/sheets/read-rows', ensureAuthenticated, async (req: Request, res: Response) => {
    try {
      const { serviceAccountJson, spreadsheetId, sheetName, filterColumn, filterValue, startRow, maxRows } = req.body;

      if (!serviceAccountJson || !spreadsheetId) {
        return res.status(400).json({
          success: false,
          error: 'Service Account JSON and Spreadsheet ID are required'
        });
      }

      const config = {
        serviceAccountJson,
        spreadsheetId,
        sheetName: sheetName || 'Sheet1'
      };

      const options: any = {};
      if (filterColumn) options.filterColumn = filterColumn;
      if (filterValue !== undefined) options.filterValue = filterValue;
      if (startRow) options.startRow = parseInt(startRow);
      if (maxRows) options.maxRows = parseInt(maxRows);

      const result = await googleSheetsService.readRows(config, options);
      return res.json(result);
    } catch (error) {
      console.error('Error reading rows from Google Sheets:', error);
      return res.status(500).json({
        success: false,
        error: 'Error reading rows from Google Sheets'
      });
    }
  });

  app.post('/api/google/sheets/update-row', ensureAuthenticated, async (req: Request, res: Response) => {
    try {
      const { serviceAccountJson, spreadsheetId, sheetName, matchColumn, matchValue, columnMappings } = req.body;

      if (!serviceAccountJson || !spreadsheetId || !matchColumn || matchValue === undefined || !columnMappings) {
        return res.status(400).json({
          success: false,
          error: 'Service Account JSON, Spreadsheet ID, Match Column, Match Value, and Column Mappings are required'
        });
      }

      const config = {
        serviceAccountJson,
        spreadsheetId,
        sheetName: sheetName || 'Sheet1'
      };

      const result = await googleSheetsService.updateRow(config, {
        matchColumn,
        matchValue,
        columnMappings
      });
      return res.json(result);
    } catch (error) {
      console.error('Error updating row in Google Sheets:', error);
      return res.status(500).json({
        success: false,
        error: 'Error updating row in Google Sheets'
      });
    }
  });


  function ensureAdmin(req: any, res: any, next: any) {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: 'Unauthorized' });
    }
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Forbidden: Admin access required' });
    }
    next();
  }

  app.get('/api/team/members', ensureAuthenticated, async (req: any, res) => {
    try {
      const user = req.user;

      let teamMembers;
      if (user.isSuperAdmin) {
        teamMembers = await storage.getAllTeamMembers();
      } else {
        teamMembers = await storage.getTeamMembersByCompany(user.companyId);
      }

      const safeTeamMembers = teamMembers.map(member => {
        const { password, ...safeUser } = member;
        return safeUser;
      });

      res.json(safeTeamMembers);
    } catch (error) {
      console.error('Error getting team members:', error);
      res.status(500).json({ message: 'Failed to retrieve team members' });
    }
  });

  app.post('/api/team/members', ensureAdmin, async (req: any, res) => {
    try {
      const teamMemberSchema = z.object({
        fullName: z.string().min(1, 'Full name is required'),
        username: z.string().min(3, 'Username must be at least 3 characters'),
        email: z.string().email('Valid email is required'),
        password: z.string().min(6, 'Password must be at least 6 characters'),
        role: z.string().refine(role => role === 'admin' || role === 'agent', {
          message: "Role must be either 'admin' or 'agent'"
        })
      });

      const validationResult = teamMemberSchema.safeParse(req.body);
      if (!validationResult.success) {
        return res.status(400).json({
          message: 'Invalid team member data',
          errors: validationResult.error.errors
        });
      }

      const { fullName, username, email, password, role } = validationResult.data;

      const existingUser = await storage.getUserByUsernameCaseInsensitive(username);
      if (existingUser) {
        return res.status(400).json({ message: 'A user with this username already exists' });
      }

      const existingEmailUser = await storage.getUserByEmail(email);
      if (existingEmailUser) {
        return res.status(400).json({ message: 'A user with this email already exists' });
      }

      const hashedPassword = await hashPassword(password);

      const newUser = await storage.createUser({
        username,
        email,
        fullName,
        password: hashedPassword,
        role,
        companyId: req.user.companyId
      });

      const { password: _, ...safeUser } = newUser;

      res.status(201).json(safeUser);
    } catch (error) {
      console.error('Error creating team member:', error);
      res.status(500).json({ message: 'Failed to create team member' });
    }
  });

  app.patch('/api/team/members/:id', ensureAdmin, async (req: any, res) => {
    try {
      const memberId = parseInt(req.params.id);
      const updateSchema = z.object({
        fullName: z.string().min(1, 'Full name is required').optional(),
        email: z.string().email('Valid email is required').optional(),
        password: z.string().min(6, 'Password must be at least 6 characters').optional(),
        role: z.string().refine(role => role === 'admin' || role === 'agent', {
          message: "Role must be either 'admin' or 'agent'"
        }).optional()
      });

      const validationResult = updateSchema.safeParse(req.body);
      if (!validationResult.success) {
        return res.status(400).json({
          message: 'Invalid update data',
          errors: validationResult.error.errors
        });
      }

      const updateData = validationResult.data;

      const existingMember = await storage.getUser(memberId);
      if (!existingMember) {
        return res.status(404).json({ message: 'Team member not found' });
      }

      if (!req.user.isSuperAdmin && existingMember.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const userUpdateData: any = {
        fullName: updateData.fullName,
        email: updateData.email,
        role: updateData.role
      };

      if (updateData.password) {
        userUpdateData.password = await hashPassword(updateData.password);
      }

      const updatedUser = await storage.updateUser(memberId, userUpdateData);

      const { password: _, ...safeUser } = updatedUser;

      res.json(safeUser);
    } catch (error) {
      console.error('Error updating team member:', error);
      res.status(500).json({ message: 'Failed to update team member' });
    }
  });

  app.delete('/api/team/members/:id', ensureAdmin, async (req: any, res) => {
    try {
      const memberId = parseInt(req.params.id);
      const user = req.user;

      if (isNaN(memberId)) {
        return res.status(400).json({ message: 'Invalid member ID' });
      }

      const existingMember = await storage.getUser(memberId);
      if (!existingMember) {
        return res.status(404).json({ message: 'Team member not found' });
      }

      if (existingMember.id === user.id) {
        return res.status(400).json({ message: 'You cannot delete your own account' });
      }

      if (!user.isSuperAdmin && existingMember.companyId !== user.companyId) {
        return res.status(403).json({ message: 'Access denied. You can only remove members from your own company.' });
      }

      const success = await storage.deleteUser(memberId);

      if (!success) {
        return res.status(500).json({ message: 'Failed to remove team member' });
      }

      res.json({ message: 'Team member removed successfully' });
    } catch (error) {
      console.error('Error deleting team member:', error);
      res.status(500).json({ message: 'Failed to remove team member' });
    }
  });

  app.get('/api/team/members/active', ensureAuthenticated, async (req: any, res) => {
    try {
      const user = req.user;

      let activeMembers;
      if (user.isSuperAdmin) {
        activeMembers = await storage.getActiveTeamMembers();
      } else {
        activeMembers = await storage.getActiveTeamMembersByCompany(user.companyId);
      }

      const safeActiveMembers = activeMembers.map(member => {
        const { password, ...safeUser } = member;
        return safeUser;
      });

      res.json(safeActiveMembers);
    } catch (error) {
      console.error('Error getting active team members:', error);
      res.status(500).json({ message: 'Failed to retrieve active team members' });
    }
  });

  app.get('/api/team/invitations', ensureAdmin, async (req: any, res) => {
    try {
      const user = req.user as any;
      const companyId = user.isSuperAdmin ? undefined : user.companyId;
      const invitations = await storage.getTeamInvitations(companyId);
      res.json(invitations);
    } catch (error) {
      console.error('Error getting team invitations:', error);
      res.status(500).json({ message: 'Failed to retrieve team invitations' });
    }
  });

  app.post('/api/team/invitations', ensureAdmin, async (req: any, res) => {
    try {
      const invitationSchema = z.object({
        email: z.string().email(),
        role: z.string().refine(role => role === 'admin' || role === 'agent', {
          message: "Role must be either 'admin' or 'agent'"
        })
      });

      const validationResult = invitationSchema.safeParse(req.body);
      if (!validationResult.success) {
        return res.status(400).json({
          message: 'Invalid invitation data',
          errors: validationResult.error.errors
        });
      }

      const { email, role } = validationResult.data;

      const existingUser = await storage.getUserByEmail(email);
      if (existingUser) {
        return res.status(400).json({ message: 'A user with this email already exists' });
      }

      const existingInvitation = await storage.getTeamInvitationByEmail(email);
      if (existingInvitation) {
        return res.status(400).json({ message: 'An invitation has already been sent to this email' });
      }

      const token = crypto.randomBytes(32).toString('hex');

      const newInvitation = await storage.createTeamInvitation({
        email,
        role,
        token,
        status: 'pending',
        invitedByUserId: req.user.id,
        companyId: req.user.companyId,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      });

      const invitationUrl = `${process.env.APP_URL || 'http://localhost:9000'}/accept-invitation?token=${token}`;
      

      try {
        const senderName = req.user.fullName || req.user.username;
        const companyName = process.env.COMPANY_NAME || 'Your Company';

        await sendTeamInvitation(
          email,
          senderName,
          companyName,
          role,
          invitationUrl
        );

        
      } catch (emailError) {
        console.error('Error sending invitation email:', emailError);
      }

      res.status(201).json(newInvitation);
    } catch (error) {
      console.error('Error creating team invitation:', error);
      res.status(500).json({ message: 'Failed to create team invitation' });
    }
  });

  app.post('/api/team/invitations/:id/resend', ensureAdmin, async (req: any, res) => {
    try {
      const invitationId = parseInt(req.params.id);

      const user = req.user as any;
      const companyId = user.isSuperAdmin ? undefined : user.companyId;
      const invitations = await storage.getTeamInvitations(companyId);
      const invitation = invitations.find(inv => inv.id === invitationId);
      if (!invitation) {
        return res.status(404).json({ message: 'Invitation not found' });
      }

      const invitationUrl = `${process.env.APP_URL || 'http://localhost:9000'}/accept-invitation?token=${invitation.token}`;
      

      try {
        const senderName = req.user.fullName || req.user.username;
        const companyName = process.env.COMPANY_NAME || 'Your Company';

        await sendTeamInvitation(
          invitation.email,
          senderName,
          companyName,
          invitation.role,
          invitationUrl
        );

        
        res.json({ message: 'Invitation resent successfully' });
      } catch (emailError) {
        console.error('Error resending invitation email:', emailError);
        res.status(500).json({
          message: 'Failed to resend invitation email',
          error: emailError instanceof Error ? emailError.message : 'Unknown error'
        });
      }
    } catch (error) {
      console.error('Error resending team invitation:', error);
      res.status(500).json({ message: 'Failed to resend team invitation' });
    }
  });

  app.get('/api/team/invitations/verify', async (req: any, res) => {
    try {
      const token = req.query.token as string;

      if (!token) {
        return res.status(400).json({ message: 'Token is required' });
      }

      const invitation = await storage.getTeamInvitationByToken(token);
      if (!invitation) {
        return res.status(404).json({ message: 'Invitation not found or has expired' });
      }

      if (invitation.status !== 'pending') {
        return res.status(400).json({ message: `Invitation has already been ${invitation.status}` });
      }

      if (new Date() > new Date(invitation.expiresAt)) {
        return res.status(400).json({ message: 'Invitation has expired' });
      }

      const { token: _, ...safeInvitation } = invitation;
      res.json(safeInvitation);
    } catch (error) {
      console.error('Error verifying team invitation:', error);
      res.status(500).json({ message: 'Failed to verify team invitation' });
    }
  });

  app.post('/api/team/invitations/accept', async (req: any, res) => {
    try {
      const acceptSchema = z.object({
        token: z.string(),
        username: z.string().min(3),
        password: z.string().min(6),
        fullName: z.string().min(1)
      });

      const validationResult = acceptSchema.safeParse(req.body);
      if (!validationResult.success) {
        return res.status(400).json({
          message: 'Invalid data',
          errors: validationResult.error.errors
        });
      }

      const { token, username, password, fullName } = validationResult.data;

      const invitation = await storage.getTeamInvitationByToken(token);
      if (!invitation) {
        return res.status(404).json({ message: 'Invitation not found or has expired' });
      }

      if (invitation.status !== 'pending') {
        return res.status(400).json({ message: `Invitation has already been ${invitation.status}` });
      }

      const existingUser = await storage.getUserByUsernameCaseInsensitive(username);
      if (existingUser) {
        return res.status(400).json({ message: 'Username already taken' });
      }

      const hashedPassword = await hashPassword(password);
      const newUser = await storage.createUser({
        username,
        password: hashedPassword,
        fullName,
        email: invitation.email,
        role: invitation.role as "super_admin" | "admin" | "agent" | null | undefined,
        companyId: invitation.companyId,
        avatarUrl: null
      });

      await storage.updateTeamInvitationStatus(invitation.id, 'accepted');

      req.login(newUser, (err: any) => {
        if (err) {
          console.error('Error logging in new user:', err);
          return res.status(500).json({ message: 'Failed to log in new user' });
        }

        const { password, ...safeUser } = newUser;
        res.status(201).json(safeUser);
      });
    } catch (error) {
      console.error('Error accepting team invitation:', error);
      res.status(500).json({ message: 'Failed to accept team invitation' });
    }
  });

  app.patch('/api/team/invitations/:id', ensureAdmin, async (req: any, res) => {
    try {
      const invitationId = parseInt(req.params.id);

      const statusSchema = z.object({
        status: invitationStatusTypes
      });

      const validationResult = statusSchema.safeParse(req.body);
      if (!validationResult.success) {
        return res.status(400).json({
          message: 'Invalid status',
          errors: validationResult.error.errors
        });
      }

      const { status } = validationResult.data;

      const user = req.user as any;
      const companyId = user.isSuperAdmin ? undefined : user.companyId;
      const invitations = await storage.getTeamInvitations(companyId);
      const invitation = invitations.find(inv => inv.id === invitationId);
      if (!invitation) {
        return res.status(404).json({ message: 'Invitation not found' });
      }

      const updatedInvitation = await storage.updateTeamInvitationStatus(invitationId, status);

      res.json(updatedInvitation);
    } catch (error) {
      console.error('Error updating team invitation status:', error);
      res.status(500).json({ message: 'Failed to update team invitation status' });
    }
  });

  app.delete('/api/team/invitations/:id', ensureAdmin, async (req: any, res) => {
    try {
      const invitationId = parseInt(req.params.id);

      const user = req.user as any;
      const companyId = user.isSuperAdmin ? undefined : user.companyId;
      const invitations = await storage.getTeamInvitations(companyId);
      const invitation = invitations.find(inv => inv.id === invitationId);
      if (!invitation) {
        return res.status(404).json({ message: 'Invitation not found' });
      }

      const deleted = await storage.deleteTeamInvitation(invitationId);

      if (deleted) {
        res.json({ message: 'Invitation deleted successfully' });
      } else {
        res.status(500).json({ message: 'Failed to delete invitation' });
      }
    } catch (error) {
      console.error('Error deleting team invitation:', error);
      res.status(500).json({ message: 'Failed to delete team invitation' });
    }
  });

  app.get('/api/role-permissions', ensureAuthenticated, requirePermission('manage_team'), async (req: any, res) => {
    try {
      const user = req.user;
      const companyId = user.isSuperAdmin ? undefined : user.companyId;

      const rolePermissions = await storage.getRolePermissions(companyId);
      res.json(rolePermissions);
    } catch (error) {
      console.error('Error fetching role permissions:', error);
      res.status(500).json({ message: 'Failed to fetch role permissions' });
    }
  });

  app.put('/api/role-permissions/:role', ensureAuthenticated, requirePermission('manage_team'), async (req: any, res) => {
    try {
      const role = req.params.role as 'admin' | 'agent';
      const { permissions } = req.body;

      if (!['admin', 'agent'].includes(role)) {
        return res.status(400).json({ message: 'Invalid role. Must be admin or agent.' });
      }

      if (!permissions || typeof permissions !== 'object') {
        return res.status(400).json({ message: 'Permissions object is required' });
      }

      const user = req.user;
      const companyId = user.isSuperAdmin ? undefined : user.companyId;

      const updatedRolePermissions = await storage.updateRolePermissions(role, permissions, companyId);
      res.json(updatedRolePermissions);
    } catch (error) {
      console.error('Error updating role permissions:', error);
      res.status(500).json({ message: 'Failed to update role permissions' });
    }
  });

  app.get('/api/smtp-config', ensureAuthenticated, ensureAdmin, async (req, res) => {
    try {
      const user = req.user as any;

      const companyId = user.isSuperAdmin ? undefined : user.companyId;
      const config = await storage.getSmtpConfig(companyId);

      res.json(config || {
        host: '',
        port: 465,
        secure: false,
        auth: {
          user: '',
          pass: ''
        },
        senderEmail: '',
        senderName: ''
      });
    } catch (error) {
      console.error('Error getting SMTP configuration:', error);
      res.status(500).json({ message: 'Failed to get SMTP configuration' });
    }
  });

  app.post('/api/smtp-config', ensureAuthenticated, ensureAdmin, async (req, res) => {
    try {
      const user = req.user as any;
      const config = req.body as SmtpConfig;

      const companyId = user.isSuperAdmin ? undefined : user.companyId;
      const success = await storage.saveSmtpConfig(config, companyId);

      if (!success) {
        return res.status(500).json({ message: 'Failed to update SMTP configuration' });
      }

      res.json({ message: 'SMTP configuration updated successfully' });
    } catch (error) {
      console.error('Error updating SMTP configuration:', error);
      res.status(500).json({ message: 'Failed to update SMTP configuration' });
    }
  });

  app.post('/api/smtp-config/test', ensureAuthenticated, ensureAdmin, async (req, res) => {
    try {
      const { config, testEmail } = req.body;

      if (!config || !testEmail) {
        return res.status(400).json({ message: 'Missing configuration or test email address' });
      }

      await testSmtpConfig(config, testEmail);
      res.json({ message: 'Test email sent successfully' });
    } catch (error) {
      console.error('Error testing SMTP configuration:', error);
      res.status(500).json({
      });
    }
  });

  app.get('/api/email/config/:connectionId', ensureAuthenticated, async (req, res) => {
    try {
      const user = req.user;
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'USER_NOT_AUTHENTICATED',
          message: 'User not authenticated'
        });
      }

      const connectionId = parseInt(req.params.connectionId);
      if (isNaN(connectionId)) {
        return res.status(400).json({
          success: false,
          error: 'INVALID_CONNECTION_ID',
          message: 'Invalid connection ID'
        });
      }

      const channelConnection = await storage.getChannelConnection(connectionId);
      if (!channelConnection) {
        return res.status(404).json({
          success: false,
          error: 'CONNECTION_NOT_FOUND',
          message: 'Channel connection not found'
        });
      }

      if (channelConnection.companyId !== user.companyId) {
        return res.status(403).json({
          success: false,
          error: 'ACCESS_DENIED',
          message: 'Access denied to this connection'
        });
      }

      const emailConfig = await storage.getEmailConfigByConnectionId(connectionId);

      let configData;
      if (emailConfig) {
        configData = {
          connectionId: channelConnection.id,
          connectionName: channelConnection.accountName,
          emailAddress: emailConfig.emailAddress,
          displayName: emailConfig.displayName,
          signature: emailConfig.signature,
          imapHost: emailConfig.imapHost,
          imapPort: emailConfig.imapPort,
          imapSecure: emailConfig.imapSecure,
          imapUsername: emailConfig.imapUsername,
          smtpHost: emailConfig.smtpHost,
          smtpPort: emailConfig.smtpPort,
          smtpSecure: emailConfig.smtpSecure,
          smtpUsername: emailConfig.smtpUsername,
          syncFolder: emailConfig.syncFolder,
          syncFrequency: emailConfig.syncFrequency,
          maxSyncMessages: emailConfig.maxSyncMessages,
          useOAuth2: !!emailConfig.oauthProvider,
          oauth2ClientId: emailConfig.oauthClientId,
          status: emailConfig.status,
          lastSyncAt: emailConfig.lastSyncAt,
          lastError: emailConfig.lastError
        };
      } else if (channelConnection.connectionData) {
        const connData = channelConnection.connectionData as any;
        configData = {
          connectionId: channelConnection.id,
          connectionName: channelConnection.accountName,
          emailAddress: connData.emailAddress || channelConnection.accountId,
          displayName: connData.displayName || '',
          signature: connData.signature || '',
          imapHost: connData.imapHost || '',
          imapPort: connData.imapPort || 993,
          imapSecure: connData.imapSecure !== false,
          imapUsername: connData.imapUsername || connData.emailAddress || channelConnection.accountId,
          smtpHost: connData.smtpHost || '',
          smtpPort: connData.smtpPort || 465,
          smtpSecure: connData.smtpSecure !== false,
          smtpUsername: connData.smtpUsername || connData.emailAddress || channelConnection.accountId,
          syncFolder: connData.syncFolder || 'INBOX',
          syncFrequency: connData.syncFrequency || 60,
          maxSyncMessages: connData.maxSyncMessages || 100,
          useOAuth2: !!connData.useOAuth2,
          oauth2ClientId: connData.oauth2ClientId || '',
          status: 'inactive',
          lastSyncAt: null,
          lastError: null
        };
      } else {
        configData = {
          connectionId: channelConnection.id,
          connectionName: channelConnection.accountName,
          emailAddress: channelConnection.accountId,
          displayName: '',
          signature: '',
          imapHost: '',
          imapPort: 993,
          imapSecure: true,
          imapUsername: '',
          smtpHost: '',
          smtpPort: 465,
          smtpSecure: true,
          smtpUsername: '',
          syncFolder: 'INBOX',
          syncFrequency: 60,
          maxSyncMessages: 100,
          useOAuth2: false,
          oauth2ClientId: '',
          status: 'inactive',
          lastSyncAt: null,
          lastError: null
        };
      }

      res.json({
        success: true,
        data: configData
      });

    } catch (error: any) {
      console.error('Error getting email configuration:', error);
      res.status(500).json({
        success: false,
        error: 'EMAIL_CONFIG_ERROR',
        message: error.message || 'Failed to get email configuration'
      });
    }
  });

  app.post('/api/email/test-connection', ensureAuthenticated, async (req, res) => {
    try {
      const {
        imapHost,
        imapPort,
        imapSecure,
        imapUsername,
        imapPassword,
        smtpHost,
        smtpPort,
        smtpSecure,
        smtpUsername,
        smtpPassword,
        useOAuth2,
        oauth2ClientId,
        oauth2ClientSecret,
        oauth2RefreshToken
      } = req.body;

      if (!imapHost || !imapPort || !smtpHost || !smtpPort) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_REQUIRED_FIELDS',
          message: 'IMAP and SMTP host/port are required'
        });
      }

      if (!useOAuth2 && (!imapPassword || !smtpPassword)) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_CREDENTIALS',
          message: 'Passwords are required when not using OAuth2'
        });
      }

      const { ImapFlow } = await import('imapflow');
      const imapClient = new ImapFlow({
        host: imapHost,
        port: imapPort,
        secure: imapSecure,
        auth: useOAuth2 ? {
          user: imapUsername,
          accessToken: oauth2RefreshToken
        } : {
          user: imapUsername,
          pass: imapPassword
        }
      });

      try {
        await imapClient.connect();
        await imapClient.logout();
      } catch (error: any) {
        return res.status(400).json({
          success: false,
          error: 'IMAP_CONNECTION_FAILED',
          message: `IMAP connection failed: ${error.message}`
        });
      }

      const nodemailer = await import('nodemailer');
      const smtpTransporter = nodemailer.createTransport({
        host: smtpHost,
        port: smtpPort,
        secure: smtpSecure,
        auth: useOAuth2 ? {
          type: 'OAuth2',
          user: smtpUsername,
          clientId: oauth2ClientId,
          clientSecret: oauth2ClientSecret,
          refreshToken: oauth2RefreshToken
        } : {
          user: smtpUsername,
          pass: smtpPassword
        }
      } as any);

      try {
        await smtpTransporter.verify();
      } catch (error: any) {
        return res.status(400).json({
          success: false,
          error: 'SMTP_CONNECTION_FAILED',
          message: `SMTP connection failed: ${error.message}`
        });
      }

      res.json({
        success: true,
        message: 'Email connection test successful'
      });

    } catch (error: any) {
      console.error('Error testing email connection:', error);
      res.status(500).json({
        success: false,
        error: 'EMAIL_TEST_ERROR',
        message: error.message || 'Failed to test email connection'
      });
    }
  });

  app.post('/api/email/configure', ensureAuthenticated, async (req, res) => {
    try {
      const user = req.user;
      if (!user || !user.companyId) {
        return res.status(403).json({
          success: false,
          error: 'NO_COMPANY_ASSOCIATION',
          message: 'No company association found'
        });
      }

      const {
        connectionId,
        imapHost,
        imapPort,
        imapSecure,
        imapUsername,
        imapPassword,
        smtpHost,
        smtpPort,
        smtpSecure,
        smtpUsername,
        smtpPassword,
        emailAddress,
        displayName,
        signature,
        syncFolder,
        syncFrequency,
        maxSyncMessages,
        isUpdate = false
      } = req.body;

      if (!connectionId || !imapHost || !imapPort || !imapUsername ||
          !smtpHost || !smtpPort || !smtpUsername || !emailAddress) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_REQUIRED_FIELDS',
          message: 'Missing required email configuration fields'
        });
      }

      if (!isUpdate && (!imapPassword || !smtpPassword)) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_PASSWORDS',
          message: 'IMAP and SMTP passwords are required for new configurations'
        });
      }

      const channelConnection = await storage.getChannelConnection(connectionId);
      if (!channelConnection) {
        return res.status(404).json({
          success: false,
          error: 'CONNECTION_NOT_FOUND',
          message: 'Channel connection not found'
        });
      }

      if (channelConnection.companyId !== user.companyId) {
        return res.status(403).json({
          success: false,
          error: 'ACCESS_DENIED',
          message: 'Access denied to this connection'
        });
      }

      let finalImapPassword = imapPassword;
      let finalSmtpPassword = smtpPassword;

      if (isUpdate) {
        const existingConfig = await storage.getEmailConfigByConnectionId(connectionId);
        if (existingConfig) {
          finalImapPassword = imapPassword || existingConfig.imapPassword;
          finalSmtpPassword = smtpPassword || existingConfig.smtpPassword;
        }
      }

      const emailService = await import('./services/channels/email');

      const success = await emailService.default.initializeConnection(connectionId, {
        imapHost,
        imapPort: parseInt(imapPort),
        imapSecure: Boolean(imapSecure),
        imapUsername,
        imapPassword: finalImapPassword,
        smtpHost,
        smtpPort: parseInt(smtpPort),
        smtpSecure: Boolean(smtpSecure),
        smtpUsername,
        smtpPassword: finalSmtpPassword,
        emailAddress,
        displayName,
        signature,
        syncFolder: syncFolder || 'INBOX',
        syncFrequency: syncFrequency ? parseInt(syncFrequency) : 60,
        maxSyncMessages: maxSyncMessages ? parseInt(maxSyncMessages) : 100
      });

      if (success) {
        res.json({
          success: true,
          message: isUpdate ? 'Email channel updated successfully' : 'Email channel configured successfully'
        });
      } else {
        res.status(400).json({
          success: false,
          error: 'EMAIL_CONFIG_FAILED',
          message: isUpdate ? 'Failed to update email channel' : 'Failed to configure email channel'
        });
      }

    } catch (error: any) {
      console.error('Error configuring email channel:', error);
      res.status(500).json({
        success: false,
        error: 'EMAIL_CONFIG_ERROR',
        message: error.message || 'Failed to configure email channel'
      });
    }
  });

  app.post('/api/email/connect', ensureAuthenticated, async (req, res) => {
    try {
      const user = req.user;
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'USER_NOT_AUTHENTICATED',
          message: 'User not authenticated'
        });
      }

      const { connectionId } = req.body;

      if (!connectionId) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_CONNECTION_ID',
          message: 'Connection ID is required'
        });
      }

      const emailService = await import('./services/channels/email');

      const success = await emailService.connectToEmail(connectionId, user.id);

      if (success) {
        res.json({
          success: true,
          message: 'Email channel connected successfully'
        });
      } else {
        res.status(400).json({
          success: false,
          error: 'EMAIL_CONNECT_FAILED',
          message: 'Failed to connect to email channel'
        });
      }

    } catch (error: any) {
      console.error('Error connecting email channel:', error);
      res.status(500).json({
        success: false,
        error: 'EMAIL_CONNECT_ERROR',
        message: error.message || 'Failed to connect to email channel'
      });
    }
  });

  app.post('/api/email/disconnect', ensureAuthenticated, async (req, res) => {
    try {
      const { connectionId } = req.body;

      if (!connectionId) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_CONNECTION_ID',
          message: 'Connection ID is required'
        });
      }

      const emailService = await import('./services/channels/email');

      const success = await emailService.disconnectFromEmail(connectionId);

      if (success) {
        res.json({
          success: true,
          message: 'Email channel disconnected successfully'
        });
      } else {
        res.status(400).json({
          success: false,
          error: 'EMAIL_DISCONNECT_FAILED',
          message: 'Failed to disconnect from email channel'
        });
      }

    } catch (error: any) {
      console.error('Error disconnecting email channel:', error);
      res.status(500).json({
        success: false,
        error: 'EMAIL_DISCONNECT_ERROR',
        message: error.message || 'Failed to disconnect from email channel'
      });
    }
  });

  // EventEmitter monitoring endpoint
  app.get('/api/debug/event-emitters', ensureAuthenticated, async (req, res) => {
    try {
      const summary = eventEmitterMonitor.getSummary();
      res.json(summary);
    } catch (error) {
      console.error('Error getting EventEmitter status:', error);
      res.status(500).json({ error: 'Failed to get EventEmitter status' });
    }
  });

  // Force cleanup EventEmitter listeners (admin only)
  app.post('/api/debug/event-emitters/:name/cleanup', ensureAuthenticated, requirePermission('ADMIN'), async (req, res) => {
    try {
      const emitterName = req.params.name;
      const success = eventEmitterMonitor.forceCleanup(emitterName);

      if (success) {
        res.json({ message: `Successfully cleaned up listeners for ${emitterName}` });
      } else {
        res.status(404).json({ error: `EventEmitter ${emitterName} not found` });
      }
    } catch (error) {
      console.error('Error cleaning up EventEmitter:', error);
      res.status(500).json({ error: 'Failed to cleanup EventEmitter' });
    }
  });

  // Email debug endpoint
  app.get('/api/email/debug', ensureAuthenticated, async (req, res) => {
    try {
      const emailService = await import('./services/channels/email');
      const connectionsStatus = await emailService.getEmailConnectionsStatus();

      // Also get email connections from database
      const dbEmailConnections = await storage.getChannelConnectionsByType('email');
      const dbEmailConfigs = await Promise.all(
        dbEmailConnections.map(async (conn) => {
          const config = await storage.getEmailConfigByConnectionId(conn.id);
          return {
            connectionId: conn.id,
            accountName: conn.accountName,
            status: conn.status,
            companyId: conn.companyId,
            userId: conn.userId,
            emailAddress: config?.emailAddress || 'N/A',
            lastSyncAt: config?.lastSyncAt?.toISOString() || 'Never'
          };
        })
      );

      res.json({
        success: true,
        data: {
          activeConnections: connectionsStatus,
          databaseConnections: dbEmailConfigs,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error: any) {
      console.error('Error getting email debug info:', error);
      res.status(500).json({
        success: false,
        error: 'EMAIL_DEBUG_ERROR',
        message: error.message || 'Failed to get email debug info'
      });
    }
  });

  // Email cleanup endpoint
  app.post('/api/email/cleanup', ensureAuthenticated, async (req, res) => {
    try {
      const emailService = await import('./services/channels/email');
      await emailService.cleanupOrphanedConnections();
      res.json({
        success: true,
        message: 'Email connection cleanup completed'
      });
    } catch (error: any) {
      console.error('Error cleaning up email connections:', error);
      res.status(500).json({
        success: false,
        error: 'EMAIL_CLEANUP_ERROR',
        message: error.message || 'Failed to cleanup email connections'
      });
    }
  });

  // Start database-driven email polling (no auth for debugging)
  app.post('/api/email/start-polling', async (req, res) => {
    try {
      const emailService = await import('./services/channels/email');
      await emailService.startAllEmailPolling();
      res.json({
        success: true,
        message: 'Database-driven email polling started for all active connections'
      });
    } catch (error: any) {
      console.error('Error starting email polling:', error);
      res.status(500).json({
        success: false,
        error: 'EMAIL_POLLING_ERROR',
        message: error.message || 'Failed to start email polling'
      });
    }
  });

  // Stop old-style email polling
  app.post('/api/email/stop-old-polling', ensureAuthenticated, async (req, res) => {
    try {
      const emailService = await import('./services/channels/email');
      await emailService.stopAllOldPolling();
      res.json({
        success: true,
        message: 'Old-style email polling stopped and in-memory connections cleared'
      });
    } catch (error: any) {
      console.error('Error stopping old email polling:', error);
      res.status(500).json({
        success: false,
        error: 'EMAIL_STOP_POLLING_ERROR',
        message: error.message || 'Failed to stop old email polling'
      });
    }
  });

  // Manual email sync for testing
  app.post('/api/email/sync/:connectionId', ensureAuthenticated, async (req, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      if (isNaN(connectionId)) {
        return res.status(400).json({
          success: false,
          error: 'INVALID_CONNECTION_ID',
          message: 'Invalid connection ID'
        });
      }

      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: 'User authentication required'
        });
      }

      const emailService = await import('./services/channels/email');
      await emailService.syncNewEmails(connectionId, req.user.id);

      res.json({
        success: true,
        message: `Email sync completed for connection ${connectionId}`
      });
    } catch (error: any) {
      console.error('Error during manual email sync:', error);
      res.status(500).json({
        success: false,
        error: 'EMAIL_SYNC_ERROR',
        message: error.message || 'Failed to sync emails'
      });
    }
  });

  // Debug email connection status
  app.get('/api/email/debug/:connectionId', ensureAuthenticated, async (req, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      if (isNaN(connectionId)) {
        return res.status(400).json({
          success: false,
          error: 'INVALID_CONNECTION_ID',
          message: 'Invalid connection ID'
        });
      }

      // Get email config
      const emailConfig = await storage.getEmailConfigByConnectionId(connectionId);
      const channelConnection = await storage.getChannelConnection(connectionId);

      // Get recent messages from database
      const recentMessages = await storage.getMessagesByConversationPaginated(connectionId, 10, 0);

      // Import email service to check internal state
      const emailService = await import('./services/channels/email');
      const pollingStatus = await emailService.getPollingStatus(connectionId);

      res.json({
        success: true,
        debug: {
          connectionId,
          emailConfig: emailConfig ? {
            emailAddress: emailConfig.emailAddress,
            lastSyncAt: emailConfig.lastSyncAt,
            syncFrequency: emailConfig.syncFrequency,
            status: emailConfig.status,
            imapHost: emailConfig.imapHost,
            imapPort: emailConfig.imapPort,
            imapSecure: emailConfig.imapSecure
          } : null,
          channelConnection: channelConnection ? {
            id: channelConnection.id,
            status: channelConnection.status,
            accountName: channelConnection.accountName,
            channelType: channelConnection.channelType,
            companyId: channelConnection.companyId
          } : null,
          pollingStatus,
          recentMessagesCount: recentMessages.length,
          recentMessages: recentMessages.slice(0, 3).map(msg => ({
            id: msg.id,
            content: msg.content.substring(0, 100),
            createdAt: msg.createdAt,
            direction: msg.direction,
            type: msg.type
          }))
        }
      });
    } catch (error: any) {
      console.error('Error getting email debug info:', error);
      res.status(500).json({
        success: false,
        error: 'EMAIL_DEBUG_ERROR',
        message: error.message || 'Failed to get debug info'
      });
    }
  });

  // Get all email connections status
  app.get('/api/email/status/all', ensureAuthenticated, async (req, res) => {
    try {
      const emailService = await import('./services/channels/email');
      const allStatus = await emailService.getAllPollingStatus();

      res.json({
        success: true,
        status: allStatus
      });
    } catch (error: any) {
      console.error('Error getting all email status:', error);
      res.status(500).json({
        success: false,
        error: 'EMAIL_STATUS_ERROR',
        message: error.message || 'Failed to get status'
      });
    }
  });

  // Force restart email polling for debugging
  app.post('/api/email/restart-polling/:connectionId', ensureAuthenticated, async (req, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      if (isNaN(connectionId)) {
        return res.status(400).json({
          success: false,
          error: 'INVALID_CONNECTION_ID',
          message: 'Invalid connection ID'
        });
      }

      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: 'User authentication required'
        });
      }

      const emailService = await import('./services/channels/email');

      // Stop existing polling
      await emailService.disconnectEmailChannel(connectionId);

      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Restart polling
      await emailService.startEmailPollingForConnection(connectionId, req.user.id);

      res.json({
        success: true,
        message: `Email polling restarted for connection ${connectionId}`
      });
    } catch (error: any) {
      console.error('Error restarting email polling:', error);
      res.status(500).json({
        success: false,
        error: 'EMAIL_RESTART_ERROR',
        message: error.message || 'Failed to restart polling'
      });
    }
  });

  // Get detailed email configuration for debugging
  app.get('/api/email/config/:connectionId', ensureAuthenticated, async (req, res) => {
    try {
      const { connectionId } = req.params;
      const connection = await storage.getChannelConnection(parseInt(connectionId));
      const emailConfig = await storage.getEmailConfigByConnectionId(parseInt(connectionId));

      if (!connection) {
        return res.status(404).json({
          success: false,
          error: 'CONNECTION_NOT_FOUND',
          message: `Channel connection ${connectionId} not found`
        });
      }

      res.json({
        success: true,
        data: {
          connection: {
            id: connection.id,
            accountName: connection.accountName,
            status: connection.status,
            channelType: connection.channelType,
            companyId: connection.companyId,
            userId: connection.userId
          },
          emailConfig: emailConfig ? {
            id: emailConfig.id,
            emailAddress: emailConfig.emailAddress,
            imapHost: emailConfig.imapHost,
            imapPort: emailConfig.imapPort,
            imapSecure: emailConfig.imapSecure,
            smtpHost: emailConfig.smtpHost,
            smtpPort: emailConfig.smtpPort,
            smtpSecure: emailConfig.smtpSecure,
            syncFolder: emailConfig.syncFolder,
            syncFrequency: emailConfig.syncFrequency,
            lastSyncAt: emailConfig.lastSyncAt?.toISOString(),
            status: emailConfig.status
          } : null
        }
      });
    } catch (error: any) {
      console.error('Error getting email configuration:', error);
      res.status(500).json({
        success: false,
        error: 'EMAIL_CONFIG_ERROR',
        message: error.message || 'Failed to get email configuration'
      });
    }
  });

  // Manual email sync trigger for testing (no auth for debugging)
  app.post('/api/email/sync/:connectionId', async (req, res) => {
    try {
      const { connectionId } = req.params;
      const user = req.user;

      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: 'User authentication required'
        });
      }

      if (!connectionId) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_CONNECTION_ID',
          message: 'Connection ID is required'
        });
      }

      const emailService = await import('./services/channels/email');

      // Trigger manual sync
      await emailService.default.syncNewEmails(parseInt(connectionId), user.id);

      res.json({
        success: true,
        message: 'Email sync triggered successfully'
      });
    } catch (error: any) {
      console.error('Error triggering email sync:', error);
      res.status(500).json({
        success: false,
        error: 'EMAIL_SYNC_ERROR',
        message: error.message || 'Failed to trigger email sync'
      });
    }
  });

  // List email mailboxes for debugging
  app.get('/api/email/mailboxes/:connectionId', ensureAuthenticated, async (req, res) => {
    try {
      const { connectionId } = req.params;

      if (!connectionId) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_CONNECTION_ID',
          message: 'Connection ID is required'
        });
      }

      const emailService = await import('./services/channels/email');
      const mailboxes = await emailService.default.listMailboxes(parseInt(connectionId));

      res.json({
        success: true,
        data: mailboxes
      });
    } catch (error: any) {
      console.error('Error listing email mailboxes:', error);
      res.status(500).json({
        success: false,
        error: 'EMAIL_MAILBOX_LIST_ERROR',
        message: error.message || 'Failed to list email mailboxes'
      });
    }
  });






  app.delete('/api/pipeline/stages/:id', ensureAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const moveDealsToStageId = req.query.moveToStageId ? parseInt(req.query.moveToStageId as string) : undefined;

      if (isNaN(id)) {
        return res.status(400).json({ message: 'Invalid stage ID' });
      }

      if (moveDealsToStageId !== undefined && isNaN(moveDealsToStageId)) {
        return res.status(400).json({ message: 'Invalid target stage ID' });
      }

      const existingStage = await storage.getPipelineStage(id);
      if (!existingStage) {
        return res.status(404).json({ message: 'Pipeline stage not found' });
      }

      if (moveDealsToStageId !== undefined) {
        if (moveDealsToStageId === id) {
          return res.status(400).json({ message: 'Target stage cannot be the same as the deleted stage' });
        }

        const targetStage = await storage.getPipelineStage(moveDealsToStageId);
        if (!targetStage) {
          return res.status(404).json({ message: 'Target stage not found' });
        }
      }

      const result = await storage.deletePipelineStage(id, moveDealsToStageId);

      if (result) {
        res.status(200).json({ message: 'Pipeline stage deleted successfully' });
      } else {
        res.status(500).json({ message: 'Failed to delete pipeline stage' });
      }
    } catch (error) {
      console.error('Error deleting pipeline stage:', error);
      res.status(500).json({ message: 'Failed to delete pipeline stage' });
    }
  });

  app.get('/api/deals', ensureAuthenticated, async (req, res) => {
    try {
      const user = req.user as any;
      const priority = req.query.priority as string | undefined;
      const assignedTo = req.query.assignedTo ? parseInt(req.query.assignedTo as string) : undefined;
      const stageId = req.query.stageId ? parseInt(req.query.stageId as string) : undefined;
      const titleSearch = req.query.titleSearch as string | undefined;
      const tags = req.query.tags as string | undefined;
      const contactPhone = req.query.contactPhone as string | undefined;
      const contactName = req.query.contactName as string | undefined;

      if (!user.companyId) {
        return res.status(400).json({ message: 'User must be associated with a company' });
      }

      const filter: {
        priority?: string;
        assignedToUserId?: number;
        stageId?: number;
        companyId?: number;
        titleSearch?: string;
        tags?: string[];
        contactPhone?: string;
        contactName?: string;
      } = {
        companyId: user.companyId
      };

      if (priority) {
        filter.priority = priority;
      }

      if (assignedTo) {
        filter.assignedToUserId = assignedTo;
      }

      if (stageId) {
        filter.stageId = stageId;
      }

      if (titleSearch) {
        filter.titleSearch = titleSearch;
      }

      if (tags) {

        filter.tags = tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
      }

      if (contactPhone) {
        filter.contactPhone = contactPhone;
      }

      if (contactName) {
        filter.contactName = contactName;
      }

      const deals = await storage.getDeals(filter);
      return res.status(200).json(deals);
    } catch (error) {
      console.error("Error fetching deals:", error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/deals/stage/:stage', ensureAuthenticated, async (req, res) => {
    try {
      const { stage } = req.params;
      const deals = await storage.getDealsByStage(stage as any);
      return res.status(200).json(deals);
    } catch (error) {
      console.error(`Error fetching deals for stage ${req.params.stage}:`, error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/deals/contact/:contactId', ensureAuthenticated, async (req, res) => {
    try {
      const contactId = parseInt(req.params.contactId);
      const deals = await storage.getDealsByContact(contactId);
      return res.status(200).json(deals);
    } catch (error) {
      console.error(`Error fetching deals for contact ${req.params.contactId}:`, error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/deals/user/:userId', ensureAuthenticated, async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const deals = await storage.getDealsByAssignedUser(userId);
      return res.status(200).json(deals);
    } catch (error) {
      console.error(`Error fetching deals for user ${req.params.userId}:`, error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/deals/tags', ensureAuthenticated, async (req, res) => {
    try {
      const user = req.user as any;

      if (!user.companyId) {
        return res.status(400).json({ message: 'User must be associated with a company' });
      }

      const tags = await storage.getDealTags(user.companyId);
      return res.status(200).json(tags);
    } catch (error) {
      console.error('Error fetching deal tags:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/deals/:id', ensureAuthenticated, async (req, res) => {
    try {
      const dealId = parseInt(req.params.id);
      const deal = await storage.getDeal(dealId);

      if (!deal) {
        return res.status(404).json({ message: 'Deal not found' });
      }

      return res.status(200).json(deal);
    } catch (error) {
      console.error(`Error fetching deal ${req.params.id}:`, error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/deals', ensureAuthenticated, async (req, res) => {
    try {
      const user = req.user as any;

      if (!req.body.contactId) {
        return res.status(400).json({ message: 'Contact ID is required' });
      }

      if (!req.body.title) {
        return res.status(400).json({ message: 'Deal title is required' });
      }

      if (!user.companyId) {
        return res.status(400).json({ message: 'User must be associated with a company' });
      }

      const contact = await storage.getContact(req.body.contactId);
      if (!contact) {
        return res.status(404).json({ message: 'Contact not found' });
      }

      if (contact.companyId !== user.companyId) {
        return res.status(403).json({ message: 'Contact does not belong to your company' });
      }


      const mapStageNameToDbValue = (stageName: string): string => {
        const normalizedName = stageName.toLowerCase().trim();


        const stageMapping: Record<string, string> = {
          'lead': 'lead',
          'leads': 'lead',
          'qualified': 'qualified',
          'qualify': 'qualified',
          'contacted': 'contacted',
          'contact': 'contacted',
          'demo scheduled': 'demo_scheduled',
          'demo': 'demo_scheduled',
          'scheduled': 'demo_scheduled',
          'proposal': 'proposal',
          'proposals': 'proposal',
          'negotiation': 'negotiation',
          'negotiate': 'negotiation',
          'neg': 'negotiation',
          'closed won': 'closed_won',
          'won': 'closed_won',
          'closed': 'closed_won',
          'closed lost': 'closed_lost',
          'lost': 'closed_lost'
        };

        return stageMapping[normalizedName] || 'lead';
      };

      let stageId = null;
      let stage = 'lead';

      if (req.body.stage) {
        const providedStageId = parseInt(req.body.stage);
        if (!isNaN(providedStageId)) {
          const pipelineStage = await storage.getPipelineStageById(providedStageId);
          if (!pipelineStage) {
            return res.status(404).json({ message: 'Pipeline stage not found' });
          }

          if (pipelineStage.companyId !== user.companyId) {
            return res.status(403).json({ message: 'Pipeline stage does not belong to your company' });
          }

          stageId = providedStageId;
          stage = mapStageNameToDbValue(pipelineStage.name);
        }
      }

      const dealData = {
        ...req.body,
        companyId: user.companyId,
        stageId: stageId,
        stage: stage,
        assignedToUserId: req.body.assignedToUserId || null
      };

      if (dealData.assignedToUserId) {
        const assignedUser = await storage.getUser(dealData.assignedToUserId);
        if (!assignedUser || assignedUser.companyId !== user.companyId) {
          return res.status(403).json({ message: 'Assigned user does not belong to your company' });
        }
      }

      const newDeal = await storage.createDeal(dealData);

      await storage.createDealActivity({
        dealId: newDeal.id,
        userId: user.id,
        type: 'create',
        content: `Deal "${newDeal.title}" created`,
        metadata: { createdBy: user.id }
      });

      return res.status(201).json(newDeal);
    } catch (error: any) {
      console.error("Error creating deal:", error);

      if (error.message === 'Contact ID is required') {
        return res.status(400).json({ message: 'Contact ID is required' });
      }

      if (error.message === 'Deal title is required') {
        return res.status(400).json({ message: 'Deal title is required' });
      }

      if (error.message.includes('foreign key constraint')) {
        return res.status(400).json({ message: 'Invalid reference data provided' });
      }

      if (error.message.includes('deals_stage_check') || error.constraint === 'deals_stage_check') {
        return res.status(400).json({ message: 'Invalid stage value. Please select a valid pipeline stage.' });
      }

      return res.status(500).json({ message: 'Failed to create deal', error: error.message });
    }
  });

  app.patch('/api/deals/:id', ensureAuthenticated, async (req, res) => {
    try {
      const dealId = parseInt(req.params.id);
      const updatedDeal = await storage.updateDeal(dealId, req.body);

      await storage.createDealActivity({
        dealId: updatedDeal.id,
        userId: typeof req.user?.id === 'number' ? req.user.id : (() => { res.status(400).json({ message: 'User ID is required' }); throw new Error('User ID is required'); })(),
        type: 'update',
        content: `Deal "${updatedDeal.title}" updated`,
        metadata: { updatedBy: req.user.id, changes: req.body }
      });

      return res.status(200).json(updatedDeal);
    } catch (error) {
      console.error(`Error updating deal ${req.params.id}:`, error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/deals/:id/stage', ensureAuthenticated, async (req, res) => {
    try {
      const dealId = parseInt(req.params.id);
      const { stage } = req.body;

      const updatedDeal = await storage.updateDealStage(dealId, stage);

      await storage.createDealActivity({
        dealId: updatedDeal.id,
        userId: typeof req.user?.id === 'number' ? req.user.id : (() => { res.status(400).json({ message: 'User ID is required' }); throw new Error('User ID is required'); })(),
        type: 'stage_change',
        content: `Deal moved to "${stage}" stage`,
        metadata: {
          updatedBy: req.user.id,
          previousStage: updatedDeal.stage,
          newStage: stage
        }
      });

      return res.status(200).json(updatedDeal);
    } catch (error) {
      console.error(`Error updating stage for deal ${req.params.id}:`, error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/deals/:id/stageId', ensureAuthenticated, async (req, res) => {
    try {
      const dealId = parseInt(req.params.id);
      const { stageId } = req.body;

      if (!stageId || isNaN(parseInt(stageId))) {
        return res.status(400).json({ message: 'Invalid stage ID' });
      }

      const stage = await storage.getPipelineStage(parseInt(stageId));
      if (!stage) {
        return res.status(404).json({ message: 'Pipeline stage not found' });
      }

      const updatedDeal = await storage.updateDealStageId(dealId, parseInt(stageId));

      return res.status(200).json(updatedDeal);
    } catch (error) {
      console.error(`Error updating stage ID for deal ${req.params.id}:`, error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.delete('/api/deals/:id', ensureAuthenticated, async (req, res) => {
    try {
      const dealId = parseInt(req.params.id);
      const result = await storage.deleteDeal(dealId);

      if (!result) {
        return res.status(404).json({ message: 'Deal not found or already deleted' });
      }

      return res.status(200).json({ message: 'Deal deleted successfully' });
    } catch (error) {
      console.error(`Error deleting deal ${req.params.id}:`, error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/deals/:id/activities', ensureAuthenticated, async (req, res) => {
    try {
      const dealId = parseInt(req.params.id);
      const activities = await storage.getDealActivities(dealId);

      return res.status(200).json(activities);
    } catch (error) {
      console.error(`Error fetching activities for deal ${req.params.id}:`, error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/deals/:id/activities', ensureAuthenticated, async (req, res) => {
    try {
      const dealId = parseInt(req.params.id);
      const activity = await storage.createDealActivity({
        ...req.body,
        dealId,
        userId: req.user?.id || 1
      });

      return res.status(201).json(activity);
    } catch (error) {
      console.error(`Error creating activity for deal ${req.params.id}:`, error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });


  app.get('/api/debug/pipeline-stages', ensureAuthenticated, async (req, res) => {
    try {
      const user = req.user as any;
      

      const allStages = await storage.getPipelineStages();
      

      const companyStages = user.companyId ? await storage.getPipelineStagesByCompany(user.companyId) : [];
      

      const company = user.companyId ? await storage.getCompany(user.companyId) : null;
      

      const allCompanies = await storage.getAllCompanies();
      

      const dbUser = await storage.getUser(user.id);
      

      res.json({
        user: { id: user.id, companyId: user.companyId, isSuperAdmin: user.isSuperAdmin },
        dbUser: dbUser ? { id: dbUser.id, companyId: dbUser.companyId, email: dbUser.email } : null,
        allStages: allStages.length,
        companyStages: companyStages.length,
        company: company ? { id: company.id, name: company.name, active: company.active } : null,
        allCompanies: allCompanies.map((c: any) => ({ id: c.id, name: c.name, active: c.active })),
        stages: companyStages
      });
    } catch (error: any) {
      console.error('Error in debug endpoint:', error);
      res.status(500).json({ message: 'Debug failed', error: error.message });
    }
  });

  app.post('/api/debug/fix-user-company', ensureAuthenticated, async (req, res) => {
    try {
      const user = req.user as any;
      const { companyId } = req.body;

      

      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      const company = await storage.getCompany(companyId);
      if (!company) {
        return res.status(404).json({ message: 'Company not found' });
      }

      const updatedUser = await storage.updateUser(user.id, { companyId });
      

      res.json({
        message: 'User company association updated successfully',
        user: { id: updatedUser.id, companyId: updatedUser.companyId },
        company: { id: company.id, name: company.name }
      });
    } catch (error: any) {
      console.error('Error fixing user company association:', error);
      res.status(500).json({ message: 'Failed to fix user company association', error: error.message });
    }
  });

  app.post('/api/debug/create-default-stages', ensureAuthenticated, async (req, res) => {
    try {
      const user = req.user as any;
      const { companyId } = req.body;

      const targetCompanyId = companyId || user.companyId;

      if (!targetCompanyId) {
        return res.status(400).json({ message: 'Company ID is required (either in body or user must belong to a company)' });
      }

      

      const defaultStages = [
        { name: 'Lead', color: '#333235', order: 1 },
        { name: 'Qualified', color: '#8B5CF6', order: 2 },
        { name: 'Contacted', color: '#EC4899', order: 3 },
        { name: 'Demo Scheduled', color: '#F59E0B', order: 4 },
        { name: 'Proposal', color: '#10B981', order: 5 },
        { name: 'Negotiation', color: '#3B82F6', order: 6 },
        { name: 'Closed Won', color: '#059669', order: 7 },
        { name: 'Closed Lost', color: '#DC2626', order: 8 }
      ];

      const createdStages = [];
      for (const stageData of defaultStages) {
        const stage = await storage.createPipelineStage({
          companyId: targetCompanyId,
          ...stageData
        });
        createdStages.push(stage);
        
      }

      res.json({
        message: 'Default stages created successfully',
        stages: createdStages
      });
    } catch (error: any) {
      console.error('Error creating default stages:', error);
      res.status(500).json({ message: 'Failed to create default stages', error: error.message });
    }
  });

  app.get('/api/pipeline/stages', ensureAuthenticated, async (req, res) => {
    try {
      const user = req.user as any;
      

      if (!user.companyId) {
        
        return res.json([]);
      }

      const stages = await storage.getPipelineStagesByCompany(user.companyId);
      
      res.json(stages);
    } catch (error) {
      console.error('Error fetching pipeline stages:', error);
      res.status(500).json({ message: 'Failed to fetch pipeline stages' });
    }
  });

  app.post('/api/pipeline/stages', ensureAuthenticated, async (req, res) => {
    try {
      const { name, color, order } = req.body;
      const user = req.user as any;

      
      

      if (!name) {
        return res.status(400).json({ message: 'Stage name is required' });
      }

      if (!user.companyId) {
        console.error('❌ User has no companyId:', user);
        return res.status(400).json({ message: 'User must belong to a company to create stages' });
      }

      const newStage = await storage.createPipelineStage({
        companyId: user.companyId,
        name,
        color: color || '#3a86ff',
        order: order || 0
      });

      
      res.status(201).json(newStage);
    } catch (error) {
      console.error('Error creating pipeline stage:', error);
      res.status(500).json({ message: 'Failed to create pipeline stage' });
    }
  });

  app.put('/api/pipeline/stages/:id', ensureAuthenticated, async (req, res) => {
    try {
      const stageId = parseInt(req.params.id);
      const { name, color, order } = req.body;
      const user = req.user as any;

      const stage = await storage.getPipelineStage(stageId);

      if (!stage) {
        return res.status(404).json({ message: 'Pipeline stage not found' });
      }

      if (stage.companyId !== user.companyId) {
        return res.status(403).json({ message: 'You do not have permission to update this stage' });
      }

      const updatedStage = await storage.updatePipelineStage(stageId, {
        name: name !== undefined ? name : stage.name,
        color: color !== undefined ? color : stage.color,
        order: order !== undefined ? order : stage.order
      });

      res.json(updatedStage);
    } catch (error) {
      console.error('Error updating pipeline stage:', error);
      res.status(500).json({ message: 'Failed to update pipeline stage' });
    }
  });

  app.delete('/api/pipeline/stages/:id', ensureAuthenticated, async (req, res) => {
    try {
      const stageId = parseInt(req.params.id);
      const moveToStageId = req.query.moveToStageId ? parseInt(req.query.moveToStageId as string) : null;
      const user = req.user as any;

      const stage = await storage.getPipelineStage(stageId);

      if (!stage) {
        return res.status(404).json({ message: 'Pipeline stage not found' });
      }

      if (stage.companyId !== user.companyId) {
        return res.status(403).json({ message: 'You do not have permission to delete this stage' });
      }

      if (moveToStageId) {
        const targetStage = await storage.getPipelineStage(moveToStageId);
        if (!targetStage) {
          return res.status(400).json({ message: 'Target stage not found' });
        }

        if (targetStage.companyId !== user.companyId) {
          return res.status(403).json({ message: 'Target stage does not belong to your company' });
        }

        if (moveToStageId === stageId) {
          return res.status(400).json({ message: 'Cannot move deals to the same stage being deleted' });
        }

        const dealsToMove = await storage.getDealsByStageId(stageId);
        for (const deal of dealsToMove) {
          await storage.updateDealStageId(deal.id, moveToStageId);
        }
      }

      await storage.deletePipelineStage(stageId);

      res.json({ success: true });
    } catch (error) {
      console.error('Error deleting pipeline stage:', error);
      res.status(500).json({ message: 'Failed to delete pipeline stage' });
    }
  });

  app.post('/api/pipeline/stages/reorder', ensureAuthenticated, async (req, res) => {
    try {
      const { stageIds } = req.body;

      if (!Array.isArray(stageIds)) {
        return res.status(400).json({ message: 'stageIds must be an array' });
      }

      await storage.reorderPipelineStages(stageIds);

      res.json({ success: true });
    } catch (error) {
      console.error('Error reordering pipeline stages:', error);
      res.status(500).json({ message: 'Failed to reorder pipeline stages' });
    }
  });

  app.get('/api/team-members', ensureAuthenticated, async (req, res) => {
    try {
      const user = req.user as any;

      let users;
      if (user.isSuperAdmin) {
        users = await storage.getActiveTeamMembers();
      } else {
        users = await storage.getActiveTeamMembersByCompany(user.companyId);
      }

      const safeUsers = users.map(member => {
        const { password, ...safeUser } = member;
        return safeUser;
      });

      return res.status(200).json(safeUsers);
    } catch (error) {
      console.error("Error fetching team members:", error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/search', ensureAuthenticated, async (req: any, res) => {
    try {
      const user = req.user as any;
      const query = req.query.q as string;
      const limit = parseInt(req.query.limit as string) || 5;

      if (!query || query.trim().length < 2) {
        return res.json({
          conversations: [],
          contacts: [],
          templates: []
        });
      }

      const companyId = user.isSuperAdmin ? undefined : user.companyId;
      const searchTerm = query.trim();

      const conversationsResult = await storage.getConversations({
        companyId,
        page: 1,
        limit,
        search: searchTerm
      });

      const conversationsWithContacts = await Promise.all(
        conversationsResult.conversations.map(async (conversation) => {
          const contact = conversation.contactId ? await storage.getContact(conversation.contactId) : null;
          const messages = await storage.getMessagesByConversation(conversation.id);
          const lastMessage = messages.length > 0 ? messages[messages.length - 1] : null;
          return {
            ...conversation,
            contact,
            lastMessage: lastMessage ? {
              content: lastMessage.content,
              createdAt: lastMessage.createdAt
            } : null
          };
        })
      );

      const contactsResult = await storage.getContacts({
        companyId,
        page: 1,
        limit,
        search: searchTerm
      });

      let templates: any[] = [];
      if (companyId) {
        try {
          const { CampaignService } = await import('./services/campaignService');
          const campaignService = new CampaignService();
          const allTemplates = await campaignService.getTemplates(companyId, {});

          templates = allTemplates.filter(template =>
            template.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            template.content?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            template.description?.toLowerCase().includes(searchTerm.toLowerCase())
          ).slice(0, limit);
        } catch (error) {
          console.error('Error searching templates:', error);
          templates = [];
        }
      }

      res.json({
        conversations: conversationsWithContacts,
        contacts: contactsResult.contacts,
        templates
      });
    } catch (error) {
      console.error('Error performing global search:', error);
      res.status(500).json({ message: 'Search failed' });
    }
  });

  app.get('/api/analytics/overview', ensureAuthenticated, async (req: any, res) => {
    try {
      const { period = '7days', from, to } = req.query;
      const user = req.user as any;


      if (!user.companyId) {
        return res.status(400).json({ message: 'User must be associated with a company' });
      }

      let startDate = new Date();
      let endDate = new Date();

      if (from && to) {
        startDate = new Date(from);
        endDate = new Date(to);
      } else {
        switch (period) {
          case 'today':
            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'yesterday':
            startDate.setDate(startDate.getDate() - 1);
            startDate.setHours(0, 0, 0, 0);
            endDate.setDate(endDate.getDate() - 1);
            endDate.setHours(23, 59, 59, 999);
            break;
          case '7days':
            startDate.setDate(startDate.getDate() - 7);
            break;
          case '30days':
            startDate.setDate(startDate.getDate() - 30);
            break;
          case '90days':
            startDate.setDate(startDate.getDate() - 90);
            break;
          default:
            startDate.setDate(startDate.getDate() - 7);
        }
      }


      const conversationsCount = await storage.getConversationsCountByCompany(user.companyId);
      const contactsData = await storage.getContacts({ companyId: user.companyId });
      const contactsCount = contactsData.total;
      const messagesCount = await storage.getMessagesCountByCompany(user.companyId);

      const periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      const prevStartDate = new Date(startDate);
      prevStartDate.setDate(prevStartDate.getDate() - periodDays);
      const prevEndDate = new Date(startDate);

      const prevConversationsCount = Math.floor(conversationsCount * (0.85 + Math.random() * 0.3));
      const prevContactsCount = Math.floor(contactsCount * (0.9 + Math.random() * 0.2));
      const prevMessagesCount = Math.floor(messagesCount * (0.8 + Math.random() * 0.4));

      const conversationsGrowth = conversationsCount > 0 ?
        ((conversationsCount - prevConversationsCount) / prevConversationsCount * 100) : 0;
      const contactsGrowth = contactsCount > 0 ?
        ((contactsCount - prevContactsCount) / prevContactsCount * 100) : 0;
      const messagesGrowth = messagesCount > 0 ?
        ((messagesCount - prevMessagesCount) / prevMessagesCount * 100) : 0;

      const totalMessages = messagesCount;
      const responseRate = totalMessages > 0 ? Math.min(95, 85 + Math.random() * 10) : 0;
      const prevResponseRate = responseRate * (0.95 + Math.random() * 0.1);
      const responseRateGrowth = prevResponseRate > 0 ?
        ((responseRate - prevResponseRate) / prevResponseRate * 100) : 0;


      const channelConnections = await storage.getChannelConnectionsByCompany(user.companyId);
      const channelDistribution = channelConnections.reduce((acc: { [key: string]: number }, conn) => {
        const type = conn.channelType;
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {} as { [key: string]: number });

      const channelDistributionData = Object.entries(channelDistribution).map(([name, value]) => {
        const total = Object.values(channelDistribution).reduce((sum, val) => sum + val, 0);
        return {
          name,
          value,
          percentage: total > 0 ? Math.round((value / total) * 100) : 0
        };
      });

      const daysToFetch = Math.max(7, periodDays);

      const conversationsByDay = await storage.getConversationsByDayByCompany(user.companyId, daysToFetch);


      const messagesByChannel = await storage.getMessagesByChannelByCompany(user.companyId);

      res.json({
        overview: {
          conversationsCount,
          contactsCount,
          messagesCount,
          responseRate: Math.round(responseRate * 10) / 10,
          conversationsGrowth: Math.round(conversationsGrowth * 10) / 10,
          contactsGrowth: Math.round(contactsGrowth * 10) / 10,
          messagesGrowth: Math.round(messagesGrowth * 10) / 10,
          responseRateGrowth: Math.round(responseRateGrowth * 10) / 10
        },
        conversationsByDay,
        channelDistribution: channelDistributionData,
        messagesByChannel,
        period: {
          start: startDate.toISOString(),
          end: endDate.toISOString(),
          days: periodDays
        }
      });
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      res.status(500).json({ message: 'Failed to fetch analytics data' });
    }
  });

  app.get('/api/analytics/export', ensureAuthenticated, async (req: any, res) => {
    try {
      const { period = '7days', from, to } = req.query;
      const user = req.user as any;


      if (!user.companyId) {
        return res.status(400).json({ message: 'User must be associated with a company' });
      }

      let startDate = new Date();
      let endDate = new Date();

      if (from && to) {
        startDate = new Date(from);
        endDate = new Date(to);
      } else {
        switch (period) {
          case 'today':
            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'yesterday':
            startDate.setDate(startDate.getDate() - 1);
            startDate.setHours(0, 0, 0, 0);
            endDate.setDate(endDate.getDate() - 1);
            endDate.setHours(23, 59, 59, 999);
            break;
          case '7days':
            startDate.setDate(startDate.getDate() - 7);
            break;
          case '30days':
            startDate.setDate(startDate.getDate() - 30);
            break;
          case '90days':
            startDate.setDate(startDate.getDate() - 90);
            break;
          default:
            startDate.setDate(startDate.getDate() - 7);
        }
      }


      const conversationsCount = await storage.getConversationsCountByCompany(user.companyId);
      const contactsData = await storage.getContacts({ companyId: user.companyId });
      const contactsCount = contactsData.total;
      const messagesCount = await storage.getMessagesCountByCompany(user.companyId);
      const channelDistribution = await storage.getChannelConnectionsByCompany(user.companyId);
      const conversationsByDay = await storage.getConversationsByDayByCompany(user.companyId, 7);


      const analyticsData = {
        overview: {
          conversationsCount,
          contactsCount,
          messagesCount,
          responseRate: 92
        },
        conversationsByDay,
        channelDistribution: channelDistribution.map(conn => ({
          name: conn.channelType,
          value: 1,
          percentage: 25
        }))
      };

      const csvRows = [
        ['Metric', 'Value', 'Percentage'],
        ['Total Conversations', analyticsData.overview?.conversationsCount || 0, ''],
        ['Total Contacts', analyticsData.overview?.contactsCount || 0, ''],
        ['Total Messages', analyticsData.overview?.messagesCount || 0, ''],
        ['Response Rate (%)', analyticsData.overview?.responseRate || 0, ''],
        ['', '', ''],
        ['Channel Distribution', '', ''],
        ...(analyticsData.channelDistribution || []).map((channel: any) => [
          channel.name || 'Unknown',
          channel.value || 0,
          `${channel.percentage || 0}%`
        ]),
        ['', '', ''],
        ['Daily Conversations', '', ''],
        ['Date', 'WhatsApp Official', 'WhatsApp Unofficial', 'Messenger', 'Instagram', 'Total'],
        ...(analyticsData.conversationsByDay || []).map((day: any) => [
          day.name || day.date || 'Unknown',
          day.whatsapp_official || day.whatsappOfficial || 0,
          day.whatsapp_unofficial || day.whatsappUnofficial || 0,
          day.messenger || 0,
          day.instagram || 0,
          day.total || 0
        ])
      ];

      const csvContent = csvRows.map(row =>
        row.map((cell: any) => `"${cell}"`).join(',')
      ).join('\n');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="analytics-${new Date().toISOString().split('T')[0]}.csv"`);
      res.send(csvContent);

    } catch (error) {
      console.error('Error exporting analytics data:', error);
      res.status(500).json({ message: 'Failed to export analytics data' });
    }
  });

  app.get('/api/webhooks/instagram', async (req, res) => {
    const mode = req.query['hub.mode'];
    const token = req.query['hub.verify_token'];
    const challenge = req.query['hub.challenge'];

    // Enhanced logging for debugging
    console.log('Instagram webhook verification attempt:', {
      mode,
      token: token ? `${token.toString().substring(0, 8)}...` : 'undefined',
      challenge: challenge ? `${challenge.toString().substring(0, 8)}...` : 'undefined',
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });

    if (mode !== 'subscribe') {
      console.log('❌ Instagram webhook verification failed: Invalid mode');
      return res.status(403).send('Forbidden');
    }

    try {
      // Check database for Instagram connections with matching verify tokens
      const instagramConnections = await storage.getChannelConnectionsByType('instagram');

      let matchingConnection = null;
      for (const connection of instagramConnections) {
        const connectionData = connection.connectionData as any;
        if (connectionData?.verifyToken === token) {
          matchingConnection = connection;
          break;
        }
      }

      // Also check global environment variable as fallback
      const globalToken = process.env.INSTAGRAM_WEBHOOK_VERIFY_TOKEN;
      const isGlobalMatch = globalToken && token === globalToken;

      if (matchingConnection || isGlobalMatch) {
        console.log('✅ Instagram webhook verification successful:', {
          matchType: matchingConnection ? 'database_connection' : 'global_env',
          connectionId: matchingConnection?.id,
          accountName: matchingConnection?.accountName,
          companyId: matchingConnection?.companyId
        });
        res.status(200).send(challenge);
      } else {
        console.log('❌ Instagram webhook verification failed:', {
          receivedToken: token,
          globalToken: globalToken,
          checkedConnections: instagramConnections.length,
          availableTokens: instagramConnections.map(conn => {
            const data = conn.connectionData as any;
            return data?.verifyToken ? `${data.verifyToken.substring(0, 8)}...` : 'none';
          })
        });
        res.status(403).send('Forbidden');
      }
    } catch (error) {
      console.error('Error during Instagram webhook verification:', error);
      res.status(500).send('Internal Server Error');
    }
  });

  app.post('/api/webhooks/instagram', async (req, res) => {
    try {
      const signature = req.headers['x-hub-signature-256'] as string;
      const body = req.body;

      console.log('Instagram webhook received:', {
        hasSignature: !!signature,
        bodyType: typeof body,
        contentType: req.get('content-type'),
        headers: {
          'x-hub-signature-256': signature ? 'present' : 'missing',
          'user-agent': req.get('user-agent')
        }
      });

      // Extract Instagram account ID from webhook payload for company scoping
      let targetConnection = null;
      if (body?.entry && Array.isArray(body.entry) && body.entry.length > 0) {
        const instagramAccountId = body.entry[0]?.id;
        if (instagramAccountId) {
          const instagramConnections = await storage.getChannelConnectionsByType('instagram');
          targetConnection = instagramConnections.find((conn: any) => {
            const connectionData = conn.connectionData as any;
            return connectionData?.instagramAccountId === instagramAccountId;
          });

          console.log('Found target connection for Instagram account ID:', {
            instagramAccountId,
            connectionId: targetConnection?.id,
            companyId: targetConnection?.companyId
          });
        }
      }

      // Process webhook with company context
      await instagramService.processWebhook(body, signature, targetConnection?.companyId || undefined);

      res.status(200).send('OK');
    } catch (error) {
      console.error('Error processing Instagram webhook:', error);
      res.status(500).send('Internal Server Error');
    }
  });


  app.post('/api/instagram/test-webhook', ensureAuthenticated, async (req: any, res) => {
    try {
      const { webhookUrl, verifyToken } = req.body;

      if (!webhookUrl || !verifyToken) {
        return res.status(400).json({
          success: false,
          message: 'Webhook URL and verify token are required'
        });
      }

      const testResult = await instagramService.testWebhookConfiguration(webhookUrl, verifyToken);

      if (testResult.success) {
        res.json({
          success: true,
          message: 'Webhook configuration is valid'
        });
      } else {
        res.status(400).json({
          success: false,
          message: testResult.error || 'Webhook test failed'
        });
      }
    } catch (error: any) {
      console.error('Error testing Instagram webhook:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Internal server error'
      });
    }
  });


  app.get('/api/instagram/health/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);


      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      if (connection.userId !== req.user.id) {
        return res.status(403).json({ message: 'Unauthorized access to connection' });
      }

      const health = instagramService.getConnectionHealth(connectionId);

      res.json({
        connectionId,
        channelType: 'instagram',
        ...health,
        status: connection.status
      });
    } catch (error: any) {
      console.error('Error getting Instagram connection health:', error);
      res.status(500).json({
        message: error.message || 'Internal server error'
      });
    }
  });


  app.post('/api/webhooks/telegram', async (req, res) => {
    try {
      const signature = req.headers['x-telegram-bot-api-secret-token'] as string;

      await telegramService.processWebhook(req.body, signature);

      res.status(200).send('OK');
    } catch (error) {
      console.error('Error processing Telegram webhook:', error);
      res.status(500).send('Internal Server Error');
    }
  });


  app.post('/api/telegram/test-webhook', ensureAuthenticated, async (req: any, res) => {
    try {
      const { webhookUrl, verifyToken } = req.body;

      if (!webhookUrl || !verifyToken) {
        return res.status(400).json({
          success: false,
          message: 'Webhook URL and verify token are required'
        });
      }

      const testResult = await telegramService.testWebhookConfiguration(webhookUrl, verifyToken);

      if (testResult.success) {
        res.json({
          success: true,
          message: 'Webhook configuration is valid'
        });
      } else {
        res.status(400).json({
          success: false,
          message: testResult.error || 'Webhook test failed'
        });
      }
    } catch (error: any) {
      console.error('Error testing Telegram webhook:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Internal server error'
      });
    }
  });


  app.post('/api/telegram/generate-qr', ensureAuthenticated, async (req: any, res) => {
    try {
      const { connectionId } = req.body;

      if (!connectionId) {
        return res.status(400).json({
          success: false,
          message: 'Connection ID is required'
        });
      }

      const qrResult = await telegramService.generateQRCode(connectionId, req.user.id);

      res.json({
        success: true,
        qrCode: qrResult.qrCode,
        loginToken: qrResult.loginToken
      });
    } catch (error: any) {
      console.error('Error generating Telegram QR code:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Internal server error'
      });
    }
  });


  app.get('/api/telegram/check-auth/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);

      const authResult = await telegramService.checkQRAuthStatus(connectionId, req.user.id);

      res.json({
        authenticated: authResult.authenticated,
        sessionString: authResult.sessionString
      });
    } catch (error: any) {
      console.error('Error checking Telegram auth status:', error);
      res.status(500).json({
        authenticated: false,
        message: error.message || 'Internal server error'
      });
    }
  });


  app.get('/api/telegram/health/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);


      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      if (connection.userId !== req.user.id) {
        return res.status(403).json({ message: 'Unauthorized access to connection' });
      }

      const health = telegramService.getConnectionHealth(connectionId);

      res.json({
        connectionId,
        channelType: 'telegram',
        ...health,
        status: connection.status
      });
    } catch (error: any) {
      console.error('Error getting Telegram connection health:', error);
      res.status(500).json({
        message: error.message || 'Internal server error'
      });
    }
  });

  app.get('/api/webhooks/messenger', async (req, res) => {
    const mode = req.query['hub.mode'];
    const token = req.query['hub.verify_token'];
    const challenge = req.query['hub.challenge'];

    // Enhanced logging for debugging
    console.log('Messenger webhook verification attempt:', {
      mode,
      token: token ? `${token.toString().substring(0, 8)}...` : 'undefined',
      challenge: challenge ? `${challenge.toString().substring(0, 8)}...` : 'undefined',
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });

    if (mode !== 'subscribe') {
      console.log('❌ Messenger webhook verification failed: Invalid mode');
      return res.status(403).send('Forbidden');
    }

    try {
      // Check database for Messenger connections with matching verify tokens
      const messengerConnections = await storage.getChannelConnectionsByType('messenger');

      let matchingConnection = null;
      for (const connection of messengerConnections) {
        const connectionData = connection.connectionData as any;
        if (connectionData?.verifyToken === token) {
          matchingConnection = connection;
          break;
        }
      }

      // Also check global environment variable as fallback
      const globalToken = process.env.MESSENGER_WEBHOOK_VERIFY_TOKEN;
      const isGlobalMatch = globalToken && token === globalToken;

      if (matchingConnection || isGlobalMatch) {
        console.log('✅ Messenger webhook verification successful:', {
          matchType: matchingConnection ? 'database_connection' : 'global_env',
          connectionId: matchingConnection?.id,
          accountName: matchingConnection?.accountName,
          companyId: matchingConnection?.companyId
        });
        res.status(200).send(challenge);
      } else {
        console.log('❌ Messenger webhook verification failed:', {
          receivedToken: token,
          globalToken: globalToken,
          checkedConnections: messengerConnections.length,
          availableTokens: messengerConnections.map(conn => {
            const data = conn.connectionData as any;
            return data?.verifyToken ? `${data.verifyToken.substring(0, 8)}...` : 'none';
          })
        });
        res.status(403).send('Forbidden');
      }
    } catch (error) {
      console.error('Error during Messenger webhook verification:', error);
      res.status(500).send('Internal Server Error');
    }
  });

  app.post('/api/webhooks/messenger', async (req, res) => {
    try {
      const signature = req.headers['x-hub-signature-256'] as string;
      const body = req.body;

      console.log('Messenger webhook received:', {
        hasSignature: !!signature,
        bodyType: typeof body,
        contentType: req.get('content-type'),
        headers: {
          'x-hub-signature-256': signature ? 'present' : 'missing',
          'user-agent': req.get('user-agent')
        }
      });

      // Extract page ID from webhook payload for company scoping
      let targetConnection = null;
      if (body?.entry && Array.isArray(body.entry) && body.entry.length > 0) {
        const pageId = body.entry[0]?.id;
        if (pageId) {
          const messengerConnections = await storage.getChannelConnectionsByType('messenger');
          targetConnection = messengerConnections.find((conn: any) => {
            const connectionData = conn.connectionData as any;
            return connectionData?.pageId === pageId;
          });

          console.log('Found target connection for page ID:', {
            pageId,
            connectionId: targetConnection?.id,
            companyId: targetConnection?.companyId
          });
        }
      }

      // Process webhook with company context
      await messengerService.processWebhook(body, signature, targetConnection?.companyId || undefined);

      res.status(200).send('OK');
    } catch (error) {
      console.error('Error processing Messenger webhook:', error);
      res.status(500).send('Internal Server Error');
    }
  });


  app.post('/api/messenger/test-webhook', ensureAuthenticated, async (req: any, res) => {
    try {
      const { webhookUrl, verifyToken } = req.body;

      if (!webhookUrl || !verifyToken) {
        return res.status(400).json({
          success: false,
          message: 'Webhook URL and verify token are required'
        });
      }

      const testResult = await messengerService.testWebhookConfiguration(webhookUrl, verifyToken);

      if (testResult.success) {
        res.json({
          success: true,
          message: 'Webhook configuration is valid'
        });
      } else {
        res.status(400).json({
          success: false,
          message: testResult.error || 'Webhook test failed'
        });
      }
    } catch (error: any) {
      console.error('Error testing Messenger webhook:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Internal server error'
      });
    }
  });

  // Test WhatsApp webhook configuration
  app.post('/api/whatsapp/test-webhook', ensureAuthenticated, async (req: any, res) => {
    try {
      const { webhookUrl, verifyToken } = req.body;

      if (!webhookUrl || !verifyToken) {
        return res.status(400).json({
          success: false,
          message: 'Webhook URL and verify token are required'
        });
      }

      const testResult = await whatsAppOfficialService.testWebhookConfiguration(webhookUrl, verifyToken);

      if (testResult.success) {
        res.json({
          success: true,
          message: 'WhatsApp webhook configuration is valid'
        });
      } else {
        res.status(400).json({
          success: false,
          message: testResult.error || 'WhatsApp webhook test failed'
        });
      }
    } catch (error: any) {
      console.error('Error testing WhatsApp webhook:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Internal server error'
      });
    }
  });

  // Get WhatsApp webhook configuration
  app.get('/api/whatsapp/webhook-config', ensureAuthenticated, async (req: any, res) => {
    try {
      const currentToken = process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN || 'default_verify_token';

      // Generate dynamic webhook URL from request headers
      const protocol = req.get('x-forwarded-proto') || req.protocol || 'http';
      const host = req.get('host') || 'localhost:9000';
      const origin = req.get('origin') || `${protocol}://${host}`;
      const baseUrl = process.env.BASE_URL || origin;
      const webhookUrl = `${baseUrl}/api/webhooks/whatsapp`;

      res.json({
        success: true,
        webhookUrl,
        verifyToken: currentToken,
        message: 'Current WhatsApp webhook configuration'
      });
    } catch (error: any) {
      console.error('Error getting WhatsApp webhook config:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Internal server error'
      });
    }
  });


  app.get('/api/messenger/health/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);


      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      if (connection.userId !== req.user.id) {
        return res.status(403).json({ message: 'Unauthorized access to connection' });
      }

      const health = messengerService.getConnectionHealth(connectionId);

      res.json({
        connectionId,
        channelType: 'messenger',
        ...health,
        status: connection.status
      });
    } catch (error: any) {
      console.error('Error getting Messenger connection health:', error);
      res.status(500).json({
        message: error.message || 'Internal server error'
      });
    }
  });

  app.post('/api/instagram/connect/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const connection = await storage.getChannelConnection(connectionId);

      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      // Multi-tenant security: Verify company scoping
      if (req.user.companyId && connection.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Access denied: Connection does not belong to your company' });
      }

      await storage.updateChannelConnectionStatus(connectionId, 'connecting');

      try {
        await instagramService.connect(connectionId, req.user.id, req.user.companyId);

      } catch (err) {
        console.error('Error connecting to Instagram:', err);
        await storage.updateChannelConnectionStatus(connectionId, 'error');
      }

      res.json({ message: 'Instagram connection initiated' });
    } catch (err: any) {
      console.error('Error in Instagram connect endpoint:', err);
      res.status(400).json({ message: err.message });
    }
  });

  app.post('/api/messenger/connect/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const connection = await storage.getChannelConnection(connectionId);

      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      // Multi-tenant security: Verify company scoping
      if (req.user.companyId && connection.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Access denied: Connection does not belong to your company' });
      }

      await storage.updateChannelConnectionStatus(connectionId, 'connecting');

      try {
        await messengerService.connect(connectionId, req.user.id, req.user.companyId);

      } catch (err) {
        console.error('Error connecting to Messenger:', err);
        await storage.updateChannelConnectionStatus(connectionId, 'error');
      }

      res.json({ message: 'Messenger connection initiated' });
    } catch (err: any) {
      console.error('Error in Messenger connect endpoint:', err);
      res.status(400).json({ message: err.message });
    }
  });

  app.post('/api/instagram/send/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const { to, message } = req.body;

      if (!to || !message) {
        return res.status(400).json({ message: 'Missing required parameters' });
      }

      const connection = await storage.getChannelConnection(connectionId);

      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      

      let messageContent = message;
      try {
        const user = await storage.getUser(req.user.id);
        if (user && user.fullName) {
          messageContent = `> *${user.fullName}*\n\n${message}`;
        }
      } catch (userError) {
        console.error('Error fetching user for signature in Instagram send:', userError);
      }

      const result = await instagramService.sendMessage(connectionId, to, messageContent);

      if (result.success) {
        res.status(200).json({ success: true, messageId: result.messageId });
      } else {
        res.status(500).json({ message: result.error || 'Failed to send Instagram message' });
      }
    } catch (err: any) {
      console.error('Error sending Instagram message:', err);
      res.status(400).json({ message: err.message });
    }
  });

  // Send Instagram media message
  app.post('/api/instagram/send-media/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const { to, mediaUrl, mediaType, caption } = req.body;

      if (!to || !mediaUrl || !mediaType) {
        return res.status(400).json({ message: 'Recipient, media URL, and media type are required' });
      }

      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      if (connection.userId !== req.user.id) {
        return res.status(403).json({ message: 'Unauthorized access to connection' });
      }

      const result = await instagramService.sendMedia(connectionId, to, mediaUrl, mediaType, caption, req.user.id);

      if (result.success) {
        res.status(200).json({ success: true, messageId: result.messageId });
      } else {
        res.status(500).json({ message: result.error || 'Failed to send Instagram media message' });
      }
    } catch (err: any) {
      console.error('Error sending Instagram media message:', err);
      res.status(400).json({ message: err.message });
    }
  });

  // Send Instagram message with quick replies
  app.post('/api/instagram/send-quick-replies/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const { to, message, quickReplies } = req.body;

      if (!to || !message || !quickReplies || !Array.isArray(quickReplies)) {
        return res.status(400).json({ message: 'Recipient, message, and quick replies array are required' });
      }

      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      if (connection.userId !== req.user.id) {
        return res.status(403).json({ message: 'Unauthorized access to connection' });
      }

      const result = await instagramService.sendMessageWithQuickReplies(connectionId, to, message, quickReplies, req.user.id);

      if (result.success) {
        res.status(200).json({ success: true, messageId: result.messageId });
      } else {
        res.status(500).json({ message: result.error || 'Failed to send Instagram message with quick replies' });
      }
    } catch (err: any) {
      console.error('Error sending Instagram message with quick replies:', err);
      res.status(400).json({ message: err.message });
    }
  });

  // Get Instagram message templates
  app.get('/api/instagram/templates/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);

      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      if (connection.userId !== req.user.id) {
        return res.status(403).json({ message: 'Unauthorized access to connection' });
      }

      const templates = await instagramService.getMessageTemplates(connectionId);
      res.status(200).json({ templates });
    } catch (err: any) {
      console.error('Error getting Instagram templates:', err);
      res.status(400).json({ message: err.message });
    }
  });

  // Save Instagram message template
  app.post('/api/instagram/templates/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const template = req.body;

      if (!template.name || !template.content) {
        return res.status(400).json({ message: 'Template name and content are required' });
      }

      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      if (connection.userId !== req.user.id) {
        return res.status(403).json({ message: 'Unauthorized access to connection' });
      }

      // Generate ID if not provided
      if (!template.id) {
        template.id = `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      }

      const result = await instagramService.saveMessageTemplate(connectionId, template);

      if (result.success) {
        res.status(200).json({ success: true, template });
      } else {
        res.status(500).json({ message: result.error || 'Failed to save template' });
      }
    } catch (err: any) {
      console.error('Error saving Instagram template:', err);
      res.status(400).json({ message: err.message });
    }
  });

  // Refresh Instagram access token
  app.post('/api/instagram/refresh-token/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);

      const connection = await storage.getChannelConnection(connectionId);
      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      if (connection.userId !== req.user.id) {
        return res.status(403).json({ message: 'Unauthorized access to connection' });
      }

      const result = await instagramService.refreshAccessToken(connectionId);

      if (result.success) {
        res.status(200).json({
          success: true,
          expiresAt: result.expiresAt,
          message: 'Access token refreshed successfully'
        });
      } else {
        res.status(500).json({ message: result.error || 'Failed to refresh access token' });
      }
    } catch (err: any) {
      console.error('Error refreshing Instagram access token:', err);
      res.status(400).json({ message: err.message });
    }
  });

  app.post('/api/messenger/send/:connectionId', ensureAuthenticated, async (req: any, res) => {
    try {
      const connectionId = parseInt(req.params.connectionId);
      const { to, message } = req.body;

      if (!to || !message) {
        return res.status(400).json({ message: 'Missing required parameters' });
      }

      const connection = await storage.getChannelConnection(connectionId);

      if (!connection) {
        return res.status(404).json({ message: 'Connection not found' });
      }

      

      let messageContent = message;
      try {
        const user = await storage.getUser(req.user.id);
        if (user && user.fullName) {
          messageContent = `> *${user.fullName}*\n\n${message}`;
        }
      } catch (userError) {
        console.error('Error fetching user for signature in Messenger send:', userError);
      }

      const result = await messengerService.sendMessage(connectionId, to, messageContent);

      if (result.success) {
        res.status(200).json({ success: true, messageId: result.messageId });
      } else {
        res.status(500).json({ message: result.error || 'Failed to send Messenger message' });
      }
    } catch (err: any) {
      console.error('Error sending Messenger message:', err);
      res.status(400).json({ message: err.message });
    }
  });

  const mediaDir = path.join(process.cwd(), 'public', 'media');
  app.use('/media', (req, res, next) => {

    res.setHeader('X-Robots-Tag', 'noindex, nofollow, noarchive, nosnippet, noimageindex');
    res.setHeader('Cache-Control', 'private, no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');


    express.static(mediaDir)(req, res, next);
  });

  const emailAttachmentsDir = path.join(process.cwd(), 'public', 'email-attachments');
  app.use('/email-attachments', (req, res, next) => {

    res.setHeader('X-Robots-Tag', 'noindex, nofollow, noarchive, nosnippet, noimageindex');
    res.setHeader('Cache-Control', 'private, no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');


    express.static(emailAttachmentsDir)(req, res, next);
  });


  app.get('/robots.txt', (req, res) => {
    res.type('text/plain');
    res.send(`User-agent: *
Disallow: /media/
Disallow: /email-attachments/
Disallow: /uploads/
Disallow: /api/
Allow: /

# PowerChatPlus - Media files are private and should not be indexed
Crawl-delay: 10`);
  });

  app.use('/media/flow-media', express.static(FLOW_MEDIA_DIR));


  app.use('/uploads/pages', express.static(path.join(process.cwd(), 'uploads', 'pages')));


  app.get('/:slug', async (req, res, next) => {
    try {
      const slug = req.params.slug;


      if (slug.startsWith('api') || slug.includes('.')) {
        return next();
      }


      const frontendRoutes = [
        'auth', 'login', 'register', 'dashboard', 'admin', 'settings',
        'profile', 'logout', 'inbox', 'flows', 'contacts', 'calendar',
        'analytics', 'campaigns', 'pipeline', 'pages', 'users', 'billing',
        'integrations', 'reports', 'templates', 'webhooks'
      ];

      if (frontendRoutes.includes(slug)) {

        return next();
      }

      // Check if this slug matches a published website
      console.log(`[Website Route] Checking slug: ${slug}`);
      try {
        const website = await storage.getWebsiteBySlug(slug);
        console.log(`[Website Route] Found website:`, website ? { id: website.id, title: website.title, slug: website.slug, status: website.status } : 'null');

        if (website && website.status === 'published') {
          console.log(`[Website Route] Serving published website: ${website.title}`);
          // Serve the published website
          const html = `
            <!DOCTYPE html>
            <html lang="en">
              <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <title>${website.metaTitle || website.title}</title>
                ${website.metaDescription ? `<meta name="description" content="${website.metaDescription}">` : ''}
                ${website.metaKeywords ? `<meta name="keywords" content="${website.metaKeywords}">` : ''}
                ${website.favicon ? `<link rel="icon" href="${website.favicon}">` : ''}
                ${website.googleAnalyticsId ? `
                  <!-- Google Analytics -->
                  <script async src="https://www.googletagmanager.com/gtag/js?id=${website.googleAnalyticsId}"></script>
                  <script>
                    window.dataLayer = window.dataLayer || [];
                    function gtag(){dataLayer.push(arguments);}
                    gtag('js', new Date());
                    gtag('config', '${website.googleAnalyticsId}');
                  </script>
                ` : ''}
                ${website.facebookPixelId ? `
                  <!-- Facebook Pixel -->
                  <script>
                    !function(f,b,e,v,n,t,s)
                    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
                    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
                    n.queue=[];t=b.createElement(e);t.async=!0;
                    t.src=v;s=b.getElementsByTagName(e)[0];
                    s.parentNode.insertBefore(t,s)}(window, document,'script',
                    'https://connect.facebook.net/en_US/fbevents.js');
                    fbq('init', '${website.facebookPixelId}');
                    fbq('track', 'PageView');
                  </script>
                  <noscript><img height="1" width="1" style="display:none"
                    src="https://www.facebook.com/tr?id=${website.facebookPixelId}&ev=PageView&noscript=1"
                  /></noscript>
                ` : ''}
                <style>
                  ${website.grapesCss || ''}
                  ${website.customCss || ''}
                </style>
                ${website.customHead || ''}
              </head>
              <body>
                ${website.grapesHtml || ''}
                <script>
                  ${website.grapesJs || ''}
                  ${website.customJs || ''}
                </script>
              </body>
            </html>
          `;

          res.set('Cache-Control', 'public, max-age=300');
          return res.send(html);
        } else {
          console.log(`[Website Route] Website not found or not published for slug: ${slug}`);
        }
      } catch (websiteError) {
        console.error('Error checking for website by slug:', websiteError);
        // Continue to subdomain logic if website lookup fails
      }


      const hostname = req.get('host') || '';
      const cleanHostname = hostname.split(':')[0];
      const parts = cleanHostname.split('.');


      const isSubdomainRequest = (req.subdomain && req.subdomainCompany) ||
        (parts.length === 2 && parts[1] === 'localhost' && parts[0] !== 'localhost');

      if (!isSubdomainRequest) {

        return next();
      }


      let companyId: number | null = null;

      if (req.subdomain && req.subdomainCompany) {
        companyId = req.subdomainCompany.id;
      } else {

        if (parts.length === 2 && parts[1] === 'localhost') {
          const subdomain = parts[0];
          if (subdomain && subdomain !== 'localhost') {
            const company = await storage.getCompanyBySlug(subdomain);
            if (company) {
              companyId = company.id;
            }
          }
        }
      }

      if (!companyId) {
        return res.status(404).json({ message: 'Company not found' });
      }


      const page = await storage.getCompanyPageBySlug(companyId, slug);

      if (!page || !page.isPublished) {
        return res.status(404).json({ message: 'Page not found' });
      }


      const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${page.metaTitle || page.title}</title>
    ${page.metaDescription ? `<meta name="description" content="${page.metaDescription}">` : ''}
    ${page.metaKeywords ? `<meta name="keywords" content="${page.metaKeywords}">` : ''}
    <meta name="robots" content="index, follow">
    <meta property="og:title" content="${page.metaTitle || page.title}">
    ${page.metaDescription ? `<meta property="og:description" content="${page.metaDescription}">` : ''}
    <meta property="og:type" content="website">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #fff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .page-content {
            max-width: 800px;
            margin: 0 auto;
        }
        h1, h2, h3, h4, h5, h6 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        h1 {
            font-size: 2.5rem;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 1rem;
        }
        h2 { font-size: 2rem; }
        h3 { font-size: 1.5rem; }
        h4 { font-size: 1.25rem; }
        p {
            margin-bottom: 1rem;
        }
        ul, ol {
            margin-bottom: 1rem;
            padding-left: 2rem;
        }
        li {
            margin-bottom: 0.5rem;
        }
        a {
            color: #3b82f6;
            text-decoration: underline;
        }
        a:hover {
            color: #1d4ed8;
        }
        blockquote {
            border-left: 4px solid #e5e7eb;
            padding-left: 1rem;
            margin: 1rem 0;
            font-style: italic;
            color: #6b7280;
        }
        pre {
            background-color: #f3f4f6;
            padding: 1rem;
            border-radius: 0.375rem;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
        img {
            max-width: 100%;
            height: auto;
            border-radius: 0.375rem;
        }
        .footer {
            margin-top: 4rem;
            padding-top: 2rem;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            color: #6b7280;
            font-size: 0.875rem;
        }
        ${page.customCss || ''}
    </style>
</head>
<body>
    <div class="container">
        <div class="page-content">
            ${page.content}
        </div>
    </div>
    ${page.customJs ? `<script>${page.customJs}</script>` : ''}
</body>
</html>`;

      res.setHeader('Content-Type', 'text/html');
      res.send(html);
    } catch (error) {
      console.error('Error serving public page:', error);

      next();
    }
  });


  app.get('/api/subdomain-info', async (req, res) => {
    try {
      const host = req.headers.host || '';


      if (!host.includes('.localhost') || host.startsWith('localhost:')) {
        return res.status(404).json({
          error: 'NOT_SUBDOMAIN',
          message: 'This endpoint is only available on subdomains'
        });
      }


      const subdomain = host.split('.')[0];


      const company = await storage.getCompanyBySlug(subdomain);

      if (!company) {
        return res.status(404).json({
          error: 'COMPANY_NOT_FOUND',
          message: 'Company not found for this subdomain'
        });
      }


      res.json({
        company: {
          id: company.id,
          name: company.name,
          slug: company.slug,
          logo: company.logo,
          primaryColor: company.primaryColor,
          active: company.active
        },
        subdomain
      });
    } catch (error) {
      console.error('Error getting subdomain info:', error);
      res.status(500).json({
        error: 'INTERNAL_ERROR',
        message: 'Failed to get subdomain information'
      });
    }
  });


  app.get('/api/company-pages', ensureAuthenticated, requireAnyPermission([PERMISSIONS.VIEW_PAGES, PERMISSIONS.MANAGE_PAGES]), async (req, res) => {
    try {
      const user = req.user as any;
      const companyId = user.companyId;

      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      const published = req.query.published === 'true' ? true : req.query.published === 'false' ? false : undefined;
      const featured = req.query.featured === 'true' ? true : req.query.featured === 'false' ? false : undefined;

      const pages = await storage.getCompanyPages(companyId, { published, featured });
      res.json(pages);
    } catch (error) {
      console.error('Error fetching company pages:', error);
      res.status(500).json({ message: 'Failed to fetch company pages' });
    }
  });

  app.get('/api/company-pages/:id', ensureAuthenticated, requireAnyPermission([PERMISSIONS.VIEW_PAGES, PERMISSIONS.MANAGE_PAGES]), async (req, res) => {
    try {
      const user = req.user as any;
      const pageId = parseInt(req.params.id);

      const page = await storage.getCompanyPage(pageId);

      if (!page) {
        return res.status(404).json({ message: 'Page not found' });
      }


      if (!user.isSuperAdmin && page.companyId !== user.companyId) {
        return res.status(403).json({ message: 'Access denied' });
      }

      res.json(page);
    } catch (error) {
      console.error('Error fetching company page:', error);
      res.status(500).json({ message: 'Failed to fetch company page' });
    }
  });

  app.post('/api/company-pages', ensureAuthenticated, requirePermission(PERMISSIONS.MANAGE_PAGES), async (req, res) => {
    try {
      const user = req.user as any;
      const companyId = user.companyId;

      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      const pageData = validateBody(insertCompanyPageSchema, {
        ...req.body,
        companyId,
        authorId: user.id
      });


      const existingPage = await storage.getCompanyPageBySlug(companyId, pageData.slug);
      if (existingPage) {
        return res.status(400).json({ message: 'A page with this slug already exists' });
      }

      const page = await storage.createCompanyPage(pageData);
      res.status(201).json(page);
    } catch (error) {
      console.error('Error creating company page:', error);
      res.status(500).json({ message: 'Failed to create company page' });
    }
  });

  app.put('/api/company-pages/:id', ensureAuthenticated, requirePermission(PERMISSIONS.MANAGE_PAGES), async (req, res) => {
    try {
      const user = req.user as any;
      const pageId = parseInt(req.params.id);

      const existingPage = await storage.getCompanyPage(pageId);

      if (!existingPage) {
        return res.status(404).json({ message: 'Page not found' });
      }


      if (!user.isSuperAdmin && existingPage.companyId !== user.companyId) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const updateData = {
        ...req.body,
        authorId: user.id
      };


      if (updateData.slug && updateData.slug !== existingPage.slug) {
        const conflictingPage = await storage.getCompanyPageBySlug(existingPage.companyId, updateData.slug);
        if (conflictingPage && conflictingPage.id !== pageId) {
          return res.status(400).json({ message: 'A page with this slug already exists' });
        }
      }

      const page = await storage.updateCompanyPage(pageId, updateData);
      res.json(page);
    } catch (error) {
      console.error('Error updating company page:', error);
      res.status(500).json({ message: 'Failed to update company page' });
    }
  });

  app.delete('/api/company-pages/:id', ensureAuthenticated, requirePermission(PERMISSIONS.MANAGE_PAGES), async (req, res) => {
    try {
      const user = req.user as any;
      const pageId = parseInt(req.params.id);

      const existingPage = await storage.getCompanyPage(pageId);

      if (!existingPage) {
        return res.status(404).json({ message: 'Page not found' });
      }


      if (!user.isSuperAdmin && existingPage.companyId !== user.companyId) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const success = await storage.deleteCompanyPage(pageId);

      if (success) {
        res.json({ message: 'Page deleted successfully' });
      } else {
        res.status(500).json({ message: 'Failed to delete page' });
      }
    } catch (error) {
      console.error('Error deleting company page:', error);
      res.status(500).json({ message: 'Failed to delete company page' });
    }
  });

  app.post('/api/company-pages/:id/publish', ensureAuthenticated, requirePermission(PERMISSIONS.MANAGE_PAGES), async (req, res) => {
    try {
      const user = req.user as any;
      const pageId = parseInt(req.params.id);

      const existingPage = await storage.getCompanyPage(pageId);

      if (!existingPage) {
        return res.status(404).json({ message: 'Page not found' });
      }


      if (!user.isSuperAdmin && existingPage.companyId !== user.companyId) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const page = await storage.publishCompanyPage(pageId);
      res.json(page);
    } catch (error) {
      console.error('Error publishing company page:', error);
      res.status(500).json({ message: 'Failed to publish company page' });
    }
  });

  app.post('/api/company-pages/:id/unpublish', ensureAuthenticated, requirePermission(PERMISSIONS.MANAGE_PAGES), async (req, res) => {
    try {
      const user = req.user as any;
      const pageId = parseInt(req.params.id);

      const existingPage = await storage.getCompanyPage(pageId);

      if (!existingPage) {
        return res.status(404).json({ message: 'Page not found' });
      }


      if (!user.isSuperAdmin && existingPage.companyId !== user.companyId) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const page = await storage.unpublishCompanyPage(pageId);
      res.json(page);
    } catch (error) {
      console.error('Error unpublishing company page:', error);
      res.status(500).json({ message: 'Failed to unpublish company page' });
    }
  });


  app.post('/api/company-pages/upload-media', ensureAuthenticated, requirePermission(PERMISSIONS.MANAGE_PAGES), (req, res, next) => {
    const pagesMediaUpload = multer({
      storage: multer.diskStorage({
        destination: function (req, file, cb) {
          const uploadDir = path.join(process.cwd(), 'uploads', 'pages');
          fsExtra.ensureDirSync(uploadDir);
          cb(null, uploadDir);
        },
        filename: function (req, file, cb) {
          const uniqueId = crypto.randomBytes(16).toString('hex');
          const fileExt = path.extname(file.originalname) || '';
          cb(null, `${uniqueId}${fileExt}`);
        }
      }),
      fileFilter: (req, file, cb) => {
        const allowedTypes = [
          'image/jpeg', 'image/png', 'image/gif', 'image/webp'
        ];
        if (allowedTypes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          const error = new Error('Only image files are allowed');
          cb(error);
        }
      },
      limits: { fileSize: 10 * 1024 * 1024 } // 10MB limit
    });

    pagesMediaUpload.single('file')(req, res, async (err) => {
      if (err) {
        console.error('Pages media upload error:', err);
        return res.status(400).json({
          success: false,
          error: 'UPLOAD_ERROR',
          message: err.message || 'Failed to upload file'
        });
      }

      try {
        if (!req.file) {
          return res.status(400).json({
            success: false,
            error: 'NO_FILE_PROVIDED',
            message: 'No file was uploaded'
          });
        }

        const publicUrl = `${req.protocol}://${req.get('host')}/uploads/pages/${path.basename(req.file.path)}`;

        res.json({
          success: true,
          data: {
            url: publicUrl,
            filename: req.file.originalname,
            size: req.file.size,
            mimetype: req.file.mimetype
          }
        });
      } catch (error: any) {
        console.error('Error processing pages media upload:', error);

        if (req.file && req.file.path) {
          fsExtra.unlink(req.file.path).catch(console.error);
        }

        res.status(500).json({
          success: false,
          error: 'PROCESSING_ERROR',
          message: error.message || 'Failed to process uploaded file'
        });
      }
    });
  });

  return httpServer;
}
