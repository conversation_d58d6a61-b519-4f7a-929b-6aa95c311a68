import {
  users, type User, type InsertUser,
  contacts, type Contact, type InsertContact,
  conversations, type Conversation, type InsertConversation,
  groupParticipants, type GroupParticipant, type InsertGroupParticipant,
  messages, type Message, type InsertMessage,
  notes, type Note, type InsertNote,
  channelConnections, type ChannelConnection, type InsertChannelConnection,
  partnerConfigurations, type PartnerConfiguration, type InsertPartnerConfiguration,
  dialog360Clients, type Dialog360Client, type InsertDialog360Client,
  dialog360Channels, type Dialog360Channel, type InsertDialog360Channel,
  metaWhatsappClients, type MetaWhatsappClient, type InsertMetaWhatsappClient,
  metaWhatsappPhoneNumbers, type MetaWhatsappPhoneNumber, type InsertMetaWhatsappPhoneNumber,
  apiKeys, type ApiKey, type InsertApiKey,
  apiUsage, type ApiUsage, type InsertApiUsage,
  apiRateLimits, type ApiRateLimit, type InsertApiRateLimit,
  flows, type Flow, type InsertFlow,
  flowAssignments, type FlowAssignment, type InsertFlowAssignment,
  flowExecutions, flowStepExecutions,
  flowSessions, flowSessionVariables, flowSessionCursors,
  followUpSchedules, followUpTemplates, followUpExecutionLog,
  googleCalendarTokens,
  teamInvitations, type TeamInvitation, type InsertTeamInvitation,
  deals, type Deal, type InsertDeal,
  dealActivities, type DealActivity, type InsertDealActivity,
  pipelineStages, type PipelineStage, type InsertPipelineStage,
  companies, type Company, type InsertCompany,
  rolePermissions,
  companyPages, type CompanyPage, type InsertCompanyPage,
  plans,
  appSettings,
  companySettings,
  paymentTransactions,
  languages,
  translationNamespaces,
  translationKeys,
  translations,
  systemUpdates, type SystemUpdate, type InsertSystemUpdate,

  userSocialAccounts, type UserSocialAccount, type InsertUserSocialAccount, type SocialProvider,
  emailConfigs, type EmailConfig, type InsertEmailConfig,
  emailAttachments, type EmailAttachment, type InsertEmailAttachment,
  emailTemplates, type EmailTemplate, type InsertEmailTemplate,
  emailSignatures, type EmailSignature, type InsertEmailSignature,

  affiliateApplications,
  affiliates,
  affiliateCommissionStructures,
  affiliateReferrals,
  affiliatePayouts,
  affiliateAnalytics,
  affiliateClicks,
  affiliateRelationships,

  websites, type Website, type InsertWebsite,
  websiteAssets, type WebsiteAsset, type InsertWebsiteAsset,

  type DealStatus, type DealPriority,
  type CompanySetting} from "@shared/schema";

import session from "express-session";
import { eq, and, desc, or, sql, count, isNull, gt, gte, lt, lte, inArray, ne } from "drizzle-orm";
import { filterGroupChatsFromConversations, isWhatsAppGroupChatId } from "./utils/whatsapp-group-filter";

export interface Plan {
  id: number;
  name: string;
  description: string | null;
  price: string;
  maxUsers: number;
  maxContacts: number;
  maxChannels: number;
  maxFlows: number;
  maxCampaigns: number;
  maxCampaignRecipients: number;
  campaignFeatures: string[];
  isActive: boolean;
  isFree: boolean;
  hasTrialPeriod: boolean;
  trialDays: number;
  features: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface InsertPlan {
  name: string;
  description: string | null;
  price: string;
  maxUsers: number;
  maxContacts: number;
  maxChannels: number;
  maxFlows: number;
  isActive: boolean;
  features: string[];
}

export interface AppSetting {
  id: number;
  key: string;
  value: unknown;
  createdAt: Date;
  updatedAt: Date;
}

export interface PaymentTransaction {
  id: number;
  companyId: number | null;
  planId: number | null;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded' | 'cancelled';
  paymentMethod: 'stripe' | 'bank_transfer' | 'other' | 'mercadopago' | 'paypal' | 'moyasar';
  paymentIntentId?: string | null;
  externalTransactionId?: string | null;
  receiptUrl?: string | null;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;
}

export interface InsertPaymentTransaction {
  companyId: number | null;
  planId?: number | null;
  amount: string;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded' | 'cancelled';
  paymentMethod: 'stripe' | 'bank_transfer' | 'other' | 'mercadopago' | 'paypal' | 'moyasar';
  paymentIntentId?: string | null;
  externalTransactionId?: string | null;
  receiptUrl?: string | null;
  metadata?: Record<string, unknown>;
}

export interface Language {
  id: number;
  code: string;
  name: string;
  nativeName: string;
  flagIcon?: string | null;
  isActive: boolean | null;
  isDefault: boolean | null;
  direction: string | null;
  createdAt: Date | null;
  updatedAt: Date | null;
}

export interface InsertLanguage {
  code: string;
  name: string;
  nativeName: string;
  flagIcon?: string;
  isActive?: boolean;
  isDefault?: boolean;
  direction?: string;
}

export interface TranslationNamespace {
  id: number;
  name: string;
  description?: string | null;
  createdAt: Date | null;
  updatedAt: Date | null;
}

export interface InsertTranslationNamespace {
  name: string;
  description?: string | null;
}

export interface TranslationKey {
  id: number;
  namespaceId: number | null;
  key: string;
  description?: string | null;
  createdAt: Date | null;
  updatedAt: Date | null;
}

export interface InsertTranslationKey {
  namespaceId: number | null;
  key: string;
  description?: string | null;
}

export interface Translation {
  id: number;
  keyId: number;
  languageId: number;
  value: string;
  createdAt: Date | null;
  updatedAt: Date | null;
}

export interface InsertTranslation {
  keyId: number;
  languageId: number;
  value: string;
}

export interface RolePermission {
  id: number;
  companyId: number;
  role: 'super_admin' | 'admin' | 'agent';
  permissions: Record<string, boolean>;
  createdAt: Date | null;
  updatedAt: Date | null;
}

export interface InsertRolePermission {
  companyId: number;
  role: 'admin' | 'agent';
  permissions: Record<string, boolean>;
}

export interface GoogleTokens {
  access_token: string;
  refresh_token?: string;
  id_token?: string;
  token_type?: string;
  expiry_date?: number;
  scope?: string;
}

export interface IStorage {
  getAllCompanies(): Promise<Company[]>;
  getCompany(id: number): Promise<Company | undefined>;
  getCompanyBySlug(slug: string): Promise<Company | undefined>;
  getCompanyBySubdomain(subdomain: string): Promise<Company | undefined>;
  createCompany(company: InsertCompany): Promise<Company>;
  updateCompany(id: number, updates: Partial<InsertCompany>): Promise<Company>;

  getAllUsers(): Promise<User[]>;
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  getUserByUsernameOrEmail(credential: string): Promise<User | undefined>;
  getUserByUsernameCaseInsensitive(username: string): Promise<User | undefined>;
  getUsersByCompany(companyId: number): Promise<User[]>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, updates: Partial<InsertUser>): Promise<User>;
  updateUserPassword(id: number, newPassword: string): Promise<boolean>;
  deleteUser(id: number): Promise<boolean>;

  getAllPlans(): Promise<Plan[]>;
  getPlan(id: number): Promise<Plan | undefined>;
  createPlan(plan: InsertPlan): Promise<Plan>;
  updatePlan(id: number, updates: Partial<InsertPlan>): Promise<Plan>;
  deletePlan(id: number): Promise<boolean>;

  getAppSetting(key: string): Promise<AppSetting | undefined>;
  getAllAppSettings(): Promise<AppSetting[]>;
  saveAppSetting(key: string, value: unknown): Promise<AppSetting>;
  deleteAppSetting(key: string): Promise<boolean>;

  getAllPaymentTransactions(): Promise<PaymentTransaction[]>;
  getPaymentTransactionsByCompany(companyId: number): Promise<PaymentTransaction[]>;
  getPaymentTransaction(id: number): Promise<PaymentTransaction | undefined>;
  createPaymentTransaction(transaction: InsertPaymentTransaction): Promise<PaymentTransaction>;
  updatePaymentTransaction(id: number, updates: Partial<InsertPaymentTransaction>): Promise<PaymentTransaction>;

  getActiveSubscriptionsCount(): Promise<number>;
  getPaymentTransactionsSince(startDate: Date): Promise<PaymentTransaction[]>;
  getCompaniesWithPaymentDetails(filters: Record<string, unknown>): Promise<unknown>;
  getPaymentTransactionsWithFilters(filters: Record<string, unknown>): Promise<{ data: PaymentTransaction[], total: number }>;
  getPendingPayments(offset: number, limit: number): Promise<{ data: PaymentTransaction[], total: number }>;
  updatePaymentTransactionStatus(id: number, status: string, notes?: string): Promise<PaymentTransaction | null>;
  createPaymentReminder(reminder: Record<string, unknown>): Promise<unknown>;
  getPaymentMethodPerformance(filters: Record<string, unknown>): Promise<unknown>;
  getPaymentTransactionsForExport(filters: Record<string, unknown>): Promise<PaymentTransaction[]>;
  generatePaymentCSV(transactions: PaymentTransaction[]): Promise<string>;
  updateCompanySubscription(companyId: number, subscription: Record<string, unknown>): Promise<unknown>;
  startCompanyTrial(companyId: number, planId: number, trialDays: number): Promise<Company>;
  endCompanyTrial(companyId: number): Promise<Company>;
  getCompaniesWithExpiredTrials(): Promise<Company[]>;
  getCompaniesWithExpiringTrials(daysBeforeExpiry: number): Promise<Company[]>;

  getAllLanguages(): Promise<Language[]>;
  getLanguage(id: number): Promise<Language | undefined>;
  getLanguageByCode(code: string): Promise<Language | undefined>;
  getDefaultLanguage(): Promise<Language | undefined>;
  createLanguage(language: InsertLanguage): Promise<Language>;
  updateLanguage(id: number, updates: Partial<InsertLanguage>): Promise<Language>;
  deleteLanguage(id: number): Promise<boolean>;
  setDefaultLanguage(id: number): Promise<boolean>;

  getAllNamespaces(): Promise<TranslationNamespace[]>;
  getNamespace(id: number): Promise<TranslationNamespace | undefined>;
  getNamespaceByName(name: string): Promise<TranslationNamespace | undefined>;
  createNamespace(namespace: InsertTranslationNamespace): Promise<TranslationNamespace>;
  updateNamespace(id: number, updates: Partial<InsertTranslationNamespace>): Promise<TranslationNamespace>;
  deleteNamespace(id: number): Promise<boolean>;

  getAllKeys(namespaceId?: number): Promise<TranslationKey[]>;
  getKey(id: number): Promise<TranslationKey | undefined>;
  getKeyByNameAndKey(namespaceId: number, key: string): Promise<TranslationKey | undefined>;
  createKey(key: InsertTranslationKey): Promise<TranslationKey>;
  updateKey(id: number, updates: Partial<InsertTranslationKey>): Promise<TranslationKey>;
  deleteKey(id: number): Promise<boolean>;

  getAllTranslations(languageId?: number, keyId?: number): Promise<Translation[]>;
  getTranslation(id: number): Promise<Translation | undefined>;
  getTranslationByKeyAndLanguage(keyId: number, languageId: number): Promise<Translation | undefined>;
  createTranslation(translation: InsertTranslation): Promise<Translation>;
  updateTranslation(id: number, updates: Partial<InsertTranslation>): Promise<Translation>;
  deleteTranslation(id: number): Promise<boolean>;

  getTranslationsForLanguage(languageCode: string): Promise<Array<{id: number, key: string, value: string}>>;
  getTranslationsForLanguageByNamespace(languageCode: string): Promise<Record<string, Record<string, string>>>;
  getTranslationsForLanguageAsArray(languageCode: string): Promise<Array<{key: string, value: string}>>;
  convertArrayToNestedFormat(arrayData: Array<{key: string, value: string}>): Promise<Record<string, Record<string, string>>>;
  importTranslations(languageId: number, translations: Record<string, Record<string, string>>): Promise<boolean>;

  getRolePermissions(companyId?: number): Promise<RolePermission[]>;
  getRolePermissionsByRole(companyId: number, role: 'admin' | 'agent'): Promise<RolePermission | undefined>;
  createRolePermissions(rolePermission: InsertRolePermission): Promise<RolePermission>;
  updateRolePermissions(role: 'admin' | 'agent', permissions: Record<string, boolean>, companyId?: number): Promise<RolePermission>;

  getCompanyPages(companyId: number, options?: { published?: boolean; featured?: boolean }): Promise<CompanyPage[]>;
  getCompanyPage(id: number): Promise<CompanyPage | undefined>;
  getCompanyPageBySlug(companyId: number, slug: string): Promise<CompanyPage | undefined>;
  createCompanyPage(page: InsertCompanyPage): Promise<CompanyPage>;
  updateCompanyPage(id: number, page: Partial<InsertCompanyPage>): Promise<CompanyPage>;
  deleteCompanyPage(id: number): Promise<boolean>;
  publishCompanyPage(id: number): Promise<CompanyPage>;
  unpublishCompanyPage(id: number): Promise<CompanyPage>;

  getChannelConnections(userId: number | null, companyId?: number): Promise<ChannelConnection[]>;
  getChannelConnectionsByCompany(companyId: number): Promise<ChannelConnection[]>;
  getChannelConnectionsByType(channelType: string): Promise<ChannelConnection[]>;
  getChannelConnection(id: number): Promise<ChannelConnection | undefined>;
  createChannelConnection(connection: InsertChannelConnection): Promise<ChannelConnection>;
  updateChannelConnectionStatus(id: number, status: string): Promise<ChannelConnection>;
  updateChannelConnectionName(id: number, accountName: string): Promise<ChannelConnection>;
  updateChannelConnection(id: number, updates: Partial<InsertChannelConnection>): Promise<ChannelConnection>;
  deleteChannelConnection(id: number): Promise<boolean>;

  getSmtpConfig(companyId?: number): Promise<Record<string, unknown> | null>;
  saveSmtpConfig(config: Record<string, unknown>, companyId?: number): Promise<boolean>;

  getCompanySetting(companyId: number, key: string): Promise<CompanySetting | undefined>;
  getAllCompanySettings(companyId: number): Promise<CompanySetting[]>;
  saveCompanySetting(companyId: number, key: string, value: unknown): Promise<CompanySetting>;
  deleteCompanySetting(companyId: number, key: string): Promise<boolean>;

  getGoogleTokens(userId: number, companyId: number): Promise<GoogleTokens | null>;
  saveGoogleTokens(userId: number, companyId: number, tokens: GoogleTokens): Promise<boolean>;
  deleteGoogleTokens(userId: number, companyId: number): Promise<boolean>;
  getGoogleCalendarCredentials(companyId: number): Promise<Record<string, unknown> | null>;
  saveGoogleCalendarCredentials(companyId: number, credentials: Record<string, unknown>): Promise<boolean>;

  getContacts(options?: { page?: number; limit?: number; search?: string; channel?: string; companyId?: number }): Promise<{ contacts: Contact[]; total: number }>;
  getContact(id: number): Promise<Contact | undefined>;
  getContactByIdentifier(identifier: string, identifierType: string): Promise<Contact | undefined>;
  getContactByEmail(email: string, companyId: number): Promise<Contact | undefined>;
  getContactByPhone(phone: string, companyId: number): Promise<Contact | undefined>;
  createContact(contact: InsertContact): Promise<Contact>;
  updateContact(id: number, contact: Partial<InsertContact>): Promise<Contact>;
  deleteContact(id: number): Promise<boolean>;

  getConversations(options?: { companyId?: number; page?: number; limit?: number; search?: string }): Promise<{ conversations: Conversation[]; total: number }>;
  getConversation(id: number): Promise<Conversation | undefined>;
  getConversationsByContact(contactId: number): Promise<Conversation[]>;
  getConversationByContactAndChannel(contactId: number, channelId: number): Promise<Conversation | undefined>;
  getConversationByGroupJid(groupJid: string): Promise<Conversation | undefined>;
  createConversation(conversation: InsertConversation): Promise<Conversation>;
  updateConversation(id: number, updates: Partial<InsertConversation>): Promise<Conversation>;

  upsertGroupParticipant(data: {
    conversationId: number;
    contactId?: number;
    participantJid: string;
    participantName?: string;
    isAdmin?: boolean;
    isSuperAdmin?: boolean;
    isActive?: boolean;
  }): Promise<GroupParticipant>;

  getMessagesByConversation(conversationId: number): Promise<Message[]>;
  getMessagesByConversationPaginated(conversationId: number, limit: number, offset: number): Promise<Message[]>;
  getMessagesCountByConversation(conversationId: number): Promise<number>;

  getMessagesByConversationWithCompanyValidation(conversationId: number, companyId: number): Promise<Message[]>;
  getMessagesByConversationPaginatedWithCompanyValidation(conversationId: number, companyId: number, limit: number, offset: number): Promise<Message[]>;
  getMessagesCountByConversationWithCompanyValidation(conversationId: number, companyId: number): Promise<number>;
  getMessageById(id: number): Promise<Message | undefined>;
  getMessageByExternalId(externalId: string, companyId?: number): Promise<Message | undefined>;
  createMessage(message: InsertMessage): Promise<Message>;
  updateMessage(id: number, updates: Partial<InsertMessage>): Promise<Message>;
  deleteMessage(id: number): Promise<boolean>;
  clearConversationHistory(conversationId: number): Promise<{ success: boolean; deletedCount: number; mediaFiles: string[] }>;

  getConversationsCount(): Promise<number>;
  getConversationsCountByCompany(companyId: number): Promise<number>;
  getMessagesCount(): Promise<number>;
  getMessagesCountByCompany(companyId: number): Promise<number>;
  getConversationsByDay(days: number): Promise<Record<string, unknown>[]>;
  getConversationsByDayByCompany(companyId: number, days: number): Promise<Record<string, unknown>[]>;
  getMessagesByChannel(): Promise<Record<string, unknown>[]>;
  getMessagesByChannelByCompany(companyId: number): Promise<Record<string, unknown>[]>;





  createFlowSession(session: Record<string, unknown>): Promise<unknown>;
  updateFlowSession(sessionId: string, updates: Record<string, unknown>): Promise<unknown>;
  getFlowSession(sessionId: string): Promise<unknown>;
  getActiveFlowSessionsForConversation(conversationId: number): Promise<Record<string, unknown>[]>;
  expireFlowSession(sessionId: string): Promise<unknown>;

  createFlowSessionVariable(variable: Record<string, unknown>): Promise<unknown>;
  upsertFlowSessionVariable(variable: Record<string, unknown>): Promise<unknown>;
  getFlowSessionVariables(sessionId: string): Promise<Record<string, unknown>[]>;
  getFlowSessionVariable(sessionId: string, variableKey: string): Promise<unknown>;
  deleteFlowSessionVariable(sessionId: string, variableKey: string): Promise<unknown>;

  createFlowSessionCursor(cursor: Record<string, unknown>): Promise<unknown>;
  updateFlowSessionCursor(sessionId: string, updates: Record<string, unknown>): Promise<unknown>;
  getFlowSessionCursor(sessionId: string): Promise<unknown>;

  createFollowUpSchedule(schedule: Record<string, unknown>): Promise<unknown>;
  updateFollowUpSchedule(scheduleId: string, updates: Record<string, unknown>): Promise<unknown>;
  getFollowUpSchedule(scheduleId: string): Promise<unknown>;
  getFollowUpSchedulesByConversation(conversationId: number): Promise<Record<string, unknown>[]>;
  getFollowUpSchedulesByContact(contactId: number): Promise<Record<string, unknown>[]>;
  getScheduledFollowUps(limit?: number): Promise<Record<string, unknown>[]>;
  cancelFollowUpSchedule(scheduleId: string): Promise<unknown>;

  createFollowUpTemplate(template: Record<string, unknown>): Promise<unknown>;
  updateFollowUpTemplate(id: number, updates: Record<string, unknown>): Promise<unknown>;
  getFollowUpTemplate(id: number): Promise<unknown>;
  getFollowUpTemplatesByCompany(companyId: number): Promise<Record<string, unknown>[]>;
  deleteFollowUpTemplate(id: number): Promise<boolean>;

  createFollowUpExecutionLog(log: Record<string, unknown>): Promise<unknown>;
  getFollowUpExecutionLogs(scheduleId: string): Promise<Record<string, unknown>[]>;

  createFlowExecution(data: {
    executionId: string;
    flowId: number;
    conversationId: number;
    contactId: number;
    companyId?: number;
    triggerNodeId: string;
    contextData?: Record<string, unknown>;
  }): Promise<number>;

  updateFlowExecution(executionId: string, data: {
    status?: string;
    currentNodeId?: string;
    executionPath?: string[];
    contextData?: Record<string, unknown>;
    completedAt?: Date;
    totalDurationMs?: number;
    completionRate?: number;
    errorMessage?: string;
  }): Promise<void>;

  createFlowStepExecution(data: {
    flowExecutionId: number;
    nodeId: string;
    nodeType: string;
    stepOrder: number;
    inputData?: Record<string, unknown>;
  }): Promise<number>;

  updateFlowStepExecution(stepId: number, data: {
    status?: string;
    completedAt?: Date;
    durationMs?: number;
    outputData?: Record<string, unknown>;
    errorMessage?: string;
  }): Promise<void>;

  getFlowDropoffAnalysis(flowId: number, companyId?: number): Promise<Array<{
    nodeId: string;
    nodeType: string;
    dropoffCount: number;
    dropoffRate: number;
  }>>;

  getNotesByContact(contactId: number): Promise<Note[]>;
  createNote(note: InsertNote): Promise<Note>;

  getFlows(userId: number): Promise<Flow[]>;
  getFlow(id: number): Promise<Flow | undefined>;
  createFlow(flow: InsertFlow): Promise<Flow>;
  updateFlow(id: number, updates: Partial<InsertFlow>): Promise<Flow>;
  deleteFlow(id: number): Promise<boolean>;

  getFlowAssignments(channelId?: number, flowId?: number): Promise<FlowAssignment[]>;
  getFlowAssignment(id: number): Promise<FlowAssignment | undefined>;
  createFlowAssignment(assignment: InsertFlowAssignment): Promise<FlowAssignment>;
  updateFlowAssignmentStatus(id: number, isActive: boolean): Promise<FlowAssignment>;
  deleteFlowAssignment(id: number): Promise<boolean>;

  getAllTeamMembers(): Promise<User[]>;
  getActiveTeamMembers(): Promise<User[]>;
  getTeamMembersByCompany(companyId: number): Promise<User[]>;
  getActiveTeamMembersByCompany(companyId: number): Promise<User[]>;

  getTeamInvitations(companyId?: number): Promise<TeamInvitation[]>;
  getTeamInvitationByEmail(email: string): Promise<TeamInvitation | undefined>;
  getTeamInvitationByToken(token: string): Promise<TeamInvitation | undefined>;
  createTeamInvitation(invitation: InsertTeamInvitation): Promise<TeamInvitation>;
  updateTeamInvitationStatus(id: number, status: string): Promise<TeamInvitation>;
  deleteTeamInvitation(id: number): Promise<boolean>;

  getPipelineStages(): Promise<PipelineStage[]>;
  getPipelineStage(id: number): Promise<PipelineStage | undefined>;
  createPipelineStage(stage: InsertPipelineStage): Promise<PipelineStage>;
  updatePipelineStage(id: number, updates: Partial<PipelineStage>): Promise<PipelineStage>;
  deletePipelineStage(id: number, moveDealsToStageId?: number): Promise<boolean>;
  reorderPipelineStages(stageIds: number[]): Promise<boolean>;

  getDeals(filter?: {
    priority?: string;
    assignedToUserId?: number;
    stageId?: number;
    companyId?: number;
    titleSearch?: string;
    tags?: string[];
    contactPhone?: string;
    contactName?: string;
  }): Promise<Deal[]>;
  getDealsByStage(stage: DealStatus): Promise<Deal[]>;
  getDealsByStageId(stageId: number): Promise<Deal[]>;
  getDeal(id: number): Promise<Deal | undefined>;
  getDealsByContact(contactId: number): Promise<Deal[]>;
  getDealsByAssignedUser(userId: number): Promise<Deal[]>;
  getDealTags(companyId: number): Promise<string[]>;
  createDeal(deal: InsertDeal): Promise<Deal>;
  updateDeal(id: number, updates: Partial<InsertDeal>): Promise<Deal>;
  updateDealStage(id: number, stage: DealStatus): Promise<Deal>;
  updateDealStageId(id: number, stageId: number): Promise<Deal>;
  deleteDeal(id: number): Promise<boolean>;

  getDealActivities(dealId: number): Promise<DealActivity[]>;
  createDealActivity(activity: InsertDealActivity): Promise<DealActivity>;

  createSystemUpdate(update: InsertSystemUpdate): Promise<SystemUpdate>;
  updateSystemUpdate(id: number, updates: Partial<InsertSystemUpdate>): Promise<SystemUpdate>;
  getSystemUpdate(id: number): Promise<SystemUpdate | undefined>;
  getAllSystemUpdates(): Promise<SystemUpdate[]>;
  getLatestSystemUpdate(): Promise<SystemUpdate | undefined>;
  deleteSystemUpdate(id: number): Promise<boolean>;



  createDatabaseBackup(name: string): Promise<string>;


  getAffiliateMetrics(): Promise<Record<string, unknown>>;
  getAffiliates(params: Record<string, unknown>): Promise<{ data: unknown[], total: number, page: number, limit: number, totalPages: number }>;
  getAffiliate(id: number): Promise<unknown | undefined>;
  createAffiliate(affiliate: Record<string, unknown>): Promise<unknown>;
  updateAffiliate(id: number, updates: Record<string, unknown>): Promise<unknown | undefined>;
  deleteAffiliate(id: number): Promise<boolean>;
  generateAffiliateCode(name: string): Promise<string>;


  createAffiliateApplication(application: Record<string, unknown>): Promise<unknown>;
  getAffiliateApplications(): Promise<unknown[]>;
  getAffiliateApplication(id: number): Promise<unknown | undefined>;
  getAffiliateApplicationByEmail(email: string): Promise<unknown | undefined>;
  updateAffiliateApplication(id: number, updates: Record<string, unknown>): Promise<unknown | undefined>;
  getAffiliateByEmail(email: string): Promise<unknown | undefined>;

  getAffiliateCommissionStructures(affiliateId: number): Promise<unknown[]>;
  createCommissionStructure(structure: Record<string, unknown>): Promise<unknown>;
  updateCommissionStructure(id: number, updates: Record<string, unknown>): Promise<unknown | undefined>;
  deleteCommissionStructure(id: number): Promise<boolean>;

  getAffiliateReferrals(params: Record<string, unknown>): Promise<{ data: unknown[], total: number, page: number, limit: number, totalPages: number }>;
  updateAffiliateReferral(id: number, updates: Record<string, unknown>): Promise<unknown | undefined>;

  getAffiliatePayouts(params: Record<string, unknown>): Promise<{ data: unknown[], total: number, page: number, limit: number, totalPages: number }>;
  createAffiliatePayout(payout: Record<string, unknown>): Promise<unknown>;
  updateAffiliatePayout(id: number, updates: Record<string, unknown>): Promise<unknown | undefined>;

  getAffiliateAnalytics(params: Record<string, unknown>): Promise<unknown[]>;
  getAffiliatePerformance(params: Record<string, unknown>): Promise<unknown[]>;
  exportAffiliateData(params: Record<string, unknown>): Promise<string>;

  sessionStore: session.Store;
}

import { db } from "./db";
import connectPg from "connect-pg-simple";
import { pool } from "./db";
import { PgColumn } from "drizzle-orm/pg-core";

const PostgresSessionStore = connectPg(session);

export class DatabaseStorage implements IStorage {
  sessionStore: session.Store;
  public db = db;
  public pool = pool;

  constructor() {
    this.sessionStore = new PostgresSessionStore({
      pool,
      createTableIfMissing: true
    });
  }

  private mapToPaymentTransaction(transaction: any): PaymentTransaction {
    return {
      ...transaction,
      amount: Number(transaction.amount),
      metadata: transaction.metadata as Record<string, unknown> | undefined
    };
  }

  async getAllCompanies(): Promise<Company[]> {
    try {
      return await db.select().from(companies).orderBy(companies.name);
    } catch (error) {
      console.error("Error getting all companies:", error);
      return [];
    }
  }

  async getCompany(id: number): Promise<Company | undefined> {
    try {
      const [company] = await db.select().from(companies).where(eq(companies.id, id));
      return company || undefined;
    } catch (error) {
      console.error(`Error getting company with ID ${id}:`, error);
      return undefined;
    }
  }

  async getCompanyBySlug(slug: string): Promise<Company | undefined> {
    try {
      const [company] = await db.select().from(companies).where(eq(companies.slug, slug));
      return company || undefined;
    } catch (error) {
      console.error(`Error getting company with slug ${slug}:`, error);
      return undefined;
    }
  }

  async getCompanyBySubdomain(subdomain: string): Promise<Company | undefined> {
    try {
      const [company] = await db.select().from(companies).where(eq(companies.subdomain, subdomain));
      return company || undefined;
    } catch (error) {
      console.error(`Error getting company with subdomain ${subdomain}:`, error);
      return undefined;
    }
  }

  async createCompany(company: InsertCompany): Promise<Company> {
    try {
      const [newCompany] = await db.insert(companies).values({
        ...company,
        createdAt: new Date(),
        updatedAt: new Date()
      }).returning();
      return newCompany;
    } catch (error) {
      console.error("Error creating company:", error);
      throw error;
    }
  }

  async updateCompany(id: number, updates: Partial<InsertCompany>): Promise<Company> {
    try {
      const [updatedCompany] = await db
        .update(companies)
        .set({
          ...updates,
          updatedAt: new Date()
        })
        .where(eq(companies.id, id))
        .returning();

      if (!updatedCompany) {
        throw new Error(`Company with ID ${id} not found`);
      }

      return updatedCompany;
    } catch (error) {
      console.error("Error updating company:", error);
      throw error;
    }
  }

  async getAllUsers(): Promise<User[]> {
    try {
      return await db
        .select()
        .from(users)
        .orderBy(users.fullName);
    } catch (error) {
      console.error("Error getting all users:", error);
      return [];
    }
  }

  async getUsersByCompany(companyId: number): Promise<User[]> {
    try {
      return await db
        .select()
        .from(users)
        .where(eq(users.companyId, companyId))
        .orderBy(users.fullName);
    } catch (error) {
      console.error(`Error getting users for company ${companyId}:`, error);
      return [];
    }
  }

  async deleteUser(id: number): Promise<boolean> {
    try {
      await db
        .delete(users)
        .where(eq(users.id, id));
      return true;
    } catch (error) {
      console.error(`Error deleting user with ID ${id}:`, error);
      return false;
    }
  }

  async getAllPlans(): Promise<Plan[]> {
    try {
      const result = await db
        .select()
        .from(plans)
        .orderBy(plans.name);

      return result.map(plan => ({
        ...plan,
        features: Array.isArray(plan.features) ? plan.features : [],
        campaignFeatures: Array.isArray(plan.campaignFeatures) ? plan.campaignFeatures : ["basic_campaigns"],
        trialDays: plan.trialDays || 0
      }));
    } catch (error) {
      console.error("Error getting all plans:", error);
      return [];
    }
  }

  async getPlan(id: number): Promise<Plan | undefined> {
    try {
      const [plan] = await db
        .select()
        .from(plans)
        .where(eq(plans.id, id));

      if (!plan) return undefined;

      return {
        ...plan,
        features: Array.isArray(plan.features) ? plan.features : [],
        campaignFeatures: Array.isArray(plan.campaignFeatures) ? plan.campaignFeatures : ["basic_campaigns"],
        trialDays: plan.trialDays || 0
      };
    } catch (error) {
      console.error(`Error getting plan with ID ${id}:`, error);
      return undefined;
    }
  }

  async createPlan(plan: InsertPlan): Promise<Plan> {
    try {
      const [newPlan] = await db
        .insert(plans)
        .values({
          ...plan,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning();

      return {
        ...newPlan,
        features: Array.isArray(newPlan.features) ? newPlan.features : [],
        campaignFeatures: Array.isArray(newPlan.campaignFeatures) ? newPlan.campaignFeatures : ["basic_campaigns"],
        trialDays: newPlan.trialDays || 0
      };
    } catch (error) {
      console.error("Error creating plan:", error);
      throw error;
    }
  }

  async updatePlan(id: number, updates: Partial<InsertPlan>): Promise<Plan> {
    try {
      const [updatedPlan] = await db
        .update(plans)
        .set({
          ...updates,
          updatedAt: new Date()
        })
        .where(eq(plans.id, id))
        .returning();

      if (!updatedPlan) {
        throw new Error(`Plan with ID ${id} not found`);
      }

      return {
        ...updatedPlan,
        features: Array.isArray(updatedPlan.features) ? updatedPlan.features : [],
        campaignFeatures: Array.isArray(updatedPlan.campaignFeatures) ? updatedPlan.campaignFeatures : ["basic_campaigns"],
        trialDays: updatedPlan.trialDays || 0
      };
    } catch (error) {
      console.error("Error updating plan:", error);
      throw error;
    }
  }

  async deletePlan(id: number): Promise<boolean> {
    try {
      await db
        .delete(plans)
        .where(eq(plans.id, id));

      return true;
    } catch (error) {
      console.error(`Error deleting plan with ID ${id}:`, error);
      return false;
    }
  }

  async getGoogleTokens(userId: number, companyId: number): Promise<GoogleTokens | null> {
    try {
      const [tokens] = await db
        .select()
        .from(googleCalendarTokens)
        .where(
          and(
            eq(googleCalendarTokens.userId, userId),
            eq(googleCalendarTokens.companyId, companyId)
          )
        );

      if (!tokens) return null;

      return {
        access_token: tokens.accessToken,
        refresh_token: tokens.refreshToken || undefined,
        id_token: tokens.idToken || undefined,
        token_type: tokens.tokenType || undefined,
        expiry_date: tokens.expiryDate ? tokens.expiryDate.getTime() : undefined,
        scope: tokens.scope || undefined
      };
    } catch (error) {
      console.error('Error getting Google tokens:', error);
      return null;
    }
  }

  async saveGoogleTokens(userId: number, companyId: number, tokens: GoogleTokens): Promise<boolean> {
    try {
      const existingTokens = await this.getGoogleTokens(userId, companyId);

      if (existingTokens) {
        await db
          .update(googleCalendarTokens)
          .set({
            accessToken: tokens.access_token,
            refreshToken: tokens.refresh_token || null,
            idToken: tokens.id_token || null,
            tokenType: tokens.token_type || null,
            expiryDate: tokens.expiry_date ? new Date(tokens.expiry_date) : null,
            scope: tokens.scope || null,
            updatedAt: new Date()
          })
          .where(
            and(
              eq(googleCalendarTokens.userId, userId),
              eq(googleCalendarTokens.companyId, companyId)
            )
          );
      } else {
        await db
          .insert(googleCalendarTokens)
          .values({
            userId,
            companyId,
            accessToken: tokens.access_token,
            refreshToken: tokens.refresh_token || null,
            idToken: tokens.id_token || null,
            tokenType: tokens.token_type || null,
            expiryDate: tokens.expiry_date ? new Date(tokens.expiry_date) : null,
            scope: tokens.scope || null
          });
      }

      return true;
    } catch (error) {
      console.error('Error saving Google tokens:', error);
      return false;
    }
  }

  async deleteGoogleTokens(userId: number, companyId: number): Promise<boolean> {
    try {
      await db
        .delete(googleCalendarTokens)
        .where(
          and(
            eq(googleCalendarTokens.userId, userId),
            eq(googleCalendarTokens.companyId, companyId)
          )
        );

      return true;
    } catch (error) {
      console.error('Error deleting Google tokens:', error);
      return false;
    }
  }

  async getGoogleCalendarCredentials(companyId: number): Promise<any | null> {
    try {
      const setting = await this.getCompanySetting(companyId, 'google_calendar_credentials');
      return setting?.value || null;
    } catch (error) {
      console.error('Error getting Google Calendar credentials:', error);
      return null;
    }
  }

  async saveGoogleCalendarCredentials(companyId: number, credentials: any): Promise<boolean> {
    try {
      await this.saveCompanySetting(companyId, 'google_calendar_credentials', credentials);
      return true;
    } catch (error) {
      console.error('Error saving Google Calendar credentials:', error);
      return false;
    }
  }

  async getUser(id: number): Promise<User | undefined> {
    try {
      const [user] = await db.select().from(users).where(eq(users.id, id));
      return user || undefined;
    } catch (error) {
      console.error("Error getting user by ID:", error);
      return undefined;
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    try {
      const [user] = await db.select().from(users).where(eq(users.username, username));
      return user || undefined;
    } catch (error) {
      console.error("Error getting user by username:", error);
      return undefined;
    }
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    try {
      const [user] = await db.select().from(users).where(eq(users.email, email));
      return user || undefined;
    } catch (error) {
      console.error("Error getting user by email:", error);
      return undefined;
    }
  }

  async getUserByUsernameOrEmail(credential: string): Promise<User | undefined> {
    try {
      const lowerCredential = credential.toLowerCase();

      const isEmail = lowerCredential.includes('@');

      if (isEmail) {
        const [user] = await db.select().from(users).where(
          sql`LOWER(${users.email}) = ${lowerCredential}`
        );
        return user || undefined;
      } else {
        const [user] = await db.select().from(users).where(
          sql`LOWER(${users.username}) = ${lowerCredential}`
        );
        return user || undefined;
      }
    } catch (error) {
      console.error("Error getting user by username or email:", error);
      return undefined;
    }
  }

  async getUserByUsernameCaseInsensitive(username: string): Promise<User | undefined> {
    try {
      const lowerUsername = username.toLowerCase();
      const [user] = await db.select().from(users).where(
        sql`LOWER(${users.username}) = ${lowerUsername}`
      );
      return user || undefined;
    } catch (error) {
      console.error("Error getting user by username (case-insensitive):", error);
      return undefined;
    }
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    try {
      const [user] = await db.insert(users).values({
        ...insertUser,
        updatedAt: new Date()
      }).returning();

      return user;
    } catch (error) {
      console.error("Error creating user:", error);
      throw error;
    }
  }

  async updateUser(id: number, updates: Partial<InsertUser>): Promise<User> {
  try {
    const [updatedUser] = await db
      .update(users)
      .set({
        ...updates,
        updatedAt: new Date()
      })
      .where(eq(users.id, id))
      .returning();

    if (!updatedUser) {
      throw new Error(`User with ID ${id} not found`);
    }

    return updatedUser;
  } catch (error) {
    console.error("Error updating user:", error);
    throw error;
  }
}

  async updateUserPassword(id: number, newPassword: string): Promise<boolean> {
    try {
      // Import hashPassword function from auth.ts
      const { hashPassword } = await import('./auth');
      const hashedPassword = await hashPassword(newPassword);

      const [updatedUser] = await db
        .update(users)
        .set({
          password: hashedPassword,
          updatedAt: new Date()
        })
        .where(eq(users.id, id))
        .returning();

      return !!updatedUser;
    } catch (error) {
      console.error("Error updating user password:", error);
      return false;
    }
  }

  async getChannelConnections(userId: number | null, companyId?: number): Promise<ChannelConnection[]> {
    if (userId === null && !companyId) {
      return db.select().from(channelConnections);
    }

    // For multi-tenant security, always filter by companyId when available
    if (companyId) {
      if (userId) {
        // Filter by both company and user for additional security
        return db.select().from(channelConnections).where(
          and(
            eq(channelConnections.companyId, companyId),
            eq(channelConnections.userId, userId)
          )
        );
      } else {
        // Filter by company only (for admin operations)
        return db.select().from(channelConnections).where(eq(channelConnections.companyId, companyId));
      }
    }

    // Fallback to userId filtering (legacy support)
    return db.select().from(channelConnections).where(eq(channelConnections.userId, userId!));
  }

  async getChannelConnectionsByCompany(companyId: number): Promise<ChannelConnection[]> {
    console.log(`[Storage] Getting channel connections for company ${companyId}`);

    const allConnections = await db.select().from(channelConnections);
    console.log(`[Storage] DEBUG - All channel connections in database:`,
      allConnections.map(c => ({ id: c.id, name: c.accountName, status: c.status, companyId: c.companyId, userId: c.userId })));

    let result = await db.select().from(channelConnections).where(eq(channelConnections.companyId, companyId));
    console.log(`[Storage] Found ${result.length} connections with companyId=${companyId}:`,
      result.map(c => ({ id: c.id, name: c.accountName, status: c.status, companyId: c.companyId })));

    const companyUsers = await db.select().from(users).where(eq(users.companyId, companyId));
    const userIds = companyUsers.map(u => u.id);
    console.log(`[Storage] Found ${companyUsers.length} users in company ${companyId}, userIds:`, userIds);

    if (userIds.length > 0) {
      const legacyConnections = await db.select().from(channelConnections).where(
        and(
          inArray(channelConnections.userId, userIds),
          isNull(channelConnections.companyId)
        )
      );
      console.log(`[Storage] Found ${legacyConnections.length} legacy connections (companyId=null):`,
        legacyConnections.map(c => ({ id: c.id, name: c.accountName, status: c.status, userId: c.userId, companyId: c.companyId })));

      if (legacyConnections.length > 0) {
        console.log(`[Storage] Updating legacy connections with companyId=${companyId}`);
        for (const connection of legacyConnections) {
          await db.update(channelConnections)
            .set({ companyId: companyId, updatedAt: new Date() })
            .where(eq(channelConnections.id, connection.id));
        }

        result = [...result, ...legacyConnections.map(conn => ({ ...conn, companyId }))];
      }
    }

    console.log(`[Storage] Final result: ${result.length} connections for company ${companyId}`);
    return result;
  }

  async getChannelConnectionsByType(channelType: string): Promise<ChannelConnection[]> {
    return db.select().from(channelConnections).where(eq(channelConnections.channelType, channelType));
  }

  async getChannelConnection(id: number): Promise<ChannelConnection | undefined> {
    const [connection] = await db.select().from(channelConnections).where(eq(channelConnections.id, id));
    return connection;
  }

  async createChannelConnection(connection: InsertChannelConnection): Promise<ChannelConnection> {
    const [newConnection] = await db.insert(channelConnections).values(connection).returning();
    return newConnection;
  }

  async updateChannelConnectionStatus(id: number, status: string): Promise<ChannelConnection> {
    const [updatedConnection] = await db
      .update(channelConnections)
      .set({ status, updatedAt: new Date() })
      .where(eq(channelConnections.id, id))
      .returning();
    return updatedConnection;
  }

  async updateChannelConnectionName(id: number, accountName: string): Promise<ChannelConnection> {
    const [updatedConnection] = await db
      .update(channelConnections)
      .set({ accountName, updatedAt: new Date() })
      .where(eq(channelConnections.id, id))
      .returning();
    return updatedConnection;
  }

  async updateChannelConnection(id: number, updates: Partial<InsertChannelConnection>): Promise<ChannelConnection> {
    const [updatedConnection] = await db
      .update(channelConnections)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(channelConnections.id, id))
      .returning();
    return updatedConnection;
  }

  async deleteChannelConnection(id: number): Promise<boolean> {
    try {
      await db
        .delete(channelConnections)
        .where(eq(channelConnections.id, id));

      return true;
    } catch (error) {
      console.error('Error deleting channel connection:', error);
      return false;
    }
  }



  async getContacts(options?: { page?: number; limit?: number; search?: string; channel?: string; companyId?: number }): Promise<{ contacts: Contact[]; total: number }> {
    try {
      const page = options?.page || 1;
      const limit = options?.limit || 10;
      const offset = (page - 1) * limit;

      let whereConditions = undefined;

      const companyCondition = options?.companyId ? eq(contacts.companyId, options.companyId) : undefined;


      const groupChatFilter = and(
        or(
          isNull(contacts.phone),
          sql`NOT (LENGTH(REGEXP_REPLACE(${contacts.phone}, '[^0-9]', '', 'g')) >= 15 AND REGEXP_REPLACE(${contacts.phone}, '[^0-9]', '', 'g') ~ '^120[0-9]+$')`
        ),
        or(
          isNull(contacts.identifier),
          sql`NOT (LENGTH(REGEXP_REPLACE(${contacts.identifier}, '[^0-9]', '', 'g')) >= 15 AND REGEXP_REPLACE(${contacts.identifier}, '[^0-9]', '', 'g') ~ '^120[0-9]+$')`
        )
      );

      if (options?.search) {
        const searchTerm = `%${options.search}%`;
        const searchCondition = or(
          sql`${contacts.name} ILIKE ${searchTerm}`,
          sql`${contacts.email} ILIKE ${searchTerm}`,
          sql`${contacts.phone} ILIKE ${searchTerm}`
        );

        if (options?.channel && companyCondition) {
          whereConditions = and(
            companyCondition,
            searchCondition,
            eq(contacts.identifierType, options.channel),
            groupChatFilter
          );
        } else if (options?.channel) {
          whereConditions = and(
            searchCondition,
            eq(contacts.identifierType, options.channel),
            groupChatFilter
          );
        } else if (companyCondition) {
          whereConditions = and(companyCondition, searchCondition, groupChatFilter);
        } else {
          whereConditions = and(searchCondition, groupChatFilter);
        }
      } else if (options?.channel && companyCondition) {
        whereConditions = and(
          companyCondition,
          eq(contacts.identifierType, options.channel),
          groupChatFilter
        );
      } else if (options?.channel) {
        whereConditions = and(eq(contacts.identifierType, options.channel), groupChatFilter);
      } else if (companyCondition) {
        whereConditions = and(companyCondition, groupChatFilter);
      } else {
        whereConditions = groupChatFilter;
      }

      let totalCount = 0;
      if (whereConditions) {
        const countResult = await db
          .select({ count: sql`COUNT(*)::int` })
          .from(contacts)
          .where(whereConditions);
        totalCount = Number(countResult[0]?.count || 0);
      } else {
        const countResult = await db
          .select({ count: sql`COUNT(*)::int` })
          .from(contacts);
        totalCount = Number(countResult[0]?.count || 0);
      }

      let contactsList: Contact[] = [];
      if (whereConditions) {
        contactsList = await db
          .select()
          .from(contacts)
          .where(whereConditions)
          .orderBy(desc(contacts.updatedAt))
          .limit(limit)
          .offset(offset);
      } else {
        contactsList = await db
          .select()
          .from(contacts)
          .orderBy(desc(contacts.updatedAt))
          .limit(limit)
          .offset(offset);
      }

      return {
        contacts: contactsList,
        total: totalCount
      };
    } catch (error) {
      console.error('Error getting contacts:', error);
      return { contacts: [], total: 0 };
    }
  }

  async getContact(id: number): Promise<Contact | undefined> {
    try {
      if (!id || typeof id !== 'number' || id <= 0) {
        console.error(`Invalid contact ID: ${id}`);
        return undefined;
      }

      const result = await db
        .select()
        .from(contacts)
        .where(eq(contacts.id, id));

      const [contact] = result;
      return contact;
    } catch (error) {
      console.error(`Error fetching contact with ID ${id}:`, error);
      console.error("Error details:", error instanceof Error ? error.message : String(error));
      return undefined;
    }
  }

  async deleteContact(id: number): Promise<boolean> {
    try {
      const conversationList = await db.select().from(conversations).where(eq(conversations.contactId, id));

      await db.transaction(async (tx) => {
        for (const conversation of conversationList) {
          await tx.delete(messages).where(eq(messages.conversationId, conversation.id));
        }

        if (conversationList.length > 0) {
          await tx.delete(conversations).where(eq(conversations.contactId, id));
        }

        await tx.delete(notes).where(eq(notes.contactId, id));

        await tx.delete(deals).where(eq(deals.contactId, id));

        await tx.delete(contacts).where(eq(contacts.id, id));
      });

      return true;
    } catch (error) {
      console.error("Error deleting contact:", error);
      return false;
    }
  }

  async getContactByIdentifier(identifier: string, identifierType: string): Promise<Contact | undefined> {
    const [contact] = await db.select().from(contacts).where(
      and(
        eq(contacts.identifier, identifier),
        eq(contacts.identifierType, identifierType)
      )
    );
    return contact;
  }

  async getContactByEmail(email: string, companyId: number): Promise<Contact | undefined> {
    const [contact] = await db.select().from(contacts).where(
      and(
        eq(contacts.email, email),
        eq(contacts.companyId, companyId)
      )
    );
    return contact;
  }

  async getContactByPhone(phone: string, companyId: number): Promise<Contact | undefined> {
    const [contact] = await db.select().from(contacts).where(
      and(
        eq(contacts.phone, phone),
        eq(contacts.companyId, companyId)
      )
    );
    return contact;
  }

  async createContact(contact: InsertContact): Promise<Contact> {
    const [newContact] = await db.insert(contacts).values(contact).returning();
    return newContact;
  }

  async updateContact(id: number, updates: Partial<InsertContact>): Promise<Contact> {
    const [updatedContact] = await db
      .update(contacts)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(contacts.id, id))
      .returning();
    return updatedContact;
  }

  async getConversations(options?: { companyId?: number; page?: number; limit?: number; search?: string }): Promise<{ conversations: Conversation[]; total: number }> {
    try {
      const page = options?.page || 1;
      const limit = options?.limit || 50;
      const offset = (page - 1) * limit;

      const whereConditions = [];

      if (options?.companyId) {
        whereConditions.push(eq(conversations.companyId, options.companyId));
      }

      if (options?.search) {
        whereConditions.push(
          or(
            sql`${conversations.status} ILIKE ${`%${options.search}%`}`,
            sql`${conversations.channelType} ILIKE ${`%${options.search}%`}`
          )
        );
      }



      whereConditions.push(
        or(
          eq(conversations.isGroup, false),
          isNull(conversations.isGroup)
        )
      );


      whereConditions.push(isNull(conversations.groupJid));

      const totalQuery = db
        .select({ count: count() })
        .from(conversations)
        .leftJoin(contacts, eq(conversations.contactId, contacts.id));

      const totalResult = await totalQuery.where(
        and(
          ...whereConditions,

          or(
            isNull(contacts.phone),
            sql`NOT (LENGTH(REGEXP_REPLACE(${contacts.phone}, '[^0-9]', '', 'g')) >= 15 AND REGEXP_REPLACE(${contacts.phone}, '[^0-9]', '', 'g') ~ '^120[0-9]+$')`,
            sql`NOT (LENGTH(REGEXP_REPLACE(${contacts.identifier}, '[^0-9]', '', 'g')) >= 15 AND REGEXP_REPLACE(${contacts.identifier}, '[^0-9]', '', 'g') ~ '^120[0-9]+$')`
          )
        )
      );
      const [{ count: totalCount }] = totalResult;

      const conversationsQuery = db
        .select({
          id: conversations.id,
          contactId: conversations.contactId,
          channelId: conversations.channelId,
          channelType: conversations.channelType,
          companyId: conversations.companyId,
          status: conversations.status,
          lastMessageAt: conversations.lastMessageAt,
          isGroup: conversations.isGroup,
          groupJid: conversations.groupJid,
          groupName: conversations.groupName,
          groupDescription: conversations.groupDescription,
          groupParticipantCount: conversations.groupParticipantCount,
          groupCreatedAt: conversations.groupCreatedAt,
          groupMetadata: conversations.groupMetadata,
          createdAt: conversations.createdAt,
          updatedAt: conversations.updatedAt,
          assignedToUserId: conversations.assignedToUserId,
          unreadCount: conversations.unreadCount,
          botDisabled: conversations.botDisabled,
          disabledAt: conversations.disabledAt,
          disableDuration: conversations.disableDuration,
          disableReason: conversations.disableReason
        })
        .from(conversations)
        .leftJoin(contacts, eq(conversations.contactId, contacts.id))
        .where(
          and(
            ...whereConditions,

            or(
              isNull(contacts.phone),
              sql`NOT (LENGTH(REGEXP_REPLACE(${contacts.phone}, '[^0-9]', '', 'g')) >= 15 AND REGEXP_REPLACE(${contacts.phone}, '[^0-9]', '', 'g') ~ '^120[0-9]+$')`,
              sql`NOT (LENGTH(REGEXP_REPLACE(${contacts.identifier}, '[^0-9]', '', 'g')) >= 15 AND REGEXP_REPLACE(${contacts.identifier}, '[^0-9]', '', 'g') ~ '^120[0-9]+$')`
            )
          )
        )
        .orderBy(desc(conversations.lastMessageAt))
        .limit(limit)
        .offset(offset);

      const conversationsList = await conversationsQuery;

      return {
        conversations: conversationsList,
        total: Number(totalCount)
      };
    } catch (error) {
      console.error('Error getting conversations:', error);
      return { conversations: [], total: 0 };
    }
  }

  async getConversation(id: number): Promise<Conversation | undefined> {
    const [conversation] = await db.select().from(conversations).where(eq(conversations.id, id));
    return conversation;
  }

  async getConversationsByContact(contactId: number): Promise<Conversation[]> {

    const contact = await this.getContact(contactId);
    if (contact && (isWhatsAppGroupChatId(contact.phone) || isWhatsAppGroupChatId(contact.identifier))) {
      return []; // Return empty array for group chat contacts
    }

    return db
      .select()
      .from(conversations)
      .where(
        and(
          eq(conversations.contactId, contactId),
          or(
            eq(conversations.isGroup, false),
            isNull(conversations.isGroup)
          ),
          isNull(conversations.groupJid)
        )
      )
      .orderBy(desc(conversations.lastMessageAt));
  }

  async getConversationByContactAndChannel(contactId: number, channelId: number): Promise<Conversation | undefined> {
    const [conversation] = await db
      .select()
      .from(conversations)
      .where(
        and(
          eq(conversations.contactId, contactId),
          eq(conversations.channelId, channelId)
        )
      );
    return conversation;
  }

  async createConversation(conversation: InsertConversation): Promise<Conversation> {
    const [newConversation] = await db.insert(conversations).values(conversation).returning();
    return newConversation;
  }

  async updateConversation(id: number, updates: Partial<InsertConversation>): Promise<Conversation> {
    const [updatedConversation] = await db
      .update(conversations)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(conversations.id, id))
      .returning();
    return updatedConversation;
  }

  async getConversationByGroupJid(groupJid: string): Promise<Conversation | undefined> {
    const [conversation] = await db
      .select()
      .from(conversations)
      .where(
        and(
          eq(conversations.isGroup, true),
          eq(conversations.groupJid, groupJid)
        )
      );
    return conversation;
  }

  async upsertGroupParticipant(data: {
    conversationId: number;
    contactId?: number;
    participantJid: string;
    participantName?: string;
    isAdmin?: boolean;
    isSuperAdmin?: boolean;
    isActive?: boolean;
  }): Promise<GroupParticipant> {
    const [existing] = await db
      .select()
      .from(groupParticipants)
      .where(
        and(
          eq(groupParticipants.conversationId, data.conversationId),
          eq(groupParticipants.participantJid, data.participantJid)
        )
      );

    if (existing) {
      const [updated] = await db
        .update(groupParticipants)
        .set({
          contactId: data.contactId,
          participantName: data.participantName,
          isAdmin: data.isAdmin ?? false,
          isSuperAdmin: data.isSuperAdmin ?? false,
          isActive: data.isActive ?? true,
          updatedAt: new Date()
        })
        .where(eq(groupParticipants.id, existing.id))
        .returning();

      return updated;
    } else {
      const [created] = await db
        .insert(groupParticipants)
        .values({
          conversationId: data.conversationId,
          contactId: data.contactId,
          participantJid: data.participantJid,
          participantName: data.participantName,
          isAdmin: data.isAdmin ?? false,
          isSuperAdmin: data.isSuperAdmin ?? false,
          isActive: data.isActive ?? true
        })
        .returning();

      return created;
    }
  }

  async getMessagesByConversation(conversationId: number): Promise<Message[]> {
    return db
      .select()
      .from(messages)
      .where(eq(messages.conversationId, conversationId))
      .orderBy(messages.createdAt);
  }

  async getMessagesByConversationPaginated(conversationId: number, limit: number, offset: number): Promise<Message[]> {
    return db
      .select()
      .from(messages)
      .where(eq(messages.conversationId, conversationId))
      .orderBy(desc(messages.createdAt))
      .limit(limit)
      .offset(offset);
  }

  async getMessagesCountByConversation(conversationId: number): Promise<number> {
    const result = await db
      .select({ count: count() })
      .from(messages)
      .where(eq(messages.conversationId, conversationId));
    return Number(result[0]?.count || 0);
  }


  async getMessagesByConversationWithCompanyValidation(conversationId: number, companyId: number): Promise<Message[]> {
    return db
      .select()
      .from(messages)
      .innerJoin(conversations, eq(messages.conversationId, conversations.id))
      .where(
        and(
          eq(messages.conversationId, conversationId),
          eq(conversations.companyId, companyId)
        )
      )
      .orderBy(messages.createdAt)
      .then(results => results.map(result => result.messages));
  }

  async getMessagesByConversationPaginatedWithCompanyValidation(conversationId: number, companyId: number, limit: number, offset: number): Promise<Message[]> {
    return db
      .select()
      .from(messages)
      .innerJoin(conversations, eq(messages.conversationId, conversations.id))
      .where(
        and(
          eq(messages.conversationId, conversationId),
          eq(conversations.companyId, companyId)
        )
      )
      .orderBy(desc(messages.createdAt))
      .limit(limit)
      .offset(offset)
      .then(results => results.map(result => result.messages));
  }

  async getMessagesCountByConversationWithCompanyValidation(conversationId: number, companyId: number): Promise<number> {
    const result = await db
      .select({ count: count() })
      .from(messages)
      .innerJoin(conversations, eq(messages.conversationId, conversations.id))
      .where(
        and(
          eq(messages.conversationId, conversationId),
          eq(conversations.companyId, companyId)
        )
      );
    return Number(result[0]?.count || 0);
  }

  async getMessageById(id: number): Promise<Message | undefined> {
    const [message] = await db
      .select()
      .from(messages)
      .where(eq(messages.id, id));
    return message;
  }

  async getMessageByExternalId(externalId: string, companyId?: number): Promise<Message | undefined> {
    if (companyId) {
      const result = await db
        .select()
        .from(messages)
        .innerJoin(conversations, eq(messages.conversationId, conversations.id))
        .where(
          and(
            eq(messages.externalId, externalId),
            eq(conversations.companyId, companyId)
          )
        )
        .limit(1);

      return result[0]?.messages;
    } else {
      const result = await db
        .select()
        .from(messages)
        .where(eq(messages.externalId, externalId))
        .limit(1);

      return result[0];
    }
  }

  async createMessage(message: InsertMessage): Promise<Message> {
    try {
      const [newMessage] = await db.insert(messages).values(message).returning();

      await db
        .update(conversations)
        .set({
          lastMessageAt: new Date(),
          updatedAt: new Date()
        })
        .where(eq(conversations.id, message.conversationId));

      if (message.direction === 'inbound' && !message.isFromBot) {
        await this.updateConversationUnreadCount(message.conversationId);
      }

      return newMessage;
    } catch (error: any) {
      if (error.message?.includes('duplicate') || error.message?.includes('unique') || error.code === '23505') {
       

        if (message.externalId) {
          const existingMessage = await this.getMessageByWhatsAppId(message.conversationId, message.externalId);
          if (existingMessage) {
            return existingMessage;
          }
        }

        const recentMessages = await db
          .select()
          .from(messages)
          .where(
            and(
              eq(messages.conversationId, message.conversationId),
              eq(messages.content, message.content),
              eq(messages.direction, message.direction),
              gte(messages.createdAt, new Date(Date.now() - 60000))
            )
          )
          .limit(1);

        if (recentMessages.length > 0) {
          
          return recentMessages[0];
        }
      }

      throw error;
    }
  }

  async updateMessage(id: number, updates: Partial<InsertMessage>): Promise<Message> {
    const [updatedMessage] = await db
      .update(messages)
      .set(updates)
      .where(eq(messages.id, id))
      .returning();
    return updatedMessage;
  }

  async deleteMessage(id: number): Promise<boolean> {
    try {
      await db.delete(messages).where(eq(messages.id, id));
      return true;
    } catch (error) {
      console.error('Error deleting message:', error);
      return false;
    }
  }

  async clearConversationHistory(conversationId: number): Promise<{ success: boolean; deletedCount: number; mediaFiles: string[] }> {
    try {
      const messagesWithMedia = await db
        .select({
          id: messages.id,
          mediaUrl: messages.mediaUrl,
          metadata: messages.metadata
        })
        .from(messages)
        .where(eq(messages.conversationId, conversationId));

      const mediaFiles: string[] = [];
      messagesWithMedia.forEach(msg => {
        if (msg.mediaUrl) {
          mediaFiles.push(msg.mediaUrl);
        }

        if (msg.metadata) {
          try {
            const metadata = typeof msg.metadata === 'string'
              ? JSON.parse(msg.metadata)
              : msg.metadata;

            if (metadata.mediaUrl) {
              mediaFiles.push(metadata.mediaUrl);
            }
          } catch (e) {
          }
        }
      });

      const deleteResult = await db
        .delete(messages)
        .where(eq(messages.conversationId, conversationId));

      await db
        .update(conversations)
        .set({
          unreadCount: 0,
          lastMessageAt: new Date(),
          updatedAt: new Date()
        })
        .where(eq(conversations.id, conversationId));

      return {
        success: true,
        deletedCount: deleteResult.rowCount || 0,
        mediaFiles: Array.from(new Set(mediaFiles))
      };
    } catch (error) {
      console.error('Error clearing conversation history:', error);
      return {
        success: false,
        deletedCount: 0,
        mediaFiles: []
      };
    }
  }

  async getMessageByWhatsAppId(conversationId: number, whatsappMessageId: string): Promise<Message | undefined> {
    const [message] = await db
      .select()
      .from(messages)
      .where(
        and(
          eq(messages.conversationId, conversationId),
          eq(messages.externalId, whatsappMessageId)
        )
      );
    return message;
  }

  async markConversationAsRead(conversationId: number): Promise<void> {
    const now = new Date();

    await db
      .update(messages)
      .set({ readAt: now })
      .where(
        and(
          eq(messages.conversationId, conversationId),
          eq(messages.direction, 'inbound'),
          isNull(messages.readAt)
        )
      );

    await db
      .update(conversations)
      .set({
        unreadCount: 0,
        updatedAt: now
      })
      .where(eq(conversations.id, conversationId));
  }

  async getUnreadCount(conversationId: number): Promise<number> {
    const [result] = await db
      .select({ count: count() })
      .from(messages)
      .where(
        and(
          eq(messages.conversationId, conversationId),
          eq(messages.direction, 'inbound'),
          isNull(messages.readAt),
          or(
            eq(messages.isFromBot, false),
            isNull(messages.isFromBot)
          )
        )
      );

    return result?.count || 0;
  }

  async getAllUnreadCounts(userId: number): Promise<{ conversationId: number; unreadCount: number }[]> {
    const results = await db
      .select({
        conversationId: conversations.id,
        unreadCount: conversations.unreadCount
      })
      .from(conversations)
      .leftJoin(channelConnections, eq(conversations.channelId, channelConnections.id))
      .where(
        and(
          eq(channelConnections.userId, userId),
          gt(conversations.unreadCount, 0)
        )
      );

    return results.map(result => ({
      ...result,
      unreadCount: result.unreadCount ?? 0
    }));
  }

  async updateConversationUnreadCount(conversationId: number): Promise<void> {
    const [result] = await db
      .select({ count: count() })
      .from(messages)
      .where(
        and(
          eq(messages.conversationId, conversationId),
          eq(messages.direction, 'inbound'),
          isNull(messages.readAt),
          or(
            eq(messages.isFromBot, false),
            isNull(messages.isFromBot)
          )
        )
      );

    const unreadCount = result?.count || 0;

    await db
      .update(conversations)
      .set({
        unreadCount,
        updatedAt: new Date()
      })
      .where(eq(conversations.id, conversationId));
  }

  async getNotesByContact(contactId: number): Promise<Note[]> {
    return db
      .select()
      .from(notes)
      .where(eq(notes.contactId, contactId))
      .orderBy(desc(notes.createdAt));
  }

  async createNote(note: InsertNote): Promise<Note> {
    const [newNote] = await db.insert(notes).values(note).returning();
    return newNote;
  }

  async getFlows(userId: number): Promise<Flow[]> {
    return db
      .select()
      .from(flows)
      .where(eq(flows.userId, userId))
      .orderBy(desc(flows.updatedAt));
  }

  async getFlowsByCompany(companyId: number): Promise<Flow[]> {
    return db
      .select()
      .from(flows)
      .where(eq(flows.companyId, companyId))
      .orderBy(desc(flows.updatedAt));
  }

  async getFlow(id: number): Promise<Flow | undefined> {
    const [flow] = await db.select().from(flows).where(eq(flows.id, id));
    return flow;
  }

  async createFlow(flow: InsertFlow): Promise<Flow> {
    const [newFlow] = await db.insert(flows).values(flow).returning();
    return newFlow;
  }

  async updateFlow(id: number, updates: Partial<InsertFlow>): Promise<Flow> {
    const currentFlow = await this.getFlow(id);
    if (!currentFlow) {
      throw new Error(`Flow with id ${id} not found`);
    }

    const [updatedFlow] = await db
      .update(flows)
      .set({
        ...updates,
        updatedAt: new Date(),
        version: currentFlow.version + 1
      })
      .where(eq(flows.id, id))
      .returning();

    return updatedFlow;
  }

  async deleteFlow(id: number): Promise<boolean> {
    try {
      const assignments = await this.getFlowAssignments(undefined, id);
      for (const assignment of assignments) {
        await this.deleteFlowAssignment(assignment.id);
      }

      await db.delete(flows).where(eq(flows.id, id));
      return true;
    } catch (error) {
      console.error('Error deleting flow:', error);
      return false;
    }
  }

  async getFlowAssignments(channelId?: number, flowId?: number): Promise<FlowAssignment[]> {
    if (channelId !== undefined && flowId !== undefined) {
      return db
        .select()
        .from(flowAssignments)
        .where(
          and(
            eq(flowAssignments.channelId, channelId),
            eq(flowAssignments.flowId, flowId)
          )
        )
        .orderBy(flowAssignments.createdAt);
    } else if (channelId !== undefined) {
      return db
        .select()
        .from(flowAssignments)
        .where(eq(flowAssignments.channelId, channelId))
        .orderBy(flowAssignments.createdAt);
    } else if (flowId !== undefined) {
      return db
        .select()
        .from(flowAssignments)
        .where(eq(flowAssignments.flowId, flowId))
        .orderBy(flowAssignments.createdAt);
    } else {
      return db
        .select()
        .from(flowAssignments)
        .orderBy(flowAssignments.createdAt);
    }
  }

  async getFlowAssignment(id: number): Promise<FlowAssignment | undefined> {
    const [assignment] = await db
      .select()
      .from(flowAssignments)
      .where(eq(flowAssignments.id, id));

    return assignment;
  }

  async createFlowAssignment(assignment: InsertFlowAssignment): Promise<FlowAssignment> {
    const flow = await this.getFlow(assignment.flowId);
    if (!flow) {
      throw new Error(`Flow with id ${assignment.flowId} not found`);
    }

    const channel = await this.getChannelConnection(assignment.channelId);
    if (!channel) {
      throw new Error(`Channel with id ${assignment.channelId} not found`);
    }

    const existingAssignments = await this.getFlowAssignments(assignment.channelId, assignment.flowId);
    if (existingAssignments.length > 0) {
      throw new Error(`A flow assignment already exists for this channel and flow combination`);
    }

    const [newAssignment] = await db
      .insert(flowAssignments)
      .values(assignment)
      .returning();

    return newAssignment;
  }

  async updateFlowAssignmentStatus(id: number, isActive: boolean): Promise<FlowAssignment> {
    const assignment = await this.getFlowAssignment(id);
    if (!assignment) {
      throw new Error(`Flow assignment with id ${id} not found`);
    }

    if (isActive) {
      const otherActiveAssignments = await db
        .select()
        .from(flowAssignments)
        .where(
          and(
            eq(flowAssignments.channelId, assignment.channelId),
            eq(flowAssignments.isActive, true)
          )
        );

      for (const otherAssignment of otherActiveAssignments) {
        if (otherAssignment.id !== id) {
          await db
            .update(flowAssignments)
            .set({ isActive: false, updatedAt: new Date() })
            .where(eq(flowAssignments.id, otherAssignment.id));
        }
      }
    }

    const [updatedAssignment] = await db
      .update(flowAssignments)
      .set({ isActive, updatedAt: new Date() })
      .where(eq(flowAssignments.id, id))
      .returning();

    return updatedAssignment;
  }

  async deleteFlowAssignment(id: number): Promise<boolean> {
    try {
      await db
        .delete(flowAssignments)
        .where(eq(flowAssignments.id, id));

      return true;
    } catch (error) {
      console.error('Error deleting flow assignment:', error);
      return false;
    }
  }

  async getAllTeamMembers(): Promise<User[]> {
    try {
      return await db.select().from(users);
    } catch (error) {
      console.error('Error getting all team members:', error);
      return [];
    }
  }

  async getActiveTeamMembers(): Promise<User[]> {
    try {
      return await db.select().from(users);
    } catch (error) {
      console.error('Error getting active team members:', error);
      return [];
    }
  }

  async getTeamMembersByCompany(companyId: number): Promise<User[]> {
    try {
      return await db
        .select()
        .from(users)
        .where(eq(users.companyId, companyId))
        .orderBy(users.fullName);
    } catch (error) {
      console.error(`Error getting team members for company ${companyId}:`, error);
      return [];
    }
  }

  async getActiveTeamMembersByCompany(companyId: number): Promise<User[]> {
    try {
      return await db
        .select()
        .from(users)
        .where(eq(users.companyId, companyId))
        .orderBy(users.fullName);
    } catch (error) {
      console.error(`Error getting active team members for company ${companyId}:`, error);
      return [];
    }
  }

  async getTeamInvitations(companyId?: number): Promise<TeamInvitation[]> {
    try {
      if (companyId) {
        return await db
          .select()
          .from(teamInvitations)
          .where(eq(teamInvitations.companyId, companyId))
          .orderBy(desc(teamInvitations.createdAt));
      } else {
        return await db
          .select()
          .from(teamInvitations)
          .orderBy(desc(teamInvitations.createdAt));
      }
    } catch (error) {
      console.error('Error getting team invitations:', error);
      return [];
    }
  }

  async getTeamInvitationByEmail(email: string): Promise<TeamInvitation | undefined> {
    try {
      const [invitation] = await db
        .select()
        .from(teamInvitations)
        .where(
          and(
            eq(teamInvitations.email, email),
            eq(teamInvitations.status, 'pending')
          )
        );
      return invitation;
    } catch (error) {
      console.error('Error getting team invitation by email:', error);
      return undefined;
    }
  }

  async getTeamInvitationByToken(token: string): Promise<TeamInvitation | undefined> {
    try {
      const [invitation] = await db
        .select()
        .from(teamInvitations)
        .where(eq(teamInvitations.token, token));
      return invitation;
    } catch (error) {
      console.error('Error getting team invitation by token:', error);
      return undefined;
    }
  }

  async createTeamInvitation(invitation: InsertTeamInvitation): Promise<TeamInvitation> {
    try {
      const [newInvitation] = await db
        .insert(teamInvitations)
        .values(invitation)
        .returning();
      return newInvitation;
    } catch (error) {
      console.error('Error creating team invitation:', error);
      throw error;
    }
  }

  async updateTeamInvitationStatus(id: number, status: 'pending' | 'accepted' | 'expired' | 'revoked'): Promise<TeamInvitation> {
    try {
      if (!['pending', 'accepted', 'expired', 'revoked'].includes(status)) {
        throw new Error(`Invalid invitation status: ${status}. Must be one of: pending, accepted, expired, revoked`);
      }

      const existingInvitation = await db
        .select()
        .from(teamInvitations)
        .where(eq(teamInvitations.id, id))
        .limit(1);

      if (!existingInvitation || existingInvitation.length === 0) {
        throw new Error(`Team invitation with id ${id} not found`);
      }

      const [updatedInvitation] = await db
        .update(teamInvitations)
        .set({
          status,
          updatedAt: new Date()
        })
        .where(eq(teamInvitations.id, id))
        .returning();

      if (!updatedInvitation) {
        throw new Error(`Failed to update team invitation with id ${id}`);
      }

      return updatedInvitation;
    } catch (error) {
      console.error('Error updating team invitation status:', error);
      throw error;
    }
  }

  async deleteDealActivity(id: number): Promise<boolean> {
    try {
      await db
        .delete(dealActivities)
        .where(eq(dealActivities.id, id));
      return true;
    } catch (error) {
      console.error(`Error deleting deal activity with ID ${id}:`, error);
      return false;
    }
  }

  async deleteTeamInvitation(id: number): Promise<boolean> {
    try {
      await db
        .delete(teamInvitations)
        .where(eq(teamInvitations.id, id));

      return true;
    } catch (error) {
      console.error('Error deleting team invitation:', error);
      return false;
    }
  }

  async getConversationsCount(): Promise<number> {
    try {
      const result = await db
        .select({ count: sql`count(*)` })
        .from(conversations)
        .leftJoin(contacts, eq(conversations.contactId, contacts.id))
        .where(
          and(
            or(
              eq(conversations.isGroup, false),
              isNull(conversations.isGroup)
            ),
            isNull(conversations.groupJid),
            or(
              isNull(contacts.phone),
              sql`NOT (LENGTH(REGEXP_REPLACE(${contacts.phone}, '[^0-9]', '', 'g')) >= 15 AND REGEXP_REPLACE(${contacts.phone}, '[^0-9]', '', 'g') ~ '^120[0-9]+$')`,
              sql`NOT (LENGTH(REGEXP_REPLACE(${contacts.identifier}, '[^0-9]', '', 'g')) >= 15 AND REGEXP_REPLACE(${contacts.identifier}, '[^0-9]', '', 'g') ~ '^120[0-9]+$')`
            )
          )
        );
      return parseInt(String(result[0].count));
    } catch (error) {
      console.error('Error getting conversations count:', error);
      return 0;
    }
  }

  async getConversationsCountByCompany(companyId: number): Promise<number> {
    try {
      const result = await db
        .select({ count: sql`count(*)` })
        .from(conversations)
        .leftJoin(contacts, eq(conversations.contactId, contacts.id))
        .where(
          and(
            eq(conversations.companyId, companyId),
            or(
              eq(conversations.isGroup, false),
              isNull(conversations.isGroup)
            ),
            isNull(conversations.groupJid),
            or(
              isNull(contacts.phone),
              sql`NOT (LENGTH(REGEXP_REPLACE(${contacts.phone}, '[^0-9]', '', 'g')) >= 15 AND REGEXP_REPLACE(${contacts.phone}, '[^0-9]', '', 'g') ~ '^120[0-9]+$')`,
              sql`NOT (LENGTH(REGEXP_REPLACE(${contacts.identifier}, '[^0-9]', '', 'g')) >= 15 AND REGEXP_REPLACE(${contacts.identifier}, '[^0-9]', '', 'g') ~ '^120[0-9]+$')`
            )
          )
        );
      return parseInt(String(result[0].count));
    } catch (error) {
      console.error('Error getting conversations count by company:', error);
      return 0;
    }
  }

  async getMessagesCount(): Promise<number> {
    try {
      const result = await db
        .select({ count: sql`count(*)` })
        .from(messages);
      return parseInt(String(result[0].count));
    } catch (error) {
      console.error('Error getting messages count:', error);
      return 0;
    }
  }

  async getMessagesCountByCompany(companyId: number): Promise<number> {
    try {
      const result = await db
        .select({ count: sql`count(*)` })
        .from(messages)
        .innerJoin(conversations, eq(messages.conversationId, conversations.id))
        .where(eq(conversations.companyId, companyId));
      return parseInt(String(result[0].count));
    } catch (error) {
      console.error('Error getting messages count by company:', error);
      return 0;
    }
  }

  async getConversationsByDay(days: number): Promise<any[]> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      startDate.setHours(0, 0, 0, 0);

      const result = await db
        .select({
          date: sql`date_trunc('day', ${conversations.createdAt})`,
          channelType: conversations.channelType,
          count: sql`count(*)`
        })
        .from(conversations)
        .where(sql`${conversations.createdAt} >= ${startDate}`)
        .groupBy(sql`date_trunc('day', ${conversations.createdAt})`, conversations.channelType)
        .orderBy(sql`date_trunc('day', ${conversations.createdAt})`);

      const dateMap = new Map<string, Record<string, any>>();

      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - (days - 1 - i));
        date.setHours(0, 0, 0, 0);
        const dateStr = date.toISOString().split('T')[0];
        dateMap.set(dateStr, {
          name: dateStr,
          whatsapp_official: 0,
          whatsapp_unofficial: 0,
          messenger: 0,
          instagram: 0
        });
      }

      result.forEach((row) => {
        if (row.date) {
          const date = new Date(String(row.date)).toISOString().split('T')[0];
          const channelType = String(row.channelType);
          const count = parseInt(String(row.count));

          if (dateMap.has(date)) {
            const dayData = dateMap.get(date);
            if (dayData) {
              dayData[channelType] = count;
            }
          }
        }
      });

      return Array.from(dateMap.values());
    } catch (error) {
      console.error('Error getting conversations by day:', error);
      return [];
    }
  }

  async getConversationsByDayByCompany(companyId: number, days: number): Promise<any[]> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      startDate.setHours(0, 0, 0, 0);

      const result = await db
        .select({
          date: sql`date_trunc('day', ${conversations.createdAt})`,
          channelType: conversations.channelType,
          count: sql`count(*)`
        })
        .from(conversations)
        .where(
          and(
            sql`${conversations.createdAt} >= ${startDate}`,
            eq(conversations.companyId, companyId)
          )
        )
        .groupBy(sql`date_trunc('day', ${conversations.createdAt})`, conversations.channelType)
        .orderBy(sql`date_trunc('day', ${conversations.createdAt})`);

      const dateMap = new Map<string, Record<string, any>>();

      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - (days - 1 - i));
        date.setHours(0, 0, 0, 0);
        const dateStr = date.toISOString().split('T')[0];
        dateMap.set(dateStr, {
          name: dateStr,
          whatsapp_official: 0,
          whatsapp_unofficial: 0,
          messenger: 0,
          instagram: 0
        });
      }

      result.forEach((row) => {
        if (row.date) {
          const date = new Date(String(row.date)).toISOString().split('T')[0];
          const channelType = String(row.channelType);
          const count = parseInt(String(row.count));

          if (dateMap.has(date)) {
            const dayData = dateMap.get(date);
            if (dayData) {
              dayData[channelType] = count;
            }
          }
        }
      });

      return Array.from(dateMap.values());
    } catch (error) {
      console.error('Error getting conversations by day by company:', error);
      return [];
    }
  }

  async getMessagesByChannel(): Promise<any[]> {
    try {
      const result = await db
        .select({
          channelType: conversations.channelType,
          count: sql`count(*)`
        })
        .from(messages)
        .innerJoin(conversations, eq(messages.conversationId, conversations.id))
        .groupBy(conversations.channelType);

      return result.map(row => ({
        name: String(row.channelType),
        value: parseInt(String(row.count))
      }));
    } catch (error) {
      console.error('Error getting messages by channel:', error);
      return [];
    }
  }

  async getMessagesByChannelByCompany(companyId: number): Promise<any[]> {
    try {
      const result = await db
        .select({
          channelType: conversations.channelType,
          count: sql`count(*)`
        })
        .from(messages)
        .innerJoin(conversations, eq(messages.conversationId, conversations.id))
        .where(eq(conversations.companyId, companyId))
        .groupBy(conversations.channelType);

      return result.map(row => ({
        name: String(row.channelType),
        value: parseInt(String(row.count))
      }));
    } catch (error) {
      console.error('Error getting messages by channel by company:', error);
      return [];
    }
  }









  async createFlowSession(session: any) {
    try {
      return await db.insert(flowSessions).values(session).returning();
    } catch (error) {
      console.error('Error creating flow session:', error);
      throw error;
    }
  }

  async updateFlowSession(sessionId: string, updates: any) {
    try {
      return await db.update(flowSessions)
        .set({ ...updates, updatedAt: new Date() })
        .where(eq(flowSessions.sessionId, sessionId))
        .returning();
    } catch (error) {
      console.error('Error updating flow session:', error);
      throw error;
    }
  }

  async getFlowSession(sessionId: string) {
    try {
      const result = await db.select()
        .from(flowSessions)
        .where(eq(flowSessions.sessionId, sessionId))
        .limit(1);
      return result[0] || null;
    } catch (error) {
      console.error('Error getting flow session:', error);
      return null;
    }
  }

  async getActiveFlowSessionsForConversation(conversationId: number) {
    try {
      return await db.select()
        .from(flowSessions)
        .where(
          and(
            eq(flowSessions.conversationId, conversationId),
            sql`${flowSessions.status} IN ('active', 'waiting', 'paused')`
          )
        );
    } catch (error) {
      console.error('Error getting active flow sessions:', error);
      return [];
    }
  }

  async expireFlowSession(sessionId: string) {
    try {
      return await db.update(flowSessions)
        .set({ status: 'timeout', updatedAt: new Date() })
        .where(eq(flowSessions.sessionId, sessionId))
        .returning();
    } catch (error) {
      console.error('Error expiring flow session:', error);
      throw error;
    }
  }

  async createFlowSessionVariable(variable: any) {
    try {
      return await db.insert(flowSessionVariables).values(variable).returning();
    } catch (error) {
      console.error('Error creating flow session variable:', error);
      throw error;
    }
  }

  async upsertFlowSessionVariable(variable: any) {
    try {
      return await db.insert(flowSessionVariables)
        .values(variable)
        .onConflictDoUpdate({
          target: [flowSessionVariables.sessionId, flowSessionVariables.variableKey],
          set: {
            variableValue: variable.variableValue,
            variableType: variable.variableType,
            scope: variable.scope,
            nodeId: variable.nodeId,
            isEncrypted: variable.isEncrypted,
            expiresAt: variable.expiresAt,
            updatedAt: new Date()
          }
        })
        .returning();
    } catch (error) {
      console.error('Error upserting flow session variable:', error);
      throw error;
    }
  }

  async getFlowSessionVariables(sessionId: string) {
    try {
      return await db.select()
        .from(flowSessionVariables)
        .where(eq(flowSessionVariables.sessionId, sessionId));
    } catch (error) {
      console.error('Error getting flow session variables:', error);
      return [];
    }
  }

  async getFlowSessionVariable(sessionId: string, variableKey: string) {
    try {
      const result = await db.select()
        .from(flowSessionVariables)
        .where(
          and(
            eq(flowSessionVariables.sessionId, sessionId),
            eq(flowSessionVariables.variableKey, variableKey)
          )
        )
        .limit(1);
      return result[0] || null;
    } catch (error) {
      console.error('Error getting flow session variable:', error);
      return null;
    }
  }

  async deleteFlowSessionVariable(sessionId: string, variableKey: string) {
    try {
      return await db.delete(flowSessionVariables)
        .where(
          and(
            eq(flowSessionVariables.sessionId, sessionId),
            eq(flowSessionVariables.variableKey, variableKey)
          )
        );
    } catch (error) {
      console.error('Error deleting flow session variable:', error);
      throw error;
    }
  }

  async createFlowSessionCursor(cursor: any) {
    try {
      return await db.insert(flowSessionCursors).values(cursor).returning();
    } catch (error) {
      console.error('Error creating flow session cursor:', error);
      throw error;
    }
  }

  async updateFlowSessionCursor(sessionId: string, updates: any) {
    try {
      return await db.update(flowSessionCursors)
        .set({ ...updates, updatedAt: new Date() })
        .where(eq(flowSessionCursors.sessionId, sessionId))
        .returning();
    } catch (error) {
      console.error('Error updating flow session cursor:', error);
      throw error;
    }
  }

  async getFlowSessionCursor(sessionId: string) {
    try {
      const result = await db.select()
        .from(flowSessionCursors)
        .where(eq(flowSessionCursors.sessionId, sessionId))
        .limit(1);
      return result[0] || null;
    } catch (error) {
      console.error('Error getting flow session cursor:', error);
      return null;
    }
  }

  async createFollowUpSchedule(schedule: any) {
    try {
      return await db.insert(followUpSchedules).values(schedule).returning();
    } catch (error) {
      console.error('Error creating follow-up schedule:', error);
      throw error;
    }
  }

  async updateFollowUpSchedule(scheduleId: string, updates: any) {
    try {
      return await db.update(followUpSchedules)
        .set({ ...updates, updatedAt: new Date() })
        .where(eq(followUpSchedules.scheduleId, scheduleId))
        .returning();
    } catch (error) {
      console.error('Error updating follow-up schedule:', error);
      throw error;
    }
  }

  async getFollowUpSchedule(scheduleId: string) {
    try {
      const result = await db.select()
        .from(followUpSchedules)
        .where(eq(followUpSchedules.scheduleId, scheduleId))
        .limit(1);
      return result[0] || null;
    } catch (error) {
      console.error('Error getting follow-up schedule:', error);
      return null;
    }
  }

  async getFollowUpSchedulesByConversation(conversationId: number) {
    try {
      return await db.select()
        .from(followUpSchedules)
        .where(eq(followUpSchedules.conversationId, conversationId))
        .orderBy(followUpSchedules.scheduledFor);
    } catch (error) {
      console.error('Error getting follow-up schedules by conversation:', error);
      return [];
    }
  }

  async getFollowUpSchedulesByContact(contactId: number) {
    try {
      return await db.select()
        .from(followUpSchedules)
        .where(eq(followUpSchedules.contactId, contactId))
        .orderBy(followUpSchedules.scheduledFor);
    } catch (error) {
      console.error('Error getting follow-up schedules by contact:', error);
      return [];
    }
  }

  async getScheduledFollowUps(limit: number = 100) {
    try {
      const results = await db.select()
        .from(followUpSchedules)
        .where(
          and(
            eq(followUpSchedules.status, 'scheduled'),
            sql`${followUpSchedules.scheduledFor} <= NOW()`
          )
        )
        .orderBy(followUpSchedules.scheduledFor)
        .limit(limit);

      const dueNow = results.filter((followUp: any) => {
        const scheduledTime = new Date(followUp.scheduledFor);
        const now = new Date();
        return scheduledTime.getTime() <= now.getTime();
      });

      return dueNow;
    } catch (error) {
      console.error('Error getting scheduled follow-ups:', error);
      return [];
    }
  }

  async cancelFollowUpSchedule(scheduleId: string) {
    try {
      return await db.update(followUpSchedules)
        .set({
          status: 'cancelled',
          updatedAt: new Date()
        })
        .where(eq(followUpSchedules.scheduleId, scheduleId))
        .returning();
    } catch (error) {
      console.error('Error cancelling follow-up schedule:', error);
      throw error;
    }
  }

  async createFollowUpTemplate(template: any) {
    try {
      return await db.insert(followUpTemplates).values(template).returning();
    } catch (error) {
      console.error('Error creating follow-up template:', error);
      throw error;
    }
  }

  async updateFollowUpTemplate(id: number, updates: any) {
    try {
      return await db.update(followUpTemplates)
        .set({ ...updates, updatedAt: new Date() })
        .where(eq(followUpTemplates.id, id))
        .returning();
    } catch (error) {
      console.error('Error updating follow-up template:', error);
      throw error;
    }
  }

  async getFollowUpTemplate(id: number) {
    try {
      const result = await db.select()
        .from(followUpTemplates)
        .where(eq(followUpTemplates.id, id))
        .limit(1);
      return result[0] || null;
    } catch (error) {
      console.error('Error getting follow-up template:', error);
      return null;
    }
  }

  async getFollowUpTemplatesByCompany(companyId: number) {
    try {
      return await db.select()
        .from(followUpTemplates)
        .where(eq(followUpTemplates.companyId, companyId))
        .orderBy(followUpTemplates.name);
    } catch (error) {
      console.error('Error getting follow-up templates by company:', error);
      return [];
    }
  }

  async deleteFollowUpTemplate(id: number): Promise<boolean> {
    try {
      await db.delete(followUpTemplates)
        .where(eq(followUpTemplates.id, id));
      return true;
    } catch (error) {
      console.error('Error deleting follow-up template:', error);
      return false;
    }
  }

  async createFollowUpExecutionLog(log: any) {
    try {
      return await db.insert(followUpExecutionLog).values(log).returning();
    } catch (error) {
      console.error('Error creating follow-up execution log:', error);
      throw error;
    }
  }

  async getFollowUpExecutionLogs(scheduleId: string) {
    try {
      return await db.select()
        .from(followUpExecutionLog)
        .where(eq(followUpExecutionLog.scheduleId, scheduleId))
        .orderBy(followUpExecutionLog.executedAt);
    } catch (error) {
      console.error('Error getting follow-up execution logs:', error);
      return [];
    }
  }

  async createFlowExecution(data: {
    executionId: string;
    flowId: number;
    conversationId: number;
    contactId: number;
    companyId?: number;
    triggerNodeId: string;
    contextData?: any;
  }): Promise<number> {
    try {
      const [result] = await db.insert(flowExecutions).values({
        executionId: data.executionId,
        flowId: data.flowId,
        conversationId: data.conversationId,
        contactId: data.contactId,
        companyId: data.companyId,
        triggerNodeId: data.triggerNodeId,
        contextData: data.contextData || {},
        status: 'running',
        executionPath: [data.triggerNodeId],
        startedAt: new Date(),
        lastActivityAt: new Date()
      }).returning({ id: flowExecutions.id });

      
      return result.id;
    } catch (error) {
      console.error('Error creating flow execution:', error);
      throw error;
    }
  }

  async updateFlowExecution(executionId: string, data: {
    status?: string;
    currentNodeId?: string;
    executionPath?: string[];
    contextData?: any;
    completedAt?: Date;
    totalDurationMs?: number;
    completionRate?: number;
    errorMessage?: string;
  }): Promise<void> {
    try {
      const updateData: any = {
        lastActivityAt: new Date(),
        updatedAt: new Date()
      };

      if (data.status) updateData.status = data.status;
      if (data.currentNodeId) updateData.currentNodeId = data.currentNodeId;
      if (data.executionPath) updateData.executionPath = data.executionPath;
      if (data.contextData) updateData.contextData = data.contextData;
      if (data.completedAt) updateData.completedAt = data.completedAt;
      if (data.totalDurationMs) updateData.totalDurationMs = data.totalDurationMs;
      if (data.completionRate) updateData.completionRate = data.completionRate.toString();
      if (data.errorMessage) updateData.errorMessage = data.errorMessage;

      await db.update(flowExecutions)
        .set(updateData)
        .where(eq(flowExecutions.executionId, executionId));

      
    } catch (error) {
      console.error('Error updating flow execution:', error);
      throw error;
    }
  }

  async createFlowStepExecution(data: {
    flowExecutionId: number;
    nodeId: string;
    nodeType: string;
    stepOrder: number;
    inputData?: any;
  }): Promise<number> {
    try {
      const [result] = await db.insert(flowStepExecutions).values({
        flowExecutionId: data.flowExecutionId,
        nodeId: data.nodeId,
        nodeType: data.nodeType,
        stepOrder: data.stepOrder,
        inputData: data.inputData || {},
        status: 'running',
        startedAt: new Date()
      }).returning({ id: flowStepExecutions.id });

      
      return result.id;
    } catch (error) {
      console.error('Error creating flow step execution:', error);
      throw error;
    }
  }

  async updateFlowStepExecution(stepId: number, data: {
    status?: string;
    completedAt?: Date;
    durationMs?: number;
    outputData?: any;
    errorMessage?: string;
  }): Promise<void> {
    try {
      const updateData: any = {};

      if (data.status) updateData.status = data.status;
      if (data.completedAt) updateData.completedAt = data.completedAt;
      if (data.durationMs) updateData.durationMs = data.durationMs;
      if (data.outputData) updateData.outputData = data.outputData;
      if (data.errorMessage) updateData.errorMessage = data.errorMessage;

      await db.update(flowStepExecutions)
        .set(updateData)
        .where(eq(flowStepExecutions.id, stepId));

      
    } catch (error) {
      console.error('Error updating flow step execution:', error);
      throw error;
    }
  }



  async getFlowDropoffAnalysis(flowId: number, companyId?: number): Promise<Array<{
    nodeId: string;
    nodeType: string;
    dropoffCount: number;
    dropoffRate: number;
  }>> {
    try {
      const whereConditions = [eq(flowExecutions.flowId, flowId)];

      if (companyId) {
        whereConditions.push(eq(flowExecutions.companyId, companyId));
      }

      const query = db
        .select({
          nodeId: flowStepExecutions.nodeId,
          nodeType: flowStepExecutions.nodeType,
          dropoffCount: sql`COUNT(CASE WHEN ${flowStepExecutions.status} IN ('failed', 'skipped') THEN 1 END)`,
          totalCount: sql`COUNT(${flowStepExecutions.id})`
        })
        .from(flowStepExecutions)
        .innerJoin(flowExecutions, eq(flowStepExecutions.flowExecutionId, flowExecutions.id))
        .where(and(...whereConditions))
        .groupBy(flowStepExecutions.nodeId, flowStepExecutions.nodeType);

      const results = await query;

      return results.map(row => {
        const dropoffCount = Number(row.dropoffCount);
        const totalCount = Number(row.totalCount);
        const dropoffRate = totalCount > 0 ? Math.round((dropoffCount / totalCount) * 100) : 0;

        return {
          nodeId: row.nodeId,
          nodeType: row.nodeType,
          dropoffCount,
          dropoffRate
        };
      });
    } catch (error) {
      console.error('Error getting flow dropoff analysis:', error);
      return [];
    }
  }



  async getAppSetting(key: string): Promise<AppSetting | undefined> {
    try {
      const [setting] = await db
        .select()
        .from(appSettings)
        .where(eq(appSettings.key, key));

      return setting;
    } catch (error) {
      console.error(`Error getting app setting with key ${key}:`, error);
      return undefined;
    }
  }

  async getAllAppSettings(): Promise<AppSetting[]> {
    try {
      return await db
        .select()
        .from(appSettings)
        .orderBy(appSettings.key);
    } catch (error) {
      console.error("Error getting all app settings:", error);
      return [];
    }
  }

  async saveAppSetting(key: string, value: unknown): Promise<AppSetting> {
    try {
      if (!key) {
        throw new Error('Setting key is required');
      }

      if (value === undefined || value === null) {
        throw new Error('Setting value is required');
      }

      const existingSetting = await this.getAppSetting(key);

      if (existingSetting) {
        const [updatedSetting] = await db
          .update(appSettings)
          .set({
            value,
            updatedAt: new Date()
          })
          .where(eq(appSettings.key, key))
          .returning();

        if (!updatedSetting) {
          throw new Error(`Failed to update setting with key ${key}`);
        }

        return updatedSetting;
      } else {
        const [newSetting] = await db
          .insert(appSettings)
          .values({
            key,
            value
          })
          .returning();

        if (!newSetting) {
          throw new Error(`Failed to create setting with key ${key}`);
        }

        return newSetting;
      }
    } catch (error) {
      console.error(`Error saving app setting with key ${key}:`, error);
      throw error;
    }
  }

  async deleteAppSetting(key: string): Promise<boolean> {
    try {
      await db
        .delete(appSettings)
        .where(eq(appSettings.key, key));

      return true;
    } catch (error) {
      console.error(`Error deleting app setting with key ${key}:`, error);
      return false;
    }
  }

  async getAllPaymentTransactions(): Promise<PaymentTransaction[]> {
    try {
      const result = await db
        .select()
        .from(paymentTransactions)
        .orderBy(desc(paymentTransactions.createdAt));

      return result.map(transaction => ({
        ...transaction,
        amount: Number(transaction.amount),
        metadata: transaction.metadata as Record<string, unknown> | undefined
      })) as PaymentTransaction[];
    } catch (error) {
      console.error("Error getting all payment transactions:", error);
      return [];
    }
  }

  async getPaymentTransactionsByCompany(companyId: number): Promise<PaymentTransaction[]> {
    try {
      const result = await db
        .select()
        .from(paymentTransactions)
        .where(eq(paymentTransactions.companyId, companyId))
        .orderBy(desc(paymentTransactions.createdAt));

      return result.map(transaction => this.mapToPaymentTransaction(transaction));
    } catch (error) {
      console.error(`Error getting payment transactions for company ${companyId}:`, error);
      return [];
    }
  }

  async getPaymentTransaction(id: number): Promise<PaymentTransaction | undefined> {
    try {
      const [transaction] = await db
        .select()
        .from(paymentTransactions)
        .where(eq(paymentTransactions.id, id));

      if (!transaction) return undefined;

      return this.mapToPaymentTransaction(transaction);
    } catch (error) {
      console.error(`Error getting payment transaction with ID ${id}:`, error);
      return undefined;
    }
  }

  async createPaymentTransaction(transaction: InsertPaymentTransaction): Promise<PaymentTransaction> {
    try {
      const [newTransaction] = await db
        .insert(paymentTransactions)
        .values(transaction)
        .returning();

      return this.mapToPaymentTransaction(newTransaction);
    } catch (error) {
      console.error("Error creating payment transaction:", error);
      throw error;
    }
  }

  async updatePaymentTransaction(id: number, updates: Partial<InsertPaymentTransaction>): Promise<PaymentTransaction> {
    try {
      const [updatedTransaction] = await db
        .update(paymentTransactions)
        .set({
          ...updates,
          updatedAt: new Date()
        })
        .where(eq(paymentTransactions.id, id))
        .returning();

      if (!updatedTransaction) {
        throw new Error(`Payment transaction with ID ${id} not found`);
      }

      return this.mapToPaymentTransaction(updatedTransaction);
    } catch (error) {
      console.error(`Error updating payment transaction with ID ${id}:`, error);
      throw error;
    }
  }

  async getAllLanguages(): Promise<Language[]> {
    try {
      return await db
        .select()
        .from(languages)
        .orderBy(languages.name);
    } catch (error) {
      console.error("Error getting all languages:", error);
      return [];
    }
  }

  async getLanguage(id: number): Promise<Language | undefined> {
    try {
      const [language] = await db
        .select()
        .from(languages)
        .where(eq(languages.id, id));
      return language;
    } catch (error) {
      console.error(`Error getting language with ID ${id}:`, error);
      return undefined;
    }
  }

  async getLanguageByCode(code: string): Promise<Language | undefined> {
    try {
      const [language] = await db
        .select()
        .from(languages)
        .where(eq(languages.code, code));
      return language;
    } catch (error) {
      console.error(`Error getting language with code ${code}:`, error);
      return undefined;
    }
  }

  async getDefaultLanguage(): Promise<Language | undefined> {
    try {
      const [language] = await db
        .select()
        .from(languages)
        .where(eq(languages.isDefault, true));
      return language;
    } catch (error) {
      console.error("Error getting default language:", error);
      return undefined;
    }
  }

  async createLanguage(language: InsertLanguage): Promise<Language> {
    try {
      const [newLanguage] = await db
        .insert(languages)
        .values({
          ...language,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning();
      return newLanguage;
    } catch (error) {
      console.error("Error creating language:", error);
      throw error;
    }
  }

  async updateLanguage(id: number, updates: Partial<InsertLanguage>): Promise<Language> {
    try {
      const [updatedLanguage] = await db
        .update(languages)
        .set({
          ...updates,
          updatedAt: new Date()
        })
        .where(eq(languages.id, id))
        .returning();

      if (!updatedLanguage) {
        throw new Error(`Language with ID ${id} not found`);
      }

      return updatedLanguage;
    } catch (error) {
      console.error("Error updating language:", error);
      throw error;
    }
  }

  async deleteLanguage(id: number): Promise<boolean> {
    try {
      const language = await this.getLanguage(id);
      if (language?.isDefault) {
        throw new Error("Cannot delete the default language");
      }

      await db
        .delete(translations)
        .where(eq(translations.languageId, id));

      await db
        .delete(languages)
        .where(eq(languages.id, id));

      return true;
    } catch (error) {
      console.error(`Error deleting language with ID ${id}:`, error);
      return false;
    }
  }

  async setDefaultLanguage(id: number): Promise<boolean> {
    try {
      await db
        .update(languages)
        .set({ isDefault: false })
        .where(sql`true`);

      const [updatedLanguage] = await db
        .update(languages)
        .set({ isDefault: true })
        .where(eq(languages.id, id))
        .returning();

      if (!updatedLanguage) {
        throw new Error(`Language with ID ${id} not found`);
      }

      return true;
    } catch (error) {
      console.error(`Error setting language ${id} as default:`, error);
      return false;
    }
  }

  async getAllNamespaces(): Promise<TranslationNamespace[]> {
    try {
      return await db
        .select()
        .from(translationNamespaces)
        .orderBy(translationNamespaces.name);
    } catch (error) {
      console.error("Error getting all namespaces:", error);
      return [];
    }
  }

  async getNamespace(id: number): Promise<TranslationNamespace | undefined> {
    try {
      const [namespace] = await db
        .select()
        .from(translationNamespaces)
        .where(eq(translationNamespaces.id, id));
      return namespace;
    } catch (error) {
      console.error(`Error getting namespace with ID ${id}:`, error);
      return undefined;
    }
  }

  async getNamespaceByName(name: string): Promise<TranslationNamespace | undefined> {
    try {
      const [namespace] = await db
        .select()
        .from(translationNamespaces)
        .where(eq(translationNamespaces.name, name));
      return namespace;
    } catch (error) {
      console.error(`Error getting namespace with name ${name}:`, error);
      return undefined;
    }
  }

  async createNamespace(namespace: InsertTranslationNamespace): Promise<TranslationNamespace> {
    try {
      const [newNamespace] = await db
        .insert(translationNamespaces)
        .values({
          ...namespace,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning();
      return newNamespace;
    } catch (error) {
      console.error("Error creating namespace:", error);
      throw error;
    }
  }

  async updateNamespace(id: number, updates: Partial<InsertTranslationNamespace>): Promise<TranslationNamespace> {
    try {
      const [updatedNamespace] = await db
        .update(translationNamespaces)
        .set({
          ...updates,
          updatedAt: new Date()
        })
        .where(eq(translationNamespaces.id, id))
        .returning();

      if (!updatedNamespace) {
        throw new Error(`Namespace with ID ${id} not found`);
      }

      return updatedNamespace;
    } catch (error) {
      console.error("Error updating namespace:", error);
      throw error;
    }
  }

  async deleteNamespace(id: number): Promise<boolean> {
    try {
      const keys = await this.getAllKeys(id);

      for (const key of keys) {
        await db
          .delete(translations)
          .where(eq(translations.keyId, key.id));
      }

      await db
        .delete(translationKeys)
        .where(eq(translationKeys.namespaceId, id));

      await db
        .delete(translationNamespaces)
        .where(eq(translationNamespaces.id, id));

      return true;
    } catch (error) {
      console.error(`Error deleting namespace with ID ${id}:`, error);
      return false;
    }
  }

  async getAllKeys(namespaceId?: number): Promise<TranslationKey[]> {
    try {
      if (namespaceId) {
        return await db
          .select()
          .from(translationKeys)
          .where(eq(translationKeys.namespaceId, namespaceId))
          .orderBy(translationKeys.key);
      } else {
        return await db
          .select()
          .from(translationKeys)
          .orderBy(translationKeys.key);
      }
    } catch (error) {
      console.error("Error getting all keys:", error);
      return [];
    }
  }

  async getKey(id: number): Promise<TranslationKey | undefined> {
    try {
      const [key] = await db
        .select()
        .from(translationKeys)
        .where(eq(translationKeys.id, id));
      return key;
    } catch (error) {
      console.error(`Error getting key with ID ${id}:`, error);
      return undefined;
    }
  }

  async getKeyByNameAndKey(namespaceId: number, key: string): Promise<TranslationKey | undefined> {
    try {
      const [translationKey] = await db
        .select()
        .from(translationKeys)
        .where(
          and(
            eq(translationKeys.namespaceId, namespaceId),
            eq(translationKeys.key, key)
          )
        );
      return translationKey;
    } catch (error) {
      console.error(`Error getting key ${key} in namespace ${namespaceId}:`, error);
      return undefined;
    }
  }

  async createKey(key: InsertTranslationKey): Promise<TranslationKey> {
    try {
      const [newKey] = await db
        .insert(translationKeys)
        .values({
          ...key,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning();
      return newKey;
    } catch (error) {
      console.error("Error creating key:", error);
      throw error;
    }
  }

  async updateKey(id: number, updates: Partial<InsertTranslationKey>): Promise<TranslationKey> {
    try {
      const [updatedKey] = await db
        .update(translationKeys)
        .set({
          ...updates,
          updatedAt: new Date()
        })
        .where(eq(translationKeys.id, id))
        .returning();

      if (!updatedKey) {
        throw new Error(`Key with ID ${id} not found`);
      }

      return updatedKey;
    } catch (error) {
      console.error("Error updating key:", error);
      throw error;
    }
  }

  async deleteKey(id: number): Promise<boolean> {
    try {
      await db
        .delete(translations)
        .where(eq(translations.keyId, id));

      await db
        .delete(translationKeys)
        .where(eq(translationKeys.id, id));

      return true;
    } catch (error) {
      console.error(`Error deleting key with ID ${id}:`, error);
      return false;
    }
  }

  async getAllTranslations(languageId?: number, keyId?: number): Promise<Translation[]> {
    try {
      if (languageId && keyId) {
        return await db
          .select()
          .from(translations)
          .where(
            and(
              eq(translations.languageId, languageId),
              eq(translations.keyId, keyId)
            )
          );
      } else if (languageId) {
        return await db
          .select()
          .from(translations)
          .where(eq(translations.languageId, languageId));
      } else if (keyId) {
        return await db
          .select()
          .from(translations)
          .where(eq(translations.keyId, keyId));
      } else {
        return await db
          .select()
          .from(translations);
      }
    } catch (error) {
      console.error("Error getting translations:", error);
      return [];
    }
  }

  async getTranslation(id: number): Promise<Translation | undefined> {
    try {
      const [translation] = await db
        .select()
        .from(translations)
        .where(eq(translations.id, id));
      return translation;
    } catch (error) {
      console.error(`Error getting translation with ID ${id}:`, error);
      return undefined;
    }
  }

  async getTranslationByKeyAndLanguage(keyId: number, languageId: number): Promise<Translation | undefined> {
    try {
      const [translation] = await db
        .select()
        .from(translations)
        .where(
          and(
            eq(translations.keyId, keyId),
            eq(translations.languageId, languageId)
          )
        );
      return translation;
    } catch (error) {
      console.error(`Error getting translation for key ${keyId} and language ${languageId}:`, error);
      return undefined;
    }
  }

  async createTranslation(translation: InsertTranslation): Promise<Translation> {
    try {
      const existingTranslation = await this.getTranslationByKeyAndLanguage(
        translation.keyId,
        translation.languageId
      );

      if (existingTranslation) {
        return await this.updateTranslation(existingTranslation.id, { value: translation.value });
      }

      const [newTranslation] = await db
        .insert(translations)
        .values({
          ...translation,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning();
      return newTranslation;
    } catch (error) {
      console.error("Error creating translation:", error);
      throw error;
    }
  }

  async updateTranslation(id: number, updates: Partial<InsertTranslation>): Promise<Translation> {
    try {
      const [updatedTranslation] = await db
        .update(translations)
        .set({
          ...updates,
          updatedAt: new Date()
        })
        .where(eq(translations.id, id))
        .returning();

      if (!updatedTranslation) {
        throw new Error(`Translation with ID ${id} not found`);
      }

      return updatedTranslation;
    } catch (error) {
      console.error("Error updating translation:", error);
      throw error;
    }
  }

  async deleteTranslation(id: number): Promise<boolean> {
    try {
      await db
        .delete(translations)
        .where(eq(translations.id, id));

      return true;
    } catch (error) {
      console.error(`Error deleting translation with ID ${id}:`, error);
      return false;
    }
  }

  async getTranslationsForLanguage(languageCode: string): Promise<Array<{id: number, key: string, value: string}>> {
    try {
      const language = await this.getLanguageByCode(languageCode);
      if (!language) {
        throw new Error(`Language with code ${languageCode} not found`);
      }

      const result = await db
        .select({
          id: translations.id,
          key: translationKeys.key,
          value: translations.value
        })
        .from(translations)
        .innerJoin(translationKeys, eq(translations.keyId, translationKeys.id))
        .where(eq(translations.languageId, language.id))
        .orderBy(translationKeys.key);

      return result;
    } catch (error) {
      console.error(`Error getting translations for language ${languageCode}:`, error);
      return [];
    }
  }

  async getTranslationsForLanguageByNamespace(languageCode: string): Promise<Record<string, Record<string, string>>> {
    try {
      const language = await this.getLanguageByCode(languageCode);
      if (!language) {
        throw new Error(`Language with code ${languageCode} not found`);
      }

      const result = await db
        .select({
          namespaceName: translationNamespaces.name,
          key: translationKeys.key,
          value: translations.value
        })
        .from(translations)
        .innerJoin(translationKeys, eq(translations.keyId, translationKeys.id))
        .innerJoin(translationNamespaces, eq(translationKeys.namespaceId, translationNamespaces.id))
        .where(eq(translations.languageId, language.id))
        .orderBy(translationNamespaces.name, translationKeys.key);

      const organized: Record<string, Record<string, string>> = {};
      for (const row of result) {
        if (!organized[row.namespaceName]) {
          organized[row.namespaceName] = {};
        }
        organized[row.namespaceName][row.key] = row.value;
      }

      return organized;
    } catch (error) {
      console.error(`Error getting organized translations for language ${languageCode}:`, error);
      return {};
    }
  }

  async getTranslationsForLanguageAsArray(languageCode: string): Promise<Array<{key: string, value: string}>> {
    try {
      const language = await this.getLanguageByCode(languageCode);
      if (!language) {
        throw new Error(`Language with code ${languageCode} not found`);
      }

      const result = await db
        .select({
          namespaceName: translationNamespaces.name,
          key: translationKeys.key,
          value: translations.value
        })
        .from(translations)
        .innerJoin(translationKeys, eq(translations.keyId, translationKeys.id))
        .innerJoin(translationNamespaces, eq(translationKeys.namespaceId, translationNamespaces.id))
        .where(eq(translations.languageId, language.id))
        .orderBy(translationNamespaces.name, translationKeys.key);

      return result.map(row => ({
        key: `${row.namespaceName}.${row.key}`,
        value: row.value
      }));
    } catch (error) {
      console.error(`Error getting array translations for language ${languageCode}:`, error);
      return [];
    }
  }

  async convertArrayToNestedFormat(arrayData: Array<{key: string, value: string}>): Promise<Record<string, Record<string, string>>> {
    const nested: Record<string, Record<string, string>> = {};

    for (const item of arrayData) {
      const keyParts = item.key.split('.');
      if (keyParts.length < 2) {
        
        continue;
      }

      const namespaceName = keyParts[0];
      const keyName = keyParts.slice(1).join('.');

      if (!nested[namespaceName]) {
        nested[namespaceName] = {};
      }

      nested[namespaceName][keyName] = item.value;
    }

    return nested;
  }

  async importTranslations(languageId: number, translations: Record<string, Record<string, string>>): Promise<boolean> {
    try {
      const language = await this.getLanguage(languageId);
      if (!language) {
        throw new Error(`Language with ID ${languageId} not found`);
      }

      if (!translations || typeof translations !== 'object') {
        throw new Error('Invalid translations format. Expected nested object structure.');
      }

      let importedCount = 0;
      let skippedCount = 0;

      for (const namespaceName in translations) {
        if (!namespaceName || typeof namespaceName !== 'string') {
          
          continue;
        }

        const namespaceTranslations = translations[namespaceName];
        if (!namespaceTranslations || typeof namespaceTranslations !== 'object') {
          
          continue;
        }

        let namespace = await this.getNamespaceByName(namespaceName);
        if (!namespace) {
          namespace = await this.createNamespace({
            name: namespaceName,
            description: `Auto-created during import for language ${language.code}`
          });
        }

        for (const keyName in namespaceTranslations) {
          if (!keyName || typeof keyName !== 'string') {
            
            skippedCount++;
            continue;
          }

          const value = namespaceTranslations[keyName];
          if (typeof value !== 'string') {
            
            skippedCount++;
            continue;
          }

          let key = await this.getKeyByNameAndKey(namespace.id, keyName);
          if (!key) {
            key = await this.createKey({
              namespaceId: namespace.id,
              key: keyName,
              description: `Auto-created during import`
            });
          }

          await this.createTranslation({
            keyId: key.id,
            languageId: language.id,
            value: value
          });

          importedCount++;
        }
      }

      
      return true;
    } catch (error) {
      console.error(`Error importing translations for language ${languageId}:`, error);
      return false;
    }
  }

  async getSmtpConfig(companyId?: number): Promise<any | null> {
    try {
      if (companyId) {
        const setting = await this.getCompanySetting(companyId, 'smtp_config');
        return setting?.value || null;
      } else {
        const setting = await this.getAppSetting('smtp_config');
        return setting?.value || null;
      }
    } catch (error) {
      console.error('Error getting SMTP configuration:', error);
      return null;
    }
  }

  async saveSmtpConfig(config: any, companyId?: number): Promise<boolean> {
    try {
      if (companyId) {
        await this.saveCompanySetting(companyId, 'smtp_config', config);
        return true;
      } else {
        await this.saveAppSetting('smtp_config', config);
        return true;
      }
    } catch (error) {
      console.error('Error saving SMTP configuration:', error);
      return false;
    }
  }

  async getCompanySetting(companyId: number, key: string): Promise<CompanySetting | undefined> {
    try {
      const [setting] = await db
        .select()
        .from(companySettings)
        .where(
          and(
            eq(companySettings.companyId, companyId),
            eq(companySettings.key, key)
          )
        );

      return setting;
    } catch (error) {
      console.error(`Error getting company setting with key ${key} for company ${companyId}:`, error);
      return undefined;
    }
  }

  async getAllCompanySettings(companyId: number): Promise<CompanySetting[]> {
    try {
      return await db
        .select()
        .from(companySettings)
        .where(eq(companySettings.companyId, companyId))
        .orderBy(companySettings.key);
    } catch (error) {
      console.error(`Error getting all company settings for company ${companyId}:`, error);
      return [];
    }
  }

  async saveCompanySetting(companyId: number, key: string, value: any): Promise<CompanySetting> {
    try {
      if (!key) {
        throw new Error('Setting key is required');
      }

      if (value === undefined || value === null) {
        throw new Error('Setting value is required');
      }

      const existingSetting = await this.getCompanySetting(companyId, key);

      if (existingSetting) {
        const [updatedSetting] = await db
          .update(companySettings)
          .set({
            value,
            updatedAt: new Date()
          })
          .where(
            and(
              eq(companySettings.companyId, companyId),
              eq(companySettings.key, key)
            )
          )
          .returning();

        if (!updatedSetting) {
          throw new Error(`Failed to update company setting with key ${key} for company ${companyId}`);
        }

        return updatedSetting;
      } else {
        const [newSetting] = await db
          .insert(companySettings)
          .values({
            companyId,
            key,
            value
          })
          .returning();

        if (!newSetting) {
          throw new Error(`Failed to create company setting with key ${key} for company ${companyId}`);
        }

        return newSetting;
      }
    } catch (error) {
      console.error(`Error saving company setting with key ${key} for company ${companyId}:`, error);
      throw error;
    }
  }

  async deleteCompanySetting(companyId: number, key: string): Promise<boolean> {
    try {
      await db
        .delete(companySettings)
        .where(
          and(
            eq(companySettings.companyId, companyId),
            eq(companySettings.key, key)
          )
        );

      return true;
    } catch (error) {
      console.error(`Error deleting company setting with key ${key} for company ${companyId}:`, error);
      return false;
    }
  }



async getDealsByStage(stage: DealStatus): Promise<Deal[]> {
  try {
    return db
      .select()
      .from(deals)
      .where(eq(deals.stage, stage))
      .orderBy(desc(deals.lastActivityAt));
  } catch (error) {
    console.error(`Error getting deals by stage ${stage}:`, error);
    return [];
  }
}

async getDeal(id: number): Promise<Deal | undefined> {
  try {
    const [deal] = await db
      .select()
      .from(deals)
      .where(eq(deals.id, id));
    return deal;
  } catch (error) {
    console.error(`Error getting deal with ID ${id}:`, error);
    return undefined;
  }
}

async getDealsByContact(contactId: number): Promise<Deal[]> {
  try {
    return db
      .select()
      .from(deals)
      .where(eq(deals.contactId, contactId))
      .orderBy(desc(deals.lastActivityAt));
  } catch (error) {
    console.error(`Error getting deals for contact ${contactId}:`, error);
    return [];
  }
}

async getDealsByAssignedUser(userId: number): Promise<Deal[]> {
  try {
    return db
      .select()
      .from(deals)
      .where(eq(deals.assignedToUserId, userId))
      .orderBy(desc(deals.lastActivityAt));
  } catch (error) {
    console.error(`Error getting deals for user ${userId}:`, error);
    return [];
  }
}

async getDealTags(companyId: number): Promise<string[]> {
  try {
    const result = await db
      .select({ tags: deals.tags })
      .from(deals)
      .where(
        and(
          eq(deals.companyId, companyId),
          sql`${deals.status} != 'archived'`,
          sql`${deals.tags} IS NOT NULL`,
          sql`array_length(${deals.tags}, 1) > 0`
        )
      );


    const allTags = new Set<string>();
    result.forEach(row => {
      if (row.tags && Array.isArray(row.tags)) {
        row.tags.forEach(tag => {
          if (tag && tag.trim()) {
            allTags.add(tag.trim());
          }
        });
      }
    });

    return Array.from(allTags).sort();
  } catch (error) {
    console.error(`Error getting deal tags for company ${companyId}:`, error);
    return [];
  }
}

async createDeal(deal: InsertDeal): Promise<Deal> {
  try {
    if (!deal.contactId) {
      throw new Error('Contact ID is required');
    }

    const processedDeal = {
      ...deal,
      dueDate: deal.dueDate ? new Date(deal.dueDate) : undefined,
      lastActivityAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      stage: deal.stage || 'lead',
      status: deal.status || 'active',
      priority: deal.priority || 'medium'
    };

    const [newDeal] = await db
      .insert(deals)
      .values(processedDeal)
      .returning();
    return newDeal;
  } catch (error: any) {
    console.error('Error creating deal:', error);
    if (error.message === 'Contact ID is required') {
      throw error;
    }
    throw new Error('Failed to create deal');
  }
}

async updateDeal(id: number, updates: Partial<InsertDeal>): Promise<Deal> {
  try {
    if (updates.stageId !== undefined) {
      const stageId = updates.stageId;
      const otherUpdates = { ...updates };
      delete otherUpdates.stageId;

      let dealWithNewStage = null;
      if (stageId !== null) {
        dealWithNewStage = await this.updateDealStageId(id, stageId);
      }

      if (Object.keys(otherUpdates).length > 0) {
        const processedUpdates = {
          ...otherUpdates,
          dueDate: otherUpdates.dueDate ? new Date(otherUpdates.dueDate) : undefined,
          updatedAt: new Date()
        };

        const [finalUpdatedDeal] = await db
          .update(deals)
          .set(processedUpdates)
          .where(eq(deals.id, id))
          .returning();

        return finalUpdatedDeal || dealWithNewStage;
      }

      if (dealWithNewStage) {
        return dealWithNewStage;
      }

      const [currentDeal] = await db
        .select()
        .from(deals)
        .where(eq(deals.id, id));

      if (!currentDeal) {
        throw new Error(`Deal with ID ${id} not found`);
      }

      return currentDeal;
    }

    const processedUpdates = {
      ...updates,
      dueDate: updates.dueDate ? new Date(updates.dueDate) : undefined,
      updatedAt: new Date()
    };

    const [updatedDeal] = await db
      .update(deals)
      .set(processedUpdates)
      .where(eq(deals.id, id))
      .returning();

    if (!updatedDeal) {
      throw new Error(`Deal with ID ${id} not found`);
    }

    return updatedDeal;
  } catch (error) {
    console.error(`Error updating deal with ID ${id}:`, error);
    throw new Error('Failed to update deal');
  }
}

async updateDealStage(id: number, stage: DealStatus): Promise<Deal> {
  try {
    const [updatedDeal] = await db
      .update(deals)
      .set({
        stage,
        lastActivityAt: new Date(),
        updatedAt: new Date()
      })
      .where(eq(deals.id, id))
      .returning();

    if (!updatedDeal) {
      throw new Error(`Deal with ID ${id} not found`);
    }

    return updatedDeal;
  } catch (error) {
    console.error(`Error updating stage for deal with ID ${id}:`, error);
    throw new Error('Failed to update deal stage');
  }
}

async deleteDeal(id: number): Promise<boolean> {
  try {
    const [updatedDeal] = await db
      .update(deals)
      .set({
        status: 'archived',
        updatedAt: new Date()
      })
      .where(eq(deals.id, id))
      .returning();

    return !!updatedDeal;
  } catch (error) {
    console.error(`Error deleting deal with ID ${id}:`, error);
    return false;
  }
}

async getDealActivities(dealId: number): Promise<DealActivity[]> {
  try {
    return db
      .select()
      .from(dealActivities)
      .where(eq(dealActivities.dealId, dealId))
      .orderBy(desc(dealActivities.createdAt));
  } catch (error) {
    console.error(`Error getting activities for deal ${dealId}:`, error);
    return [];
  }
}

async createDealActivity(activity: InsertDealActivity): Promise<DealActivity> {
  try {
    const [newActivity] = await db
      .insert(dealActivities)
      .values({
        ...activity,
        createdAt: new Date()
      })
      .returning();

    await db
      .update(deals)
      .set({ lastActivityAt: new Date() })
      .where(eq(deals.id, activity.dealId));

    return newActivity;
  } catch (error) {
    console.error('Error creating deal activity:', error);
    throw new Error('Failed to create deal activity');
  }
}

async getPipelineStages(): Promise<PipelineStage[]> {
  try {
    return db
      .select()
      .from(pipelineStages)
      .orderBy(pipelineStages.order);
  } catch (error) {
    console.error('Error getting pipeline stages:', error);
    return [];
  }
}

async getPipelineStageById(id: number): Promise<PipelineStage | null> {
  try {
    const [stage] = await db
      .select()
      .from(pipelineStages)
      .where(eq(pipelineStages.id, id));

    return stage || null;
  } catch (error) {
    console.error(`Error getting pipeline stage with ID ${id}:`, error);
    return null;
  }
}

async getPipelineStagesByCompany(companyId: number): Promise<PipelineStage[]> {
  try {
    return db
      .select()
      .from(pipelineStages)
      .where(eq(pipelineStages.companyId, companyId))
      .orderBy(pipelineStages.order);
  } catch (error) {
    console.error(`Error getting pipeline stages for company ${companyId}:`, error);
    return [];
  }
}

async getPipelineStage(id: number): Promise<PipelineStage | undefined> {
  try {
    const [stage] = await db
      .select()
      .from(pipelineStages)
      .where(eq(pipelineStages.id, id));
    return stage;
  } catch (error) {
    console.error(`Error getting pipeline stage with ID ${id}:`, error);
    return undefined;
  }
}

async createPipelineStage(stage: InsertPipelineStage): Promise<PipelineStage> {
  try {
    const maxOrderResult = await db
      .select({ maxOrder: sql`MAX(${pipelineStages.order})` })
      .from(pipelineStages)
      .where(stage.companyId ? eq(pipelineStages.companyId, stage.companyId) : isNull(pipelineStages.companyId));

    const maxOrder = maxOrderResult[0]?.maxOrder || 0;
    const newOrder = stage.order || (maxOrder as number) + 1;

    const [newStage] = await db
      .insert(pipelineStages)
      .values({
        ...stage,
        order: newOrder,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning();

    return newStage;
  } catch (error) {
    console.error('Error creating pipeline stage:', error);
    throw new Error('Failed to create pipeline stage');
  }
}

async updatePipelineStage(id: number, updates: Partial<InsertPipelineStage>): Promise<PipelineStage> {
  try {
    const [updatedStage] = await db
      .update(pipelineStages)
      .set({
        ...updates,
        updatedAt: new Date()
      })
      .where(eq(pipelineStages.id, id))
      .returning();

    if (!updatedStage) {
      throw new Error(`Pipeline stage with ID ${id} not found`);
    }

    return updatedStage;
  } catch (error) {
    console.error(`Error updating pipeline stage with ID ${id}:`, error);
    throw new Error('Failed to update pipeline stage');
  }
}

async deletePipelineStage(id: number, moveDealsToStageId?: number): Promise<boolean> {
  try {
    return await db.transaction(async (tx) => {
      if (moveDealsToStageId) {
        await tx
          .update(deals)
          .set({
            stageId: moveDealsToStageId,
            updatedAt: new Date()
          })
          .where(eq(deals.stageId, id));
      }

      await tx
        .delete(pipelineStages)
        .where(eq(pipelineStages.id, id));

      const remainingStages = await tx
        .select()
        .from(pipelineStages)
        .orderBy(pipelineStages.order);

      for (let i = 0; i < remainingStages.length; i++) {
        await tx
          .update(pipelineStages)
          .set({ order: i + 1 })
          .where(eq(pipelineStages.id, remainingStages[i].id));
      }

      return true;
    });
  } catch (error) {
    console.error(`Error deleting pipeline stage with ID ${id}:`, error);
    return false;
  }
}

async reorderPipelineStages(stageIds: number[]): Promise<boolean> {
  try {
    return await db.transaction(async (tx) => {
      for (let i = 0; i < stageIds.length; i++) {
        await tx
          .update(pipelineStages)
          .set({
            order: i + 1,
            updatedAt: new Date()
          })
          .where(eq(pipelineStages.id, stageIds[i]));
      }
      return true;
    });
  } catch (error) {
    console.error('Error reordering pipeline stages:', error);
    return false;
  }
}

async getDeals(filter?: {
  priority?: string;
  assignedToUserId?: number;
  stageId?: number;
  companyId?: number;
  titleSearch?: string;
  tags?: string[];
  contactPhone?: string;
  contactName?: string;
}): Promise<Deal[]> {
  try {
    const conditions = [sql`${deals.status} != 'archived'`];

    if (filter) {
      if (filter.companyId) {
        conditions.push(eq(deals.companyId, filter.companyId));
      }

      if (filter.priority) {
        conditions.push(eq(deals.priority, filter.priority as DealPriority));
      }

      if (filter.assignedToUserId) {
        conditions.push(eq(deals.assignedToUserId, filter.assignedToUserId));
      }

      if (filter.stageId) {
        conditions.push(eq(deals.stageId, filter.stageId));
      }

      if (filter.titleSearch) {
        conditions.push(sql`${deals.title} ILIKE ${'%' + filter.titleSearch + '%'}`);
      }

      if (filter.tags && filter.tags.length > 0) {

        const tagConditions = filter.tags.map(tag =>
          sql`${deals.tags} @> ARRAY[${tag}]`
        );
        if (tagConditions.length > 0) {
          conditions.push(or(...tagConditions)!);
        }
      }
    }

    let query = db
      .select({
        id: deals.id,
        companyId: deals.companyId,
        contactId: deals.contactId,
        title: deals.title,
        stageId: deals.stageId,
        stage: deals.stage,
        value: deals.value,
        priority: deals.priority,
        dueDate: deals.dueDate,
        assignedToUserId: deals.assignedToUserId,
        description: deals.description,
        tags: deals.tags,
        status: deals.status,
        lastActivityAt: deals.lastActivityAt,
        createdAt: deals.createdAt,
        updatedAt: deals.updatedAt,

        contactName: contacts.name,
        contactPhone: contacts.phone
      })
      .from(deals)
      .leftJoin(contacts, eq(deals.contactId, contacts.id));


    if (filter?.contactPhone) {
      conditions.push(sql`${contacts.phone} ILIKE ${'%' + filter.contactPhone + '%'}`);
    }

    if (filter?.contactName) {
      conditions.push(sql`${contacts.name} ILIKE ${'%' + filter.contactName + '%'}`);
    }

    const result = await query
      .where(and(...conditions))
      .orderBy(desc(deals.lastActivityAt));


    return result.map(({ contactName, contactPhone, ...deal }) => deal);
  } catch (error) {
    console.error('Error getting deals with filter:', error);
    return [];
  }
}

async getDealsByStageId(stageId: number): Promise<Deal[]> {
  try {
    return db
      .select()
      .from(deals)
      .where(
        and(
          eq(deals.stageId, stageId),
          sql`${deals.status} != 'archived'`
        )
      )
      .orderBy(desc(deals.lastActivityAt));
  } catch (error) {
    console.error(`Error getting deals for stage ID ${stageId}:`, error);
    return [];
  }
}

async updateDealStageId(id: number, stageId: number): Promise<Deal> {
  try {
    return await db.transaction(async (tx) => {
      const [pipelineStage] = await tx
        .select()
        .from(pipelineStages)
        .where(eq(pipelineStages.id, stageId));

      if (!pipelineStage) {
        throw new Error(`Pipeline stage with ID ${stageId} not found`);
      }

      const stageEnumValue = this.mapPipelineStageToEnum(pipelineStage.name);

      const [updatedDeal] = await tx
        .update(deals)
        .set({
          stageId,
          stage: stageEnumValue as any,
          updatedAt: new Date(),
          lastActivityAt: new Date()
        })
        .where(eq(deals.id, id))
        .returning();

      if (!updatedDeal) {
        throw new Error(`Deal with ID ${id} not found`);
      }

      const stageName = pipelineStage.name;

      await tx
        .insert(dealActivities)
        .values({
          dealId: id,
          userId: updatedDeal.assignedToUserId || 1,
          type: 'stage_change',
          content: `Deal moved to ${stageName} stage`,
          metadata: {
            previousStageId: updatedDeal.stageId,
            newStageId: stageId
          },
          createdAt: new Date()
        });

      return updatedDeal;
    });
  } catch (error) {
    console.error(`Error updating stage for deal ${id}:`, error);
    throw new Error(`Failed to update deal stage: ${error instanceof Error ? error.message : String(error)}`);
  }
}

  private mapPipelineStageToEnum(stageName: string): string {
    const lowerStageName = stageName.toLowerCase();

    if (lowerStageName.includes('lead') || lowerStageName.includes('new')) {
      return 'lead';
    }
    if (lowerStageName.includes('qualified') || lowerStageName.includes('qualify')) {
      return 'qualified';
    }
    if (lowerStageName.includes('contact') || lowerStageName.includes('reach')) {
      return 'contacted';
    }
    if (lowerStageName.includes('demo') || lowerStageName.includes('presentation')) {
      return 'demo_scheduled';
    }
    if (lowerStageName.includes('proposal') || lowerStageName.includes('quote')) {
      return 'proposal';
    }
    if (lowerStageName.includes('negotiat') || lowerStageName.includes('discuss')) {
      return 'negotiation';
    }
    if (lowerStageName.includes('won') || lowerStageName.includes('closed') || lowerStageName.includes('success')) {
      return 'closed_won';
    }
    if (lowerStageName.includes('lost') || lowerStageName.includes('reject')) {
      return 'closed_lost';
    }

    return 'lead';
  }

  async getRolePermissions(companyId?: number): Promise<RolePermission[]> {
    try {
      const query = db.select().from(rolePermissions);

      if (companyId) {
        query.where(eq(rolePermissions.companyId, companyId));
      }

      const results = await query;

      return results.map(rp => ({
        ...rp,
        permissions: rp.permissions as Record<string, boolean>
      }));
    } catch (error) {
      console.error(`Error getting role permissions for company ${companyId}:`, error);
      return [];
    }
  }

  async getRolePermissionsByRole(companyId: number, role: 'admin' | 'agent'): Promise<RolePermission | undefined> {
    try {
      const [rolePermission] = await db
        .select()
        .from(rolePermissions)
        .where(
          and(
            eq(rolePermissions.companyId, companyId),
            eq(rolePermissions.role, role)
          )
        );

      if (rolePermission) {
        return {
          ...rolePermission,
          permissions: rolePermission.permissions as Record<string, boolean>
        };
      }
      return undefined;
    } catch (error) {
      console.error(`Error getting role permissions for company ${companyId} and role ${role}:`, error);
      return undefined;
    }
  }

async createRolePermissions(rolePermission: InsertRolePermission): Promise<RolePermission> {
  try {
    const [newRolePermission] = await db
      .insert(rolePermissions)
      .values({
        ...rolePermission,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning();

    return {
      ...newRolePermission,
      permissions: newRolePermission.permissions as Record<string, boolean>
    };
  } catch (error) {
    console.error("Error creating role permissions:", error);
    throw error;
  }
}

async updateRolePermissions(role: 'admin' | 'agent', permissions: Record<string, boolean>, companyId?: number): Promise<RolePermission> {
  try {
    if (!companyId) {
      throw new Error('Company ID is required for updating role permissions');
    }

    const [updatedRolePermission] = await db
      .update(rolePermissions)
      .set({
        permissions,
        updatedAt: new Date()
      })
      .where(
        and(
          eq(rolePermissions.companyId, companyId),
          eq(rolePermissions.role, role)
        )
      )
      .returning();

    if (updatedRolePermission) {
      return {
        ...updatedRolePermission,
        permissions: updatedRolePermission.permissions as Record<string, boolean>
      };
    }

    return await this.createRolePermissions({
      companyId,
      role,
      permissions
    });
  } catch (error) {
    console.error(`Error updating role permissions for company ${companyId} and role ${role}:`, error);
    throw error;
  }
}

  async getActiveSubscriptionsCount(): Promise<number> {
    try {
      const result = await db
        .select({ count: sql`COUNT(*)` })
        .from(companies)
        .where(eq(companies.subscriptionStatus, 'active'));

      return Number(result[0]?.count || 0);
    } catch (error) {
      console.error("Error getting active subscriptions count:", error);
      return 0;
    }
  }

  async getPaymentTransactionsSince(startDate: Date): Promise<PaymentTransaction[]> {
    try {
      const result = await db
        .select()
        .from(paymentTransactions)
        .where(sql`${paymentTransactions.createdAt} >= ${startDate}`)
        .orderBy(desc(paymentTransactions.createdAt));

      return result.map(transaction => this.mapToPaymentTransaction(transaction));
    } catch (error) {
      console.error("Error getting payment transactions since date:", error);
      return [];
    }
  }

  async getCompaniesWithPaymentDetails(filters: any): Promise<any> {
    try {
      const { offset, limit, search, status, paymentMethod: _paymentMethod } = filters;

      const conditions = [];

      if (search) {
        conditions.push(sql`${companies.name} ILIKE ${`%${search}%`}`);
      }

      if (status) {
        conditions.push(eq(companies.subscriptionStatus, status));
      }

      const baseQuery = db
        .select({
          id: companies.id,
          name: companies.name,
          subscriptionStatus: companies.subscriptionStatus,
          planId: companies.planId,
          subscriptionEndDate: companies.subscriptionEndDate,
          createdAt: companies.createdAt
        })
        .from(companies);

      const query = conditions.length > 0
        ? baseQuery.where(and(...conditions))
        : baseQuery;

      const baseCountQuery = db
        .select({ count: sql`COUNT(*)` })
        .from(companies);

      const countQuery = conditions.length > 0
        ? baseCountQuery.where(and(...conditions))
        : baseCountQuery;

      const [{ count: totalCount }] = await countQuery;

      const result = await query
        .offset(offset)
        .limit(limit)
        .orderBy(desc(companies.createdAt));

      const enhancedCompanies = await Promise.all(result.map(async (company) => {
        const [lastPayment] = await db
          .select()
          .from(paymentTransactions)
          .where(eq(paymentTransactions.companyId, company.id))
          .orderBy(desc(paymentTransactions.createdAt))
          .limit(1);

        const plan = company.planId ? await this.getPlan(company.planId) : null;

        const totalPaid = await db
          .select({ total: sql`SUM(${paymentTransactions.amount})` })
          .from(paymentTransactions)
          .where(
            and(
              eq(paymentTransactions.companyId, company.id),
              eq(paymentTransactions.status, 'completed')
            )
          );

        return {
          ...company,
          planName: plan?.name || 'No Plan',
          lastPaymentDate: lastPayment?.createdAt || null,
          lastPaymentAmount: lastPayment ? Number(lastPayment.amount) : 0,
          lastPaymentMethod: lastPayment?.paymentMethod || null,
          totalPaid: Number(totalPaid[0]?.total || 0)
        };
      }));

      return {
        data: enhancedCompanies,
        total: Number(totalCount)
      };
    } catch (error) {
      console.error("Error getting companies with payment details:", error);
      return {
        data: [],
        total: 0
      };
    }
  }

  async getPaymentTransactionsWithFilters(filters: Record<string, unknown>): Promise<{ data: PaymentTransaction[], total: number }> {
    try {
      const {
        paymentMethod,
        status,
        startDate,
        endDate,
        companyId,
        offset,
        limit
      } = filters;

      const conditions = [];

      if (paymentMethod && typeof paymentMethod === 'string') {
        conditions.push(eq(paymentTransactions.paymentMethod, paymentMethod as any));
      }

      if (status && typeof status === 'string') {
        conditions.push(eq(paymentTransactions.status, status as any));
      }

      if (startDate) {
        conditions.push(sql`${paymentTransactions.createdAt} >= ${new Date(startDate as string)}`);
      }

      if (endDate) {
        conditions.push(sql`${paymentTransactions.createdAt} <= ${new Date(endDate as string)}`);
      }

      if (companyId && typeof companyId === 'number') {
        conditions.push(eq(paymentTransactions.companyId, companyId));
      }

      const baseCountQuery = db
        .select({ count: sql`COUNT(*)` })
        .from(paymentTransactions);

      const countQuery = conditions.length > 0
        ? baseCountQuery.where(and(...conditions))
        : baseCountQuery;

      const [{ count }] = await countQuery;

      const baseDataQuery = db
        .select()
        .from(paymentTransactions);

      const dataQuery = conditions.length > 0
        ? baseDataQuery.where(and(...conditions))
        : baseDataQuery;

      const data = await dataQuery
        .orderBy(desc(paymentTransactions.createdAt))
        .offset(Number(offset) || 0)
        .limit(Number(limit) || 10);

      return {
        data: data.map(transaction => this.mapToPaymentTransaction(transaction)),
        total: Number(count)
      };
    } catch (error) {
      console.error("Error getting payment transactions with filters:", error);
      return { data: [], total: 0 };
    }
  }

  async getPendingPayments(offset: number, limit: number): Promise<{ data: PaymentTransaction[], total: number }> {
    try {
      const [{ count }] = await db
        .select({ count: sql`COUNT(*)` })
        .from(paymentTransactions)
        .where(eq(paymentTransactions.status, 'pending'));

      const data = await db
        .select()
        .from(paymentTransactions)
        .where(eq(paymentTransactions.status, 'pending'))
        .orderBy(desc(paymentTransactions.createdAt))
        .offset(offset)
        .limit(limit);

      return {
        data: data.map(transaction => this.mapToPaymentTransaction(transaction)),
        total: Number(count)
      };
    } catch (error) {
      console.error("Error getting pending payments:", error);
      return { data: [], total: 0 };
    }
  }

  async updatePaymentTransactionStatus(id: number, status: string, notes?: string): Promise<PaymentTransaction | null> {
    try {
      const updates: Record<string, unknown> = {
        status,
        updatedAt: new Date()
      };

      if (notes) {
        updates.metadata = sql`COALESCE(${paymentTransactions.metadata}, '{}') || ${JSON.stringify({ notes })}`;
      }

      const [updatedTransaction] = await db
        .update(paymentTransactions)
        .set(updates)
        .where(eq(paymentTransactions.id, id))
        .returning();

      if (updatedTransaction) {
        return this.mapToPaymentTransaction(updatedTransaction);
      }

      return null;
    } catch (error) {
      console.error("Error updating payment transaction status:", error);
      return null;
    }
  }

  async createPaymentReminder(reminder: Record<string, unknown>): Promise<unknown> {
    try {
      return {
        id: Date.now(),
        ...reminder
      };
    } catch (error) {
      console.error("Error creating payment reminder:", error);
      throw error;
    }
  }

  async getPaymentMethodPerformance(filters: Record<string, unknown>): Promise<unknown> {
    try {
      const { startDate, endDate } = filters;

      const conditions = [];

      if (startDate) {
        conditions.push(sql`${paymentTransactions.createdAt} >= ${new Date(startDate as string)}`);
      }

      if (endDate) {
        conditions.push(sql`${paymentTransactions.createdAt} <= ${new Date(endDate as string)}`);
      }

      const baseQuery = db
        .select({
          paymentMethod: paymentTransactions.paymentMethod,
          totalTransactions: sql`COUNT(*)`,
          successfulTransactions: sql`COUNT(CASE WHEN ${paymentTransactions.status} = 'completed' THEN 1 END)`,
          totalRevenue: sql`SUM(CASE WHEN ${paymentTransactions.status} = 'completed' THEN ${paymentTransactions.amount} ELSE 0 END)`,
          averageAmount: sql`AVG(CASE WHEN ${paymentTransactions.status} = 'completed' THEN ${paymentTransactions.amount} ELSE NULL END)`
        })
        .from(paymentTransactions)
        .groupBy(paymentTransactions.paymentMethod);

      const query = conditions.length > 0
        ? baseQuery.where(and(...conditions))
        : baseQuery;

      const result = await query;

      return result.map(row => ({
        paymentMethod: row.paymentMethod,
        totalTransactions: Number(row.totalTransactions),
        successfulTransactions: Number(row.successfulTransactions),
        totalRevenue: Number(row.totalRevenue || 0),
        averageAmount: Number(row.averageAmount || 0),
        successRate: Number(row.totalTransactions) > 0
          ? (Number(row.successfulTransactions) / Number(row.totalTransactions)) * 100
          : 0
      }));
    } catch (error) {
      console.error("Error getting payment method performance:", error);
      return [];
    }
  }

  async getPaymentTransactionsForExport(filters: Record<string, unknown>): Promise<PaymentTransaction[]> {
    try {
      const { startDate, endDate, paymentMethod, status } = filters;

      const conditions = [];

      if (paymentMethod && typeof paymentMethod === 'string') {
        conditions.push(eq(paymentTransactions.paymentMethod, paymentMethod as any));
      }

      if (status && typeof status === 'string') {
        conditions.push(eq(paymentTransactions.status, status as any));
      }

      if (startDate) {
        conditions.push(sql`${paymentTransactions.createdAt} >= ${new Date(startDate as string)}`);
      }

      if (endDate) {
        conditions.push(sql`${paymentTransactions.createdAt} <= ${new Date(endDate as string)}`);
      }

      const baseQuery = db
        .select()
        .from(paymentTransactions);

      const query = conditions.length > 0
        ? baseQuery.where(and(...conditions))
        : baseQuery;

      const result = await query.orderBy(desc(paymentTransactions.createdAt));

      return result.map(transaction => this.mapToPaymentTransaction(transaction));
    } catch (error) {
      console.error("Error getting payment transactions for export:", error);
      return [];
    }
  }

  async generatePaymentCSV(transactions: PaymentTransaction[]): Promise<string> {
    try {
      const headers = [
        'ID',
        'Company ID',
        'Plan ID',
        'Amount',
        'Currency',
        'Status',
        'Payment Method',
        'External Transaction ID',
        'Created At',
        'Updated At'
      ];

      const rows = transactions.map(transaction => [
        transaction.id,
        transaction.companyId,
        transaction.planId,
        transaction.amount,
        transaction.currency,
        transaction.status,
        transaction.paymentMethod,
        transaction.externalTransactionId || '',
        transaction.createdAt.toISOString(),
        transaction.updatedAt.toISOString()
      ]);

      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.map(field => `"${field}"`).join(','))
      ].join('\n');

      return csvContent;
    } catch (error) {
      console.error("Error generating payment CSV:", error);
      throw error;
    }
  }

  async updateCompanySubscription(companyId: number, subscription: Record<string, unknown>): Promise<unknown> {
    try {
      const { planId, status, startDate: _startDate, endDate } = subscription;

      const [updatedCompany] = await db
        .update(companies)
        .set({
          planId: planId as number | null,
          subscriptionStatus: status as "active" | "inactive" | "pending" | "cancelled" | "overdue" | "trial",
          subscriptionEndDate: endDate as Date | null,
          updatedAt: new Date()
        })
        .where(eq(companies.id, companyId))
        .returning();

      return updatedCompany;
    } catch (error) {
      console.error("Error updating company subscription:", error);
      throw error;
    }
  }

  async startCompanyTrial(companyId: number, planId: number, trialDays: number): Promise<Company> {
    try {
      const trialStartDate = new Date();
      const trialEndDate = new Date();
      trialEndDate.setDate(trialStartDate.getDate() + trialDays);

      const [updatedCompany] = await db
        .update(companies)
        .set({
          planId,
          subscriptionStatus: "trial",
          trialStartDate,
          trialEndDate,
          isInTrial: true,
          updatedAt: new Date()
        })
        .where(eq(companies.id, companyId))
        .returning();

      if (!updatedCompany) {
        throw new Error(`Company with ID ${companyId} not found`);
      }

      return updatedCompany;
    } catch (error) {
      console.error("Error starting company trial:", error);
      throw error;
    }
  }

  async endCompanyTrial(companyId: number): Promise<Company> {
    try {
      const [updatedCompany] = await db
        .update(companies)
        .set({
          subscriptionStatus: "inactive",
          isInTrial: false,
          updatedAt: new Date()
        })
        .where(eq(companies.id, companyId))
        .returning();

      if (!updatedCompany) {
        throw new Error(`Company with ID ${companyId} not found`);
      }

      return updatedCompany;
    } catch (error) {
      console.error("Error ending company trial:", error);
      throw error;
    }
  }

  async getCompaniesWithExpiredTrials(): Promise<Company[]> {
    try {
      const now = new Date();
      return await db
        .select()
        .from(companies)
        .where(
          and(
            eq(companies.isInTrial, true),
            eq(companies.subscriptionStatus, "trial"),
            lt(companies.trialEndDate, now)
          )
        );
    } catch (error) {
      console.error("Error getting companies with expired trials:", error);
      return [];
    }
  }

  async getCompaniesWithExpiringTrials(daysBeforeExpiry: number): Promise<Company[]> {
    try {
      const now = new Date();
      const expiryThreshold = new Date();
      expiryThreshold.setDate(now.getDate() + daysBeforeExpiry);

      return await db
        .select()
        .from(companies)
        .where(
          and(
            eq(companies.isInTrial, true),
            eq(companies.subscriptionStatus, "trial"),
            gte(companies.trialEndDate, now),
            lt(companies.trialEndDate, expiryThreshold)
          )
        );
    } catch (error) {
      console.error("Error getting companies with expiring trials:", error);
      return [];
    }
  }

  async createSystemUpdate(update: InsertSystemUpdate): Promise<SystemUpdate> {
    try {
      const [newUpdate] = await db.insert(systemUpdates).values({
        ...update,
        createdAt: new Date(),
        updatedAt: new Date()
      }).returning();
      return newUpdate;
    } catch (error) {
      console.error("Error creating system update:", error);
      throw error;
    }
  }

  async updateSystemUpdate(id: number, updates: Partial<InsertSystemUpdate>): Promise<SystemUpdate> {
    try {
      const [updatedRecord] = await db.update(systemUpdates)
        .set({
          ...updates,
          updatedAt: new Date()
        })
        .where(eq(systemUpdates.id, id))
        .returning();

      if (!updatedRecord) {
        throw new Error(`System update with ID ${id} not found`);
      }

      return updatedRecord;
    } catch (error) {
      console.error("Error updating system update:", error);
      throw error;
    }
  }

  async getSystemUpdate(id: number): Promise<SystemUpdate | undefined> {
    try {
      const [update] = await db.select().from(systemUpdates).where(eq(systemUpdates.id, id));
      return update || undefined;
    } catch (error) {
      console.error("Error getting system update:", error);
      return undefined;
    }
  }

  async getAllSystemUpdates(): Promise<SystemUpdate[]> {
    try {
      return await db.select().from(systemUpdates).orderBy(desc(systemUpdates.createdAt));
    } catch (error) {
      console.error("Error getting all system updates:", error);
      return [];
    }
  }

  async getLatestSystemUpdate(): Promise<SystemUpdate | undefined> {
    try {
      const [update] = await db.select().from(systemUpdates)
        .orderBy(desc(systemUpdates.createdAt))
        .limit(1);
      return update || undefined;
    } catch (error) {
      console.error("Error getting latest system update:", error);
      return undefined;
    }
  }

  async deleteSystemUpdate(id: number): Promise<boolean> {
    try {
      await db.delete(systemUpdates).where(eq(systemUpdates.id, id));
      return true;
    } catch (error) {
      console.error("Error deleting system update:", error);
      return false;
    }
  }



  async createDatabaseBackup(name: string): Promise<string> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = `backups/updates/${name}-${timestamp}.sql`;


      return backupPath;
    } catch (error) {
      console.error("Error creating database backup:", error);
      throw error;
    }
  }

  async getPartnerConfiguration(provider: string): Promise<PartnerConfiguration | null> {
    try {
      const [config] = await db
        .select()
        .from(partnerConfigurations)
        .where(and(
          eq(partnerConfigurations.provider, provider),
          eq(partnerConfigurations.isActive, true)
        ))
        .limit(1);
      return config || null;
    } catch (error) {
      console.error("Error getting partner configuration:", error);
      throw error;
    }
  }

  async createPartnerConfiguration(data: InsertPartnerConfiguration): Promise<PartnerConfiguration> {
    try {
      const [config] = await db
        .insert(partnerConfigurations)
        .values(data)
        .returning();
      return config;
    } catch (error) {
      console.error("Error creating partner configuration:", error);
      throw error;
    }
  }

  async updatePartnerConfiguration(id: number, data: Partial<InsertPartnerConfiguration>): Promise<PartnerConfiguration> {
    try {
      const [config] = await db
        .update(partnerConfigurations)
        .set({ ...data, updatedAt: new Date() })
        .where(eq(partnerConfigurations.id, id))
        .returning();
      return config;
    } catch (error) {
      console.error("Error updating partner configuration:", error);
      throw error;
    }
  }

  async deletePartnerConfiguration(id: number): Promise<void> {
    try {
      await db
        .delete(partnerConfigurations)
        .where(eq(partnerConfigurations.id, id));
    } catch (error) {
      console.error("Error deleting partner configuration:", error);
      throw error;
    }
  }

  async getAllPartnerConfigurations(): Promise<PartnerConfiguration[]> {
    try {
      return await db
        .select()
        .from(partnerConfigurations)
        .orderBy(desc(partnerConfigurations.createdAt));
    } catch (error) {
      console.error("Error getting all partner configurations:", error);
      throw error;
    }
  }

  async createDialog360Client(data: InsertDialog360Client): Promise<Dialog360Client> {
    try {
      const [client] = await db
        .insert(dialog360Clients)
        .values(data)
        .returning();
      return client;
    } catch (error) {
      console.error("Error creating 360Dialog client:", error);
      throw error;
    }
  }

  async getDialog360ClientByClientId(clientId: string): Promise<Dialog360Client | null> {
    try {
      const [client] = await db
        .select()
        .from(dialog360Clients)
        .where(eq(dialog360Clients.clientId, clientId))
        .limit(1);
      return client || null;
    } catch (error) {
      console.error("Error getting 360Dialog client:", error);
      throw error;
    }
  }

  async getDialog360ClientByCompanyId(companyId: number): Promise<Dialog360Client | null> {
    try {
      const [client] = await db
        .select()
        .from(dialog360Clients)
        .where(eq(dialog360Clients.companyId, companyId))
        .limit(1);
      return client || null;
    } catch (error) {
      console.error("Error getting 360Dialog client by company:", error);
      throw error;
    }
  }

  async updateDialog360Client(id: number, data: Partial<InsertDialog360Client>): Promise<Dialog360Client> {
    try {
      const [client] = await db
        .update(dialog360Clients)
        .set({ ...data, updatedAt: new Date() })
        .where(eq(dialog360Clients.id, id))
        .returning();
      return client;
    } catch (error) {
      console.error("Error updating 360Dialog client:", error);
      throw error;
    }
  }

  async createDialog360Channel(data: InsertDialog360Channel): Promise<Dialog360Channel> {
    try {
      const [channel] = await db
        .insert(dialog360Channels)
        .values(data)
        .returning();
      return channel;
    } catch (error) {
      console.error("Error creating 360Dialog channel:", error);
      throw error;
    }
  }

  async getDialog360ChannelByChannelId(channelId: string): Promise<Dialog360Channel | null> {
    try {
      const [channel] = await db
        .select()
        .from(dialog360Channels)
        .where(eq(dialog360Channels.channelId, channelId))
        .limit(1);
      return channel || null;
    } catch (error) {
      console.error("Error getting 360Dialog channel:", error);
      throw error;
    }
  }

  async getDialog360ChannelsByClientId(clientId: number): Promise<Dialog360Channel[]> {
    try {
      return await db
        .select()
        .from(dialog360Channels)
        .where(eq(dialog360Channels.clientId, clientId))
        .orderBy(desc(dialog360Channels.createdAt));
    } catch (error) {
      console.error("Error getting 360Dialog channels by client:", error);
      throw error;
    }
  }

  async updateDialog360Channel(id: number, data: Partial<InsertDialog360Channel>): Promise<Dialog360Channel> {
    try {
      const [channel] = await db
        .update(dialog360Channels)
        .set({ ...data, updatedAt: new Date() })
        .where(eq(dialog360Channels.id, id))
        .returning();
      return channel;
    } catch (error) {
      console.error("Error updating 360Dialog channel:", error);
      throw error;
    }
  }

  async getDialog360ChannelByPhoneNumber(phoneNumber: string): Promise<Dialog360Channel | null> {
    try {
      const [channel] = await db
        .select()
        .from(dialog360Channels)
        .where(eq(dialog360Channels.phoneNumber, phoneNumber))
        .limit(1);
      return channel || null;
    } catch (error) {
      console.error("Error getting 360Dialog channel by phone number:", error);
      throw error;
    }
  }

  async createMetaWhatsappClient(data: InsertMetaWhatsappClient): Promise<MetaWhatsappClient> {
    try {
      const [client] = await db
        .insert(metaWhatsappClients)
        .values(data)
        .returning();
      return client;
    } catch (error) {
      console.error("Error creating Meta WhatsApp client:", error);
      throw error;
    }
  }

  async getMetaWhatsappClientByBusinessAccountId(businessAccountId: string): Promise<MetaWhatsappClient | null> {
    try {
      const [client] = await db
        .select()
        .from(metaWhatsappClients)
        .where(eq(metaWhatsappClients.businessAccountId, businessAccountId))
        .limit(1);
      return client || null;
    } catch (error) {
      console.error("Error getting Meta WhatsApp client:", error);
      throw error;
    }
  }

  async getMetaWhatsappClientByCompanyId(companyId: number): Promise<MetaWhatsappClient | null> {
    try {
      const [client] = await db
        .select()
        .from(metaWhatsappClients)
        .where(eq(metaWhatsappClients.companyId, companyId))
        .limit(1);
      return client || null;
    } catch (error) {
      console.error("Error getting Meta WhatsApp client by company:", error);
      throw error;
    }
  }

  async updateMetaWhatsappClient(id: number, data: Partial<InsertMetaWhatsappClient>): Promise<MetaWhatsappClient> {
    try {
      const [client] = await db
        .update(metaWhatsappClients)
        .set({ ...data, updatedAt: new Date() })
        .where(eq(metaWhatsappClients.id, id))
        .returning();
      return client;
    } catch (error) {
      console.error("Error updating Meta WhatsApp client:", error);
      throw error;
    }
  }

  async createMetaWhatsappPhoneNumber(data: InsertMetaWhatsappPhoneNumber): Promise<MetaWhatsappPhoneNumber> {
    try {
      const [phoneNumber] = await db
        .insert(metaWhatsappPhoneNumbers)
        .values(data)
        .returning();
      return phoneNumber;
    } catch (error) {
      console.error("Error creating Meta WhatsApp phone number:", error);
      throw error;
    }
  }

  async getMetaWhatsappPhoneNumbersByClientId(clientId: number): Promise<MetaWhatsappPhoneNumber[]> {
    try {
      return await db
        .select()
        .from(metaWhatsappPhoneNumbers)
        .where(eq(metaWhatsappPhoneNumbers.clientId, clientId))
        .orderBy(metaWhatsappPhoneNumbers.createdAt);
    } catch (error) {
      throw error;
    }
  }

  async getMetaWhatsappPhoneNumberByPhoneNumberId(phoneNumberId: string): Promise<MetaWhatsappPhoneNumber | null> {
    try {
      const [phoneNumber] = await db
        .select()
        .from(metaWhatsappPhoneNumbers)
        .where(eq(metaWhatsappPhoneNumbers.phoneNumberId, phoneNumberId))
        .limit(1);
      return phoneNumber || null;
    } catch (error) {
      throw error;
    }
  }

  async updateMetaWhatsappPhoneNumber(id: number, data: Partial<InsertMetaWhatsappPhoneNumber>): Promise<MetaWhatsappPhoneNumber> {
    try {
      const [phoneNumber] = await db
        .update(metaWhatsappPhoneNumbers)
        .set({ ...data, updatedAt: new Date() })
        .where(eq(metaWhatsappPhoneNumbers.id, id))
        .returning();
      return phoneNumber;
    } catch (error) {
      throw error;
    }
  }

  async getMetaWhatsappPhoneNumbersByCompanyId(companyId: number): Promise<MetaWhatsappPhoneNumber[]> {
    try {
      return await db
        .select({
          id: metaWhatsappPhoneNumbers.id,
          clientId: metaWhatsappPhoneNumbers.clientId,
          phoneNumberId: metaWhatsappPhoneNumbers.phoneNumberId,
          phoneNumber: metaWhatsappPhoneNumbers.phoneNumber,
          displayName: metaWhatsappPhoneNumbers.displayName,
          status: metaWhatsappPhoneNumbers.status,
          qualityRating: metaWhatsappPhoneNumbers.qualityRating,
          messagingLimit: metaWhatsappPhoneNumbers.messagingLimit,
          accessToken: metaWhatsappPhoneNumbers.accessToken,
          createdAt: metaWhatsappPhoneNumbers.createdAt,
          updatedAt: metaWhatsappPhoneNumbers.updatedAt
        })
        .from(metaWhatsappPhoneNumbers)
        .innerJoin(metaWhatsappClients, eq(metaWhatsappPhoneNumbers.clientId, metaWhatsappClients.id))
        .where(eq(metaWhatsappClients.companyId, companyId))
        .orderBy(metaWhatsappPhoneNumbers.createdAt);
    } catch (error) {
      throw error;
    }
  }

  async createApiKey(data: InsertApiKey): Promise<ApiKey> {
    try {
      const [apiKey] = await db
        .insert(apiKeys)
        .values(data)
        .returning();
      return apiKey;
    } catch (error) {
      throw error;
    }
  }

  async getApiKeysByCompanyId(companyId: number): Promise<ApiKey[]> {
    try {
      return await db
        .select()
        .from(apiKeys)
        .where(eq(apiKeys.companyId, companyId))
        .orderBy(apiKeys.createdAt);
    } catch (error) {
      throw error;
    }
  }

  async getApiKeyByHash(keyHash: string): Promise<ApiKey | null> {
    try {
      const [apiKey] = await db
        .select()
        .from(apiKeys)
        .where(eq(apiKeys.keyHash, keyHash))
        .limit(1);
      return apiKey || null;
    } catch (error) {
      throw error;
    }
  }

  async updateApiKey(id: number, data: Partial<InsertApiKey>): Promise<ApiKey> {
    try {
      const [apiKey] = await db
        .update(apiKeys)
        .set({ ...data, updatedAt: new Date() })
        .where(eq(apiKeys.id, id))
        .returning();
      return apiKey;
    } catch (error) {
      console.error("Error updating API key:", error);
      throw error;
    }
  }

  async deleteApiKey(id: number): Promise<void> {
    try {
      await db
        .delete(apiKeys)
        .where(eq(apiKeys.id, id));
    } catch (error) {
      throw error;
    }
  }

  async updateApiKeyLastUsed(id: number): Promise<void> {
    try {
      await db
        .update(apiKeys)
        .set({ lastUsedAt: new Date() })
        .where(eq(apiKeys.id, id));
    } catch (error) {
      throw error;
    }
  }

  async createApiUsage(data: InsertApiUsage): Promise<ApiUsage> {
    try {
      const [usage] = await db
        .insert(apiUsage)
        .values(data)
        .returning();
      return usage;
    } catch (error) {
      throw error;
    }
  }

  async getApiUsageByKeyId(apiKeyId: number, limit: number = 100): Promise<ApiUsage[]> {
    try {
      return await db
        .select()
        .from(apiUsage)
        .where(eq(apiUsage.apiKeyId, apiKeyId))
        .orderBy(apiUsage.createdAt)
        .limit(limit);
    } catch (error) {
      throw error;
    }
  }

  async getApiUsageStats(companyId: number, startDate: Date, endDate: Date): Promise<any> {
    try {
      const stats = await db
        .select({
          totalRequests: sql<number>`count(*)`,
          successfulRequests: sql<number>`count(*) filter (where status_code < 400)`,
          failedRequests: sql<number>`count(*) filter (where status_code >= 400)`,
          avgDuration: sql<number>`avg(duration)`,
          totalDataTransfer: sql<number>`sum(request_size + response_size)`
        })
        .from(apiUsage)
        .where(
          and(
            eq(apiUsage.companyId, companyId),
            gte(apiUsage.createdAt, startDate),
            lte(apiUsage.createdAt, endDate)
          )
        );

      return stats[0] || {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        avgDuration: 0,
        totalDataTransfer: 0
      };
    } catch (error) {
      throw error;
    }
  }

  async getRateLimit(apiKeyId: number, windowType: string, windowStart: Date): Promise<ApiRateLimit | null> {
    try {
      const [rateLimit] = await db
        .select()
        .from(apiRateLimits)
        .where(
          and(
            eq(apiRateLimits.apiKeyId, apiKeyId),
            eq(apiRateLimits.windowType, windowType),
            eq(apiRateLimits.windowStart, windowStart)
          )
        )
        .limit(1);
      return rateLimit || null;
    } catch (error) {
      throw error;
    }
  }

  async createOrUpdateRateLimit(data: InsertApiRateLimit): Promise<ApiRateLimit> {
    try {
      const existing = await this.getRateLimit(data.apiKeyId, data.windowType, data.windowStart);

      if (existing) {
        const [updated] = await db
          .update(apiRateLimits)
          .set({
            requestCount: existing.requestCount + 1,
            updatedAt: new Date()
          })
          .where(eq(apiRateLimits.id, existing.id))
          .returning();
        return updated;
      } else {
        const [created] = await db
          .insert(apiRateLimits)
          .values({ ...data, requestCount: 1 })
          .returning();
        return created;
      }
    } catch (error) {
      throw error;
    }
  }

  async cleanupOldRateLimits(olderThan: Date): Promise<void> {
    try {
      await db
        .delete(apiRateLimits)
        .where(lt(apiRateLimits.windowStart, olderThan));
    } catch (error) {
      throw error;
    }
  }

  async getUserBySocialAccount(provider: SocialProvider, providerUserId: string): Promise<User | undefined> {
    try {
      const result = await db
        .select()
        .from(users)
        .innerJoin(userSocialAccounts, eq(users.id, userSocialAccounts.userId))
        .where(
          and(
            eq(userSocialAccounts.provider, provider),
            eq(userSocialAccounts.providerUserId, providerUserId)
          )
        )
        .limit(1);

      return result[0]?.users;
    } catch (error) {
      throw error;
    }
  }

  async createUserSocialAccount(socialAccount: InsertUserSocialAccount): Promise<UserSocialAccount> {
    try {
      const [newSocialAccount] = await db
        .insert(userSocialAccounts)
        .values({
          ...socialAccount,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning();
      return newSocialAccount;
    } catch (error) {
      throw error;
    }
  }

  async createUserFromSocialLogin(userData: {
    email: string;
    fullName: string;
    avatarUrl?: string;
    provider: SocialProvider;
    providerUserId: string;
    providerData: any;
  }): Promise<User> {
    try {
      const baseUsername = userData.email.split('@')[0];
      let username = baseUsername;
      let counter = 1;

      while (await this.getUserByUsername(username)) {
        username = `${baseUsername}${counter}`;
        counter++;
      }

      const [newUser] = await db
        .insert(users)
        .values({
          username,
          email: userData.email,
          fullName: userData.fullName,
          avatarUrl: userData.avatarUrl,
          password: '',
          role: 'admin',
          isSuperAdmin: false,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning();

      return newUser;
    } catch (error) {
      throw error;
    }
  }

  async getUserSocialAccounts(userId: number): Promise<UserSocialAccount[]> {
    try {
      return await db
        .select()
        .from(userSocialAccounts)
        .where(eq(userSocialAccounts.userId, userId));
    } catch (error) {
      throw error;
    }
  }

  async updateUserSocialAccount(
    userId: number,
    provider: SocialProvider,
    updates: Partial<InsertUserSocialAccount>
  ): Promise<UserSocialAccount | undefined> {
    try {
      const [updatedAccount] = await db
        .update(userSocialAccounts)
        .set({
          ...updates,
          updatedAt: new Date()
        })
        .where(
          and(
            eq(userSocialAccounts.userId, userId),
            eq(userSocialAccounts.provider, provider)
          )
        )
        .returning();

      return updatedAccount;
    } catch (error) {
      throw error;
    }
  }

  async deleteUserSocialAccount(userId: number, provider: SocialProvider): Promise<boolean> {
    try {
      const result = await db
        .delete(userSocialAccounts)
        .where(
          and(
            eq(userSocialAccounts.userId, userId),
            eq(userSocialAccounts.provider, provider)
          )
        );

      return (result.rowCount ?? 0) > 0;
    } catch (error) {
      throw error;
    }
  }




  async getMessagesByEmailMessageId(emailMessageId: string): Promise<Message[]> {
    return db
      .select()
      .from(messages)
      .where(eq(messages.emailMessageId, emailMessageId));
  }

  async getEmailConfigByConnectionId(connectionId: number): Promise<EmailConfig | undefined> {
    const [config] = await db
      .select()
      .from(emailConfigs)
      .where(eq(emailConfigs.channelConnectionId, connectionId));
    return config;
  }

  async createEmailAttachment(attachment: InsertEmailAttachment): Promise<EmailAttachment> {
    const [newAttachment] = await db.insert(emailAttachments).values(attachment).returning();
    return newAttachment;
  }

  async getEmailAttachmentsByMessageId(messageId: number): Promise<EmailAttachment[]> {
    const attachments = await db
      .select()
      .from(emailAttachments)
      .where(eq(emailAttachments.messageId, messageId))
      .orderBy(emailAttachments.createdAt);
    return attachments;
  }

  async createOrUpdateEmailConfig(connectionId: number, config: Partial<InsertEmailConfig>): Promise<EmailConfig> {
    const existingConfig = await this.getEmailConfigByConnectionId(connectionId);

    if (existingConfig) {
      const [updatedConfig] = await db
        .update(emailConfigs)
        .set({ ...config, updatedAt: new Date() })
        .where(eq(emailConfigs.channelConnectionId, connectionId))
        .returning();
      return updatedConfig;
    } else {
      const [newConfig] = await db.insert(emailConfigs).values({
        channelConnectionId: connectionId,
        ...config
      } as InsertEmailConfig).returning();
      return newConfig;
    }
  }

  async updateEmailConfigStatus(connectionId: number, status: string, errorMessage?: string): Promise<void> {
    await db
      .update(emailConfigs)
      .set({
        status,
        lastError: errorMessage || null,
        updatedAt: new Date()
      })
      .where(eq(emailConfigs.channelConnectionId, connectionId));
  }

  async updateEmailConfigLastSync(connectionId: number, lastSyncAt: Date): Promise<void> {
    await db
      .update(emailConfigs)
      .set({
        lastSyncAt,
        updatedAt: new Date()
      })
      .where(eq(emailConfigs.channelConnectionId, connectionId));
  }

  async createEmailTemplate(templateData: InsertEmailTemplate): Promise<EmailTemplate> {
    const [template] = await db.insert(emailTemplates).values(templateData).returning();
    return template;
  }

  async getEmailTemplatesByCompany(companyId: number): Promise<EmailTemplate[]> {
    return await db.select().from(emailTemplates)
      .where(and(eq(emailTemplates.companyId, companyId), eq(emailTemplates.isActive, true)))
      .orderBy(emailTemplates.name);
  }

  async getEmailTemplateById(templateId: number): Promise<EmailTemplate | null> {
    const [template] = await db.select().from(emailTemplates).where(eq(emailTemplates.id, templateId));
    return template || null;
  }

  async updateEmailTemplate(templateId: number, updates: Partial<InsertEmailTemplate>): Promise<EmailTemplate> {
    const [template] = await db.update(emailTemplates)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(emailTemplates.id, templateId))
      .returning();
    return template;
  }

  async deleteEmailTemplate(templateId: number): Promise<void> {
    await db.update(emailTemplates)
      .set({ isActive: false, updatedAt: new Date() })
      .where(eq(emailTemplates.id, templateId));
  }

  async incrementEmailTemplateUsage(templateId: number): Promise<void> {
    await db.update(emailTemplates)
      .set({
        usageCount: sql`${emailTemplates.usageCount} + 1`,
        updatedAt: new Date()
      })
      .where(eq(emailTemplates.id, templateId));
  }

  async createEmailSignature(signatureData: InsertEmailSignature): Promise<EmailSignature> {
    if (signatureData.isDefault) {
      await db.update(emailSignatures)
        .set({ isDefault: false, updatedAt: new Date() })
        .where(and(
          eq(emailSignatures.userId, signatureData.userId),
          eq(emailSignatures.isDefault, true)
        ));
    }

    const [signature] = await db.insert(emailSignatures).values(signatureData).returning();
    return signature;
  }

  async getEmailSignaturesByUser(userId: number): Promise<EmailSignature[]> {
    return await db.select().from(emailSignatures)
      .where(and(eq(emailSignatures.userId, userId), eq(emailSignatures.isActive, true)))
      .orderBy(desc(emailSignatures.isDefault), emailSignatures.name);
  }

  async getDefaultEmailSignature(userId: number): Promise<EmailSignature | null> {
    const [signature] = await db.select().from(emailSignatures)
      .where(and(
        eq(emailSignatures.userId, userId),
        eq(emailSignatures.isDefault, true),
        eq(emailSignatures.isActive, true)
      ));
    return signature || null;
  }

  async getEmailSignatureById(signatureId: number): Promise<EmailSignature | null> {
    const [signature] = await db.select().from(emailSignatures).where(eq(emailSignatures.id, signatureId));
    return signature || null;
  }

  async updateEmailSignature(signatureId: number, updates: Partial<InsertEmailSignature>): Promise<EmailSignature> {
    if (updates.isDefault) {
      const signature = await this.getEmailSignatureById(signatureId);
      if (signature) {
        await db.update(emailSignatures)
          .set({ isDefault: false, updatedAt: new Date() })
          .where(and(
            eq(emailSignatures.userId, signature.userId),
            eq(emailSignatures.isDefault, true),
            ne(emailSignatures.id, signatureId)
          ));
      }
    }

    const [updatedSignature] = await db.update(emailSignatures)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(emailSignatures.id, signatureId))
      .returning();
    return updatedSignature;
  }

  async deleteEmailSignature(signatureId: number): Promise<void> {
    await db.update(emailSignatures)
      .set({ isActive: false, updatedAt: new Date() })
      .where(eq(emailSignatures.id, signatureId));
  }



  async getAffiliateMetrics(): Promise<Record<string, unknown>> {
    try {

      const [totalAffiliatesResult] = await db
        .select({ count: sql`COUNT(*)` })
        .from(affiliates);


      const [activeAffiliatesResult] = await db
        .select({ count: sql`COUNT(*)` })
        .from(affiliates)
        .where(eq(affiliates.status, 'active'));


      const [pendingAffiliatesResult] = await db
        .select({ count: sql`COUNT(*)` })
        .from(affiliates)
        .where(eq(affiliates.status, 'pending'));


      const [totalReferralsResult] = await db
        .select({ count: sql`COUNT(*)` })
        .from(affiliateReferrals);


      const [convertedReferralsResult] = await db
        .select({ count: sql`COUNT(*)` })
        .from(affiliateReferrals)
        .where(eq(affiliateReferrals.status, 'converted'));


      const [totalCommissionResult] = await db
        .select({ total: sql`COALESCE(SUM(${affiliateReferrals.commissionAmount}), 0)` })
        .from(affiliateReferrals)
        .where(eq(affiliateReferrals.status, 'converted'));


      const [pendingPayoutsResult] = await db
        .select({
          count: sql`COUNT(*)`,
          total: sql`COALESCE(SUM(${affiliatePayouts.amount}), 0)`
        })
        .from(affiliatePayouts)
        .where(eq(affiliatePayouts.status, 'pending'));


      const totalReferrals = Number(totalReferralsResult.count);
      const convertedReferrals = Number(convertedReferralsResult.count);
      const conversionRate = totalReferrals > 0 ? (convertedReferrals / totalReferrals) * 100 : 0;

      return {
        totalAffiliates: Number(totalAffiliatesResult.count),
        activeAffiliates: Number(activeAffiliatesResult.count),
        pendingAffiliates: Number(pendingAffiliatesResult.count),
        totalReferrals: totalReferrals,
        convertedReferrals: convertedReferrals,
        conversionRate: Math.round(conversionRate * 100) / 100,
        totalCommissionEarned: Number(totalCommissionResult.total),
        pendingPayouts: {
          count: Number(pendingPayoutsResult.count),
          amount: Number(pendingPayoutsResult.total)
        }
      };
    } catch (error) {
      console.error("Error fetching affiliate metrics:", error);
      throw error;
    }
  }

  async getAffiliates(params: Record<string, unknown>): Promise<{ data: unknown[], total: number, page: number, limit: number, totalPages: number }> {
    try {
      const page = Number(params.page) || 1;
      const limit = Math.min(Number(params.limit) || 20, 100);
      const offset = (page - 1) * limit;
      const search = params.search as string;
      const status = params.status as string;
      const sortBy = params.sortBy as string || 'createdAt';
      const sortOrder = params.sortOrder as string || 'desc';

      const conditions = [];

      if (search) {
        conditions.push(
          or(
            sql`${affiliates.name} ILIKE ${'%' + search + '%'}`,
            sql`${affiliates.email} ILIKE ${'%' + search + '%'}`,
            sql`${affiliates.affiliateCode} ILIKE ${'%' + search + '%'}`
          )
        );
      }

      if (status && status !== 'all') {
        conditions.push(eq(affiliates.status, status as any));
      }


      const countQuery = conditions.length > 0
        ? db.select({ count: sql`COUNT(*)` }).from(affiliates).where(and(...conditions))
        : db.select({ count: sql`COUNT(*)` }).from(affiliates);

      const [{ count }] = await countQuery;


      const sortColumn = sortBy === 'name' ? affiliates.name :
                        sortBy === 'email' ? affiliates.email :
                        sortBy === 'status' ? affiliates.status :
                        sortBy === 'totalEarnings' ? affiliates.totalEarnings :
                        affiliates.createdAt;

      const orderBy = sortOrder === 'asc' ? sortColumn : desc(sortColumn);

      const dataQuery = conditions.length > 0
        ? db.select().from(affiliates).where(and(...conditions))
        : db.select().from(affiliates);

      const data = await dataQuery
        .orderBy(orderBy)
        .offset(offset)
        .limit(limit);

      const totalPages = Math.ceil(Number(count) / limit);

      return {
        data,
        total: Number(count),
        page,
        limit,
        totalPages
      };
    } catch (error) {
      console.error("Error fetching affiliates:", error);
      throw error;
    }
  }

  async getAffiliate(id: number): Promise<unknown | undefined> {
    try {
      const [affiliate] = await db
        .select()
        .from(affiliates)
        .where(eq(affiliates.id, id));

      return affiliate;
    } catch (error) {
      console.error("Error fetching affiliate:", error);
      throw error;
    }
  }

  async createAffiliate(affiliate: Record<string, unknown>): Promise<unknown> {
    try {
      const [newAffiliate] = await db
        .insert(affiliates)
        .values({
          ...affiliate,
          createdAt: new Date(),
          updatedAt: new Date()
        } as any)
        .returning();

      return newAffiliate;
    } catch (error) {
      console.error("Error creating affiliate:", error);
      throw error;
    }
  }

  async updateAffiliate(id: number, updates: Record<string, unknown>): Promise<unknown | undefined> {
    try {
      const [updatedAffiliate] = await db
        .update(affiliates)
        .set({
          ...updates,
          updatedAt: new Date()
        } as any)
        .where(eq(affiliates.id, id))
        .returning();

      return updatedAffiliate;
    } catch (error) {
      console.error("Error updating affiliate:", error);
      throw error;
    }
  }

  async deleteAffiliate(id: number): Promise<boolean> {
    try {
      const result = await db
        .delete(affiliates)
        .where(eq(affiliates.id, id));

      return (result.rowCount ?? 0) > 0;
    } catch (error) {
      console.error("Error deleting affiliate:", error);
      return false;
    }
  }

  async generateAffiliateCode(name: string): Promise<string> {
    try {

      const baseCode = name
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '')
        .substring(0, 8);

      let code = baseCode;
      let counter = 1;


      while (true) {
        const [existing] = await db
          .select()
          .from(affiliates)
          .where(eq(affiliates.affiliateCode, code))
          .limit(1);

        if (!existing) {
          break;
        }

        code = `${baseCode}${counter}`;
        counter++;
      }

      return code.toUpperCase();
    } catch (error) {
      console.error("Error generating affiliate code:", error);
      throw error;
    }
  }


  async createAffiliateApplication(application: Record<string, unknown>): Promise<unknown> {
    try {
      const [newApplication] = await db
        .insert(affiliateApplications)
        .values({
          ...application,
          createdAt: new Date(),
          updatedAt: new Date()
        } as any)
        .returning();

      return newApplication;
    } catch (error) {
      console.error("Error creating affiliate application:", error);
      throw error;
    }
  }

  async getAffiliateApplicationByEmail(email: string): Promise<unknown | undefined> {
    try {
      const [application] = await db
        .select()
        .from(affiliateApplications)
        .where(eq(affiliateApplications.email, email))
        .limit(1);

      return application;
    } catch (error) {
      console.error("Error fetching affiliate application by email:", error);
      throw error;
    }
  }

  async getAffiliateApplications(): Promise<unknown[]> {
    try {
      const applications = await db
        .select()
        .from(affiliateApplications)
        .orderBy(desc(affiliateApplications.submittedAt));

      return applications;
    } catch (error) {
      console.error("Error fetching affiliate applications:", error);
      throw error;
    }
  }

  async getAffiliateApplication(id: number): Promise<unknown | undefined> {
    try {
      const [application] = await db
        .select()
        .from(affiliateApplications)
        .where(eq(affiliateApplications.id, id))
        .limit(1);

      return application;
    } catch (error) {
      console.error("Error fetching affiliate application:", error);
      throw error;
    }
  }

  async updateAffiliateApplication(id: number, updates: Record<string, unknown>): Promise<unknown | undefined> {
    try {
      const [updatedApplication] = await db
        .update(affiliateApplications)
        .set({
          ...updates,
          updatedAt: new Date()
        } as any)
        .where(eq(affiliateApplications.id, id))
        .returning();

      return updatedApplication;
    } catch (error) {
      console.error("Error updating affiliate application:", error);
      throw error;
    }
  }

  async getAffiliateByEmail(email: string): Promise<unknown | undefined> {
    try {
      const [affiliate] = await db
        .select()
        .from(affiliates)
        .where(eq(affiliates.email, email))
        .limit(1);

      return affiliate;
    } catch (error) {
      console.error("Error fetching affiliate by email:", error);
      throw error;
    }
  }

  async getAffiliateCommissionStructures(affiliateId: number): Promise<unknown[]> {
    try {
      return await db
        .select()
        .from(affiliateCommissionStructures)
        .where(eq(affiliateCommissionStructures.affiliateId, affiliateId))
        .orderBy(desc(affiliateCommissionStructures.createdAt));
    } catch (error) {
      console.error("Error fetching commission structures:", error);
      throw error;
    }
  }

  async createCommissionStructure(structure: Record<string, unknown>): Promise<unknown> {
    try {
      const [newStructure] = await db
        .insert(affiliateCommissionStructures)
        .values({
          ...structure,
          createdAt: new Date(),
          updatedAt: new Date()
        } as any)
        .returning();

      return newStructure;
    } catch (error) {
      console.error("Error creating commission structure:", error);
      throw error;
    }
  }

  async updateCommissionStructure(id: number, updates: Record<string, unknown>): Promise<unknown | undefined> {
    try {
      const [updatedStructure] = await db
        .update(affiliateCommissionStructures)
        .set({
          ...updates,
          updatedAt: new Date()
        } as any)
        .where(eq(affiliateCommissionStructures.id, id))
        .returning();

      return updatedStructure;
    } catch (error) {
      console.error("Error updating commission structure:", error);
      throw error;
    }
  }

  async deleteCommissionStructure(id: number): Promise<boolean> {
    try {
      const result = await db
        .delete(affiliateCommissionStructures)
        .where(eq(affiliateCommissionStructures.id, id));

      return (result.rowCount ?? 0) > 0;
    } catch (error) {
      console.error("Error deleting commission structure:", error);
      return false;
    }
  }

  async getAffiliateReferrals(params: Record<string, unknown>): Promise<{ data: unknown[], total: number, page: number, limit: number, totalPages: number }> {
    try {
      const page = Number(params.page) || 1;
      const limit = Math.min(Number(params.limit) || 20, 100);
      const offset = (page - 1) * limit;
      const affiliateId = params.affiliateId as number;
      const status = params.status as string;
      const search = params.search as string;

      const conditions = [];

      if (affiliateId) {
        conditions.push(eq(affiliateReferrals.affiliateId, affiliateId));
      }

      if (status && status !== 'all') {
        conditions.push(eq(affiliateReferrals.status, status as any));
      }

      if (search) {
        conditions.push(
          or(
            sql`${affiliateReferrals.referralCode} ILIKE ${'%' + search + '%'}`,
            sql`${affiliateReferrals.referredEmail} ILIKE ${'%' + search + '%'}`
          )
        );
      }


      const countQuery = conditions.length > 0
        ? db.select({ count: sql`COUNT(*)` }).from(affiliateReferrals).where(and(...conditions))
        : db.select({ count: sql`COUNT(*)` }).from(affiliateReferrals);

      const [{ count }] = await countQuery;


      const dataQuery = db
        .select({
          referral: affiliateReferrals,
          affiliateName: affiliates.name,
          affiliateCode: affiliates.affiliateCode
        })
        .from(affiliateReferrals)
        .leftJoin(affiliates, eq(affiliateReferrals.affiliateId, affiliates.id));

      const finalQuery = conditions.length > 0
        ? dataQuery.where(and(...conditions))
        : dataQuery;

      const data = await finalQuery
        .orderBy(desc(affiliateReferrals.createdAt))
        .offset(offset)
        .limit(limit);

      const totalPages = Math.ceil(Number(count) / limit);

      return {
        data: data.map(row => ({
          ...row.referral,
          affiliateName: row.affiliateName,
          affiliateCode: row.affiliateCode
        })),
        total: Number(count),
        page,
        limit,
        totalPages
      };
    } catch (error) {
      console.error("Error fetching affiliate referrals:", error);
      throw error;
    }
  }

  async updateAffiliateReferral(id: number, updates: Record<string, unknown>): Promise<unknown | undefined> {
    try {
      const [updatedReferral] = await db
        .update(affiliateReferrals)
        .set({
          ...updates,
          updatedAt: new Date()
        } as any)
        .where(eq(affiliateReferrals.id, id))
        .returning();

      return updatedReferral;
    } catch (error) {
      console.error("Error updating affiliate referral:", error);
      throw error;
    }
  }

  async getAffiliatePayouts(params: Record<string, unknown>): Promise<{ data: unknown[], total: number, page: number, limit: number, totalPages: number }> {
    try {
      const page = Number(params.page) || 1;
      const limit = Math.min(Number(params.limit) || 20, 100);
      const offset = (page - 1) * limit;
      const affiliateId = params.affiliateId as number;
      const status = params.status as string;
      const startDate = params.startDate as string;
      const endDate = params.endDate as string;

      const conditions = [];

      if (affiliateId) {
        conditions.push(eq(affiliatePayouts.affiliateId, affiliateId));
      }

      if (status && status !== 'all') {
        conditions.push(eq(affiliatePayouts.status, status as any));
      }

      if (startDate) {
        conditions.push(gte(affiliatePayouts.createdAt, new Date(startDate)));
      }

      if (endDate) {
        conditions.push(lte(affiliatePayouts.createdAt, new Date(endDate)));
      }


      const countQuery = conditions.length > 0
        ? db.select({ count: sql`COUNT(*)` }).from(affiliatePayouts).where(and(...conditions))
        : db.select({ count: sql`COUNT(*)` }).from(affiliatePayouts);

      const [{ count }] = await countQuery;


      const dataQuery = db
        .select({
          payout: affiliatePayouts,
          affiliateName: affiliates.name,
          affiliateCode: affiliates.affiliateCode
        })
        .from(affiliatePayouts)
        .leftJoin(affiliates, eq(affiliatePayouts.affiliateId, affiliates.id));

      const finalQuery = conditions.length > 0
        ? dataQuery.where(and(...conditions))
        : dataQuery;

      const data = await finalQuery
        .orderBy(desc(affiliatePayouts.createdAt))
        .offset(offset)
        .limit(limit);

      const totalPages = Math.ceil(Number(count) / limit);

      return {
        data: data.map(row => ({
          ...row.payout,
          affiliateName: row.affiliateName,
          affiliateCode: row.affiliateCode
        })),
        total: Number(count),
        page,
        limit,
        totalPages
      };
    } catch (error) {
      console.error("Error fetching affiliate payouts:", error);
      throw error;
    }
  }

  async createAffiliatePayout(payout: Record<string, unknown>): Promise<unknown> {
    try {
      const [newPayout] = await db
        .insert(affiliatePayouts)
        .values({
          ...payout,
          createdAt: new Date(),
          updatedAt: new Date()
        } as any)
        .returning();

      return newPayout;
    } catch (error) {
      console.error("Error creating affiliate payout:", error);
      throw error;
    }
  }

  async updateAffiliatePayout(id: number, updates: Record<string, unknown>): Promise<unknown | undefined> {
    try {
      const [updatedPayout] = await db
        .update(affiliatePayouts)
        .set({
          ...updates,
          updatedAt: new Date()
        } as any)
        .where(eq(affiliatePayouts.id, id))
        .returning();

      return updatedPayout;
    } catch (error) {
      console.error("Error updating affiliate payout:", error);
      throw error;
    }
  }


  async getAffiliateAnalytics(params: Record<string, unknown>): Promise<unknown[]> {
    try {


      return [];
    } catch (error) {
      console.error("Error fetching affiliate analytics:", error);
      throw error;
    }
  }

  async getAffiliatePerformance(params: Record<string, unknown>): Promise<unknown[]> {
    try {


      return [];
    } catch (error) {
      console.error("Error fetching affiliate performance:", error);
      throw error;
    }
  }

  async exportAffiliateData(params: Record<string, unknown>): Promise<string> {
    try {


      return "No data available for export";
    } catch (error) {
      console.error("Error exporting affiliate data:", error);
      throw error;
    }
  }


  async getCompanyPages(companyId: number, options?: { published?: boolean; featured?: boolean }): Promise<CompanyPage[]> {
    try {
      const conditions = [eq(companyPages.companyId, companyId)];

      if (options?.published !== undefined) {
        conditions.push(eq(companyPages.isPublished, options.published));
      }

      if (options?.featured !== undefined) {
        conditions.push(eq(companyPages.isFeatured, options.featured));
      }

      const query = db.select().from(companyPages).where(and(...conditions));

      const results = await query.orderBy(desc(companyPages.createdAt));
      return results;
    } catch (error) {
      console.error(`Error getting company pages for company ${companyId}:`, error);
      return [];
    }
  }

  async getCompanyPage(id: number): Promise<CompanyPage | undefined> {
    try {
      const [page] = await db.select().from(companyPages).where(eq(companyPages.id, id));
      return page;
    } catch (error) {
      console.error(`Error getting company page ${id}:`, error);
      return undefined;
    }
  }

  async getCompanyPageBySlug(companyId: number, slug: string): Promise<CompanyPage | undefined> {
    try {
      const [page] = await db
        .select()
        .from(companyPages)
        .where(and(eq(companyPages.companyId, companyId), eq(companyPages.slug, slug)));
      return page;
    } catch (error) {
      console.error(`Error getting company page by slug ${slug} for company ${companyId}:`, error);
      return undefined;
    }
  }

  async createCompanyPage(page: InsertCompanyPage): Promise<CompanyPage> {
    try {
      const [newPage] = await db
        .insert(companyPages)
        .values({
          ...page,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning();

      return newPage;
    } catch (error) {
      console.error("Error creating company page:", error);
      throw error;
    }
  }

  async updateCompanyPage(id: number, page: Partial<InsertCompanyPage>): Promise<CompanyPage> {
    try {
      const [updatedPage] = await db
        .update(companyPages)
        .set({
          ...page,
          updatedAt: new Date()
        })
        .where(eq(companyPages.id, id))
        .returning();

      if (!updatedPage) {
        throw new Error('Company page not found');
      }

      return updatedPage;
    } catch (error) {
      console.error(`Error updating company page ${id}:`, error);
      throw error;
    }
  }

  async deleteCompanyPage(id: number): Promise<boolean> {
    try {
      const result = await db.delete(companyPages).where(eq(companyPages.id, id));
      return (result.rowCount || 0) > 0;
    } catch (error) {
      console.error(`Error deleting company page ${id}:`, error);
      return false;
    }
  }

  async publishCompanyPage(id: number): Promise<CompanyPage> {
    try {
      const [updatedPage] = await db
        .update(companyPages)
        .set({
          isPublished: true,
          publishedAt: new Date(),
          updatedAt: new Date()
        })
        .where(eq(companyPages.id, id))
        .returning();

      if (!updatedPage) {
        throw new Error('Company page not found');
      }

      return updatedPage;
    } catch (error) {
      console.error(`Error publishing company page ${id}:`, error);
      throw error;
    }
  }

  async unpublishCompanyPage(id: number): Promise<CompanyPage> {
    try {
      const [updatedPage] = await db
        .update(companyPages)
        .set({
          isPublished: false,
          updatedAt: new Date()
        })
        .where(eq(companyPages.id, id))
        .returning();

      if (!updatedPage) {
        throw new Error('Company page not found');
      }

      return updatedPage;
    } catch (error) {
      console.error(`Error unpublishing company page ${id}:`, error);
      throw error;
    }
  }

  // Website Builder Methods
  async getAllWebsites(): Promise<Website[]> {
    try {
      const result = await db
        .select()
        .from(websites)
        .orderBy(desc(websites.createdAt));
      return result;
    } catch (error) {
      console.error('Error fetching websites:', error);
      throw error;
    }
  }

  async getWebsite(id: number): Promise<Website | null> {
    try {
      const [website] = await db
        .select()
        .from(websites)
        .where(eq(websites.id, id));
      return website || null;
    } catch (error) {
      console.error(`Error fetching website ${id}:`, error);
      throw error;
    }
  }

  async getWebsiteBySlug(slug: string): Promise<Website | null> {
    try {
      const [website] = await db
        .select()
        .from(websites)
        .where(eq(websites.slug, slug));
      return website || null;
    } catch (error) {
      console.error(`Error fetching website by slug ${slug}:`, error);
      throw error;
    }
  }

  async createWebsite(websiteData: InsertWebsite): Promise<Website> {
    try {
      const [newWebsite] = await db
        .insert(websites)
        .values({
          ...websiteData,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning();
      return newWebsite;
    } catch (error) {
      console.error('Error creating website:', error);
      throw error;
    }
  }

  async updateWebsite(id: number, websiteData: Partial<InsertWebsite>): Promise<Website> {
    try {
      const [updatedWebsite] = await db
        .update(websites)
        .set({
          ...websiteData,
          updatedAt: new Date()
        })
        .where(eq(websites.id, id))
        .returning();

      if (!updatedWebsite) {
        throw new Error('Website not found');
      }

      return updatedWebsite;
    } catch (error) {
      console.error(`Error updating website ${id}:`, error);
      throw error;
    }
  }

  async deleteWebsite(id: number): Promise<boolean> {
    try {
      const result = await db.delete(websites).where(eq(websites.id, id));
      return (result.rowCount || 0) > 0;
    } catch (error) {
      console.error(`Error deleting website ${id}:`, error);
      return false;
    }
  }

  async publishWebsite(id: number): Promise<Website> {
    try {
      const [updatedWebsite] = await db
        .update(websites)
        .set({
          status: 'published',
          publishedAt: new Date(),
          updatedAt: new Date()
        })
        .where(eq(websites.id, id))
        .returning();

      if (!updatedWebsite) {
        throw new Error('Website not found');
      }

      return updatedWebsite;
    } catch (error) {
      console.error(`Error publishing website ${id}:`, error);
      throw error;
    }
  }

  async unpublishWebsite(id: number): Promise<Website> {
    try {
      const [updatedWebsite] = await db
        .update(websites)
        .set({
          status: 'draft',
          updatedAt: new Date()
        })
        .where(eq(websites.id, id))
        .returning();

      if (!updatedWebsite) {
        throw new Error('Website not found');
      }

      return updatedWebsite;
    } catch (error) {
      console.error(`Error unpublishing website ${id}:`, error);
      throw error;
    }
  }

  async getPublishedWebsite(): Promise<Website | null> {
    try {
      const [website] = await db
        .select()
        .from(websites)
        .where(eq(websites.status, 'published'))
        .orderBy(desc(websites.publishedAt))
        .limit(1);
      return website || null;
    } catch (error) {
      console.error('Error fetching published website:', error);
      throw error;
    }
  }



  // Website Asset Methods
  async getWebsiteAssets(websiteId: number): Promise<WebsiteAsset[]> {
    try {
      const result = await db
        .select()
        .from(websiteAssets)
        .where(eq(websiteAssets.websiteId, websiteId))
        .orderBy(desc(websiteAssets.createdAt));
      return result;
    } catch (error) {
      console.error(`Error fetching website assets for website ${websiteId}:`, error);
      throw error;
    }
  }

  async createWebsiteAsset(assetData: InsertWebsiteAsset): Promise<WebsiteAsset> {
    try {
      const [newAsset] = await db
        .insert(websiteAssets)
        .values({
          ...assetData,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning();
      return newAsset;
    } catch (error) {
      console.error('Error creating website asset:', error);
      throw error;
    }
  }

  async deleteWebsiteAsset(id: number): Promise<boolean> {
    try {
      const result = await db.delete(websiteAssets).where(eq(websiteAssets.id, id));
      return (result.rowCount || 0) > 0;
    } catch (error) {
      console.error(`Error deleting website asset ${id}:`, error);
      return false;
    }
  }
}

export const storage = new DatabaseStorage();

