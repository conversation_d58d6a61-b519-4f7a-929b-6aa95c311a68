import { storage } from '../storage';
import {
  FlowAssignment,
  Message,
  Contact,
  Conversation,
  ChannelConnection} from '@shared/schema';
import whatsAppService from './channels/whatsapp';
import instagramService from './channels/instagram';
import messengerService from './channels/messenger';
import googleCalendarService from './google-calendar';
import googleSheetsService from './google-sheets';
import axios from 'axios';
import { FlowExecutionManager } from './flow-execution-manager';
import { FlowExecutionContext } from './flow-execution-context';
import {
  NodeType,
  NodeTypeUtils,
  NodeExecutionResult as SharedNodeExecutionResult,
  NodeExecutionConfig
} from '@shared/types/node-types';
import { EventEmitter } from 'events';
import * as path from 'path';

interface Flow {
  id: number;
  name: string;
  description: string | null;
  status: "draft" | "active" | "inactive" | "archived";
  createdAt: Date;
  updatedAt: Date;
  userId: number;
  version: number;
  nodes: unknown;
  edges: unknown;
  definition?: string | any;
}


interface NodeExecutionResult {
  success: boolean;
  shouldContinue: boolean;
  nextNodeId?: string;
  waitForUserInput?: boolean;
  error?: string;
  data?: any;
}

interface FlowSessionState {
  sessionId: string;
  flowId: number;
  conversationId: number;
  contactId: number;
  companyId: number;
  status: 'active' | 'waiting' | 'paused' | 'completed' | 'failed' | 'abandoned' | 'timeout';
  currentNodeId: string | null;
  triggerNodeId: string;
  executionPath: string[];
  branchingHistory: any[];
  variables: Map<string, any>;
  nodeStates: Map<string, any>;
  waitingContext: any;
  startedAt: Date;
  lastActivityAt: Date;
  expiresAt: Date | null;
  isLoaded: boolean;
  aiSessionActive: boolean;
  aiNodeId: string | null;
  aiStopKeyword: string | null;
  aiExitOutputHandle: string | null;
}

interface FlowTransition {
  type: 'sequential' | 'conditional' | 'loop' | 'jump';
  conditions?: any[];
  conditionExpression?: string;
  loopIteration?: number;
  maxIterations?: number;
}

interface SessionVariable {
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  scope: 'global' | 'flow' | 'node' | 'user' | 'session';
  nodeId?: string;
  isEncrypted?: boolean;
  expiresAt?: Date;
}

/**
 * Enhanced Flow Executor Service - Session-Aware Flow Execution
 * Implements sequential node processing with persistent session management
 */
class FlowExecutor extends EventEmitter {
  private executionManager: FlowExecutionManager;
  private webSocketClients: Map<string, any> = new Map();

  private activeSessions: Map<string, FlowSessionState> = new Map();
  private sessionTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private static instance: FlowExecutor;

  constructor() {
    super();

    this.setMaxListeners(50);
    this.executionManager = FlowExecutionManager.getInstance();
    this.setupExecutionEventHandlers();
    this.setupSessionCleanup();
  }

  static getInstance(): FlowExecutor {
    if (!FlowExecutor.instance) {
      FlowExecutor.instance = new FlowExecutor();
    }
    return FlowExecutor.instance;
  }

  /**
   * Set WebSocket clients for real-time updates
   */
  setWebSocketClients(clients: Map<string, any>): void {
    this.webSocketClients = clients;
  }

  /**
   * Setup session cleanup for expired sessions
   */
  private setupSessionCleanup(): void {
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 5 * 60 * 1000);
  }

  /**
   * Clean up expired sessions
   */
  private async cleanupExpiredSessions(): Promise<void> {
    const now = new Date();
    const expiredSessions: string[] = [];

    for (const [sessionId, session] of Array.from(this.activeSessions.entries())) {
      if (session.expiresAt && session.expiresAt < now) {
        expiredSessions.push(sessionId);
      }
    }

    for (const sessionId of expiredSessions) {
      await this.expireSession(sessionId);
    }
  }

  /**
   * Create a new flow session
   */
  private async createSession(
    flowId: number,
    conversationId: number,
    contactId: number,
    companyId: number,
    triggerNodeId: string,
    initialContext: any
  ): Promise<string> {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date();
    const expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000);

    const session: FlowSessionState = {
      sessionId,
      flowId,
      conversationId,
      contactId,
      companyId,
      status: 'active',
      currentNodeId: triggerNodeId,
      triggerNodeId,
      executionPath: [triggerNodeId],
      branchingHistory: [],
      variables: new Map(),
      nodeStates: new Map(),
      waitingContext: null,
      startedAt: now,
      lastActivityAt: now,
      expiresAt,
      isLoaded: true,
      aiSessionActive: false,
      aiNodeId: null,
      aiStopKeyword: null,
      aiExitOutputHandle: null
    };

    this.activeSessions.set(sessionId, session);

    try {
      await storage.createFlowSession({
        sessionId,
        flowId,
        conversationId,
        contactId,
        companyId,
        status: 'active',
        currentNodeId: triggerNodeId,
        triggerNodeId,
        executionPath: JSON.stringify([triggerNodeId]),
        branchingHistory: JSON.stringify([]),
        sessionData: JSON.stringify({}),
        nodeStates: JSON.stringify({}),
        waitingContext: null,
        startedAt: now,
        lastActivityAt: now,
        expiresAt,
        nodeExecutionCount: 0,
        userInteractionCount: 0,
        errorCount: 0,
        createdAt: now,
        updatedAt: now
      });

      this.emit('sessionCreated', { sessionId, flowId, conversationId, contactId });
    } catch (error) {
    }

    return sessionId;
  }

  /**
   * Update session state
   */
  private async updateSession(sessionId: string, updates: Partial<FlowSessionState>): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    Object.assign(session, updates, { lastActivityAt: new Date() });

    try {
      const dbUpdates: any = {
        lastActivityAt: new Date()
      };

      if (updates.status) dbUpdates.status = updates.status;
      if (updates.currentNodeId) dbUpdates.currentNodeId = updates.currentNodeId;
      if (updates.executionPath) dbUpdates.executionPath = JSON.stringify(updates.executionPath);
      if (updates.branchingHistory) dbUpdates.branchingHistory = JSON.stringify(updates.branchingHistory);
      if (updates.waitingContext !== undefined) dbUpdates.waitingContext = updates.waitingContext ? JSON.stringify(updates.waitingContext) : null;

      if (updates.aiSessionActive !== undefined ||
          updates.aiNodeId !== undefined ||
          updates.aiStopKeyword !== undefined ||
          updates.aiExitOutputHandle !== undefined) {

        const currentNodeStates = session.nodeStates || new Map();
        const aiSessionData = {
          aiSessionActive: session.aiSessionActive,
          aiNodeId: session.aiNodeId,
          aiStopKeyword: session.aiStopKeyword,
          aiExitOutputHandle: session.aiExitOutputHandle
        };

        currentNodeStates.set('__aiSession', aiSessionData);
        session.nodeStates = currentNodeStates;
        dbUpdates.nodeStates = JSON.stringify(Object.fromEntries(currentNodeStates));
      }

      await storage.updateFlowSession(sessionId, dbUpdates);
      this.emit('sessionUpdated', { sessionId, updates });
    } catch (error) {
    }
  }

  /**
   * Expire a session
   */
  private async expireSession(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    session.status = 'timeout';
    this.activeSessions.delete(sessionId);

    const timeout = this.sessionTimeouts.get(sessionId);
    if (timeout) {
      clearTimeout(timeout);
      this.sessionTimeouts.delete(sessionId);
    }

    try {
      await storage.updateFlowSession(sessionId, { status: 'timeout' });
      this.emit('sessionExpired', { sessionId });
    } catch (error) {
    }
  }

  /**
   * Get active sessions for a conversation
   */
  private async getActiveSessionsForConversation(conversationId: number): Promise<FlowSessionState[]> {
    const activeSessions: FlowSessionState[] = [];

    for (const session of Array.from(this.activeSessions.values())) {
      if (session.conversationId === conversationId &&
          (['active', 'waiting', 'paused'].includes(session.status) || session.aiSessionActive)) {
        activeSessions.push(session);
      }
    }

    if (activeSessions.length === 0) {
      try {
        const dbSessions = await storage.getActiveFlowSessionsForConversation(conversationId);
        for (const dbSession of dbSessions) {
          const session = await this.loadSessionFromDatabase(dbSession.sessionId);
          if (session) {
            activeSessions.push(session);
          }
        }
      } catch (error) {
        console.error('Error loading sessions from database:', error);
      }
    }

    return activeSessions;
  }

  /**
   * Safe JSON parse with fallback
   */
  private safeJsonParse(jsonString: string | null | undefined, fallback: any): any {
    if (!jsonString) return fallback;

    if (typeof jsonString === 'object') {
      return jsonString;
    }

    if (jsonString.trim() === '') return fallback;

    try {
      const trimmed = jsonString.trim();
      if (trimmed.startsWith('{') || trimmed.startsWith('[') || trimmed.startsWith('"')) {
        return JSON.parse(jsonString);
      } else {
        
        return fallback;
      }
    } catch (error) {
      
      return fallback;
    }
  }

  /**
   * Load session from database
   */
  private async loadSessionFromDatabase(sessionId: string): Promise<FlowSessionState | null> {
    try {
      const dbSession = await storage.getFlowSession(sessionId);
      if (!dbSession) return null;

      const executionPath = this.safeJsonParse(dbSession.executionPath as string, []);
      const branchingHistory = this.safeJsonParse(dbSession.branchingHistory as string, []);
      const nodeStatesData = this.safeJsonParse(dbSession.nodeStates as string, {});
      const waitingContext = dbSession.waitingContext ? this.safeJsonParse(dbSession.waitingContext as string, null) : null;

      const session: FlowSessionState = {
        sessionId: dbSession.sessionId,
        flowId: dbSession.flowId,
        conversationId: dbSession.conversationId,
        contactId: dbSession.contactId,
        companyId: dbSession.companyId || 0,
        status: dbSession.status as any,
        currentNodeId: dbSession.currentNodeId,
        triggerNodeId: dbSession.triggerNodeId,
        executionPath: Array.isArray(executionPath) ? executionPath : [],
        branchingHistory: Array.isArray(branchingHistory) ? branchingHistory : [],
        variables: new Map(),
        nodeStates: new Map(nodeStatesData && typeof nodeStatesData === 'object' ? Object.entries(nodeStatesData) : []),
        waitingContext: waitingContext,
        startedAt: dbSession.startedAt,
        lastActivityAt: dbSession.lastActivityAt,
        expiresAt: dbSession.expiresAt,
        isLoaded: true,
        aiSessionActive: false,
        aiNodeId: null,
        aiStopKeyword: null,
        aiExitOutputHandle: null
      };

      const aiSessionData = session.nodeStates.get('__aiSession');
      if (aiSessionData) {
        session.aiSessionActive = aiSessionData.aiSessionActive || false;
        session.aiNodeId = aiSessionData.aiNodeId || null;
        session.aiStopKeyword = aiSessionData.aiStopKeyword || null;
        session.aiExitOutputHandle = aiSessionData.aiExitOutputHandle || null;
      }

      const variables = await storage.getFlowSessionVariables(sessionId);
      for (const variable of variables) {
        session.variables.set(variable.variableKey, variable.variableValue);
      }

      this.activeSessions.set(sessionId, session);
      return session;
    } catch (error) {
      console.error('Error loading session from database:', error);
      return null;
    }
  }

  /**
   * Setup execution event handlers for real-time updates
   */
  private setupExecutionEventHandlers(): void {
    this.executionManager.on('executionStarted', (data) => {
      this.broadcastExecutionEvent('flowExecutionStarted', data);
    });

    this.executionManager.on('executionUpdated', (data) => {
      this.broadcastExecutionEvent('flowExecutionUpdated', data);
    });

    this.executionManager.on('executionWaiting', (data) => {
      this.broadcastExecutionEvent('flowExecutionWaiting', data);
    });

    this.executionManager.on('executionCompleted', (data) => {
      this.broadcastExecutionEvent('flowExecutionCompleted', data);
    });

    this.executionManager.on('executionFailed', (data) => {
      this.broadcastExecutionEvent('flowExecutionFailed', data);
    });
  }

  /**
   * Broadcast execution events to connected WebSocket clients
   */
  private broadcastExecutionEvent(eventType: string, data: any): void {
    const message = JSON.stringify({
      type: eventType,
      data
    });

    this.webSocketClients.forEach((client) => {
      if (client.socket.readyState === 1 && client.isAuthenticated) {
        try {
          client.socket.send(message);
        } catch (error) {
          console.error('Error broadcasting execution event:', error);
        }
      }
    });
  }

  /**
   * Send message through the appropriate channel service
   * Unified message sending that supports all channel types
   */
  private async sendMessageThroughChannel(
    channelConnection: ChannelConnection,
    contact: Contact,
    message: string,
    conversation?: Conversation,
    isFromBot: boolean = true
  ): Promise<any> {
    try {
      const channelType = channelConnection.channelType;
      const contactIdentifier = contact.identifier || contact.phone;

      if (!contactIdentifier) {
        throw new Error('Contact identifier is required for message sending');
      }

      switch (channelType) {
        case 'whatsapp':
        case 'whatsapp_unofficial':
          return await whatsAppService.sendMessage(
            channelConnection.id,
            channelConnection.userId,
            contactIdentifier,
            message,
            isFromBot,
            conversation?.id
          );

        case 'instagram':
          return await instagramService.sendMessage(
            channelConnection.id,
            contactIdentifier,
            message,
            channelConnection.userId
          );

        case 'messenger':
          return await messengerService.sendMessage(
            channelConnection.id,
            contactIdentifier,
            message,
            channelConnection.userId
          );

        default:
          // Fallback: create message in database for unsupported channels
          if (conversation) {
            const insertMessage = {
              conversationId: conversation.id,
              senderId: channelConnection.userId,
              content: message,
              type: 'text' as const,
              direction: 'outbound' as const,
              status: 'sent',
              isFromBot: isFromBot,
              timestamp: new Date()
            };
            return await storage.createMessage(insertMessage);
          }
          throw new Error(`Unsupported channel type: ${channelType}`);
      }
    } catch (error) {
      console.error(`Error sending message through ${channelConnection.channelType}:`, error);
      throw error;
    }
  }

  /**
   * Send media message through the appropriate channel service
   * Unified media sending that supports all channel types
   */
  private async sendMediaThroughChannel(
    channelConnection: ChannelConnection,
    contact: Contact,
    mediaUrl: string,
    mediaType: 'image' | 'video' | 'audio' | 'document',
    caption?: string,
    filename?: string,
    conversation?: Conversation,
    isFromBot: boolean = true
  ): Promise<any> {
    try {
      const channelType = channelConnection.channelType;
      const contactIdentifier = contact.identifier || contact.phone;

      if (!contactIdentifier) {
        throw new Error('Contact identifier is required for media sending');
      }

      switch (channelType) {
        case 'whatsapp':
        case 'whatsapp_unofficial':
          return await whatsAppService.sendMedia(
            channelConnection.id,
            channelConnection.userId,
            contactIdentifier,
            mediaType,
            mediaUrl,
            caption || '',
            filename || '',
            isFromBot,
            conversation?.id
          );

        case 'instagram':
          // Instagram only supports image and video
          if (mediaType === 'image' || mediaType === 'video') {
            return await instagramService.sendMedia(
              channelConnection.id,
              contactIdentifier,
              mediaUrl,
              mediaType,
              caption,
              channelConnection.userId
            );
          } else {
            throw new Error(`Instagram does not support ${mediaType} media type`);
          }

        case 'messenger':
          // Convert document to file for Messenger
          const messengerMediaType = mediaType === 'document' ? 'file' : mediaType;
          return await messengerService.sendMedia(
            channelConnection.id,
            contactIdentifier,
            mediaUrl,
            messengerMediaType as 'image' | 'video' | 'audio' | 'file'
          );

        default:
          // Fallback: create message in database for unsupported channels
          if (conversation) {
            const insertMessage = {
              conversationId: conversation.id,
              senderId: channelConnection.userId,
              content: caption || `${mediaType} message`,
              type: mediaType,
              direction: 'outbound' as const,
              status: 'sent',
              mediaUrl: mediaUrl,
              isFromBot: isFromBot,
              timestamp: new Date()
            };
            return await storage.createMessage(insertMessage);
          }
          throw new Error(`Unsupported channel type for media: ${channelType}`);
      }
    } catch (error) {
      console.error(`Error sending media through ${channelConnection.channelType}:`, error);
      throw error;
    }
  }

  /**
   * Process an incoming message and execute matching flows
   * Enhanced with session-aware execution state management and user input handling
   */
  async processIncomingMessage(
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {
      if (message.direction === 'outbound') {
        return;
      }

      const isBotDisabled = await this.isBotDisabled(conversation.id);
      if (isBotDisabled) {
        const hardResetTriggered = await this.checkHardResetKeyword(message, conversation, contact, channelConnection);
        if (hardResetTriggered) {
          return;
        }
        return;
      }

      const activeSessions = await this.getActiveSessionsForConversation(conversation.id);

      if (activeSessions.length > 0) {

        for (const session of activeSessions) {
          if (session.aiSessionActive && await this.handleAISessionMessage(session, message, conversation, contact, channelConnection)) {
            return;
          }

          if (await this.handleUserInputForSession(session, message, conversation, contact, channelConnection)) {
            return;
          }
        }
      }

      const waitingExecutions = this.executionManager.getWaitingExecutionsForConversation(conversation.id);

      for (const execution of waitingExecutions) {
        if (await this.handleUserInputForExecution(execution, message, conversation, contact, channelConnection)) {
          return;
        }
      }

      await this.processNewFlowTriggers(message, conversation, contact, channelConnection);

    } catch (error) {
      console.error('Error processing flow for incoming message:', error);
    }
  }

  /**
   * Handle AI session message routing
   */
  private async handleAISessionMessage(
    session: FlowSessionState,
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<boolean> {
    try {
      if (!session.aiSessionActive || !session.aiNodeId) {
        return false;
      }


      const messageContent = (message.content || '').trim().toLowerCase();
      const stopKeyword = (session.aiStopKeyword || '').toLowerCase();

      if (stopKeyword && messageContent === stopKeyword) {
        return await this.endAISession(session, message, conversation, contact, channelConnection);
      }

      const baseFlow = await storage.getFlow(session.flowId);
      if (!baseFlow) return false;

      const flow: Flow = { ...baseFlow, definition: (baseFlow as any).definition || null };
      const { nodes } = await this.parseFlowDefinition(flow);

      const aiNode = nodes.find((node: any) => node.id === session.aiNodeId);
      if (!aiNode) {
        console.error(`AI node ${session.aiNodeId} not found in flow`);
        return false;
      }

      await this.executeAIAssistantNode(aiNode, message, conversation, contact, channelConnection);

      await this.updateSession(session.sessionId, {
        lastActivityAt: new Date()
      });

      return true;
    } catch (error) {
      console.error('Error handling AI session message:', error);
      return false;
    }
  }

  /**
   * End AI session and continue flow execution
   */
  private async endAISession(
    session: FlowSessionState,
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<boolean> {
    try {
      

      session.aiSessionActive = false;
      const aiNodeId = session.aiNodeId;
      const aiExitOutputHandle = session.aiExitOutputHandle;
      session.aiNodeId = null;
      session.aiStopKeyword = null;
      session.aiExitOutputHandle = null;

      await this.updateSession(session.sessionId, {
        lastActivityAt: new Date()
      });

      if (aiNodeId && aiExitOutputHandle) {
        const baseFlow = await storage.getFlow(session.flowId);
        if (baseFlow) {
          const flow: Flow = { ...baseFlow, definition: (baseFlow as any).definition || null };
          const { nodes, edges } = await this.parseFlowDefinition(flow);

          const aiNode = nodes.find((node: any) => node.id === aiNodeId);
          if (aiNode) {
            const exitEdges = edges.filter((edge: any) =>
              edge.source === aiNodeId && edge.sourceHandle === aiExitOutputHandle
            );

            if (exitEdges.length > 0) {
              

              const context = new FlowExecutionContext();
              session.variables.forEach((value, key) => {
                context.setVariable(key, value);
              });
              context.setMessageVariables(message);

              context.setVariable('flow.id', session.flowId);
              context.setVariable('session.id', session.sessionId);

              for (const edge of exitEdges) {
                const targetNode = nodes.find((node: any) => node.id === edge.target);
                if (targetNode) {
                  session.executionPath.push(targetNode.id);
                  session.currentNodeId = targetNode.id;

                  await this.updateSession(session.sessionId, {
                    currentNodeId: targetNode.id,
                    executionPath: session.executionPath
                  });

                  await this.executeNodeWithSession(
                    session,
                    targetNode,
                    nodes,
                    edges,
                    message,
                    conversation,
                    contact,
                    channelConnection,
                    context
                  );
                }
              }
            }
          }
        }
      }

      return true;
    } catch (error) {
      console.error('Error ending AI session:', error);
      return false;
    }
  }

  /**
   * Activate AI session if the AI Assistant node is configured for session takeover
   */
  private async activateAISessionIfConfigured(
    session: FlowSessionState,
    node: any,
    _context: FlowExecutionContext
  ): Promise<void> {
    try {
      const nodeData = node.data || {};

      const enableSessionTakeover = nodeData.enableSessionTakeover || false;
      const stopKeyword = nodeData.stopKeyword || '';
      const exitOutputHandle = nodeData.exitOutputHandle || 'ai-stopped';

      if (enableSessionTakeover) {

        session.aiSessionActive = true;
        session.aiNodeId = node.id;
        session.aiStopKeyword = stopKeyword.trim();
        session.aiExitOutputHandle = exitOutputHandle;

        await this.updateSession(session.sessionId, {
          lastActivityAt: new Date()
        });

      }
    } catch (error) {
      console.error('Error activating AI session:', error);
    }
  }

  /**
   * Handle user input for a session
   */
  private async handleUserInputForSession(
    session: FlowSessionState,
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<boolean> {
    try {
      if (session.status !== 'waiting' || !session.currentNodeId) {
        return false;
      }

      const baseFlow = await storage.getFlow(session.flowId);
      if (!baseFlow) return false;

      const flow: Flow = { ...baseFlow, definition: (baseFlow as any).definition || null };
      const { nodes, edges } = await this.parseFlowDefinition(flow);

      const currentNode = nodes.find((node: any) => node.id === session.currentNodeId);
      if (!currentNode) return false;

      const tempContext = new FlowExecutionContext();

      for (const [key, value] of Array.from(session.variables.entries())) {
        tempContext.setVariable(key, value);
      }

      tempContext.setVariable('flow.id', session.flowId);
      tempContext.setVariable('session.id', session.sessionId);

      const inputMatches = await this.checkUserInputMatch(currentNode, message, tempContext);

      if (inputMatches) {
        const completeMessage = tempContext.getVariable('message.content') || message.content || '';
        const inputType = tempContext.getVariable('user.inputType') || 'text';

        tempContext.setUserInput(completeMessage, inputType);

        const updatedMessage = { ...message, content: completeMessage };
        tempContext.setMessageVariables(updatedMessage);

        const allVariables = tempContext.getAllVariables();
        for (const [key, value] of Object.entries(allVariables)) {
          session.variables.set(key, value);

          if (value !== null && value !== undefined) {
            try {
              await storage.upsertFlowSessionVariable({
                sessionId: session.sessionId,
                variableKey: key,
                variableValue: JSON.stringify(value),
                variableType: typeof value,
                scope: 'session',
                createdAt: new Date(),
                updatedAt: new Date()
              });
            } catch (error) {
              console.error('Error persisting session variable:', error);
            }
          }
        }

        await this.updateSession(session.sessionId, {
          status: 'active',
          waitingContext: null,
          lastActivityAt: new Date()
        });

        await this.continueSessionExecutionFromNode(
          session,
          currentNode,
          nodes,
          edges,
          message,
          conversation,
          contact,
          channelConnection
        );

        return true;
      }

      return false;
    } catch (error) {
      console.error('Error handling user input for session:', error);
      return false;
    }
  }

  /**
   * Continue session execution from a specific node
   */
  private async continueSessionExecutionFromNode(
    session: FlowSessionState,
    currentNode: any,
    allNodes: any[],
    edges: any[],
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {
      const context = new FlowExecutionContext();

      session.variables.forEach((value, key) => {
        context.setVariable(key, value);
      });

      context.setVariable('flow.id', session.flowId);
      context.setVariable('session.id', session.sessionId);

      await this.executeConnectedNodesWithSession(
        session,
        currentNode,
        allNodes,
        edges,
        message,
        conversation,
        contact,
        channelConnection,
        context,
        true
      );
    } catch (error) {
      console.error('Error continuing session execution:', error);
      await this.updateSession(session.sessionId, {
        status: 'failed',
        waitingContext: null
      });
    }
  }

  /**
   * Execute connected nodes with session awareness
   */
  private async executeConnectedNodesWithSession(
    session: FlowSessionState,
    currentNode: any,
    allNodes: any[],
    edges: any[],
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection,
    context: FlowExecutionContext,
    skipWaitingCheck: boolean = false
  ): Promise<void> {
    try {
      if (!skipWaitingCheck) {
        const currentNodeType = NodeTypeUtils.normalizeNodeType(currentNode.type || '', currentNode.data?.label);
        const shouldWaitForInput = this.shouldNodeWaitForInput(currentNodeType);

        if (shouldWaitForInput) {

          await this.updateSession(session.sessionId, {
            status: 'waiting',
            waitingContext: {
              nodeId: currentNode.id,
              nodeType: currentNodeType,
              expectedInputType: this.getExpectedInputType(currentNode),
              timestamp: new Date()
            }
          });

          this.emit('sessionWaiting', {
            sessionId: session.sessionId,
            nodeId: currentNode.id,
            nodeType: currentNodeType
          });

          return;
        }
      }

      const outgoingEdges = edges.filter((edge: any) => edge.source === currentNode.id);

      if (outgoingEdges.length === 0) {
        await this.updateSession(session.sessionId, {
          status: 'completed',
          currentNodeId: null
        });

        this.emit('sessionCompleted', {
          sessionId: session.sessionId,
          flowId: session.flowId,
          conversationId: session.conversationId
        });

        return;
      }

      const nodeType = currentNode.type || '';
      const nodeLabel = (currentNode.data && currentNode.data.label) || '';
      const currentNodeType = NodeTypeUtils.normalizeNodeType(nodeType, nodeLabel);
      const isConditionNode = nodeType === 'conditionNode' ||
                             nodeType === 'condition' ||
                             nodeLabel === 'Condition Node' ||
                             currentNodeType === NodeType.CONDITION;

      let edgesToExecute = outgoingEdges;

      if (isConditionNode) {
        const conditionResult = await this.executeConditionNodeWithContext(currentNode, context);

        const yesEdges = outgoingEdges.filter((edge: any) =>
          edge.sourceHandle === 'yes' ||
          edge.sourceHandle === 'true' ||
          edge.sourceHandle === 'success' ||
          edge.sourceHandle === 'positive'
        );

        const noEdges = outgoingEdges.filter((edge: any) =>
          edge.sourceHandle === 'no' ||
          edge.sourceHandle === 'false' ||
          edge.sourceHandle === 'failure' ||
          edge.sourceHandle === 'negative'
        );

        if (conditionResult) {
          edgesToExecute = yesEdges.length > 0 ? yesEdges : [];
        } else {
          edgesToExecute = noEdges.length > 0 ? noEdges : [];
        }

        if (yesEdges.length === 0 && noEdges.length === 0) {
          
          edgesToExecute = outgoingEdges;
        }
      } else if (currentNodeType === NodeType.AI_ASSISTANT && currentNode.data?.enableTaskExecution) {
        const triggeredTasks = context.getVariable('ai.triggeredTasks') as string[];

        if (triggeredTasks && triggeredTasks.length > 0) {
          const taskEdges = outgoingEdges.filter((edge: any) =>
            triggeredTasks.includes(edge.sourceHandle)
          );

          if (taskEdges.length > 0) {
            
            edgesToExecute = taskEdges;
          } else {
            
            edgesToExecute = [];
          }
        } else {
          
          edgesToExecute = [];
        }
      }

      for (const edge of edgesToExecute) {
        const targetNode = allNodes.find((node: any) => node.id === edge.target);
        if (!targetNode) {
          
          continue;
        }

        const shouldTraverse = await this.evaluateEdgeCondition(edge, context);

        if (shouldTraverse) {
          session.executionPath.push(targetNode.id);
          session.currentNodeId = targetNode.id;

          await this.updateSession(session.sessionId, {
            currentNodeId: targetNode.id,
            executionPath: session.executionPath
          });

          await this.executeNodeWithSession(
            session,
            targetNode,
            allNodes,
            edges,
            message,
            conversation,
            contact,
            channelConnection,
            context
          );

          if (!isConditionNode && (edge.data?.conditionType || edge.data?.condition)) {
            break;
          }
        }
      }
    } catch (error) {
      console.error('Error executing connected nodes with session:', error);
      await this.updateSession(session.sessionId, {
        status: 'failed'
      });
    }
  }

  /**
   * Check if a node type should wait for user input
   */
  private shouldNodeWaitForInput(nodeType: string | null): boolean {
    if (!nodeType) return false;

    switch (nodeType) {
      case NodeType.QUICK_REPLY:
      case NodeType.INPUT:
        return true;
      case NodeType.WAIT:
        return false;
      default:
        return false;
    }
  }

  /**
   * Execute a single node with session context
   */
  private async executeNodeWithSession(
    session: FlowSessionState,
    node: any,
    allNodes: any[],
    edges: any[],
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection,
    context: FlowExecutionContext
  ): Promise<void> {
    try {
      const nodeType = NodeTypeUtils.normalizeNodeType(node.type || '', node.data?.label);

      session.nodeStates.set(node.id, {
        startTime: new Date(),
        nodeType: nodeType || 'unknown',
        status: 'executing'
      });

      switch (nodeType) {
        case NodeType.MESSAGE:
          await this.executeMessageNodeWithContext(node, context, conversation, contact, channelConnection);
          break;

        case NodeType.IMAGE:
          await this.executeMessageNodeWithContext(node, context, conversation, contact, channelConnection, 'image');
          break;

        case NodeType.VIDEO:
          await this.executeMessageNodeWithContext(node, context, conversation, contact, channelConnection, 'video');
          break;

        case NodeType.AUDIO:
          await this.executeMessageNodeWithContext(node, context, conversation, contact, channelConnection, 'audio');
          break;

        case NodeType.DOCUMENT:
          await this.executeMessageNodeWithContext(node, context, conversation, contact, channelConnection, 'document');
          break;

        case NodeType.QUICK_REPLY:
          await this.executeQuickReplyNodeWithContext(node, context, conversation, contact, channelConnection);
          break;

        case NodeType.FOLLOW_UP:
          await this.executeFollowUpNodeWithContext(node, context, conversation, contact, channelConnection);
          break;

        case NodeType.CONDITION:
          await this.executeConditionNodeWithContext(node, context);
          break;

        case NodeType.WAIT:
          await this.executeWaitNodeWithContext(node, context);
          break;

        case NodeType.AI_ASSISTANT:
          await this.executeAIAssistantNodeWithContext(node, context, conversation, contact, channelConnection);
          await this.activateAISessionIfConfigured(session, node, context);

          if (session.aiSessionActive) {
            
            return;
          }
          break;

        case NodeType.WEBHOOK:
          await this.executeWebhookNodeWithContext(node, context, conversation, contact, channelConnection);
          break;

        case NodeType.HTTP_REQUEST:
          await this.executeHttpRequestNodeWithContext(node, context, conversation, contact, channelConnection);
          break;

        case NodeType.BOT_DISABLE:
          await this.executeBotDisableNodeWithContext(node, context, conversation, contact, channelConnection);
          
          return;

        case NodeType.BOT_RESET:
          await this.executeBotResetNodeWithContext(node, context, conversation, contact, channelConnection);
          break;

        case NodeType.INPUT:
          await this.executeInputNodeWithContext(node, context, conversation, contact, channelConnection);
          
          return;

        case NodeType.ACTION:
          await this.executeActionNodeWithContext(node, context, conversation, contact, channelConnection);
          break;

        case NodeType.SHOPIFY:
          await this.executeShopifyNodeWithContext(node, context, conversation, contact, channelConnection);
          break;

        case NodeType.WOOCOMMERCE:
          await this.executeWooCommerceNodeWithContext(node, context, conversation, contact, channelConnection);
          break;

        case NodeType.TYPEBOT:
          await this.executeTypebotNodeWithContext(node, context, conversation, contact, channelConnection);
          break;

        case NodeType.FLOWISE:
          await this.executeFlowiseNodeWithContext(node, context, conversation, contact, channelConnection);
          break;

        case NodeType.N8N:
          await this.executeN8nNodeWithContext(node, context, conversation, contact, channelConnection);
          break;

        case NodeType.GOOGLE_SHEETS:
          await this.executeGoogleSheetsNodeWithContext(node, context, conversation, contact, channelConnection);
          break;

        case NodeType.DOCUMIND:
          await this.executeDocumindNodeWithContext(node, context, conversation, contact, channelConnection);
          break;

        case NodeType.GOOGLE_CALENDAR:
          await this.executeGoogleCalendarNodeWithContext(node, context, conversation, contact, channelConnection);
          break;

        case NodeType.UPDATE_PIPELINE_STAGE:
          await this.executeUpdatePipelineStageNodeWithContext(node, context, conversation, contact, channelConnection);
          break;

        case NodeType.TRIGGER:
          
          break;

        default:
          
          break;
      }

      session.nodeStates.set(node.id, {
        ...session.nodeStates.get(node.id),
        endTime: new Date(),
        status: 'completed'
      });

      const allVariables = context.getAllVariables();
      for (const [key, value] of Object.entries(allVariables)) {
        session.variables.set(key, value);

        if (value !== null && value !== undefined) {
          try {
            await storage.upsertFlowSessionVariable({
              sessionId: session.sessionId,
              variableKey: key,
              variableValue: JSON.stringify(value),
              variableType: typeof value,
              scope: 'session',
              createdAt: new Date(),
              updatedAt: new Date()
            });
          } catch (error) {
            console.error('Error persisting session variable:', error);
          }
        }
      }

      if (session.aiSessionActive) {
        
        return;
      }

      await this.executeConnectedNodesWithSession(
        session,
        node,
        allNodes,
        edges,
        message,
        conversation,
        contact,
        channelConnection,
        context
      );

    } catch (error) {
      console.error(`Error executing node ${node.id} in session ${session.sessionId}:`, error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      session.nodeStates.set(node.id, {
        ...session.nodeStates.get(node.id),
        endTime: new Date(),
        status: 'failed',
        error: errorMessage
      });

      await this.updateSession(session.sessionId, {
        status: 'failed'
      });
    }
  }

  /**
   * Get expected input type for a node
   */
  private getExpectedInputType(node: any): string {
    const nodeType = NodeTypeUtils.normalizeNodeType(node.type || '', node.data?.label);

    switch (nodeType) {
      case NodeType.QUICK_REPLY:
        return 'quick_reply';
      case NodeType.INPUT:
        return node.data?.inputType || 'text';
      case NodeType.WAIT:
        return 'none';
      default:
        return 'text';
    }
  }

  /**
   * Evaluate edge condition
   */
  private async evaluateEdgeCondition(edge: any, context: FlowExecutionContext): Promise<boolean> {
    try {
      if (edge.sourceHandle && edge.sourceHandle.startsWith('option-')) {
        return this.evaluateQuickReplyEdge(edge, context);
      }

      if (!edge.data?.condition && !edge.data?.conditionType) {
        return true;
      }

      const condition = edge.data.condition || edge.data.conditionType;
      const conditionValue = edge.data.conditionValue || edge.data.value || '';

      switch (condition?.toLowerCase()) {
        case 'always':
        case 'true':
          return true;

        case 'never':
        case 'false':
          return false;

        case 'equals':
          const variable = context.getVariable(edge.data.variable || 'selectedOption');
          return variable === conditionValue;

        case 'contains':
          const textVariable = context.getVariable(edge.data.variable || 'message.content') || '';
          return textVariable.toLowerCase().includes(conditionValue.toLowerCase());

        default:
          return true;
      }
    } catch (error) {
      console.error('Error evaluating edge condition:', error);
      return true;
    }
  }

  /**
   * Evaluate Quick Reply node edge based on user selection
   */
  private evaluateQuickReplyEdge(edge: any, context: FlowExecutionContext): boolean {
    try {
      const sourceHandle = edge.sourceHandle;
      const optionMatch = sourceHandle.match(/option-(\d+)/);

      if (!optionMatch) {
        
        return false;
      }

      const edgeOptionNumber = parseInt(optionMatch[1], 10);

      const selectedOptionIndex = context.getVariable('selectedOptionIndex');

      if (selectedOptionIndex === null || selectedOptionIndex === undefined) {
        return false;
      }

      const selectedOptionNumber = selectedOptionIndex + 1;

      const shouldTraverse = edgeOptionNumber === selectedOptionNumber;


      return shouldTraverse;
    } catch (error) {
      console.error('Error evaluating Quick Reply edge:', error);
      return false;
    }
  }

  /**
   * Handle user input for waiting executions
   */
  private async handleUserInputForExecution(
    execution: any,
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<boolean> {
    try {
      const baseFlow = await storage.getFlow(execution.flowId);
      if (!baseFlow) return false;

      const flow: Flow = { ...baseFlow, definition: (baseFlow as any).definition || null };
      const { nodes, edges } = await this.parseFlowDefinition(flow);

      const currentNode = nodes.find((node: any) => node.id === execution.currentNodeId);
      if (!currentNode) return false;

      const inputMatches = await this.checkUserInputMatch(currentNode, message, execution.context);

      if (inputMatches) {
        const completeMessage = execution.context.getVariable('message.content') || message.content || '';
        const inputType = execution.context.getVariable('user.inputType') || 'text';

        execution.context.setUserInput(completeMessage, inputType);

        const updatedMessage = { ...message, content: completeMessage };
        execution.context.setMessageVariables(updatedMessage);

        this.executionManager.resumeExecution(execution.id, completeMessage);

        await this.continueExecutionFromNode(
          execution.id,
          currentNode,
          nodes,
          edges,
          updatedMessage,
          conversation,
          contact,
          channelConnection
        );

        return true;
      }

      return false;
    } catch (error) {
      console.error('Error handling user input for execution:', error);
      return false;
    }
  }

  /**
   * Process new flow triggers
   */
  private async processNewFlowTriggers(
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    const flowAssignments = await storage.getFlowAssignments(channelConnection.id);
    const activeAssignments = flowAssignments.filter(assignment => assignment.isActive);

    if (activeAssignments.length === 0) {
      return;
    }


    for (const assignment of activeAssignments) {
      await this.executeFlow(assignment, message, conversation, contact, channelConnection);
    }
  }

  /**
   * Parse flow definition to extract nodes and edges
   */
  private async parseFlowDefinition(flow: Flow): Promise<{ nodes: any[], edges: any[] }> {
    let nodes: any[] = [];
    let edges: any[] = [];

    if (flow.definition) {
      try {
        const definition = typeof flow.definition === 'string'
          ? JSON.parse(flow.definition)
          : flow.definition;

        if (definition.nodes && Array.isArray(definition.nodes)) {
          nodes = definition.nodes;
        }

        if (definition.edges && Array.isArray(definition.edges)) {
          edges = definition.edges;
        }
      } catch (error) {
        console.error('Error parsing flow definition:', error);
      }
    }

    if (nodes.length === 0) {
      try {
        if (typeof flow.nodes === 'string') {
          nodes = JSON.parse(flow.nodes);
        } else if (flow.nodes && Array.isArray(flow.nodes)) {
          nodes = flow.nodes;
        }
      } catch (error) {
        console.error('Error parsing flow nodes:', error);
        nodes = [];
      }
    }

    if (edges.length === 0) {
      try {
        if (typeof flow.edges === 'string') {
          edges = JSON.parse(flow.edges);
        } else if (flow.edges && Array.isArray(flow.edges)) {
          edges = flow.edges;
        }
      } catch (error) {
        console.error('Error parsing flow edges:', error);
        edges = [];
      }
    }

    return { nodes, edges };
  }

  /**
   * Check if user input matches expected input for a node
   */
  private async checkUserInputMatch(node: any, message: Message, context: FlowExecutionContext): Promise<boolean> {
    try {
      const nodeType = NodeTypeUtils.normalizeNodeType(node.type || '', node.data?.label);

      if (!nodeType) {
        
        return true;
      }

      switch (nodeType) {
        case NodeType.QUICK_REPLY:
          return this.handleQuickReplyInput(node, message, context);

        case NodeType.CONDITION:
          return true;

        case NodeType.INPUT:
          return true;

        default:
          return NodeTypeUtils.requiresUserInput(nodeType);
      }
    } catch (error) {
      console.error('Flow executor: Error checking user input match:', error);
      return true;
    }
  }

  /**
   * Handle quick reply input matching
   */
  private handleQuickReplyInput(node: any, message: Message, context: FlowExecutionContext): boolean {
    try {
      const options = node.data?.options || [];
      const messageContent = message.content?.toLowerCase() || '';

      for (let i = 0; i < options.length; i++) {
        const option = options[i];
        const optionText = (option.text || option.label || '').toLowerCase();
        const optionIndex = (i + 1).toString();

        if (messageContent === optionText || messageContent === optionIndex) {
          context.setVariable('selectedOption', option);
          context.setVariable('selectedOptionIndex', i);
          context.setVariable('selectedOptionText', option.text || option.label);

          const questionText = context.getVariable('quickReply.questionText') || '';
          const selectedText = option.text || option.label || '';

          const completeMessage = questionText
            ? `${questionText} [User selected: ${selectedText}]`
            : `[User selected: ${selectedText}]`;

          context.setVariable('message.content', completeMessage);
          context.setVariable('message.originalContent', message.content);
          context.setVariable('quickReply.completeInteraction', completeMessage);
          context.setVariable('user.input', completeMessage);
          context.setVariable('user.lastInput', completeMessage);
          context.setVariable('user.inputType', 'quickreply');

          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('Flow executor: Error handling quick reply input:', error);
      return false;
    }
  }

  /**
   * Continue execution from a specific node
   */
  private async continueExecutionFromNode(
    executionId: string,
    currentNode: any,
    nodes: any[],
    edges: any[],
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    const execution = this.executionManager.getExecution(executionId);
    if (!execution) return;

    await this.executeConnectedNodesWithExecution(
      executionId,
      currentNode,
      nodes,
      edges,
      message,
      conversation,
      contact,
      channelConnection
    );
  }

  /**
   * Execute a specific flow for an incoming message - Enhanced Session-Aware Style
   */
  async executeFlow(
    assignment: FlowAssignment,
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {
      const isBotDisabled = await this.isBotDisabled(conversation.id);
      if (isBotDisabled) {
        
        return;
      }

      const baseFlow = await storage.getFlow(assignment.flowId);

      if (!baseFlow) {
        console.error(`Flow ${assignment.flowId} not found`);
        return;
      }

      const flow: Flow = { ...baseFlow, definition: (baseFlow as any).definition || null };


      const { nodes, edges } = await this.parseFlowDefinition(flow);

      if (nodes.length === 0) {
        return;
      }

      const triggerNodes = nodes.filter((node: any) =>
        node.type === 'triggerNode' ||
        node.type === 'trigger' ||
        (node.data && node.data.label === 'Trigger Node') ||
        (node.data && node.data.label === 'Message Received')
      );


      if (triggerNodes.length === 0) {
        return;
      }

      for (const triggerNode of triggerNodes) {
        if (this.matchesTrigger(triggerNode, message)) {

          const sessionId = await this.createSession(
            flow.id,
            conversation.id,
            contact.id,
            conversation.companyId || 0,
            triggerNode.id,
            {
              message,
              contact,
              conversation,
              channelConnection
            }
          );

          const session = this.activeSessions.get(sessionId);
          if (!session) {
            console.error(`Failed to create session for flow ${flow.id}`);
            return;
          }

          const context = new FlowExecutionContext();
          context.setContactVariables(contact);
          context.setMessageVariables(message);
          context.setConversationVariables(conversation);

          context.setVariable('flow.id', flow.id);
          context.setVariable('session.id', sessionId);

          const allVariables = context.getAllVariables();
          for (const [key, value] of Object.entries(allVariables)) {
            session.variables.set(key, value);

            if (value !== null && value !== undefined) {
              try {
                await storage.upsertFlowSessionVariable({
                  sessionId: session.sessionId,
                  variableKey: key,
                  variableValue: JSON.stringify(value),
                  variableType: typeof value,
                  scope: 'session',
                  createdAt: new Date(),
                  updatedAt: new Date()
                });
              } catch (error) {
                console.error('Error persisting initial session variable:', error);
              }
            }
          }

          await this.executeConnectedNodesWithSession(
            session,
            triggerNode,
            nodes,
            edges,
            message,
            conversation,
            contact,
            channelConnection,
            context
          );

          const executionId = this.executionManager.startExecution(
            flow.id,
            conversation.id,
            contact.id,
            triggerNode.id,
            {
              message: message,
              contact: contact,
              conversation: conversation,
              channelConnection: channelConnection,
              sessionId: sessionId
            }
          );

          const execution = this.executionManager.getExecution(executionId);
          if (execution) {
            execution.context.setContactVariables(contact);
            execution.context.setMessageVariables(message);
            execution.context.setConversationVariables(conversation);
            execution.context.setVariable('flow.id', flow.id);
            execution.context.setVariable('session.id', sessionId);
          }

          break;
        }
      }

    } catch (error) {
      console.error(`Error executing flow ${assignment.flowId}:`, error);
    }
  }

  /**
   * Check if a message matches a trigger node's conditions
   */
  matchesTrigger(triggerNode: any, message: Message): boolean {

    const data = triggerNode.data || {};


    const conditionType = data.conditionType || data.condition || 'Any Message';
    const conditionValue = data.conditionValue || data.value || '';


    if (message.type !== 'text' &&
        conditionType.toLowerCase() !== 'any message' &&
        conditionType.toLowerCase() !== 'any' &&
        conditionType.toLowerCase() !== 'has media') {
      return false;
    }

    const condition = conditionType.toLowerCase();

    if (condition === 'any message' || condition === 'any') {
      return true;
    }

    if (condition === 'contains word' || condition === 'contains') {
      const matches = message.content.toLowerCase().includes(conditionValue.toLowerCase());
      return matches;
    }

    if (condition === 'exact match' || condition === 'exact') {
      const matches = message.content.toLowerCase() === conditionValue.toLowerCase();
      return matches;
    }

    if (condition === 'regex pattern' || condition === 'regex') {
      try {
        const regex = new RegExp(conditionValue, 'i');
        const matches = regex.test(message.content);
        return matches;
      } catch (error) {
        console.error('Invalid regex pattern:', error);
        return false;
      }
    }

    if (condition === 'has media') {
      const hasMedia = message.mediaUrl !== null;
      return hasMedia;
    }

    return false;
  }

  /**
   * Get availability data from the flow context
   * This implementation uses data stored by the GoogleCalendarAvailabilityNode
   */
  getAvailabilityData(message: Message): string {
    try {


      const today = new Date();
      const formattedDate = today.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      const extendedMessage = message as Message & {
        flowContext?: {
          availabilityData?: string
        }
      };

      if (extendedMessage.flowContext && extendedMessage.flowContext.availabilityData) {
        return extendedMessage.flowContext.availabilityData;
      }

      const availableTimes = [
        '9:00 AM - 10:00 AM',
        '11:30 AM - 12:30 PM',
        '2:00 PM - 3:00 PM',
        '4:30 PM - 5:30 PM'
      ];

      return `Available times on ${formattedDate}:\n\n${availableTimes.join('\n')}`;
    } catch (error) {
      console.error('Error getting availability data:', error);
      return 'No availability data found. Please try again later.';
    }
  }

  /**
   * Replace variables in a message with values from the context
   */
  replaceVariables(template: string, message: Message, contact: Contact): string {
    try {
      const contactVars: Record<string, string> = {
        'contact.id': contact.id?.toString() || '',
        'contact.name': contact.name || '',
        'contact.identifier': contact.identifier || '',
        'contact.phone': contact.phone || '',
        'contact.email': contact.email || '',
      };

      const messageVars: Record<string, string> = {
        'message.content': message.content || '',
        'message.type': message.type || '',
      };

      const calendarVars: Record<string, string> = {
        'availability': this.getAvailabilityData(message)
      };

      const dateVars: Record<string, string> = {
        'date.today': new Date().toLocaleDateString(),
        'time.now': new Date().toLocaleTimeString()
      };

      const allVars = { ...contactVars, ...messageVars, ...calendarVars, ...dateVars };

      let result = template;
      for (const [key, value] of Object.entries(allVars)) {
        result = result.replace(new RegExp(`{{${key}}}`, 'g'), value.toString());
      }

      return result;
    } catch (error) {
      console.error('Error replacing variables:', error);
      return template;
    }
  }

  /**
   * Execute a message node (send a message)
   */
  async executeMessageNode(
    node: any,
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection,
    messageType: string = 'text'
  ): Promise<void> {
    try {

      const data = node.data || {};

      let content = '';
      let mediaUrl = '';

      if (messageType === 'text') {
        content = data.messageContent || data.message || data.text || '';
    

        content = this.replaceVariables(content, message, contact);
      } else {
        mediaUrl = data.mediaUrl || data.url || '';
        content = data.caption || '';
        content = this.replaceVariables(content, message, contact);
      }


      let newMessage;

      // Use unified message sending for all channel types
      try {
        if (messageType === 'text') {
          newMessage = await this.sendMessageThroughChannel(
            channelConnection,
            contact,
            content,
            conversation,
            true
          );
        } else {
          newMessage = await this.sendMediaThroughChannel(
            channelConnection,
            contact,
            mediaUrl,
            messageType as "image" | "video" | "audio" | "document",
            content,
            undefined,
            conversation,
            true
          );
        }
      } catch (channelError) {
        console.error('Error sending through channel, falling back to database:', channelError);
        // Fallback to database storage
        const insertMessage = {
          conversationId: conversation.id,
          contactId: contact.id,
          type: messageType,
          content: content,
          direction: 'outbound',
          status: 'sent',
          mediaUrl: messageType === 'text' ? null : mediaUrl,
          timestamp: new Date()
        };

        newMessage = await storage.createMessage(insertMessage);
      }
    } catch (error) {
      console.error(`Error executing ${messageType} message node:`, error);
    }
  }

  /**
   * Execute a wait node (delay execution) - Legacy method
   */
  async executeWaitNode(
    node: any  ): Promise<void> {
    try {

      const data = node.data || {};

      const waitMode = data.waitMode || 'duration';

      if (waitMode === 'duration') {
        const timeValue = data.timeValue || data.duration || 5;
        const timeUnit = data.timeUnit || data.durationUnit || 'seconds';

        let waitMs = timeValue;
        switch (timeUnit.toLowerCase()) {
          case 'milliseconds':
          case 'ms':
            waitMs = timeValue;
            break;
          case 'seconds':
          case 'sec':
          case 's':
            waitMs = timeValue * 1000;
            break;
          case 'minutes':
          case 'min':
          case 'm':
            waitMs = timeValue * 60 * 1000;
            break;
          case 'hours':
          case 'hour':
          case 'h':
            waitMs = timeValue * 60 * 60 * 1000;
            break;
          default:
            waitMs = timeValue * 1000;
        }


        await new Promise(resolve => setTimeout(resolve, waitMs));


      } else if (waitMode === 'datetime') {
        const waitDate = data.waitDate ? new Date(data.waitDate) : null;
        const waitTime = data.waitTime || '';

        if (waitDate) {
          if (waitTime) {
            const [hours, minutes] = waitTime.split(':').map(Number);
            waitDate.setHours(hours, minutes, 0, 0);
          }

          const now = new Date();
          const waitMs = waitDate.getTime() - now.getTime();

          if (waitMs > 0) {

            await new Promise(resolve => setTimeout(resolve, waitMs));

          } else {
          }
        } else {
          
        }
      }

    } catch (error) {
      console.error('Error executing wait node:', error);
    }
  }

  /**
   * Execute a quick reply node
   */
  async executeQuickReplyNode(
    node: any,
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {

      const data = node.data || {};

      let questionText = data.question || data.messageContent || data.text || data.message || data.prompt || '';
      questionText = this.replaceVariables(questionText, message, contact);

      const options = data.options || [];

      let formattedMessage = `${questionText}\n\n`;

      options.forEach((option: any, index: number) => {
        const optionText = option.text || option.label || `Option ${index + 1}`;
        formattedMessage += `${index + 1}. ${optionText}\n`;
      });

      // Use unified message sending for all channel types
      try {
        await this.sendMessageThroughChannel(
          channelConnection,
          contact,
          formattedMessage,
          conversation,
          true
        );
      } catch (channelError) {
        console.error('Error sending through channel, falling back to database:', channelError);
        // Fallback to database storage
        const insertMessage = {
          conversationId: conversation.id,
          contactId: contact.id,
          channelType: channelConnection.channelType,
          type: 'text',
          content: formattedMessage,
          direction: 'outbound',
          status: 'sent',
          mediaUrl: null,
          timestamp: new Date()
        };

        await storage.createMessage(insertMessage);
      }

    } catch (error) {
      console.error('Error executing quick reply node:', error);
    }
  }

  /**
   * Execute a condition node (evaluate a condition)
   */
  async executeConditionNode(
    node: any,
    message: Message  ): Promise<boolean> {
    try {

      const data = node.data || {};

      const conditionType = data.conditionType || 'contains';
      const conditionValue = data.conditionValue || '';


      if (conditionType.toLowerCase() === 'message contains') {
        const result = message.content.toLowerCase().includes(conditionValue.toLowerCase());
        return result;
      }

      switch (conditionType.toLowerCase()) {
        case 'contains':
          return message.content.toLowerCase().includes(conditionValue.toLowerCase());

        case 'exact match':
          return message.content.toLowerCase() === conditionValue.toLowerCase();

        case 'starts with':
          return message.content.toLowerCase().startsWith(conditionValue.toLowerCase());

        case 'ends with':
          return message.content.toLowerCase().endsWith(conditionValue.toLowerCase());

        case 'has media':
          return !!message.mediaUrl;

        default:
          return message.content.toLowerCase().includes(conditionValue.toLowerCase());
      }

    } catch (error) {
      console.error('Error executing condition node:', error);
      return false;
    }
  }


  /**
   * Execute nodes connected to the current node with execution tracking
   */
  async executeConnectedNodesWithExecution(
    executionId: string,
    currentNode: any,
    allNodes: any[],
    edges: any[],
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    const execution = this.executionManager.getExecution(executionId);
    if (!execution) {
      console.error(`Execution ${executionId} not found`);
      return;
    }

    const connectedEdges = edges.filter((edge: any) => edge.source === currentNode.id);


    const nodeType = currentNode.type || '';
    const nodeLabel = (currentNode.data && currentNode.data.label) || '';
    const isConditionNode = nodeType === 'conditionNode' ||
                           nodeType === 'condition' ||
                           nodeLabel === 'Condition Node';

    const isQuickReplyNode = nodeType === 'quickreplyNode' ||
                            nodeType === 'quickreply' ||
                            nodeLabel === 'Quickreply Node' ||
                            nodeLabel === 'Quick Reply Options';

    let edgesToExecute = connectedEdges;

    if (isConditionNode) {
      const conditionResult = await this.executeConditionNodeWithContext(currentNode, execution.context);

      const yesEdges = connectedEdges.filter((edge: any) =>
        edge.sourceHandle === 'yes' ||
        edge.sourceHandle === 'true' ||
        edge.sourceHandle === 'success' ||
        edge.sourceHandle === 'positive'
      );

      const noEdges = connectedEdges.filter((edge: any) =>
        edge.sourceHandle === 'no' ||
        edge.sourceHandle === 'false' ||
        edge.sourceHandle === 'failure' ||
        edge.sourceHandle === 'negative'
      );

      if (conditionResult) {
        edgesToExecute = yesEdges.length > 0 ? yesEdges : [];
      } else {
        edgesToExecute = noEdges.length > 0 ? noEdges : [];
      }

      if (yesEdges.length === 0 && noEdges.length === 0) {
        
        edgesToExecute = connectedEdges;
      }
    } else if (isQuickReplyNode) {
      const selectedOptionIndex = execution.context.getVariable('selectedOptionIndex');

      if (selectedOptionIndex !== null && selectedOptionIndex !== undefined) {
        const selectedOptionNumber = selectedOptionIndex + 1;
        const selectedEdges = connectedEdges.filter((edge: any) => {
          if (edge.sourceHandle && edge.sourceHandle.startsWith('option-')) {
            const optionMatch = edge.sourceHandle.match(/option-(\d+)/);
            if (optionMatch) {
              const edgeOptionNumber = parseInt(optionMatch[1], 10);
              return edgeOptionNumber === selectedOptionNumber;
            }
          }
          return false;
        });

        edgesToExecute = selectedEdges;
      } else {
        
        edgesToExecute = [];
      }
    }

    for (const edge of edgesToExecute) {
      const targetNode = allNodes.find((node: any) => node.id === edge.target);

      if (!targetNode) {
        
        continue;
      }

      this.executionManager.updateExecution(executionId, targetNode.id, 'running');


      const nodeResult = await this.executeNodeWithExecution(
        executionId,
        targetNode,
        message,
        conversation,
        contact,
        channelConnection
      );

      if (!nodeResult.success) {
        console.error(`Node execution failed: ${nodeResult.error}`);
        this.executionManager.failExecution(executionId, nodeResult.error || 'Node execution failed');
        return;
      }

      if (nodeResult.waitForUserInput) {
        this.executionManager.setWaitingForInput(executionId, targetNode.id);
        return;
      }

      if (nodeResult.shouldContinue) {
        await this.executeConnectedNodesWithExecution(
          executionId,
          targetNode,
          allNodes,
          edges,
          message,
          conversation,
          contact,
          channelConnection
        );
      }
    }

    if (edgesToExecute.length === 0) {
      this.executionManager.completeExecution(executionId, { endNode: currentNode.id });
    } else if (connectedEdges.length === 0) {
      this.executionManager.completeExecution(executionId, { endNode: currentNode.id });
    }
  }

  /**
   * Execute a single node with execution context
   */
  private async executeNodeWithExecution(
    executionId: string,
    node: any,
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<NodeExecutionResult> {
    const execution = this.executionManager.getExecution(executionId);
    if (!execution) {
      return { success: false, shouldContinue: false, error: 'Execution not found' };
    }

    const nodeType = node.type || '';
    const nodeLabel = (node.data && node.data.label) || '';

    const nodeStartTime = Date.now();
    const inputData = {
      nodeType,
      nodeLabel,
      nodeData: node.data,
      messageContent: message.content,
      contactId: contact.id
    };

    try {
      if (
        nodeType === 'messageNode' ||
        nodeType === 'message' ||
        nodeLabel === 'Message Node' ||
        nodeLabel === 'Send Message'
      ) {
        await this.executeMessageNodeWithContext(node, execution.context, conversation, contact, channelConnection, 'text');

        const duration = Date.now() - nodeStartTime;
        this.executionManager.trackNodeExecution(
          executionId,
          node.id,
          'message',
          duration,
          'completed',
          inputData,
          { messageType: 'text' },
          undefined
        );

        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'imageNode' || nodeType === 'image' ||
        nodeType === 'videoNode' || nodeType === 'video' ||
        nodeType === 'audioNode' || nodeType === 'audio' ||
        nodeType === 'documentNode' || nodeType === 'document' ||
        nodeLabel === 'Image Node' || nodeLabel === 'Send Image' ||
        nodeLabel === 'Video Node' || nodeLabel === 'Send Video' ||
        nodeLabel === 'Audio Node' || nodeLabel === 'Send Audio' ||
        nodeLabel === 'Document Node' || nodeLabel === 'Send Document'
      ) {
        const mediaType = nodeType.includes('image') || nodeLabel.includes('Image') ? 'image' :
                          nodeType.includes('video') || nodeLabel.includes('Video') ? 'video' :
                          nodeType.includes('audio') || nodeLabel.includes('Audio') ? 'audio' : 'document';

        await this.executeMessageNodeWithContext(node, execution.context, conversation, contact, channelConnection, mediaType as "image" | "video" | "audio" | "document");
        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'quickreplyNode' ||
        nodeType === 'quickreply' ||
        nodeLabel === 'Quickreply Node'
      ) {
        await this.executeQuickReplyNodeWithContext(node, execution.context, conversation, contact, channelConnection);
        return { success: true, shouldContinue: false, waitForUserInput: true };
      }

      else if (
        nodeType === 'followUpNode' ||
        nodeType === 'follow_up' ||
        nodeType === 'followup' ||
        nodeLabel === 'Follow Up Node' ||
        nodeLabel === 'Follow-up Node'
      ) {
        await this.executeFollowUpNodeWithContext(node, execution.context, conversation, contact, channelConnection);
        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'conditionNode' ||
        nodeType === 'condition' ||
        nodeLabel === 'Condition Node'
      ) {
        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'waitNode' ||
        nodeType === 'wait' ||
        nodeLabel === 'Wait Node'
      ) {
        await this.executeWaitNodeWithContext(node, execution.context);
        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'aiAssistantNode' ||
        nodeType === 'aiAssistant' ||
        nodeType === 'ai_assistant' ||
        nodeLabel === 'AI Assistant' ||
        nodeLabel === 'AI Response' ||
        nodeLabel === 'Ai_assistant Node'
      ) {
        await this.executeAIAssistantNodeWithContext(node, execution.context, conversation, contact, channelConnection);
        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'translationNode' ||
        nodeType === 'translation' ||
        nodeLabel === 'Translation' ||
        nodeLabel === 'Translation Node'
      ) {
        await this.executeTranslationNodeWithContext(node, execution.context, conversation, contact, channelConnection);
        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'updatePipelineStageNode' ||
        nodeType === 'update_pipeline_stage' ||
        nodeLabel === 'Pipeline' ||
        nodeLabel === 'Move to Pipeline Stage'
      ) {
        await this.executeUpdatePipelineStageNode(node, execution.context.getVariable('message'), execution.context.getVariable('conversation'), contact, channelConnection);
        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'webhook' || nodeType === 'webhookNode' ||
        nodeLabel === 'Webhook' || nodeLabel === 'Webhook Node'
      ) {
        await this.executeWebhookNodeWithContext(node, execution.context, conversation, contact, channelConnection);
        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'http_request' || nodeType === 'httpRequestNode' ||
        nodeLabel === 'HTTP Request' || nodeLabel === 'HTTP Request Node'
      ) {
        await this.executeHttpRequestNodeWithContext(node, execution.context, conversation, contact, channelConnection);
        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'input' || nodeType === 'inputNode' ||
        nodeLabel === 'Input' || nodeLabel === 'Input Node'
      ) {
        await this.executeInputNodeWithContext(node, execution.context, conversation, contact, channelConnection);
        return { success: true, shouldContinue: false, waitForUserInput: true };
      }

      else if (
        nodeType === 'action' || nodeType === 'actionNode' ||
        nodeLabel === 'Action' || nodeLabel === 'Action Node'
      ) {
        await this.executeActionNodeWithContext(node, execution.context, conversation, contact, channelConnection);
        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'shopify' || nodeType === 'shopifyNode' ||
        nodeLabel === 'Shopify' || nodeLabel === 'Shopify Node'
      ) {
        await this.executeShopifyNodeWithContext(node, execution.context, conversation, contact, channelConnection);
        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'woocommerce' || nodeType === 'woocommerceNode' ||
        nodeLabel === 'WooCommerce' || nodeLabel === 'WooCommerce Node'
      ) {
        await this.executeWooCommerceNodeWithContext(node, execution.context, conversation, contact, channelConnection);
        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'typebot' || nodeType === 'typebotNode' ||
        nodeLabel === 'Typebot' || nodeLabel === 'Typebot Node'
      ) {
        await this.executeTypebotNodeWithContext(node, execution.context, conversation, contact, channelConnection);
        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'flowise' || nodeType === 'flowiseNode' ||
        nodeLabel === 'Flowise' || nodeLabel === 'Flowise Node'
      ) {
        await this.executeFlowiseNodeWithContext(node, execution.context, conversation, contact, channelConnection);
        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'n8n' || nodeType === 'n8nNode' ||
        nodeLabel === 'n8n' || nodeLabel === 'n8n Node'
      ) {
        await this.executeN8nNodeWithContext(node, execution.context, conversation, contact, channelConnection);
        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'bot_disable' || nodeType === 'botDisableNode' ||
        nodeLabel === 'Agent Handoff' || nodeLabel === 'Bot Disable' || nodeLabel === 'Disable Bot'
      ) {
        await this.executeBotDisableNodeWithContext(node, execution.context, conversation, contact, channelConnection);
        return { success: true, shouldContinue: false };
      }

      else if (
        nodeType === 'bot_reset' || nodeType === 'botResetNode' ||
        nodeLabel === 'Reset Bot' || nodeLabel === 'Bot Reset' || nodeLabel === 'Re-enable Bot'
      ) {
        await this.executeBotResetNodeWithContext(node, execution.context, conversation, contact, channelConnection);
        return { success: true, shouldContinue: true };
      }

      else if (
        nodeType === 'googleSheets' || nodeType === 'google_sheets' || nodeType === 'googleSheetsNode' ||
        nodeLabel === 'Google Sheets' || nodeLabel === 'Google Sheets Node'
      ) {
        await this.executeGoogleSheetsNodeWithContext(node, execution.context, conversation, contact, channelConnection);

        const duration = Date.now() - nodeStartTime;
        this.executionManager.trackNodeExecution(
          executionId,
          node.id,
          'google_sheets',
          duration,
          'completed',
          inputData,
          { operation: node.data?.operation || 'append_row' },
          undefined
        );

        return { success: true, shouldContinue: false };
      }

      else {
        

        const duration = Date.now() - nodeStartTime;
        this.executionManager.trackNodeExecution(
          executionId,
          node.id,
          nodeType || 'unknown',
          duration,
          'completed',
          inputData,
          { nodeType, nodeLabel },
          undefined
        );

        return { success: true, shouldContinue: true };
      }

    } catch (error) {
      console.error(`Error executing node ${node.id}:`, error);

      const duration = Date.now() - nodeStartTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.executionManager.trackNodeExecution(
        executionId,
        node.id,
        nodeType || 'unknown',
        duration,
        'failed',
        inputData,
        undefined,
        errorMessage
      );

      return {
        success: false,
        shouldContinue: false,
        error: errorMessage
      };
    }
  }

  /**
   * Legacy method for backward compatibility
   */
  async executeConnectedNodes(
    currentNode: any,
    allNodes: any[],
    edges: any[],
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    const tempExecutionId = this.executionManager.startExecution(
      0,
      conversation.id,
      contact.id,
      currentNode.id
    );

    await this.executeConnectedNodesWithExecution(
      tempExecutionId,
      currentNode,
      allNodes,
      edges,
      message,
      conversation,
      contact,
      channelConnection
    );
  }

  /**
   * Execute message node with execution context
   */
  private async executeMessageNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection,
    messageType: string = 'text'
  ): Promise<void> {
    try {

      const data = node.data || {};

      let content = '';
      let mediaUrl = '';

      if (messageType === 'text') {
        content = data.messageContent || data.message || data.text || '';
        content = context.replaceVariables(content);
      } else {
        mediaUrl = data.mediaUrl || data.url || '';
        content = data.caption || '';
        content = context.replaceVariables(content);
      }


      // Use unified message sending for all channel types
      try {
        if (messageType === 'text') {
          await this.sendMessageThroughChannel(
            channelConnection,
            contact,
            content,
            conversation,
            true
          );
        } else {
          await this.sendMediaThroughChannel(
            channelConnection,
            contact,
            mediaUrl,
            messageType as "image" | "video" | "audio" | "document",
            content,
            undefined,
            conversation,
            true
          );
        }
      } catch (channelError) {
        console.error('Error sending through channel, falling back to database:', channelError);
        // Fallback to database storage
        const insertMessage = {
          conversationId: conversation.id,
          contactId: contact.id,
          channelType: channelConnection.channelType,
          type: messageType,
          content: content,
          direction: 'outbound',
          status: 'sent',
          mediaUrl: messageType === 'text' ? null : mediaUrl,
          timestamp: new Date()
        };

        await storage.createMessage(insertMessage);
      }
    } catch (error) {
      console.error(`Error executing ${messageType} message node with context:`, error);
    }
  }

  /**
   * Execute quick reply node with execution context
   */
  private async executeQuickReplyNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {

      const data = node.data || {};

      let questionText = data.question || data.messageContent || data.text || data.message || data.prompt || '';
      questionText = context.replaceVariables(questionText);

      const options = data.options || [];

      let formattedMessage = `${questionText}\n\n`;

      options.forEach((option: any, index: number) => {
        const optionText = option.text || option.label || `Option ${index + 1}`;
        formattedMessage += `${index + 1}. ${optionText}\n`;
      });

      context.setVariable('quickReply.questionText', questionText);
      context.setVariable('quickReply.formattedMessage', formattedMessage);
      context.setVariable('quickReplyOptions', options);
      context.setVariable('waitingForQuickReply', true);

      // Use unified message sending for all channel types
      try {
        await this.sendMessageThroughChannel(
          channelConnection,
          contact,
          formattedMessage,
          conversation,
          true
        );
      } catch (channelError) {
        console.error('Error sending through channel, falling back to database:', channelError);
        // Fallback to database storage
        const insertMessage = {
          conversationId: conversation.id,
          contactId: contact.id,
          channelType: channelConnection.channelType,
          type: 'text',
          content: formattedMessage,
          direction: 'outbound',
          status: 'sent',
          mediaUrl: null,
          timestamp: new Date()
        };

        await storage.createMessage(insertMessage);
      }

    } catch (error) {
      console.error('Error executing quick reply node with context:', error);
    }
  }

  /**
   * Execute condition node with execution context
   */
  private async executeConditionNodeWithContext(
    node: any,
    context: FlowExecutionContext
  ): Promise<boolean> {
    try {

      const data = node.data || {};

      const conditionType = data.conditionType || 'contains';
      const conditionValue = data.conditionValue || '';

      const userInput = context.getVariable('user.input') || context.getVariable('message.content') || '';


      switch (conditionType.toLowerCase()) {
        case 'contains':
        case 'message contains':
          return userInput.toLowerCase().includes(conditionValue.toLowerCase());

        case 'exact match':
          return userInput.toLowerCase() === conditionValue.toLowerCase();

        case 'starts with':
          return userInput.toLowerCase().startsWith(conditionValue.toLowerCase());

        case 'ends with':
          return userInput.toLowerCase().endsWith(conditionValue.toLowerCase());

        case 'has media':
          return !!context.getVariable('message.mediaUrl');

        default:
          return userInput.toLowerCase().includes(conditionValue.toLowerCase());
      }

    } catch (error) {
      console.error('Error executing condition node with context:', error);
      return false;
    }
  }

  /**
   * Execute wait node with execution context
   */
  private async executeWaitNodeWithContext(
    node: any,
    context: FlowExecutionContext
  ): Promise<void> {
    try {

      const data = node.data || {};

      const waitMode = data.waitMode || 'duration';

      if (waitMode === 'duration') {
        const timeValue = data.timeValue || data.duration || 5;
        const timeUnit = data.timeUnit || data.durationUnit || 'seconds';

        let waitMs = timeValue;
        switch (timeUnit.toLowerCase()) {
          case 'milliseconds':
          case 'ms':
            waitMs = timeValue;
            break;
          case 'seconds':
          case 'sec':
          case 's':
            waitMs = timeValue * 1000;
            break;
          case 'minutes':
          case 'min':
          case 'm':
            waitMs = timeValue * 60 * 1000;
            break;
          case 'hours':
          case 'hour':
          case 'h':
            waitMs = timeValue * 60 * 60 * 1000;
            break;
          default:
            waitMs = timeValue * 1000;
        }


        context.setVariable('wait.lastDuration', timeValue);
        context.setVariable('wait.lastUnit', timeUnit);
        context.setVariable('wait.lastMs', waitMs);
        context.setVariable('wait.startTime', new Date().toISOString());

        await new Promise(resolve => setTimeout(resolve, waitMs));

        context.setVariable('wait.endTime', new Date().toISOString());

      } else if (waitMode === 'datetime') {
        const waitDate = data.waitDate ? new Date(data.waitDate) : null;
        const waitTime = data.waitTime || '';

        if (waitDate) {
          if (waitTime) {
            const [hours, minutes] = waitTime.split(':').map(Number);
            waitDate.setHours(hours, minutes, 0, 0);
          }

          const now = new Date();
          const waitMs = waitDate.getTime() - now.getTime();

          if (waitMs > 0) {

            context.setVariable('wait.targetDate', waitDate.toISOString());
            context.setVariable('wait.waitMs', waitMs);
            context.setVariable('wait.startTime', now.toISOString());

            await new Promise(resolve => setTimeout(resolve, waitMs));

            context.setVariable('wait.endTime', new Date().toISOString());
          } else {
            context.setVariable('wait.skipped', true);
            context.setVariable('wait.reason', 'Target time in past');
          }
        } else {
          
          context.setVariable('wait.error', 'Invalid wait date');
        }
      }

    } catch (error) {
      console.error('Error executing wait node with context:', error);
      context.setVariable('wait.error', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Execute Follow-up node with execution context
   */
  private async executeFollowUpNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {
      const data = node.data || {};

      const scheduleId = `followup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      const messageType = data.messageType || 'text';
      const messageContent = data.messageContent || data.message || '';
      const mediaUrl = data.mediaUrl || '';
      const caption = data.caption || '';
      const templateId = data.templateId || null;

      const triggerEvent = data.triggerEvent || 'conversation_start';
      const triggerNodeId = data.triggerNodeId || null;
      const delayAmount = data.delayAmount || 1;
      const delayUnit = data.delayUnit || 'hours';
      const specificDatetime = data.specificDatetime ? new Date(data.specificDatetime) : null;
      const timezone = data.timezone || 'UTC';

      let scheduledFor: Date;

      const { calculateFollowUpTime } = await import('../utils/timezone');

      scheduledFor = calculateFollowUpTime(
        triggerEvent,
        specificDatetime?.toISOString(),
        timezone,
        delayAmount,
        delayUnit
      );

      const expiresAt = new Date(scheduledFor.getTime() + (30 * 24 * 60 * 60 * 1000));

      const variables = {
        contact: {
          id: contact.id,
          name: contact.name,
          phone: contact.phone,
          email: contact.email
        },
        conversation: {
          id: conversation.id,
          status: conversation.status
        },
        flow: {
          id: context.getVariable('flow.id'),
          nodeId: node.id
        },
        trigger: {
          event: triggerEvent,
          nodeId: triggerNodeId,
          scheduledAt: scheduledFor.toISOString()
        }
      };

      const executionContext = {
        sessionId: context.getVariable('session.id'),
        executionPath: context.getVariable('execution.path') || [],
        currentVariables: context.getAllVariables()
      };

      const followUpSchedule = {
        scheduleId,
        sessionId: context.getVariable('session.id'),
        flowId: context.getVariable('flow.id'),
        conversationId: conversation.id,
        contactId: contact.id,
        companyId: conversation.companyId || 0,
        nodeId: node.id,
        messageType,
        messageContent: context.replaceVariables(messageContent),
        mediaUrl: context.replaceVariables(mediaUrl),
        caption: context.replaceVariables(caption),
        templateId,
        triggerEvent,
        triggerNodeId,
        delayAmount,
        delayUnit,
        scheduledFor,
        specificDatetime,
        timezone,
        status: 'scheduled' as const,
        maxRetries: data.maxRetries || 3,
        channelType: channelConnection.channelType,
        channelConnectionId: channelConnection.id,
        variables,
        executionContext,
        expiresAt
      };

      await storage.createFollowUpSchedule(followUpSchedule);

      context.setVariable('followUp.scheduleId', scheduleId);
      context.setVariable('followUp.scheduledFor', scheduledFor.toISOString());
      context.setVariable('followUp.triggerEvent', triggerEvent);
      context.setVariable('followUp.delayAmount', delayAmount);
      context.setVariable('followUp.delayUnit', delayUnit);
      context.setVariable('followUp.messageType', messageType);



    } catch (error) {
      console.error('Error executing Follow-up node with context:', error);
      context.setVariable('followUp.error', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Execute AI Assistant node with execution context
   */
  private async executeAIAssistantNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {
      const userMessage = context.getVariable('message.content') || '';

      const tempMessage: Message = {
        id: 0,
        content: userMessage,
        type: 'text',
        direction: 'inbound',
        status: 'delivered',
        createdAt: new Date(),
        conversationId: conversation.id,
        mediaUrl: null,
        externalId: null,
        senderId: null,
        senderType: null,
        isFromBot: false,
        metadata: {},
        sentAt: null,
        readAt: null,
        groupParticipantJid: null,
        groupParticipantName: null,
        emailMessageId: null,
        emailInReplyTo: null,
        emailReferences: null,
        emailSubject: null,
        emailFrom: null,
        emailTo: null,
        emailCc: null,
        emailBcc: null,
        emailHtml: null,
        emailPlainText: null,
        emailHeaders: null
      };

      const data = node.data || {};
      if (data.enableTaskExecution && data.tasks && data.tasks.length > 0) {
        try {
          const aiAssistantServiceModule = await import('../services/ai-assistant');
          const aiAssistantService = aiAssistantServiceModule.default;

          const provider = data.provider || 'gemini';
          let model = data.model || 'gemini-1.5-pro-latest';
          const apiKey = data.apiKey || process.env.XAI_API_KEY || '';
          const systemPrompt = data.prompt || 'You are a helpful assistant.';
          const enableHistory = data.enableHistory !== undefined ? data.enableHistory : true;
          const historyLimit = data.historyLimit || 5;
          const enableAudio = data.enableAudio || false;

          let conversationHistory: Message[] = [];
          if (enableHistory) {
            conversationHistory = await storage.getMessagesByConversation(conversation.id);
          }

          const aiConfig = {
            provider,
            model,
            apiKey,
            systemPrompt,
            enableHistory,
            historyLimit,
            enableAudio,
            enableImage: data.enableImage || false,
            enableVideo: data.enableVideo || false,
            enableVoiceProcessing: data.enableVoiceProcessing || false,
            enableTextToSpeech: data.enableTextToSpeech || false,
            ttsVoice: data.ttsVoice || 'alloy',
            voiceResponseMode: data.voiceResponseMode || 'always',
            enableFunctionCalling: data.enableTaskExecution || false,
            enableTaskExecution: data.enableTaskExecution || false,
            tasks: data.tasks || []
          };

          const aiResponse = await aiAssistantService.processMessage(
            tempMessage,
            conversation,
            contact,
            channelConnection,
            aiConfig,
            conversationHistory
          );

          if (aiResponse.text) {
            const responseText = this.replaceVariables(aiResponse.text, tempMessage, contact);

            if (aiResponse.audioUrl && (channelConnection.channelType === 'whatsapp' || channelConnection.channelType === 'whatsapp_unofficial') && contact.identifier) {
              try {
                const audioPath = aiResponse.audioUrl.startsWith('/') ? aiResponse.audioUrl.slice(1) : aiResponse.audioUrl;
                const fullAudioPath = path.join(process.cwd(), audioPath);

                

                if (channelConnection.channelType === 'whatsapp_unofficial') {
                  await whatsAppService.sendWhatsAppAudioMessage(
                    channelConnection.id,
                    channelConnection.userId,
                    contact.identifier,
                    fullAudioPath
                  );
                } else {
                  
                  await whatsAppService.sendWhatsAppMessage(
                    channelConnection.id,
                    channelConnection.userId,
                    contact.identifier,
                    responseText,
                    true
                  );
                }
              } catch (error) {
                try {
                  if (channelConnection.channelType === 'whatsapp_unofficial') {
                    await whatsAppService.sendMessage(
                      channelConnection.id,
                      channelConnection.userId,
                      contact.identifier,
                      responseText,
                      true
                    );
                  } else {
                    await whatsAppService.sendWhatsAppMessage(
                      channelConnection.id,
                      channelConnection.userId,
                      contact.identifier,
                      responseText,
                      true
                    );
                  }
                } catch (textError) {
                  // Error sending fallback text message
                }
              }
            } else {
              const insertMessage = {
                conversationId: conversation.id,
                contactId: contact.id,
                channelType: channelConnection.channelType,
                type: 'text',
                content: responseText,
                direction: 'outbound',
                status: 'sent',
                mediaUrl: null,
                timestamp: new Date()
              };

              await storage.createMessage(insertMessage);

              if ((channelConnection.channelType === 'whatsapp' || channelConnection.channelType === 'whatsapp_unofficial') && contact.identifier) {
                try {
                  if (channelConnection.channelType === 'whatsapp_unofficial') {
                    await whatsAppService.sendMessage(
                      channelConnection.id,
                      channelConnection.userId,
                      contact.identifier,
                      responseText,
                      true
                    );
                  } else {
                    await whatsAppService.sendWhatsAppMessage(
                      channelConnection.id,
                      channelConnection.userId,
                      contact.identifier,
                      responseText,
                      true
                    );
                  }
                } catch (error) {
                  // Error sending message to WhatsApp
                }
              }
            }
          }

          if (aiResponse.triggeredTasks && aiResponse.triggeredTasks.length > 0) {
            context.setVariable('ai.triggeredTasks', aiResponse.triggeredTasks);
            
          } else {
            
          }

        } catch (error) {
          console.error('Error getting AI response with task execution:', error);
          await this.executeAIAssistantNode(node, tempMessage, conversation, contact, channelConnection);
        }
      } else {
        await this.executeAIAssistantNode(node, tempMessage, conversation, contact, channelConnection);
      }

      context.setVariable('ai.lastExecution', new Date().toISOString());

    } catch (error) {
      // Error executing AI Assistant node with context
    }
  }

  /**
   * Execute Webhook node with execution context
   */
  private async executeWebhookNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    _conversation: Conversation,
    _contact: Contact,
    _channelConnection: ChannelConnection
  ): Promise<void> {
    try {

      const data = node.data || {};

      const webhookUrl = context.replaceVariables(data.url || '');
      const method = data.method || 'POST';
      const headers = data.headers || {};
      const timeoutValue = data.timeout || 30;
      const timeout = typeof timeoutValue === 'number' && timeoutValue < 1000 ? timeoutValue * 1000 : timeoutValue;

      let payload: any = {};

      if (data.payload) {
        if (typeof data.payload === 'string') {
          payload = context.replaceVariables(data.payload);
          try {
            payload = JSON.parse(payload);
          } catch {
          }
        } else {
          payload = this.replaceVariablesInObject(data.payload, context);
        }
      } else {
        payload = {
          contact: context.getVariable('contact'),
          message: context.getVariable('message'),
          conversation: context.getVariable('conversation'),
          timestamp: new Date().toISOString(),
          executionId: context.getVariable('execution.id'),
          flowId: context.getVariable('flow.id')
        };
      }

      const response = await this.makeHttpRequest({
        url: webhookUrl,
        method,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'PowerChatPlus-FlowExecutor/1.0',
          ...headers
        },
        body: method !== 'GET' ? JSON.stringify(payload) : undefined,
        timeout
      });
      context.setWebhookResponse({
        status: response.status,
        statusText: response.statusText,
        data: response.data,
        headers: response.headers,
        url: webhookUrl,
        method,
        timestamp: new Date().toISOString()
      });

      if (response.data) {
        context.setVariable('webhook.lastResponse', response.data);

        if (typeof response.data === 'object') {
          Object.entries(response.data).forEach(([key, value]) => {
            context.setVariable(`webhook.${key}`, value);
          });
        }
      }

    } catch (error) {
      console.error('Error executing Webhook node with context:', error);

      context.setVariable('webhook.error', {
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });

      throw error;
    }
  }

  /**
   * Execute HTTP Request node with execution context
   */
  private async executeHttpRequestNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    _conversation: Conversation,
    _contact: Contact,
    _channelConnection: ChannelConnection
  ): Promise<void> {
    try {

      const data = node.data || {};

      const url = context.replaceVariables(data.url || '');
      const method = (data.method || 'GET').toUpperCase();
      const headers = data.headers || {};
      const timeoutValue = data.timeout || 30;
      const timeout = typeof timeoutValue === 'number' && timeoutValue < 1000 ? timeoutValue * 1000 : timeoutValue;

      let body: string | undefined;
      if (method !== 'GET' && data.body) {
        if (typeof data.body === 'string') {
          body = context.replaceVariables(data.body);
        } else {
          body = JSON.stringify(this.replaceVariablesInObject(data.body, context));
        }
      }

      let finalUrl = url;
      if (method === 'GET' && data.params) {
        const params = new URLSearchParams();
        Object.entries(data.params).forEach(([key, value]) => {
          params.append(key, context.replaceVariables(String(value)));
        });
        finalUrl = `${url}${url.includes('?') ? '&' : '?'}${params.toString()}`;
      }


      const response = await this.makeHttpRequest({
        url: finalUrl,
        method,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'PowerChatPlus-FlowExecutor/1.0',
          ...headers
        },
        body,
        timeout
      });

      context.setHttpResponse({
        status: response.status,
        statusText: response.statusText,
        data: response.data,
        headers: response.headers,
        url: finalUrl,
        method,
        timestamp: new Date().toISOString()
      });

      if (response.data) {
        context.setVariable('http.lastResponse', response.data);

        if (typeof response.data === 'object') {
          Object.entries(response.data).forEach(([key, value]) => {
            context.setVariable(`http.${key}`, value);
          });
        }
      }

    } catch (error) {
      context.setVariable('http.error', {
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });

      throw error;
    }
  }

  /**
   * Helper method to make HTTP requests
   */
  private async makeHttpRequest(options: {
    url: string;
    method: string;
    headers?: Record<string, string>;
    body?: string;
    timeout?: number;
  }): Promise<{
    status: number;
    statusText: string;
    data: any;
    headers: Record<string, string>;
  }> {
    const { url, method, headers = {}, body, timeout = 30000 } = options;

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        method,
        headers,
        body,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      let data: any;
      const contentType = response.headers.get('content-type') || '';

      const responseText = await response.text();

      if (contentType.includes('application/json')) {
        try {
          data = JSON.parse(responseText);
        } catch {
          data = responseText;
        }
      } else {
        data = responseText;
      }

      const responseHeaders: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });

      return {
        status: response.status,
        statusText: response.statusText,
        data,
        headers: responseHeaders
      };

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error(`HTTP request timeout after ${timeout}ms`);
      }
      throw error;
    }
  }

  /**
   * Helper method to replace variables in nested objects
   */
  private replaceVariablesInObject(obj: any, context: FlowExecutionContext): any {
    if (typeof obj === 'string') {
      return context.replaceVariables(obj);
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.replaceVariablesInObject(item, context));
    }

    if (obj && typeof obj === 'object') {
      const result: any = {};
      Object.entries(obj).forEach(([key, value]) => {
        result[key] = this.replaceVariablesInObject(value, context);
      });
      return result;
    }

    return obj;
  }

  /**
   * Get nested value from object using dot notation path
   */
  private getNestedValue(obj: any, path: string): any {
    if (!obj || !path) return undefined;

    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Execute Input node with execution context
   */
  private async executeInputNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {

      const data = node.data || {};

      let promptText = data.prompt || data.message || 'Please provide your input:';
      promptText = context.replaceVariables(promptText);

      const inputType = data.inputType || 'text';
      const isRequired = data.required !== false;
      const placeholder = data.placeholder || '';

      let formattedMessage = promptText;
      if (placeholder) {
        formattedMessage += `\n\n${placeholder}`;
      }

      context.setVariable('inputNode.type', inputType);
      context.setVariable('inputNode.required', isRequired);
      context.setVariable('inputNode.prompt', promptText);
      context.setVariable('waitingForInput', true);

      // Use unified message sending for all channel types
      try {
        await this.sendMessageThroughChannel(
          channelConnection,
          contact,
          formattedMessage,
          conversation,
          true
        );
      } catch (channelError) {
        console.error('Error sending through channel, falling back to database:', channelError);
        // Fallback to database storage
        const insertMessage = {
          conversationId: conversation.id,
          contactId: contact.id,
          channelType: channelConnection.channelType,
          type: 'text',
          content: formattedMessage,
          direction: 'outbound',
          status: 'sent',
          mediaUrl: null,
          timestamp: new Date()
        };

        await storage.createMessage(insertMessage);
      }

    } catch (error) {
      console.error('Error executing Input node with context:', error);
    }
  }

  /**
   * Execute Action node with execution context
   */
  private async executeActionNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    _conversation: Conversation,
    _contact: Contact,
    _channelConnection: ChannelConnection
  ): Promise<void> {
    try {

      const data = node.data || {};
      const actionType = data.actionType || data.action || 'log';

      switch (actionType.toLowerCase()) {
        case 'log':
          const logMessage = context.replaceVariables(data.message || 'Action executed');
          context.setVariable('action.lastLog', logMessage);
          break;

        case 'set_variable':
          const variableName = data.variableName || 'actionResult';
          const variableValue = context.replaceVariables(data.variableValue || '');
          context.setVariable(variableName, variableValue);
          break;

        case 'api_call':
          if (data.apiUrl) {
            const timeoutValue = data.timeout || 30;
            const timeout = typeof timeoutValue === 'number' && timeoutValue < 1000 ? timeoutValue * 1000 : timeoutValue;

            const response = await this.makeHttpRequest({
              url: context.replaceVariables(data.apiUrl),
              method: data.apiMethod || 'GET',
              headers: data.apiHeaders || {},
              body: data.apiBody ? JSON.stringify(this.replaceVariablesInObject(data.apiBody, context)) : undefined,
              timeout
            });

            context.setVariable('action.apiResponse', response.data);
            context.setVariable('action.apiStatus', response.status);
          }
          break;

        case 'delay':
          const delayValue = data.delayValue || data.delay || data.delayMs || 1;
          const delayUnit = data.delayUnit || 'seconds';

          let delayMs = delayValue;
          switch (delayUnit.toLowerCase()) {
            case 'milliseconds':
            case 'ms':
              delayMs = delayValue;
              break;
            case 'seconds':
            case 'sec':
            case 's':
              delayMs = delayValue * 1000;
              break;
            case 'minutes':
            case 'min':
            case 'm':
              delayMs = delayValue * 60 * 1000;
              break;
            case 'hours':
            case 'hour':
            case 'h':
              delayMs = delayValue * 60 * 60 * 1000;
              break;
            default:
              if (data.delayMs) {
                delayMs = data.delayMs;
              } else {
                delayMs = delayValue * 1000;
              }
          }


          await new Promise(resolve => setTimeout(resolve, delayMs));

          context.setVariable('action.lastDelay', delayMs);
          context.setVariable('action.lastDelayValue', delayValue);
          context.setVariable('action.lastDelayUnit', delayUnit);
          break;

        default:
          context.setVariable('action.error', `Unknown action type: ${actionType}`);
      }

      context.setVariable('action.lastExecution', new Date().toISOString());
      context.setVariable('action.lastType', actionType);

    } catch (error) {
      console.error('Error executing Action node with context:', error);
      context.setVariable('action.error', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Execute Shopify node with execution context
   */
  private async executeShopifyNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    _conversation: Conversation,
    _contact: Contact,
    _channelConnection: ChannelConnection
  ): Promise<void> {
    try {

      const data = node.data || {};
      const operation = data.operation || 'get_products';
      const shopifyConfig = data.shopifyConfig || {};

      const shopUrl = context.replaceVariables(shopifyConfig.shopUrl || '');
      const accessToken = context.replaceVariables(shopifyConfig.accessToken || '');

      if (!shopUrl || !accessToken) {
        throw new Error('Shopify configuration missing: shopUrl and accessToken required');
      }

      let apiUrl = '';
      let method = 'GET';
      let body: any = undefined;

      switch (operation) {
        case 'get_products':
          apiUrl = `${shopUrl}/admin/api/2023-10/products.json`;
          break;

        case 'get_product':
          const productId = context.replaceVariables(data.productId || '');
          apiUrl = `${shopUrl}/admin/api/2023-10/products/${productId}.json`;
          break;

        case 'get_orders':
          apiUrl = `${shopUrl}/admin/api/2023-10/orders.json`;
          break;

        case 'get_customer':
          const customerId = context.replaceVariables(data.customerId || '');
          apiUrl = `${shopUrl}/admin/api/2023-10/customers/${customerId}.json`;
          break;

        case 'create_customer':
          apiUrl = `${shopUrl}/admin/api/2023-10/customers.json`;
          method = 'POST';
          body = JSON.stringify({
            customer: this.replaceVariablesInObject(data.customerData || {}, context)
          });
          break;

        default:
          throw new Error(`Unknown Shopify operation: ${operation}`);
      }


      const timeoutValue = data.timeout || 30;
      const timeout = typeof timeoutValue === 'number' && timeoutValue < 1000 ? timeoutValue * 1000 : timeoutValue;

      const response = await this.makeHttpRequest({
        url: apiUrl,
        method,
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': accessToken,
          'User-Agent': 'PowerChatPlus-FlowExecutor/1.0'
        },
        body,
        timeout
      });


      context.setVariable('shopify.lastResponse', response.data);
      context.setVariable('shopify.lastOperation', operation);
      context.setVariable('shopify.lastStatus', response.status);

      if (response.data) {
        switch (operation) {
          case 'get_products':
            context.setVariable('shopify.products', response.data.products || []);
            break;
          case 'get_product':
            context.setVariable('shopify.product', response.data.product || {});
            break;
          case 'get_orders':
            context.setVariable('shopify.orders', response.data.orders || []);
            break;
          case 'get_customer':
            context.setVariable('shopify.customer', response.data.customer || {});
            break;
          case 'create_customer':
            context.setVariable('shopify.createdCustomer', response.data.customer || {});
            break;
        }
      }

    } catch (error) {
      console.error('Error executing Shopify node with context:', error);
      context.setVariable('shopify.error', {
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  /**
   * Execute WooCommerce node with execution context
   */
  private async executeWooCommerceNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    _conversation: Conversation,
    _contact: Contact,
    _channelConnection: ChannelConnection
  ): Promise<void> {
    try {

      const data = node.data || {};
      const operation = data.operation || 'get_products';
      const wooConfig = data.wooConfig || {};

      const siteUrl = context.replaceVariables(wooConfig.siteUrl || '');
      const consumerKey = context.replaceVariables(wooConfig.consumerKey || '');
      const consumerSecret = context.replaceVariables(wooConfig.consumerSecret || '');

      if (!siteUrl || !consumerKey || !consumerSecret) {
        throw new Error('WooCommerce configuration missing: siteUrl, consumerKey, and consumerSecret required');
      }

      let apiUrl = '';
      let method = 'GET';
      let body: any = undefined;

      const baseUrl = `${siteUrl}/wp-json/wc/v3`;

      switch (operation) {
        case 'get_products':
          apiUrl = `${baseUrl}/products`;
          break;

        case 'get_product':
          const productId = context.replaceVariables(data.productId || '');
          apiUrl = `${baseUrl}/products/${productId}`;
          break;

        case 'get_orders':
          apiUrl = `${baseUrl}/orders`;
          break;

        case 'get_customer':
          const customerId = context.replaceVariables(data.customerId || '');
          apiUrl = `${baseUrl}/customers/${customerId}`;
          break;

        case 'create_customer':
          apiUrl = `${baseUrl}/customers`;
          method = 'POST';
          body = JSON.stringify(this.replaceVariablesInObject(data.customerData || {}, context));
          break;

        default:
          throw new Error(`Unknown WooCommerce operation: ${operation}`);
      }

      const auth = Buffer.from(`${consumerKey}:${consumerSecret}`).toString('base64');


      const timeoutValue = data.timeout || 30;
      const timeout = typeof timeoutValue === 'number' && timeoutValue < 1000 ? timeoutValue * 1000 : timeoutValue;

      const response = await this.makeHttpRequest({
        url: apiUrl,
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${auth}`,
          'User-Agent': 'PowerChatPlus-FlowExecutor/1.0'
        },
        body,
        timeout
      });


      context.setVariable('woocommerce.lastResponse', response.data);
      context.setVariable('woocommerce.lastOperation', operation);
      context.setVariable('woocommerce.lastStatus', response.status);

      if (response.data) {
        switch (operation) {
          case 'get_products':
            context.setVariable('woocommerce.products', Array.isArray(response.data) ? response.data : []);
            break;
          case 'get_product':
            context.setVariable('woocommerce.product', response.data);
            break;
          case 'get_orders':
            context.setVariable('woocommerce.orders', Array.isArray(response.data) ? response.data : []);
            break;
          case 'get_customer':
            context.setVariable('woocommerce.customer', response.data);
            break;
          case 'create_customer':
            context.setVariable('woocommerce.createdCustomer', response.data);
            break;
        }
      }

    } catch (error) {
      console.error('Error executing WooCommerce node with context:', error);
      context.setVariable('woocommerce.error', {
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  /**
   * Execute Typebot node with execution context
   */
  private async executeTypebotNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    _conversation: Conversation,
    _contact: Contact,
    _channelConnection: ChannelConnection
  ): Promise<void> {
    try {

      const data = node.data || {};
      const operation = data.operation || 'start_conversation';
      const typebotConfig = data.typebotConfig || {};

      const typebotUrl = context.replaceVariables(typebotConfig.typebotUrl || '');
      const typebotId = context.replaceVariables(typebotConfig.typebotId || '');
      const apiKey = context.replaceVariables(typebotConfig.apiKey || '');

      if (!typebotUrl || !typebotId) {
        throw new Error('Typebot configuration missing: typebotUrl and typebotId required');
      }

      let apiUrl = '';
      let method = 'POST';
      let body: any = {};

      const baseUrl = `${typebotUrl}/api/v1/typebots/${typebotId}`;

      switch (operation) {
        case 'start_conversation':
          apiUrl = `${baseUrl}/startChat`;
          body = {
            prefilledVariables: this.replaceVariablesInObject(data.prefilledVariables || {}, context)
          };
          break;

        case 'send_message':
          const sessionId = context.getVariable('typebot.session.id') || data.sessionId;
          apiUrl = `${baseUrl}/continueChat`;
          body = {
            sessionId: context.replaceVariables(sessionId),
            message: context.replaceVariables(data.message || context.getVariable('message.content') || '')
          };
          break;

        case 'get_response':
          const getSessionId = context.getVariable('typebot.session.id') || data.sessionId;
          apiUrl = `${baseUrl}/getMessages`;
          method = 'GET';
          apiUrl += `?sessionId=${encodeURIComponent(context.replaceVariables(getSessionId))}`;
          body = undefined;
          break;

        case 'manage_session':
          const manageSessionId = context.getVariable('typebot.session.id') || data.sessionId;
          const action = data.action || 'close';
          apiUrl = `${baseUrl}/sessions/${encodeURIComponent(context.replaceVariables(manageSessionId))}`;
          method = action === 'close' ? 'DELETE' : 'PUT';
          body = action !== 'close' ? { status: action } : undefined;
          break;

        default:
          throw new Error(`Unknown Typebot operation: ${operation}`);
      }


      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'User-Agent': 'PowerChatPlus-FlowExecutor/1.0'
      };

      if (apiKey) {
        headers['Authorization'] = `Bearer ${apiKey}`;
      }

      const timeoutValue = data.timeout || 30;
      const timeout = typeof timeoutValue === 'number' && timeoutValue < 1000 ? timeoutValue * 1000 : timeoutValue;

      const response = await this.makeHttpRequest({
        url: apiUrl,
        method,
        headers,
        body: body ? JSON.stringify(body) : undefined,
        timeout
      });


      context.setVariable('typebot.lastResponse', response.data);
      context.setVariable('typebot.lastOperation', operation);
      context.setVariable('typebot.lastStatus', response.status);

      if (response.data) {
        switch (operation) {
          case 'start_conversation':
            if (response.data.sessionId) {
              context.setVariable('typebot.session.id', response.data.sessionId);
            }
            if (response.data.messages) {
              context.setVariable('typebot.messages', response.data.messages);
            }
            break;

          case 'send_message':
            if (response.data.messages) {
              context.setVariable('typebot.messages', response.data.messages);
              context.setVariable('typebot.lastMessages', response.data.messages);
            }
            break;

          case 'get_response':
            if (response.data.messages) {
              context.setVariable('typebot.messages', response.data.messages);
            }
            break;
        }
      }

    } catch (error) {
      console.error('Error executing Typebot node with context:', error);
      context.setVariable('typebot.error', {
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  /**
   * Execute Flowise node with execution context
   */
  private async executeFlowiseNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    _conversation: Conversation,
    _contact: Contact,
    _channelConnection: ChannelConnection
  ): Promise<void> {
    try {

      const data = node.data || {};
      const operation = data.operation || 'start_chatflow';
      const flowiseConfig = data.flowiseConfig || {};

      const flowiseUrl = context.replaceVariables(flowiseConfig.flowiseUrl || '');
      const chatflowId = context.replaceVariables(flowiseConfig.chatflowId || '');
      const apiKey = context.replaceVariables(flowiseConfig.apiKey || '');

      if (!flowiseUrl || !chatflowId) {
        throw new Error('Flowise configuration missing: flowiseUrl and chatflowId required');
      }

      let apiUrl = '';
      let method = 'POST';
      let body: any = {};

      const baseUrl = `${flowiseUrl}/api/v1`;

      switch (operation) {
        case 'start_chatflow':
        case 'send_message':
          apiUrl = `${baseUrl}/prediction/${chatflowId}`;
          body = {
            question: context.replaceVariables(data.question || context.getVariable('message.content') || ''),
            overrideConfig: this.replaceVariablesInObject(data.overrideConfig || {}, context),
            history: context.getVariable('flowise.history') || []
          };

          const sessionId = context.getVariable('flowise.session.id') || data.sessionId;
          if (sessionId) {
            body.sessionId = context.replaceVariables(sessionId);
          }

          if (data.streaming !== undefined) {
            body.streaming = data.streaming;
          }
          break;

        case 'get_response':
          const getSessionId = context.getVariable('flowise.session.id') || data.sessionId;
          apiUrl = `${baseUrl}/chatmessage/${chatflowId}`;
          method = 'GET';
          if (getSessionId) {
            apiUrl += `?sessionId=${encodeURIComponent(context.replaceVariables(getSessionId))}`;
          }
          body = undefined;
          break;

        case 'get_chatflows':
          apiUrl = `${baseUrl}/chatflows`;
          method = 'GET';
          body = undefined;
          break;

        default:
          throw new Error(`Unknown Flowise operation: ${operation}`);
      }


      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'User-Agent': 'PowerChatPlus-FlowExecutor/1.0'
      };

      if (apiKey) {
        headers['Authorization'] = `Bearer ${apiKey}`;
      }

      const timeoutValue = data.timeout || 60;
      const timeout = typeof timeoutValue === 'number' && timeoutValue < 1000 ? timeoutValue * 1000 : timeoutValue;

      const response = await this.makeHttpRequest({
        url: apiUrl,
        method,
        headers,
        body: body ? JSON.stringify(body) : undefined,
        timeout
      });


      context.setVariable('flowise.lastResponse', response.data);
      context.setVariable('flowise.lastOperation', operation);
      context.setVariable('flowise.lastStatus', response.status);

      if (response.data) {
        switch (operation) {
          case 'start_chatflow':
          case 'send_message':
            if (response.data.text) {
              context.setVariable('flowise.response', response.data.text);
              context.setVariable('ai.response', response.data.text);
            }

            if (response.data.sessionId) {
              context.setVariable('flowise.session.id', response.data.sessionId);
            }

            const currentHistory = context.getVariable('flowise.history') || [];
            const newHistory = [
              ...currentHistory,
              {
                role: 'user',
                content: body.question,
                timestamp: new Date().toISOString()
              },
              {
                role: 'assistant',
                content: response.data.text || '',
                timestamp: new Date().toISOString()
              }
            ];
            context.setVariable('flowise.history', newHistory);
            break;

          case 'get_response':
            if (Array.isArray(response.data)) {
              context.setVariable('flowise.messages', response.data);
            }
            break;

          case 'get_chatflows':
            if (Array.isArray(response.data)) {
              context.setVariable('flowise.chatflows', response.data);
            }
            break;
        }
      }

    } catch (error) {
      console.error('Error executing Flowise node with context:', error);
      context.setVariable('flowise.error', {
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  /**
   * Execute n8n node with execution context
   */
  private async executeN8nNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    _conversation: Conversation,
    _contact: Contact,
    _channelConnection: ChannelConnection
  ): Promise<void> {
    try {
      const data = node.data || {};
      const operation = data.operation || 'webhook_trigger';

      if (data.chatWebhookUrl && data.chatWebhookUrl.trim()) {
        const userMessage = context.getVariable('message.content') || 'Hello';
        const messageType = context.getVariable('message.type') || 'text';
        const messageObject = context.getVariable('message') || {};
        const mediaUrl = context.getVariable('message.mediaUrl') || context.getVariable('message.media_url') || messageObject.mediaUrl;

       

        const sessionId = `conv_${_conversation.id}_contact_${_contact.id}`;

        const chatBody = this.buildN8nMessagePayload(
          userMessage,
          messageType,
          mediaUrl,
          sessionId,
          data,
          context
        );

        try {
          const response = await axios({
            method: 'POST',
            url: data.chatWebhookUrl.trim(),
            headers: {
              'Content-Type': 'application/json'
            },
            data: chatBody,
            timeout: 30000
          });

          if (response?.data) {
            const processedResponse = this.processN8nResponse(response.data, data);

            if (processedResponse.text && processedResponse.text.trim()) {

              try {
                if ((_channelConnection.channelType === 'whatsapp' || _channelConnection.channelType === 'whatsapp_unofficial') && _contact.identifier) {
                  try {
                    if (_channelConnection.channelType === 'whatsapp_unofficial') {
                      await whatsAppService.sendMessage(
                        _channelConnection.id,
                        _channelConnection.userId,
                        _contact.identifier,
                        processedResponse.text,
                        true,
                        _conversation.id
                      );
                    } else {
                      await whatsAppService.sendWhatsAppMessage(
                        _channelConnection.id,
                        _channelConnection.userId,
                        _contact.identifier,
                        processedResponse.text,
                        true,
                        _conversation.id
                      );
                    }

                    if (processedResponse.media && processedResponse.media.length > 0) {
                      await this.sendN8nMediaResponses(
                        processedResponse.media,
                        _channelConnection,
                        _contact,
                        _conversation,
                        node.id,
                        data.chatWebhookUrl
                      );
                    }

                    const recentMessages = await storage.getMessagesByConversationPaginated(_conversation.id, 1, 0);
                    if (recentMessages.length > 0) {
                      const savedMessage = recentMessages[0];
                      const existingMetadata = savedMessage.metadata
                        ? (typeof savedMessage.metadata === 'string'
                           ? JSON.parse(savedMessage.metadata)
                           : savedMessage.metadata)
                        : {};
                      const updatedMetadata = {
                        ...existingMetadata,
                        source: 'n8n_direct_webhook',
                        nodeId: node.id,
                        webhookUrl: data.chatWebhookUrl,
                        hasMediaResponse: processedResponse.media && processedResponse.media.length > 0
                      };

                      await storage.updateMessage(savedMessage.id, {
                        metadata: JSON.stringify(updatedMetadata)
                      });
                    }

                  } catch (error) {
                    console.error('Error sending direct webhook AI response via WhatsApp:', error);

                    const insertMessage = {
                      conversationId: _conversation.id,
                      contactId: _contact.id,
                      content: processedResponse.text,
                      messageType: 'text' as const,
                      direction: 'outbound' as const,
                      status: 'failed' as const,
                      timestamp: new Date(),
                      metadata: {
                        source: 'n8n_direct_webhook',
                        nodeId: node.id,
                        webhookUrl: data.chatWebhookUrl,
                        error: 'Failed to send via WhatsApp'
                      }
                    };

                    await storage.createMessage(insertMessage);
                  }
                } else {
                  const insertMessage = {
                    conversationId: _conversation.id,
                    contactId: _contact.id,
                    content: processedResponse.text,
                    messageType: 'text' as const,
                    direction: 'outbound' as const,
                    status: 'sent' as const,
                    timestamp: new Date(),
                    metadata: {
                      source: 'n8n_direct_webhook',
                      nodeId: node.id,
                      webhookUrl: data.chatWebhookUrl
                    }
                  };

                  await storage.createMessage(insertMessage);
                }
              } catch (error) {
                console.error('Error processing direct webhook AI response:', error);
              }

              context.setVariable('n8n.response', {
                message: processedResponse.text,
                aiResponse: processedResponse.text,
                mediaResponse: processedResponse.media
              });
              context.setVariable('n8n.status', response.status);
              context.setVariable('n8n.lastExecution', new Date().toISOString());

              return;
            }
          }
        } catch (directWebhookError: any) {
        }
      }

      const n8nConfig = data.n8nConfig || {};

      const webhookUrl = context.replaceVariables(data.webhookUrl || n8nConfig.webhookUrl || '');
      const apiKey = context.replaceVariables(data.apiKey || n8nConfig.apiKey || '');
      const workflowId = context.replaceVariables(data.workflowId || data.workflowName || n8nConfig.workflowId || '');
      const instanceUrl = context.replaceVariables(data.instanceUrl || n8nConfig.instanceUrl || '');



      if (!webhookUrl && !workflowId) {
        throw new Error('n8n configuration missing: webhookUrl or workflowId required');
      }

      let apiUrl = '';
      let method = 'POST';
      let body: any = {};
      let headers: any = {
        'Content-Type': 'application/json'
      };

      switch (operation) {
        case 'webhook_trigger':
          if (!webhookUrl) {
            throw new Error('Webhook URL is required for webhook_trigger operation');
          }
          apiUrl = webhookUrl;
          body = this.replaceVariablesInObject(data.payload || {}, context);

          if (Object.keys(body).length === 0) {
            body = {
              message: context.getVariable('message.content') || '',
              contact: {
                id: context.getVariable('contact.id'),
                name: context.getVariable('contact.name'),
                phone: context.getVariable('contact.phone')
              },
              timestamp: new Date().toISOString()
            };
          }
          break;

        case 'execute_workflow':
          if (!workflowId || !instanceUrl) {
            throw new Error('Workflow ID and instance URL are required for execute_workflow operation');
          }
          apiUrl = `${instanceUrl}/api/v1/workflows/${workflowId}/run`;
          if (apiKey) {
            headers['X-N8N-API-KEY'] = apiKey;
          }
          body = this.replaceVariablesInObject(data.inputData || {}, context);

          if (Object.keys(body).length === 0) {
            body = {
              message: context.getVariable('message.content') || '',
              contact: {
                id: context.getVariable('contact.id'),
                name: context.getVariable('contact.name'),
                phone: context.getVariable('contact.phone')
              },
              timestamp: new Date().toISOString(),
              source: 'PowerChatPlus_Flow'
            };
          }
          break;

        case 'get_workflow_status':
          if (!workflowId || !instanceUrl) {
            throw new Error('Workflow ID and instance URL are required for get_workflow_status operation');
          }
          const executionId = context.getVariable('n8n.execution.id') || data.executionId;
          if (!executionId) {
            throw new Error('Execution ID is required for get_workflow_status operation');
          }
          apiUrl = `${instanceUrl}/api/v1/executions/${executionId}`;
          method = 'GET';
          if (apiKey) {
            headers['X-N8N-API-KEY'] = apiKey;
          }
          break;

        case 'get_workflows':
          if (!instanceUrl) {
            throw new Error('Instance URL is required for get_workflows operation');
          }
          apiUrl = `${instanceUrl}/api/v1/workflows`;
          method = 'GET';
          if (apiKey) {
            headers['X-N8N-API-KEY'] = apiKey;
          }
          break;

        default:
          throw new Error(`Unsupported n8n operation: ${operation}`);
      }



      let response;
      try {
        response = await axios({
          method,
          url: apiUrl,
          headers,
          data: method !== 'GET' ? body : undefined,
          timeout: 30000
        });
      } catch (firstError: any) {
        if (operation === 'execute_workflow' && firstError.response?.status === 404) {


          try {
            const listResponse = await axios.get(`${instanceUrl}/api/v1/workflows`, {
              headers: {
                'X-N8N-API-KEY': apiKey,
                'Content-Type': 'application/json'
              },
              timeout: 30000
            });

            const workflows = listResponse.data?.data || [];


            let matchingWorkflow = workflows.find((w: any) =>
              w.id === workflowId ||
              w.name === workflowId ||
              w.id?.toString() === workflowId
            );

            if (matchingWorkflow && matchingWorkflow.id === workflowId) {


              if (!matchingWorkflow.active) {
                ;
                const activeWorkflows = workflows.filter((w: any) => w.active);
                console.log('Active workflows:', activeWorkflows.map((w: any) => ({ id: w.id, name: w.name })));

                if (activeWorkflows.length > 0) {
                  matchingWorkflow = activeWorkflows[0];
                  console.log('Using first active workflow:', matchingWorkflow);
                }
              }
            }

            if (matchingWorkflow) {


              let executionSuccess = false;

              const hasChatTrigger = matchingWorkflow.nodes?.some((node: any) =>
                node.type === '@n8n/n8n-nodes-langchain.chatTrigger'
              );

              if (hasChatTrigger) {
                const chatTriggerNode = matchingWorkflow.nodes.find((node: any) =>
                  node.type === '@n8n/n8n-nodes-langchain.chatTrigger'
                );

                if (chatTriggerNode?.webhookId) {
                  if (!matchingWorkflow.active) {
                    try {
                      await axios({
                        method: 'POST',
                        url: `${instanceUrl}/api/v1/workflows/${matchingWorkflow.id}/activate`,
                        headers,
                        timeout: 30000
                      });
                    } catch (activationError: any) {
                    }
                  }

                  try {
                    const userMessage = context.getVariable('message.content') || 'Hello';

                    const sessionId = `conv_${_conversation.id}_contact_${_contact.id}`;



                    const chatBodyFormats = [
                      { chatInput: userMessage, sessionId: sessionId },
                      { input: userMessage, sessionId: sessionId },
                      { message: userMessage, sessionId: sessionId },
                      { text: userMessage, sessionId: sessionId },
                      userMessage,
                      { query: userMessage }
                    ];

                    let webhookSuccess = false;

                    for (const chatBody of chatBodyFormats) {
                      try {
                        const webhookUrl = `${instanceUrl}/webhook/${chatTriggerNode.webhookId}`;


                        response = await axios({
                          method: 'POST',
                          url: webhookUrl,
                          headers: {
                            'Content-Type': 'application/json'
                          },
                          data: chatBody,
                          timeout: 30000
                        });

                        executionSuccess = true;
                        webhookSuccess = true;
                        break;
                      } catch (webhookError: any) {
                      }
                    }

                    if (!webhookSuccess) {
                      for (const chatBody of chatBodyFormats) {
                        try {
                          const testWebhookUrl = `${instanceUrl}/webhook-test/${chatTriggerNode.webhookId}`;

                          response = await axios({
                            method: 'POST',
                            url: testWebhookUrl,
                            headers: {
                              'Content-Type': 'application/json'
                            },
                            data: chatBody,
                            timeout: 30000
                          });

                          executionSuccess = true;
                          webhookSuccess = true;
                          break;
                        } catch (testWebhookError: any) {
                        }
                      }
                    }

                  } catch (error: any) {
                  }
                }
              }

              if (!executionSuccess) {
                const endpoints = [
                  `/api/v1/workflows/${matchingWorkflow.id}/run`,
                  `/api/v1/workflows/${matchingWorkflow.id}/execute`,
                  `/webhook/${matchingWorkflow.id}`
                ];
              for (const endpoint of endpoints) {
                try {
                  const retryUrl = `${instanceUrl}${endpoint}`;
                  response = await axios({
                    method: 'POST',
                    url: retryUrl,
                    headers,
                    data: body,
                    timeout: 30000
                  });

                  executionSuccess = true;
                  break;
                } catch (endpointError: any) {
                }
              }

              if (!executionSuccess) {
                throw firstError;
              }
            }
            } else {
              throw firstError;
            }
          } catch (listError: any) {
            console.log('Failed to get workflow list for resolution:', listError.message);
            throw firstError;
          }
        } else {
          throw firstError;
        }
      }

      let processedData = response?.data;

      if (response?.data) {

        let aiResponse = null;

        if (typeof response.data === 'string') {
          aiResponse = response.data;
        } else if (response.data.output) {
          aiResponse = response.data.output;
        } else if (response.data.text) {
          aiResponse = response.data.text;
        } else if (response.data.response) {
          aiResponse = response.data.response;
        } else if (response.data.message) {
          aiResponse = response.data.message;
        } else if (response.data.chatResponse) {
          aiResponse = response.data.chatResponse;
        } else if (response.data.data) {
          if (typeof response.data.data === 'string') {
            aiResponse = response.data.data;
          } else if (Array.isArray(response.data.data) && response.data.data.length > 0) {
            const firstItem = response.data.data[0];
            if (firstItem.output) {
              aiResponse = firstItem.output;
            } else if (firstItem.text) {
              aiResponse = firstItem.text;
            } else if (firstItem.response) {
              aiResponse = firstItem.response;
            } else if (firstItem.message) {
              aiResponse = firstItem.message;
            } else if (firstItem.chatResponse) {
              aiResponse = firstItem.chatResponse;
            } else if (typeof firstItem === 'string') {
              aiResponse = firstItem;
            }
          } else if (response.data.data.output) {
            aiResponse = response.data.data.output;
          } else if (response.data.data.text) {
            aiResponse = response.data.data.text;
          } else if (response.data.data.response) {
            aiResponse = response.data.data.response;
          } else if (response.data.data.message) {
            aiResponse = response.data.data.message;
          } else if (response.data.data.chatResponse) {
            aiResponse = response.data.data.chatResponse;
          }
        }

        if (!aiResponse && response.data.id && response.data.name && response.data.active) {
          aiResponse = null;
        }

        if (aiResponse && aiResponse.trim()) {
          processedData = { message: aiResponse, aiResponse: aiResponse };

          try {
            if ((_channelConnection.channelType === 'whatsapp' || _channelConnection.channelType === 'whatsapp_unofficial') && _contact.identifier) {
              try {
                if (_channelConnection.channelType === 'whatsapp_unofficial') {
                  await whatsAppService.sendMessage(
                    _channelConnection.id,
                    _channelConnection.userId,
                    _contact.identifier,
                    aiResponse,
                    true,
                    _conversation.id
                  );
                } else {
                  await whatsAppService.sendWhatsAppMessage(
                    _channelConnection.id,
                    _channelConnection.userId,
                    _contact.identifier,
                    aiResponse,
                    true,
                    _conversation.id
                  );
                }

                const recentMessages = await storage.getMessagesByConversationPaginated(_conversation.id, 1, 0);
                if (recentMessages.length > 0) {
                  const savedMessage = recentMessages[0];
                  const existingMetadata = savedMessage.metadata
                    ? (typeof savedMessage.metadata === 'string'
                       ? JSON.parse(savedMessage.metadata)
                       : savedMessage.metadata)
                    : {};
                  const updatedMetadata = {
                    ...existingMetadata,
                    source: 'n8n_ai_agent',
                    nodeId: node.id,
                    workflowName: data.workflowName || 'Unknown',
                    n8nWorkflowId: data.workflowId,
                    n8nWebhookUrl: data.chatWebhookUrl
                  };

                  await storage.updateMessage(savedMessage.id, {
                    metadata: JSON.stringify(updatedMetadata)
                  });
                }

              } catch (error) {
                console.error('Error sending n8n AI response via WhatsApp:', error);

                const insertMessage = {
                  conversationId: _conversation.id,
                  contactId: _contact.id,
                  content: aiResponse,
                  messageType: 'text' as const,
                  direction: 'outbound' as const,
                  status: 'failed' as const,
                  timestamp: new Date(),
                  metadata: {
                    source: 'n8n_ai_agent',
                    nodeId: node.id,
                    workflowName: data.workflowName || 'Unknown',
                    error: 'Failed to send via WhatsApp'
                  }
                };

                await storage.createMessage(insertMessage);
              }
            } else {
              const insertMessage = {
                conversationId: _conversation.id,
                contactId: _contact.id,
                content: aiResponse,
                messageType: 'text' as const,
                direction: 'outbound' as const,
                status: 'sent' as const,
                timestamp: new Date(),
                metadata: {
                  source: 'n8n_ai_agent',
                  nodeId: node.id,
                  workflowName: data.workflowName || 'Unknown'
                }
              };

              await storage.createMessage(insertMessage);
            }
          } catch (error) {
            console.error('Error processing n8n AI response:', error);
          }
        } else {
          processedData = response.data;
        }
      }

      context.setVariable('n8n.response', processedData);
      context.setVariable('n8n.status', response?.status);
      context.setVariable('n8n.lastExecution', new Date().toISOString());

      if (response?.data) {
        switch (operation) {
          case 'webhook_trigger':
            if (response.data.data) {
              context.setVariable('n8n.webhook.data', response.data.data);
            }
            if (response.data.executionId) {
              context.setVariable('n8n.execution.id', response.data.executionId);
            }
            break;

          case 'execute_workflow':
            if (response.data.data) {
              context.setVariable('n8n.workflow.result', response.data.data);
            }
            if (response.data.executionId) {
              context.setVariable('n8n.execution.id', response.data.executionId);
            }
            break;

          case 'get_workflow_status':
            context.setVariable('n8n.execution.status', response.data.finished ? 'completed' : 'running');
            if (response.data.data) {
              context.setVariable('n8n.execution.data', response.data.data);
            }
            break;

          case 'get_workflows':
            if (Array.isArray(response.data.data)) {
              context.setVariable('n8n.workflows', response.data.data);
            }
            break;
        }
      }


    } catch (error: any) {
      console.error('Error executing n8n node with context:', error);

      if (error.response) {
        console.error('N8n API Error Response:', {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
          url: error.config?.url
        });
      }

      context.setVariable('n8n.error', {
        message: error instanceof Error ? error.message : 'Unknown error',
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  /**
   * Convert relative media URL to full absolute URL
   */
  private convertToFullMediaUrl(mediaUrl: string): string {
    if (!mediaUrl) return '';

    if (mediaUrl.startsWith('http://') || mediaUrl.startsWith('https://')) {
      return mediaUrl;
    }

    let baseUrl = process.env.APP_URL || process.env.BASE_URL || process.env.PUBLIC_URL;

    if (!baseUrl) {
      const basePort = process.env.PORT || '9000';
      const host = process.env.HOST || 'localhost';

      const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';

      let port = basePort;
      if (process.env.NODE_ENV === 'development') {
        port = basePort;
      }

      if (host === 'localhost' || host === '127.0.0.1') {
        baseUrl = `${protocol}://${host}:${port}`;
      } else {
        baseUrl = `${protocol}://${host}`;
      }
    }

    const normalizedMediaUrl = mediaUrl.startsWith('/') ? mediaUrl : `/${mediaUrl}`;

    const fullUrl = `${baseUrl.replace(/\/$/, '')}${normalizedMediaUrl}`;


    return fullUrl;
  }

  /**
   * Build N8n message payload with multimedia support
   */
  private buildN8nMessagePayload(
    userMessage: string,
    messageType: string,
    mediaUrl: string | null,
    sessionId: string,
    nodeData: any,
    context: FlowExecutionContext
  ): any {
    const basePayload = {
      chatInput: userMessage,
      sessionId: sessionId
    };

    if (!nodeData.enableMediaSupport) {
      return basePayload;
    }

    const enhancedPayload: any = {
      ...basePayload,
      messageType: messageType,
      isMediaMessage: messageType !== 'text' && !!mediaUrl,

      message: {
        content: userMessage,
        type: messageType,
        timestamp: new Date().toISOString()
      },

      contact: {
        id: context.getVariable('contact.id'),
        name: context.getVariable('contact.name'),
        phone: context.getVariable('contact.phone'),
        email: context.getVariable('contact.email')
      },

      conversation: {
        id: context.getVariable('conversation.id'),
        channelType: context.getVariable('conversation.channelType')
      }
    };

    if (mediaUrl && messageType !== 'text') {
      const fullMediaUrl = this.convertToFullMediaUrl(mediaUrl);

      enhancedPayload.media = {
        url: fullMediaUrl,
        type: messageType
      };

      

      if (nodeData.includeFileMetadata) {
        const metadata = context.getVariable('message.metadata');
        if (metadata) {
          try {
            const parsedMetadata = typeof metadata === 'string' ? JSON.parse(metadata) : metadata;
            enhancedPayload.media.metadata = {
              filename: parsedMetadata.filename,
              mimeType: parsedMetadata.mimeType,
              fileSize: parsedMetadata.fileSize,
              originalName: parsedMetadata.originalName
            };
          } catch (error) {
            console.warn('Failed to parse message metadata for N8n node:', error);
          }
        }
      }

      const supportedTypes = nodeData.supportedMediaTypes || ['image', 'video', 'audio', 'document'];
      if (!supportedTypes.includes(messageType)) {
        console.warn(`Media type ${messageType} not supported by N8n node. Supported types: ${supportedTypes.join(', ')}`);
        enhancedPayload.media.unsupported = true;
        enhancedPayload.media.supportedTypes = supportedTypes;
      }
    }


    return enhancedPayload;
  }

  /**
   * Process N8n response with multimedia support
   */
  private processN8nResponse(responseData: any, nodeData: any): {
    text: string | null;
    media: Array<{
      type: string;
      url: string;
      caption?: string;
      filename?: string;
      metadata?: any;
    }>;
  } {
    const result = {
      text: null as string | null,
      media: [] as Array<{
        type: string;
        url: string;
        caption?: string;
        filename?: string;
        metadata?: any;
      }>
    };

    if (typeof responseData === 'string') {
      result.text = responseData;
    } else if (responseData.output) {
      result.text = responseData.output;
    } else if (responseData.text) {
      result.text = responseData.text;
    } else if (responseData.response) {
      result.text = responseData.response;
    } else if (responseData.message) {
      result.text = responseData.message;
    } else if (responseData.chatResponse) {
      result.text = responseData.chatResponse;
    }

    if (nodeData.enableMediaSupport && responseData.media) {
      if (Array.isArray(responseData.media)) {
        result.media = responseData.media
          .filter((item: any) => item.url && item.type)
          .map((item: any) => ({
            type: this.normalizeMediaType(item.type),
            url: item.url,
            caption: item.caption || item.text || '',
            filename: item.filename || item.name,
            metadata: item.metadata
          }));
      } else if (responseData.media.url && responseData.media.type) {
        result.media.push({
          type: this.normalizeMediaType(responseData.media.type),
          url: responseData.media.url,
          caption: responseData.media.caption || responseData.media.text || '',
          filename: responseData.media.filename || responseData.media.name,
          metadata: responseData.media.metadata
        });
      }
    }

    if (nodeData.enableMediaSupport) {
      ['image', 'video', 'audio', 'document'].forEach(mediaType => {
        if (responseData[mediaType]) {
          const mediaItem = responseData[mediaType];
          if (typeof mediaItem === 'string') {
            result.media.push({
              type: mediaType,
              url: mediaItem,
              caption: responseData[`${mediaType}Caption`] || ''
            });
          } else if (mediaItem.url) {
            result.media.push({
              type: mediaType,
              url: mediaItem.url,
              caption: mediaItem.caption || responseData[`${mediaType}Caption`] || '',
              filename: mediaItem.filename,
              metadata: mediaItem.metadata
            });
          }
        }
      });

      if (responseData.attachments && Array.isArray(responseData.attachments)) {
        responseData.attachments.forEach((attachment: any) => {
          if (attachment.url) {
            result.media.push({
              type: this.detectMediaTypeFromUrl(attachment.url) || 'document',
              url: attachment.url,
              caption: attachment.caption || attachment.description || '',
              filename: attachment.filename || attachment.name,
              metadata: attachment.metadata
            });
          }
        });
      }
    }

    return result;
  }

  /**
   * Send media responses from N8n
   */
  private async sendN8nMediaResponses(
    mediaItems: Array<{
      type: string;
      url: string;
      caption?: string;
      filename?: string;
      metadata?: any;
    }>,
    channelConnection: ChannelConnection,
    contact: Contact,
    conversation: Conversation,
    nodeId: string,
    webhookUrl: string
  ): Promise<void> {
    for (const mediaItem of mediaItems) {
      try {
        if (channelConnection.channelType === 'whatsapp_unofficial') {
          await whatsAppService.sendMedia(
            channelConnection.id,
            channelConnection.userId,
            contact.identifier || contact.phone || '',
            mediaItem.type as 'image' | 'video' | 'audio' | 'document',
            mediaItem.url,
            mediaItem.caption || '',
            mediaItem.filename || '',
            false,
            conversation.id
          );
        } else if (channelConnection.channelType === 'whatsapp') {
          await whatsAppService.sendWhatsAppMediaMessage(
            channelConnection.id,
            channelConnection.userId,
            contact.identifier || contact.phone || '',
            mediaItem.type as 'image' | 'video' | 'audio' | 'document',
            mediaItem.url,
            mediaItem.caption || '',
            mediaItem.filename || '',
            false,
            conversation.id
          );
        }

        const recentMessages = await storage.getMessagesByConversationPaginated(conversation.id, 1, 0);
        if (recentMessages.length > 0) {
          const savedMessage = recentMessages[0];
          const existingMetadata = savedMessage.metadata
            ? (typeof savedMessage.metadata === 'string'
               ? JSON.parse(savedMessage.metadata)
               : savedMessage.metadata)
            : {};
          const updatedMetadata = {
            ...existingMetadata,
            source: 'n8n_media_response',
            nodeId: nodeId,
            webhookUrl: webhookUrl,
            mediaType: mediaItem.type,
            originalUrl: mediaItem.url
          };

          await storage.updateMessage(savedMessage.id, {
            metadata: JSON.stringify(updatedMetadata)
          });
        }
      } catch (error) {
        console.error(`Error sending N8n media response (${mediaItem.type}):`, error);

        const insertMessage = {
          conversationId: conversation.id,
          contactId: contact.id,
          content: `[${mediaItem.type.toUpperCase()}] ${mediaItem.caption || 'Media file'}`,
          type: mediaItem.type,
          direction: 'outbound' as const,
          status: 'failed' as const,
          mediaUrl: mediaItem.url,
          timestamp: new Date(),
          metadata: JSON.stringify({
            source: 'n8n_media_response',
            nodeId: nodeId,
            webhookUrl: webhookUrl,
            error: 'Failed to send media response',
            originalUrl: mediaItem.url
          })
        };

        await storage.createMessage(insertMessage);
      }
    }
  }

  /**
   * Normalize media type to standard format
   */
  private normalizeMediaType(type: string): string {
    const lowerType = type.toLowerCase();
    if (lowerType.includes('image') || lowerType.includes('photo') || lowerType.includes('picture')) {
      return 'image';
    }
    if (lowerType.includes('video') || lowerType.includes('movie') || lowerType.includes('clip')) {
      return 'video';
    }
    if (lowerType.includes('audio') || lowerType.includes('voice') || lowerType.includes('sound')) {
      return 'audio';
    }
    return 'document';
  }

  /**
   * Detect media type from URL
   */
  private detectMediaTypeFromUrl(url: string): string | null {
    const extension = url.split('.').pop()?.toLowerCase();
    if (!extension) return null;

    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp'];
    const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'm4a', 'flac'];

    if (imageExtensions.includes(extension)) return 'image';
    if (videoExtensions.includes(extension)) return 'video';
    if (audioExtensions.includes(extension)) return 'audio';
    return 'document';
  }

  /**
   * Execute a Google Calendar Event node to create a calendar event
   */
  async executeGoogleCalendarEventNode(
    node: any,
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {

      const data = node.data || {};

      const eventTitle = this.replaceVariables(data.eventTitle || 'New Event', message, contact);
      const eventDescription = this.replaceVariables(data.eventDescription || '', message, contact);
      const eventLocation = this.replaceVariables(data.eventLocation || '', message, contact);

      let startDateTime = data.startDateTime;
      let endDateTime = data.endDateTime;
      let duration = data.duration || 30;

      if (typeof startDateTime === 'string') {
        startDateTime = new Date(startDateTime);
      }

      if (!endDateTime && startDateTime && duration) {
        endDateTime = new Date(startDateTime);
        endDateTime.setMinutes(endDateTime.getMinutes() + parseInt(duration));
      }

      const startDateTimeISO = startDateTime instanceof Date ? startDateTime.toISOString() : startDateTime;
      const endDateTimeISO = endDateTime instanceof Date ? endDateTime.toISOString() : endDateTime;

      const attendees: string[] = [];
      if (contact.email) {
        attendees.push(contact.email);
      }

      const eventData = {
        summary: eventTitle,
        description: eventDescription,
        location: eventLocation,
        start: {
          dateTime: startDateTimeISO,
          timeZone: data.timeZone || 'UTC',
        },
        end: {
          dateTime: endDateTimeISO,
          timeZone: data.timeZone || 'UTC',
        },
        attendees: attendees,
      };

      const userId = channelConnection.userId;
      const companyId = channelConnection.companyId || 1;

      const eventResult = await googleCalendarService.createCalendarEvent(userId, companyId, eventData);

      if (eventResult && eventResult.success && eventResult.eventId) {
        const confirmationMessage = `✅ Calendar event created!
Title: ${eventTitle}
Time: ${new Date(startDateTimeISO).toLocaleString()}
${eventResult.eventLink ? `\nView event: ${eventResult.eventLink}` : ''}`;



        // Use unified message sending for all channel types
        try {
          await this.sendMessageThroughChannel(
            channelConnection,
            contact,
            confirmationMessage,
            conversation,
            true
          );
        } catch (channelError) {
          console.error('Error sending confirmation message:', channelError);
        }
      } else {

        const errorMessage = 'Lo siento, no pude crear el evento del calendario. Por favor intenta de nuevo.';



        // Use unified message sending for all channel types
        try {
          await this.sendMessageThroughChannel(
            channelConnection,
            contact,
            errorMessage,
            conversation,
            true
          );
        } catch (channelError) {
          console.error('Error sending error message:', channelError);
        }
      }
    } catch (error) {
      // Error executing Google Calendar Event node
    }
  }

  /**
   * Execute a Google Calendar Availability node to check calendar availability
   * and store the results for downstream nodes
   */
  async executeGoogleCalendarAvailabilityNode(
    node: any,
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<any> {
    try {

      const data = node.data || {};

      const singleDate = data.date;
      const useDateRange = data.useDateRange || false;
      const startDate = data.startDate;
      const endDate = data.endDate;
      const durationMinutes = data.durationMinutes || 60;

      const userId = channelConnection.userId;


      const availabilityResult = await googleCalendarService.getAvailableTimeSlots(
        userId,
        useDateRange ? undefined : singleDate,
        durationMinutes,
        useDateRange ? startDate : undefined,
        useDateRange ? endDate : undefined
      );

      if (!availabilityResult.success) {

        const availabilityData = {
          success: false,
          error: availabilityResult.error || 'Error al verificar la disponibilidad del calendario',
          events: [],
          timeSlots: []
        };

        await this.updateNodeData(node.id, { availabilityResults: availabilityData });

        const errorMessage = `📅 *Calendar Availability Check*\n\nSorry, I couldn't check the calendar availability: ${availabilityResult.error}`;

        const insertMessage = {
          conversationId: conversation.id,
          contactId: contact.id,
          channelType: channelConnection.channelType,
          type: 'text',
          content: errorMessage,
          direction: 'outbound',
          status: 'sent',
          mediaUrl: null,
          timestamp: new Date()
        };

        await storage.createMessage(insertMessage);

        if (channelConnection.channelType === 'whatsapp' && contact.identifier) {
          await whatsAppService.sendMessage(
            channelConnection.id,
            channelConnection.userId,
            contact.identifier,
            errorMessage
          );
        }

        return;
      }

      const timeSlots = availabilityResult.timeSlots || [];

      let availabilityMessage = '📅 *Verificación de Disponibilidad del Calendario*\n\n';

      if (timeSlots.length === 0) {
        availabilityMessage += 'No se encontraron horarios disponibles en el calendario para el período especificado.';
      } else {
        if (useDateRange) {
          availabilityMessage += `Encontré horarios disponibles para una reunión de ${durationMinutes} minutos en ${timeSlots.length} día(s):\n\n`;

          for (let i = 0; i < Math.min(timeSlots.length, 3); i++) {
            const dateData = timeSlots[i];
            availabilityMessage += `*${dateData.date}*\n`;

            if (dateData.slots.length === 0) {
              availabilityMessage += `No hay horarios disponibles en este día.\n\n`;
            } else {
              for (let j = 0; j < Math.min(dateData.slots.length, 5); j++) {
                availabilityMessage += `⏰ ${dateData.slots[j]}\n`;
              }

              if (dateData.slots.length > 5) {
                availabilityMessage += `...y ${dateData.slots.length - 5} horarios más.\n`;
              }

              availabilityMessage += '\n';
            }
          }

          if (timeSlots.length > 3) {
            availabilityMessage += `...y ${timeSlots.length - 3} días más con horarios disponibles.\n`;
          }
        } else {
          const dateData = timeSlots[0];
          availabilityMessage += `Encontré ${dateData.slots.length} horarios disponibles para una reunión de ${durationMinutes} minutos el ${dateData.date}:\n\n`;

          for (let i = 0; i < Math.min(dateData.slots.length, 8); i++) {
            availabilityMessage += `⏰ ${dateData.slots[i]}\n`;
          }

          if (dateData.slots.length > 8) {
            availabilityMessage += `...y ${dateData.slots.length - 8} horarios más disponibles.\n`;
          }
        }
      }

      const availabilityData = {
        success: true,
        timeSlots: timeSlots,
        mode: useDateRange ? 'dateRange' : 'singleDate',
        date: singleDate,
        startDate: useDateRange ? startDate : null,
        endDate: useDateRange ? endDate : null,
        durationMinutes: durationMinutes
      };



      if (channelConnection.channelType === 'whatsapp' && contact.identifier) {
        await whatsAppService.sendMessage(
          channelConnection.id,
          channelConnection.userId,
          contact.identifier,
          availabilityMessage
        );
      }

      const formattedAvailability = availabilityMessage;

      await this.updateNodeData(node.id, {
        ...node.data,
        availabilityResults: availabilityData,
        formattedAvailability: formattedAvailability
      });


      const extendedMessage = message as Message & {
        flowContext?: {
          availabilityData?: string
        }
      };

      if (!extendedMessage.flowContext) {
        extendedMessage.flowContext = {};
      }

      extendedMessage.flowContext.availabilityData = formattedAvailability;


      return availabilityData;
    } catch (error: unknown) {
      const errorMessage = 'Lo siento, no pude verificar la disponibilidad del calendario. Por favor intenta de nuevo.';


      let errorDetail = 'Ocurrió un error desconocido';
      if (error instanceof Error) {
        errorDetail = error.message;
      } else if (typeof error === 'string') {
        errorDetail = error;
      } else if (error && typeof error === 'object' && 'message' in error) {
        errorDetail = String(error.message);
      }



      if (channelConnection.channelType === 'whatsapp' && contact.identifier) {
        await whatsAppService.sendMessage(
          channelConnection.id,
          channelConnection.userId,
          contact.identifier,
          errorMessage
        );
      }

      await this.updateNodeData(node.id, {
        ...node.data,
        availabilityResults: {
          success: false,
          error: errorDetail
        }
      });

      return {
        success: false,
        error: errorDetail
      };
    }
  }

  /**
   * Update the data of a node in the flow
   */
  private async updateNodeData(_nodeId: string, _newData: any): Promise<void> {
    try {
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Error updating node data:', errorMessage);
    }
  }

  /**
   * Execute a node to update pipeline stage for a contact/deal
   */
  async executeUpdatePipelineStageNode(
    node: any,
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {
      const data = node.data || {};
      const operation = data.operation || 'update_stage';
      const errorHandling = data.errorHandling || 'continue';


      try {
        switch (operation) {
          case 'create_stage':
            await this.executeCreateStageOperation(data, message, contact, channelConnection);
            break;
          case 'create_deal':
            await this.executeCreateDealOperation(data, message, conversation, contact, channelConnection);
            break;
          case 'update_deal':
            await this.executeUpdateDealOperation(data, message, contact, channelConnection);
            break;
          case 'manage_tags':
            await this.executeManageTagsOperation(data, message, contact, channelConnection);
            break;
          case 'update_stage':
          default:
            await this.executeUpdateStageOperation(data, message, contact, channelConnection);
            break;
        }

      } catch (operationError) {
        console.error(`[Pipeline Node] Operation ${operation} failed:`, operationError);

        if (errorHandling === 'stop') {
          throw operationError;
        }
      }
    } catch (error) {
      console.error(`[Pipeline Node] Critical error:`, error);
      throw error;
    }
  }

  /**
   * Execute create stage operation
   */
  private async executeCreateStageOperation(
    data: any,
    message: Message,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    const stageName = this.replaceVariables(data.stageName || '', message, contact);
    const stageColor = data.stageColor || '#3a86ff';

    if (!stageName.trim()) {
      throw new Error('Stage name is required for create_stage operation');
    }

    const user = await storage.getUser(channelConnection.userId);
    if (!user?.companyId) {
      throw new Error('User must be associated with a company to create stages');
    }

    const newStage = await storage.createPipelineStage({
      companyId: user.companyId,
      name: stageName,
      color: stageColor,
      order: 0
    });

    ;
  }

  /**
   * Execute create deal operation
   */
  private async executeCreateDealOperation(
    data: any,
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<any> {
    const dealTitle = this.replaceVariables(data.dealTitle || `${contact.name} - New Deal`, message, contact);
    const dealValue = data.dealValue ? parseInt(this.replaceVariables(data.dealValue, message, contact)) : null;
    const dealPriority = data.dealPriority || 'medium';
    const dealDescription = this.replaceVariables(data.dealDescription || '', message, contact);
    const stageId = data.stageId ? parseInt(data.stageId) : null;

    const user = await storage.getUser(channelConnection.userId);
    if (!user?.companyId) {
      throw new Error('User must be associated with a company to create deals');
    }

    const dealData = {
      companyId: user.companyId,
      contactId: contact.id,
      title: dealTitle,
      value: dealValue,
      priority: dealPriority as 'low' | 'medium' | 'high',
      description: dealDescription,
      stageId: stageId,
      assignedToUserId: channelConnection.userId,
      tags: data.tagsToAdd || []
    };

    const newDeal = await storage.createDeal(dealData);

    await storage.createDealActivity({
      dealId: newDeal.id,
      userId: channelConnection.userId,
      type: 'create',
      content: `Deal "${newDeal.title}" created via flow automation`,
      metadata: {
        createdBy: 'flow',
        flowNodeId: data.id,
        conversationId: conversation.id
      }
    });

    ;
    return newDeal;
  }

  /**
   * Execute update deal operation
   */
  private async executeUpdateDealOperation(
    data: any,
    message: Message,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    const dealIdVar = data.dealIdVariable || '{{contact.phone}}';
    const dealId = this.replaceVariables(dealIdVar, message, contact);

    const user = await storage.getUser(channelConnection.userId);
    const companyId = user?.companyId || undefined;

    let deal = await this.findDealByIdOrContact(dealId, contact.id, companyId);

    if (!deal && data.createDealIfNotExists) {
      ;
      deal = await this.executeCreateDealOperation(data, message, { id: 0 } as any, contact, channelConnection);
      ;
    }

    if (!deal) {
      throw new Error(`No deal found for ID/variable: ${dealId}`);
    }

    const updates: any = {};
    let hasUpdates = false;

    if (data.dealTitle) {
      updates.title = this.replaceVariables(data.dealTitle, message, contact);
      hasUpdates = true;
    }

    if (data.dealValue) {
      const value = parseInt(this.replaceVariables(data.dealValue, message, contact));
      if (!isNaN(value)) {
        updates.value = value;
        hasUpdates = true;
      }
    }

    if (data.dealPriority && data.dealPriority !== 'keep_current') {
      updates.priority = data.dealPriority;
      hasUpdates = true;
    }

    if (data.stageId && data.stageId !== 'keep_current') {
      const stageIdNum = parseInt(data.stageId);
      if (!isNaN(stageIdNum)) {
        updates.stageId = stageIdNum;
        hasUpdates = true;
      }
    }

    if (data.dealDescription) {
      updates.description = this.replaceVariables(data.dealDescription, message, contact);
      hasUpdates = true;
    }

    if (hasUpdates) {
      const updatedDeal = await storage.updateDeal(deal.id, updates);

      await storage.createDealActivity({
        dealId: deal.id,
        userId: channelConnection.userId,
        type: 'update',
        content: `Deal "${updatedDeal.title}" updated via flow automation`,
        metadata: {
          updatedBy: 'flow',
          flowNodeId: data.id,
          changes: updates
        }
      });

      ;
    } else {
      ;
    }
  }

  /**
   * Execute manage tags operation
   */
  private async executeManageTagsOperation(
    data: any,
    message: Message,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    const dealIdVar = data.dealIdVariable || '{{contact.phone}}';
    const dealId = this.replaceVariables(dealIdVar, message, contact);

    const user = await storage.getUser(channelConnection.userId);
    const companyId = user?.companyId || undefined;

    const deal = await this.findDealByIdOrContact(dealId, contact.id, companyId);
    if (!deal) {
      throw new Error(`No deal found for ID/variable: ${dealId}`);
    }

    const currentTags = deal.tags || [];
    let newTags = [...currentTags];
    let hasChanges = false;

    if (data.tagsToAdd && data.tagsToAdd.length > 0) {
      for (const tag of data.tagsToAdd) {
        const processedTag = this.replaceVariables(tag, message, contact).trim();
        if (processedTag && !newTags.includes(processedTag)) {
          newTags.push(processedTag);
          hasChanges = true;
        }
      }
    }

    if (data.tagsToRemove && data.tagsToRemove.length > 0) {
      for (const tag of data.tagsToRemove) {
        const processedTag = this.replaceVariables(tag, message, contact).trim();
        const index = newTags.indexOf(processedTag);
        if (index > -1) {
          newTags.splice(index, 1);
          hasChanges = true;
        }
      }
    }

    if (hasChanges) {
      const updatedDeal = await storage.updateDeal(deal.id, { tags: newTags });

      await storage.createDealActivity({
        dealId: deal.id,
        userId: channelConnection.userId,
        type: 'update',
        content: `Deal tags updated via flow automation`,
        metadata: {
          updatedBy: 'flow',
          flowNodeId: data.id,
          oldTags: currentTags,
          newTags: newTags,
          tagsAdded: data.tagsToAdd || [],
          tagsRemoved: data.tagsToRemove || []
        }
      });

      ;
    } else {
      ;
    }
  }

  /**
   * Execute update stage operation (original functionality)
   */
  private async executeUpdateStageOperation(
    data: any,
    message: Message,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    const stageId = data.stageId;
    if (!stageId) {
      throw new Error('No stage ID specified for update_stage operation');
    }

    const dealIdVar = data.dealIdVariable || '{{contact.phone}}';
    const dealId = this.replaceVariables(dealIdVar, message, contact);

    const user = await storage.getUser(channelConnection.userId);
    const companyId = user?.companyId || undefined;

    let deal = await this.findDealByIdOrContact(dealId, contact.id, companyId);

    if (!deal && data.createDealIfNotExists) {
      ;
      deal = await this.executeCreateDealOperation(data, message, { id: 0 } as any, contact, channelConnection);
      ;
    }

    if (!deal) {
      throw new Error(`No deal found for ID/variable: ${dealId}`);
    }

    const stageIdNum = parseInt(stageId);
    if (isNaN(stageIdNum)) {
      throw new Error(`Invalid stage ID: ${stageId}`);
    }

    let stage = await storage.getPipelineStage(stageIdNum);
    if (!stage && data.createStageIfNotExists && data.stageName) {
      const user = await storage.getUser(channelConnection.userId);
      if (user?.companyId) {
        stage = await storage.createPipelineStage({
          companyId: user.companyId,
          name: this.replaceVariables(data.stageName, message, contact),
          color: data.stageColor || '#3a86ff',
          order: 0
        });
        ;
      }
    }

    if (!stage) {
      throw new Error(`Stage with ID ${stageIdNum} not found`);
    }

    const updatedDeal = await storage.updateDeal(deal.id, { stageId: stageIdNum });

    await storage.createDealActivity({
      dealId: deal.id,
      userId: channelConnection.userId,
      type: 'stage_change',
      content: `Deal moved to stage: ${stage.name}`,
      metadata: {
        oldStageId: deal.stageId,
        newStageId: stageIdNum,
        changedBy: 'flow',
        flowNodeId: data.id
      }
    });

    ;
  }

  /**
   * Helper method to find deal by ID, phone number, or contact
   */
  private async findDealByIdOrContact(dealId: string, contactId: number, companyId?: number): Promise<any> {
    try {
      ;

      if (dealId && (dealId.length > 8 || dealId.includes('+') || dealId.includes('-') || dealId.includes(' '))) {
        ;
        const filter: any = { contactPhone: dealId };
        if (companyId) {
          filter.companyId = companyId;
        }
        const deals = await storage.getDeals(filter);
        if (deals && deals.length > 0) {
          ;
          return deals[0];
        }
      }

      const dealIdNum = parseInt(dealId);
      if (!isNaN(dealIdNum) && dealIdNum > 0 && dealIdNum <= 2147483647) {
        ;
        try {
          const deal = await storage.getDeal(dealIdNum);
          if (deal) {
            ;
            return deal;
          }
        } catch (error) {
          ;
        }
      }

      const contactIdNum = parseInt(dealId);
      if (!isNaN(contactIdNum) && contactIdNum > 0 && contactIdNum <= 2147483647) {
        ;
        try {
          const deals = await storage.getDealsByContact(contactIdNum);
          if (deals && deals.length > 0) {
            ;
            return deals[0];
          }
        } catch (error) {
          ;
        }
      }

      if (dealId === contactId.toString()) {
        ;
        try {
          const deals = await storage.getDealsByContact(contactId);
          if (deals && deals.length > 0) {
            ;
            return deals[0];
          }
        } catch (error) {
          ;
        }
      }

      ;
      return null;
    } catch (error) {
      console.error(`[Pipeline Node] Error finding deal by ID/contact: ${dealId}`, error);
      return null;
    }
  }

  /**
   * Execute an AI Assistant node to generate AI responses from multiple providers
   */
  async executeAIAssistantNode(
    node: any,
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {

      const data = node.data || {};

      const provider = data.provider || 'gemini';

      let model = data.model || 'gemini-1.5-pro-latest';

      if (provider === 'gemini') {
        if (model === 'gemini-2.5-pro-preview-03-25') {
          model = 'gemini-1.5-pro-latest';
        } else if (model === 'gemini-2.5-flash-preview-04-17') {
          model = 'gemini-1.5-flash-latest';
        } else if (model === 'gemini-2.0-flash') {
          model = 'gemini-pro';
        }
      }

      const apiKey = data.apiKey || process.env.XAI_API_KEY || '';

      const systemPrompt = data.prompt || 'You are a helpful assistant.';
      const enableHistory = data.enableHistory !== undefined ? data.enableHistory : true;
      const historyLimit = data.historyLimit || 5;
      const enableAudio = data.enableAudio || false;

      if (!apiKey) {

        const errorMessage = 'Error: AI Assistant is not configured with an API key. Please set up the API key in the flow builder.';

        const insertMessage = {
          conversationId: conversation.id,
          contactId: contact.id,
          channelType: channelConnection.channelType,
          type: 'text',
          content: errorMessage,
          direction: 'outbound',
          status: 'sent',
          mediaUrl: null,
          timestamp: new Date()
        };

        await storage.createMessage(insertMessage);

        if ((channelConnection.channelType === 'whatsapp' || channelConnection.channelType === 'whatsapp_unofficial') && contact.identifier) {
          await whatsAppService.sendMessage(
            channelConnection.id,
            channelConnection.userId,
            contact.identifier,
            errorMessage,
            true
          );
        }

        return;
      }

      let conversationHistory: Message[] = [];
      if (enableHistory) {
        conversationHistory = await storage.getMessagesByConversation(conversation.id);
      }


      let aiResponse: {
        text: string;
        audioUrl?: string;
        functionCalls?: any[];
        triggeredTasks?: string[];
        triggeredCalendarFunctions?: any[];
      };

      try {
        const aiAssistantServiceModule = await import('../services/ai-assistant');
        const aiAssistantService = aiAssistantServiceModule.default;



        const aiConfig = {
          provider,
          model,
          apiKey,
          systemPrompt,
          enableHistory,
          historyLimit,
          enableAudio: true,
          enableImage: true,
          enableVideo: true,
          enableVoiceProcessing: provider === 'openai',
          enableTextToSpeech: data.enableTextToSpeech || false,
          ttsProvider: data.ttsProvider || 'openai',
          ttsVoice: data.ttsVoice || 'alloy',
          voiceResponseMode: data.voiceResponseMode || 'always',
          maxAudioDuration: data.maxAudioDuration || 30,
          enableFunctionCalling: data.enableTaskExecution || data.enableGoogleCalendar || false,
          enableTaskExecution: data.enableTaskExecution || false,
          tasks: data.tasks || [],
          enableGoogleCalendar: data.enableGoogleCalendar || false,
          calendarFunctions: data.calendarFunctions || [],
          elevenLabsApiKey: data.elevenLabsApiKey,
          elevenLabsVoiceId: data.elevenLabsVoiceId,
          elevenLabsCustomVoiceId: data.elevenLabsCustomVoiceId,
          elevenLabsModel: data.elevenLabsModel || 'eleven_monolingual_v1',
          elevenLabsStability: data.elevenLabsStability ?? 0.5,
          elevenLabsSimilarityBoost: data.elevenLabsSimilarityBoost ?? 0.75,
          elevenLabsStyle: data.elevenLabsStyle ?? 0.0,
          elevenLabsUseSpeakerBoost: data.elevenLabsUseSpeakerBoost ?? true
        };

        aiResponse = await aiAssistantService.processMessage(
          message,
          conversation,
          contact,
          channelConnection,
          aiConfig,
          conversationHistory
        );


      } catch (error) {

        aiResponse = {
          text: `I'm sorry, I encountered an error while processing your request: "${error instanceof Error ? error.message : 'Unknown error'}". Please try again with a different question.`,
          functionCalls: [],
          triggeredTasks: [],
          triggeredCalendarFunctions: []
        };

      }




      const responseText = this.replaceVariables(aiResponse.text, message, contact);

      if (aiResponse.audioUrl && (channelConnection.channelType === 'whatsapp' || channelConnection.channelType === 'whatsapp_unofficial') && contact.identifier) {
        try {
          const audioPath = aiResponse.audioUrl.startsWith('/') ? aiResponse.audioUrl.slice(1) : aiResponse.audioUrl;
          const fullAudioPath = path.join(process.cwd(), audioPath);

          

          if (channelConnection.channelType === 'whatsapp_unofficial') {
            await whatsAppService.sendWhatsAppAudioMessage(
              channelConnection.id,
              channelConnection.userId,
              contact.identifier,
              fullAudioPath
            );
          } else {
            
            await whatsAppService.sendWhatsAppMessage(
              channelConnection.id,
              channelConnection.userId,
              contact.identifier,
              responseText
            );
          }
        } catch (error) {
          try {
            if (channelConnection.channelType === 'whatsapp_unofficial') {
              await whatsAppService.sendMessage(
                channelConnection.id,
                channelConnection.userId,
                contact.identifier,
                responseText
              );
            } else {
              await whatsAppService.sendWhatsAppMessage(
                channelConnection.id,
                channelConnection.userId,
                contact.identifier,
                responseText
              );
            }
          } catch (textError) {
            // Error sending fallback text message
          }
        }
      } else if (responseText && responseText.trim()) {
        // Only send message if responseText has actual content
        // Use unified message sending for all channel types
        try {
          await this.sendMessageThroughChannel(
            channelConnection,
            contact,
            responseText,
            conversation,
            true
          );
        } catch (error) {

            const insertMessage = {
              conversationId: conversation.id,
              contactId: contact.id,
              channelType: channelConnection.channelType,
              type: 'text',
              content: responseText,
              direction: 'outbound',
              status: 'failed',
              isFromBot: true,
              mediaUrl: null,
              timestamp: new Date()
            };

            await storage.createMessage(insertMessage);
          }
      }

      // Handle task execution
      if (data.enableTaskExecution && aiResponse.functionCalls && aiResponse.functionCalls.length > 0) {
        // Task execution functions detected but not implemented yet
      }

      // Handle calendar function execution
      if (data.enableGoogleCalendar && aiResponse.triggeredCalendarFunctions && aiResponse.triggeredCalendarFunctions.length > 0) {
        for (const calendarFunction of aiResponse.triggeredCalendarFunctions) {
          try {
            await this.executeCalendarFunction(calendarFunction, conversation, contact, channelConnection, data);
          } catch (error) {
            // Send error message to user
            const errorMessage = `Encontré un error al intentar ${calendarFunction.name}: ${error instanceof Error ? error.message : 'Error desconocido'}`;
            await this.sendMessageThroughChannel(
              channelConnection,
              contact,
              errorMessage,
              conversation,
              true
            );
          }
        }
      }

    } catch (error) {
      // Error executing AI Assistant node
    }
  }

  /**
   * Execute a calendar function call from AI Assistant
   */
  async executeCalendarFunction(
    calendarFunction: any,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection,
    nodeData: any
  ): Promise<void> {
    const { name, arguments: args } = calendarFunction;



    try {
      // Import the Google Calendar service
      const googleCalendarModule = await import('../services/google-calendar');
      const googleCalendarService = googleCalendarModule.default;

      // Get user context from conversation - ensure we have valid IDs
      // Priority: 1) assigned user, 2) channel connection user, 3) company admin user
      let userId = conversation.assignedToUserId;

      if (!userId) {
        // If conversation is not assigned, try to use the channel connection's user
        userId = channelConnection.userId;
      }

      if (!userId && conversation.companyId) {
        // Final fallback: find the company's admin user
        const companyUsers = await storage.getUsersByCompany(conversation.companyId);
        const adminUser = companyUsers.find(user => user.role === 'admin') || companyUsers[0];
        userId = adminUser?.id || 1;
      }

      const companyId = conversation.companyId;
      const timeZone = nodeData.calendarTimeZone || 'UTC';

      if (!companyId) {
        throw new Error('Se requiere el ID de la empresa para las operaciones del calendario');
      }

      if (!userId) {
        throw new Error('No se pudo resolver el ID del usuario para las operaciones del calendario');
      }

      let result: any;
      let successMessage = '';

      switch (name) {
        case 'check_availability':
          result = await googleCalendarService.getAvailableTimeSlots(
            userId,
            companyId,
            args.date,
            args.duration_minutes || args.duration || 30
          );

          if (result.success) {
            const timeSlots = result.timeSlots || [];
            if (timeSlots.length > 0 && timeSlots[0].slots.length > 0) {
              const slotList = timeSlots[0].slots.join('\n');
              successMessage = `Horarios disponibles para ${args.date}:\n${slotList}`;
            } else {
              successMessage = `No se encontraron horarios disponibles para ${args.date}.`;
            }
          } else {
            throw new Error(result.error || 'Error al verificar disponibilidad');
          }
          break;

        case 'book_appointment':

          const eventData = {
            summary: args.title || args.summary,
            description: args.description || '',
            location: args.location || 'Virtual',
            start: {
              dateTime: args.start_datetime || args.start_time || args.startDateTime,
              timeZone: timeZone
            },
            end: {
              dateTime: args.end_datetime || args.end_time || args.endDateTime,
              timeZone: timeZone
            },
            attendees: args.attendees || []
          };

          result = await googleCalendarService.createCalendarEvent(
            userId,
            companyId,
            eventData
          );

          if (result.success) {
            const eventDate = new Date(eventData.start.dateTime).toLocaleString();
            const duration = args.duration_minutes || args.duration || 30;

            successMessage = `¡Perfecto! He agendado tu cita.\n\n` +
              `${eventData.summary}\n` +
              `${eventDate}\n` +
              `Duración: ${duration} minutos`;

            if (eventData.location) {
              successMessage += `\nUbicación: ${eventData.location}`;
            }

            if (result.eventLink) {
              successMessage += `\n\nEnlace del evento: ${result.eventLink}`;
            }
          } else {
            throw new Error(result.error || 'Error al agendar la cita');
          }
          break;

        case 'update_calendar_event':

          const updateData = {
            summary: args.title || args.summary,
            description: args.description || '',
            location: args.location || '',
            start: {
              dateTime: args.start_datetime || args.start_time || args.startDateTime,
              timeZone: timeZone
            },
            end: {
              dateTime: args.end_datetime || args.end_time || args.endDateTime,
              timeZone: timeZone
            },
            attendees: args.attendees || []
          };

          result = await googleCalendarService.updateCalendarEvent(
            userId,
            companyId,
            args.event_id || args.eventId,
            updateData
          );

          if (result.success) {
            const newEventDate = new Date(updateData.start.dateTime).toLocaleString();
            successMessage = `¡Excelente! He actualizado tu cita.\n\n` +
              `${updateData.summary}\n` +
              `Nueva hora: ${newEventDate}`;
          } else {
            throw new Error(result.error || 'Error al actualizar la cita');
          }
          break;

        case 'cancel_calendar_event':

          result = await googleCalendarService.deleteCalendarEvent(
            userId,
            companyId,
            args.event_id || args.eventId
          );

          if (result.success) {
            successMessage = `¡Listo! He cancelado tu cita.`;
          } else {
            throw new Error(result.error || 'Error al cancelar la cita');
          }
          break;

        case 'list_calendar_events':

          // Convert date format to ISO datetime for API
          const startDateTime = args.start_date ? `${args.start_date}T00:00:00Z` : args.time_min || args.timeMin;
          const endDateTime = args.end_date ? `${args.end_date}T23:59:59Z` : args.time_max || args.timeMax;

          result = await googleCalendarService.listCalendarEvents(
            userId,
            companyId,
            startDateTime,
            endDateTime,
            args.max_results || args.maxResults || 10
          );

          if (result.success) {
            const events = result.items || [];
            if (events.length > 0) {
              const eventList = events.map((event: any) =>
                `• ${event.summary} - ${new Date(event.start.dateTime).toLocaleString()}`
              ).join('\n');
              successMessage = `Tus próximas citas:\n${eventList}`;
            } else {
              successMessage = 'No se encontraron citas para el período especificado.';
            }
          } else {
            throw new Error(result.error || 'Error al listar las citas');
          }
          break;

        default:
          throw new Error(`Función de calendario desconocida: ${name}`);
      }

      // Send success message to user
      if (successMessage) {
        await this.sendMessageThroughChannel(
          channelConnection,
          contact,
          successMessage,
          conversation,
          true
        );
      }

    } catch (error) {
      throw error; // Re-throw to be handled by caller
    }
  }

  /**
   * Execute Bot Disable node with execution context
   */
  private async executeBotDisableNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {

      const data = node.data || {};

      const triggerMethod = data.triggerMethod || 'always';
      let shouldDisable = false;

      if (triggerMethod === 'always') {
        shouldDisable = true;
      } else if (triggerMethod === 'keyword') {
        const keyword = data.keyword || 'agent';
        const caseSensitive = data.caseSensitive || false;
        const userInput = context.getVariable('message.content') || '';

        if (caseSensitive) {
          shouldDisable = userInput.includes(keyword);
        } else {
          shouldDisable = userInput.toLowerCase().includes(keyword.toLowerCase());
        }
      }

      if (shouldDisable) {
        let durationMinutes: number | null = null;
        const disableDuration = data.disableDuration || '30';

        if (disableDuration === 'manual') {
          durationMinutes = null;
        } else if (disableDuration === 'custom') {
          const customDuration = data.customDuration || 60;
          const customDurationUnit = data.customDurationUnit || 'minutes';

          switch (customDurationUnit) {
            case 'minutes':
              durationMinutes = customDuration;
              break;
            case 'hours':
              durationMinutes = customDuration * 60;
              break;
            case 'days':
              durationMinutes = customDuration * 60 * 24;
              break;
            default:
              durationMinutes = customDuration;
          }
        } else {
          durationMinutes = parseInt(disableDuration);
        }

        const assignToAgent = data.assignToAgent;
        let assignToUserId: number | null = null;

        if (assignToAgent && assignToAgent !== 'auto') {
          const parsedUserId = parseInt(assignToAgent);
          if (!isNaN(parsedUserId)) {
            assignToUserId = parsedUserId;
          }
        }

        await this.disableBot(
          conversation.id,
          durationMinutes || undefined,
          `Triggered by ${triggerMethod === 'keyword' ? `keyword "${data.keyword}"` : 'flow node'}`,
          assignToUserId || undefined
        );

        const handoffMessage = data.handoffMessage || 'Your request has been forwarded to our support team. An agent will assist you shortly.';

        if (handoffMessage) {
          const insertMessage = {
            conversationId: conversation.id,
            contactId: contact.id,
            channelType: channelConnection.channelType,
            type: 'text',
            content: context.replaceVariables(handoffMessage),
            direction: 'outbound',
            status: 'sent',
            mediaUrl: null,
            timestamp: new Date()
          };

          await storage.createMessage(insertMessage);

          if ((channelConnection.channelType === 'whatsapp' || channelConnection.channelType === 'whatsapp_unofficial') && contact.identifier) {
            await whatsAppService.sendMessage(
              channelConnection.id,
              channelConnection.userId,
              contact.identifier,
              context.replaceVariables(handoffMessage)
            );
          }
        }

        context.setVariable('bot.disabled', true);
        context.setVariable('bot.disabledAt', new Date().toISOString());
        context.setVariable('bot.disableDuration', durationMinutes);
        context.setVariable('bot.assignedAgent', assignToUserId);

      } else {
        context.setVariable('bot.disableTriggerMet', false);
      }

    } catch (error) {
      context.setVariable('bot.disableError', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Execute Bot Reset node with execution context
   */
  private async executeBotResetNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {

      const data = node.data || {};
      const resetScope = data.resetScope || 'bot_only';

      await this.enableBot(conversation.id);

      if (resetScope === 'bot_and_context' || resetScope === 'full_reset') {
        if (data.clearVariables) {
          if (typeof (context as any).variables?.clear === 'function') {
            (context as any).variables.clear();
          } else {
            const allVars = context.getAllVariables();
            Object.keys(allVars).forEach(key => context.setVariable(key, undefined));
          }
        }

        if (data.resetFlowPosition) {
          context.setVariable('flow.resetToStart', true);
        }
      }

      if (resetScope === 'full_reset') {
        context.setVariable('conversation.fullReset', true);
      }

      if (data.sendConfirmation && data.confirmationMessage) {
        const confirmationMessage = context.replaceVariables(data.confirmationMessage);

        const insertMessage = {
          conversationId: conversation.id,
          contactId: contact.id,
          channelType: channelConnection.channelType,
          type: 'text',
          content: confirmationMessage,
          direction: 'outbound',
          status: 'sent',
          mediaUrl: null,
          timestamp: new Date()
        };

        await storage.createMessage(insertMessage);

        if ((channelConnection.channelType === 'whatsapp' || channelConnection.channelType === 'whatsapp_unofficial') && contact.identifier) {
          await whatsAppService.sendMessage(
            channelConnection.id,
            channelConnection.userId,
            contact.identifier,
            confirmationMessage
          );
        }
      }

      context.setVariable('bot.enabled', true);
      context.setVariable('bot.resetAt', new Date().toISOString());
      context.setVariable('bot.resetScope', resetScope);


    } catch (error) {
      console.error('Error executing Bot Reset node with context:', error);
      context.setVariable('bot.resetError', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Check if bot is disabled for a conversation
   */
  async isBotDisabled(conversationId: number): Promise<boolean> {
    try {
      const conversation = await storage.getConversation(conversationId);
      if (!conversation) return false;

      if (!conversation.botDisabled) return false;

      if (conversation.disableDuration && conversation.disabledAt) {
        const disabledAt = new Date(conversation.disabledAt);
        const expiresAt = new Date(disabledAt.getTime() + (conversation.disableDuration * 60 * 1000));
        const now = new Date();

        if (now > expiresAt) {
          await this.enableBot(conversationId);
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Error checking bot disabled status:', error);
      return false;
    }
  }

  /**
   * Disable bot for a conversation
   */
  async disableBot(
    conversationId: number,
    duration?: number,
    reason?: string,
    assignToUserId?: number
  ): Promise<void> {
    try {
      await storage.updateConversation(conversationId, {
        botDisabled: true,
        disabledAt: new Date(),
        disableDuration: duration || null,
        disableReason: reason || null,
        assignedToUserId: assignToUserId || null
      });

    } catch (error) {
      console.error('Error disabling bot:', error);
    }
  }

  /**
   * Enable bot for a conversation
   */
  async enableBot(conversationId: number): Promise<void> {
    try {
      await storage.updateConversation(conversationId, {
        botDisabled: false,
        disabledAt: null,
        disableDuration: null,
        disableReason: null
      });

    } catch (error) {
      console.error('Error enabling bot:', error);
    }
  }

  /**
   * Check if incoming message matches any hard reset keyword from active flows
   */
  private async checkHardResetKeyword(
    message: Message,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<boolean> {
    try {
      if (message.type !== 'text' || !message.content) {
        return false;
      }

      const messageContent = message.content.trim();
      if (!messageContent) {
        return false;
      }

      const flowAssignments = await storage.getFlowAssignments(channelConnection.id);
      const activeAssignments = flowAssignments.filter(assignment => assignment.isActive);

      for (const assignment of activeAssignments) {
        const baseFlow = await storage.getFlow(assignment.flowId);
        if (!baseFlow) continue;

        const flow: Flow = { ...baseFlow, definition: (baseFlow as any).definition || null };
        const { nodes } = await this.parseFlowDefinition(flow);

        const triggerNodes = nodes.filter((node: any) =>
          node.type === 'triggerNode' ||
          node.type === 'trigger' ||
          (node.data && node.data.label === 'Trigger Node') ||
          (node.data && node.data.label === 'Message Received')
        );

        for (const triggerNode of triggerNodes) {
          const data = triggerNode.data || {};
          const hardResetKeyword = data.hardResetKeyword;

          if (hardResetKeyword && hardResetKeyword.trim()) {
            const keyword = hardResetKeyword.trim().toLowerCase();
            const userMessage = messageContent.toLowerCase();

            if (userMessage === keyword) {
              

              await this.performHardReset(conversation, contact, channelConnection, flow, triggerNode);
              return true;
            }
          }
        }
      }

      return false;
    } catch (error) {
      console.error('Error checking hard reset keyword:', error);
      return false;
    }
  }

  /**
   * Perform hard reset: re-enable bot, clear session state, and start fresh flow execution
   */
  private async performHardReset(
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection,
    flow: Flow,
    triggerNode: any
  ): Promise<void> {
    try {
      

      await this.enableBot(conversation.id);

      await this.clearConversationSessions(conversation.id);

      const waitingExecutions = this.executionManager.getWaitingExecutionsForConversation(conversation.id);
      for (const execution of waitingExecutions) {
        this.executionManager.completeExecution(execution.id);
      }

      const data = triggerNode.data || {};
      const confirmationMessage = data.hardResetConfirmationMessage ||
        'Bot has been reactivated. Starting fresh conversation...';

      if (confirmationMessage && confirmationMessage.trim()) {
        if ((channelConnection.channelType === 'whatsapp' || channelConnection.channelType === 'whatsapp_unofficial') && contact.identifier) {
          const whatsAppService = (await import('./channels/whatsapp')).default;
          await whatsAppService.sendMessage(
            channelConnection.id,
            channelConnection.userId,
            contact.identifier,
            confirmationMessage,
            true,
            conversation.id
          );

          const recentMessages = await storage.getMessagesByConversationPaginated(conversation.id, 1, 0);
          if (recentMessages.length > 0) {
            const savedMessage = recentMessages[0];
            const existingMetadata = savedMessage.metadata
              ? (typeof savedMessage.metadata === 'string'
                 ? JSON.parse(savedMessage.metadata)
                 : savedMessage.metadata)
              : {};
            const updatedMetadata = {
              ...existingMetadata,
              hardReset: true
            };

            await storage.updateMessage(savedMessage.id, {
              metadata: JSON.stringify(updatedMetadata)
            });
          }
        } else {
          const insertMessage = {
            conversationId: conversation.id,
            externalId: null,
            direction: 'outbound' as const,
            type: 'text',
            content: confirmationMessage,
            metadata: { hardReset: true },
            senderId: null,
            senderType: null,
            status: 'sent',
            sentAt: new Date(),
            readAt: null,
            isFromBot: true,
            mediaUrl: null,
            createdAt: new Date()
          };

          await storage.createMessage(insertMessage);
        }
      }

      const resetMessage: Message = {
        id: 0,
        conversationId: conversation.id,
        externalId: null,
        direction: 'inbound',
        type: 'text',
        content: data.hardResetKeyword || 'reset',
        metadata: { hardReset: true },
        senderId: contact.id,
        senderType: 'contact',
        status: 'received',
        sentAt: new Date(),
        readAt: null,
        isFromBot: false,
        mediaUrl: null,
        createdAt: new Date(),
        groupParticipantJid: null,
        groupParticipantName: null,
        emailMessageId: null,
        emailInReplyTo: null,
        emailReferences: null,
        emailSubject: null,
        emailFrom: null,
        emailTo: null,
        emailCc: null,
        emailBcc: null,
        emailHtml: null,
        emailPlainText: null,
        emailHeaders: null
      };

      if (this.matchesTrigger(triggerNode, resetMessage)) {
        const sessionId = await this.createSession(
          flow.id,
          conversation.id,
          contact.id,
          conversation.companyId || 0,
          triggerNode.id,
          {
            message: resetMessage,
            contact,
            conversation,
            channelConnection
          }
        );

        const session = this.activeSessions.get(sessionId);
        if (session) {
          session.variables.set('hardReset', true);
          session.variables.set('hardResetAt', new Date().toISOString());
          session.variables.set('hardResetKeyword', data.hardResetKeyword || 'reset');

          const { nodes, edges } = await this.parseFlowDefinition(flow);

          const resetContext = new FlowExecutionContext();
          resetContext.setMessageVariables(resetMessage);
          resetContext.setContactVariables(contact);
          resetContext.setVariable('hardReset', true);
          resetContext.setVariable('hardResetAt', new Date().toISOString());

          await this.executeConnectedNodesWithSession(
            session,
            triggerNode,
            nodes,
            edges,
            resetMessage,
            conversation,
            contact,
            channelConnection,
            resetContext
          );
        }
      }

      
    } catch (error) {
      console.error('Error performing hard reset:', error);
    }
  }

  /**
   * Clear all active sessions for a conversation
   */
  private async clearConversationSessions(conversationId: number): Promise<void> {
    try {
      const activeSessions = await this.getActiveSessionsForConversation(conversationId);

      for (const session of activeSessions) {
        await this.updateSession(session.sessionId, {
          status: 'completed'
        });

        this.activeSessions.delete(session.sessionId);

        try {
          
        } catch (error) {
          console.error(`Error clearing variables for session ${session.sessionId}:`, error);
        }
      }

      
    } catch (error) {
      console.error('Error clearing conversation sessions:', error);
    }
  }

  /**
   * Execute Google Sheets node with execution context
   */
  private async executeGoogleSheetsNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    _conversation: Conversation,
    _contact: Contact,
    _channelConnection: ChannelConnection
  ): Promise<void> {
    try {
      context.updateCurrentTimeVariables();

      const data = node.data || {};
      const operation = data.operation || 'append_row';

      const serviceAccountJson = context.replaceVariables(data.serviceAccountJson || '');
      const spreadsheetId = context.replaceVariables(data.spreadsheetId || '');
      const sheetName = context.replaceVariables(data.sheetName || 'Sheet1');

      if (!serviceAccountJson || !spreadsheetId) {
        throw new Error('Google Sheets configuration missing: serviceAccountJson and spreadsheetId required');
      }

      const config = {
        serviceAccountJson,
        spreadsheetId,
        sheetName
      };

      let result;

      switch (operation) {
        case 'append_row':
          const columnMappings: Record<string, any> = {};
          if (data.config && data.config.columnMappings) {
            Object.entries(data.config.columnMappings).forEach(([columnName, value]) => {
              if (columnName && value !== undefined) {
                columnMappings[columnName] = context.replaceVariables(value as string);
              }
            });
          }

          const appendOptions: any = { columnMappings };
          if (data.config?.duplicateCheck?.enabled) {
            appendOptions.duplicateCheck = {
              enabled: true,
              columns: data.config.duplicateCheck.columns || [],
              caseSensitive: data.config.duplicateCheck.caseSensitive !== false,
              onDuplicate: data.config.duplicateCheck.onDuplicate || 'skip'
            };
          }

          result = await googleSheetsService.appendRow(config, appendOptions);
          break;

        case 'read_rows':
          const readOptions: any = {};
          if (data.config?.filterColumn) {
            readOptions.filterColumn = context.replaceVariables(data.config.filterColumn);
          }
          if (data.config?.filterValue !== undefined) {
            readOptions.filterValue = context.replaceVariables(data.config.filterValue);
          }
          if (data.config?.startRow) {
            readOptions.startRow = parseInt(context.replaceVariables(data.config.startRow.toString()));
          }
          if (data.config?.maxRows) {
            readOptions.maxRows = parseInt(context.replaceVariables(data.config.maxRows.toString()));
          }
          result = await googleSheetsService.readRows(config, readOptions);
          break;

        case 'update_row':
          const matchColumn = context.replaceVariables(data.config?.matchColumn || '');
          const matchValue = context.replaceVariables(data.config?.matchValue || '');

          if (!matchColumn || matchValue === undefined) {
            throw new Error('Match column and match value are required for update_row operation');
          }

          const updateMappings: Record<string, any> = {};
          if (data.config && data.config.columnMappings) {
            Object.entries(data.config.columnMappings).forEach(([columnName, value]) => {
              if (columnName && value !== undefined) {
                updateMappings[columnName] = context.replaceVariables(value as string);
              }
            });
          }

          result = await googleSheetsService.updateRow(config, {
            matchColumn,
            matchValue,
            columnMappings: updateMappings
          });
          break;

        case 'get_sheet_info':
          result = await googleSheetsService.getSheetInfo(config);
          break;

        default:
          throw new Error(`Unsupported Google Sheets operation: ${operation}`);
      }

      context.setVariable('google_sheets.response', result);
      context.setVariable('google_sheets.success', result.success);
      context.setVariable('google_sheets.lastExecution', new Date().toISOString());

      if (result.success) {
        context.setVariable('google_sheets.data', result.data);
        if (result.rowsAffected !== undefined) {
          context.setVariable('google_sheets.rowsAffected', result.rowsAffected);
        }

        switch (operation) {
          case 'read_rows':
            if (result.data?.rows) {
              context.setVariable('google_sheets.rows', result.data.rows);
              context.setVariable('google_sheets.totalRows', result.data.totalRows);
            }
            break;

          case 'get_sheet_info':
            if (result.data?.headers) {
              context.setVariable('google_sheets.headers', result.data.headers);
            }
            break;
        }
      } else {
        context.setVariable('google_sheets.error', result.error);
      }

      if (data.variableMappings && Array.isArray(data.variableMappings)) {
        data.variableMappings.forEach((mapping: any) => {
          if (mapping.responseField && mapping.variableName) {
            const value = this.getNestedValue(result, mapping.responseField);
            if (value !== undefined) {
              context.setVariable(mapping.variableName, value);
            }
          }
        });
      }

    } catch (error) {
      console.error('Error executing Google Sheets node with context:', error);
      context.setVariable('google_sheets.error', {
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  /**
   * Execute Documind node with execution context
   */
  private async executeDocumindNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {
      const data = node.data || {};
      const apiKey = data.apiKey || '';
      const selectedFolder = data.selectedFolder || '';
      const operation = data.operation || 'ask_question';
      const question = context.getVariable('message.content') || '';

      if (!apiKey) {
        const errorMessage = 'Error: Documind node is not configured with an API key.';
        context.setVariable('documind.error', errorMessage);

        // Send error message to user
        await this.sendMessageThroughChannel(
          channelConnection,
          contact,
          errorMessage,
          conversation,
          true
        );
        return;
      }

      // Debug API key format
      console.log('=== API KEY DEBUG ===');
      console.log('API Key length:', apiKey.length);
      console.log('API Key starts with:', apiKey.substring(0, 20) + '...');
      console.log('API Key format check:');
      console.log('- Is JWT-like (starts with eyJ):', apiKey.startsWith('eyJ'));
      console.log('- Contains dots (JWT structure):', apiKey.includes('.'));
      console.log('=== END API KEY DEBUG ===');

      // Test API key by calling get-folders endpoint
      console.log('=== TESTING API KEY ===');
      try {
        const testFormData = new FormData();
        testFormData.append('secretkey', apiKey);

        const testResponse = await fetch('https://documind.onrender.com/api-get-folders', {
          method: 'POST',
          body: testFormData
        });

        console.log('API Key test response status:', testResponse.status);
        if (testResponse.ok) {
          const testResult = await testResponse.json();
          console.log('API Key test result:', JSON.stringify(testResult, null, 2));
          console.log('✅ API Key appears to be valid');
        } else {
          console.log('❌ API Key test failed - response not ok');
          const errorText = await testResponse.text();
          console.log('Error response:', errorText);
        }
      } catch (testError) {
        console.log('❌ API Key test error:', testError);
      }
      console.log('=== END API KEY TEST ===');

      if (!selectedFolder) {
        const errorMessage = 'Error: No folder selected for Documind analysis.';
        context.setVariable('documind.error', errorMessage);

        // Send error message to user
        await this.sendMessageThroughChannel(
          channelConnection,
          contact,
          errorMessage,
          conversation,
          true
        );
        return;
      }

      if (!question.trim()) {
        const errorMessage = 'Error: No question provided for document analysis.';
        context.setVariable('documind.error', errorMessage);

        // Send error message to user
        await this.sendMessageThroughChannel(
          channelConnection,
          contact,
          errorMessage,
          conversation,
          true
        );
        return;
      }

      // First, let's check what documents are in the folder
      console.log('=== CHECKING FOLDER CONTENTS ===');
      let documents: any[] = []; // Store documents in higher scope for error handling
      try {
        const checkFormData = new FormData();
        checkFormData.append('secretkey', apiKey);
        checkFormData.append('folder_id', selectedFolder);

        const checkResponse = await fetch('https://documind.onrender.com/api-get-documents', {
          method: 'POST',
          body: checkFormData
        });

        if (checkResponse.ok) {
          const checkResult = await checkResponse.json();
          console.log('Folder contents:', JSON.stringify(checkResult, null, 2));

          if (checkResult.status === 200 && checkResult.data && checkResult.data.list) {
            documents = checkResult.data.list; // Assign to higher scope variable
            console.log(`Found ${documents.length} documents in folder:`);
            let processingDocuments = 0;
            let readyDocuments = 0;

            documents.forEach((doc: any, index: number) => {
              console.log(`  ${index + 1}. ${doc.file_name} (ID: ${doc.id})`);
              console.log(`     Status: ${doc.status_message} (Code: ${doc.status})`);
              console.log(`     Chunks: ${doc.chunks_count}`);
              console.log(`     Tokens: ${doc.tokens}`);

              // Debug the chunks and extracted_text fields
              console.log(`     🔍 CHUNK DEBUG:`);
              console.log(`        - chunks array length: ${doc.chunks ? doc.chunks.length : 'null/undefined'}`);
              console.log(`        - extracted_text length: ${doc.extracted_text ? doc.extracted_text.length : 'null/undefined/empty'}`);
              console.log(`        - chunks_count vs actual chunks: ${doc.chunks_count} vs ${doc.chunks ? doc.chunks.length : 0}`);

              if (doc.chunks && doc.chunks.length > 0) {
                console.log(`        - First chunk preview: ${JSON.stringify(doc.chunks[0]).substring(0, 100)}...`);
              }

              if (doc.extracted_text && doc.extracted_text.length > 0) {
                console.log(`        - Extracted text preview: ${doc.extracted_text.substring(0, 100)}...`);
              }

              // Check if document is still processing
              // Based on API docs: status 200 = "PDF Processed", but we've seen working docs with status 102
              // Let's be more flexible and also check if the document can actually be queried
              const isProcessing = (
                doc.status < 100 || // Definitely not processed yet
                (doc.status !== 200 && doc.chunks_count === 0 && doc.tokens <= 1) || // Likely still processing
                doc.status_message?.toLowerCase().includes('uploading') ||
                doc.status_message?.toLowerCase().includes('processing') ||
                doc.status_message?.toLowerCase().includes('extracting')
              );

              if (isProcessing) {
                processingDocuments++;
                console.log(`     ⚠️ Document still processing (Status: ${doc.status}, Chunks: ${doc.chunks_count}, Tokens: ${doc.tokens})`);
              } else {
                readyDocuments++;
                console.log(`     ✅ Document ready for queries (Status: ${doc.status}, Chunks: ${doc.chunks_count}, Tokens: ${doc.tokens})`);
              }
            });

            if (documents.length === 0) {
              console.log('❌ FOLDER IS EMPTY - No documents found in the selected folder');
              const errorMessage = 'The selected folder is empty. Please upload documents to this folder first.';
              context.setVariable('documind.error', errorMessage);

              await this.sendMessageThroughChannel(
                channelConnection,
                contact,
                errorMessage,
                conversation,
                true
              );
              return;
            }

            // Instead of blocking based on status, let's try the query anyway
            // If it fails due to processing, we'll handle it in the API response
            if (processingDocuments > 0 && readyDocuments === 0) {
              console.log('⚠️ ALL DOCUMENTS APPEAR TO BE PROCESSING - BUT TRYING QUERY ANYWAY');
              console.log('   (Sometimes documents work even when status suggests they are processing)');
            }

            // Store document info for later use in "No results found" handling
            context.setVariable('documind.folderDocuments', documents);
            context.setVariable('documind.processingCount', processingDocuments);
            context.setVariable('documind.readyCount', readyDocuments);
          }
        } else {
          console.log('Failed to check folder contents:', checkResponse.status);
        }
      } catch (checkError) {
        console.log('Error checking folder contents:', checkError);
      }
      console.log('=== END FOLDER CHECK ===');

      // Prepare the API request
      console.log('=== PREPARING DOCUMIND REQUEST ===');
      console.log('Question:', question);
      console.log('Folder ID:', selectedFolder);
      console.log('API Key (first 10 chars):', apiKey.substring(0, 10) + '...');

      const formData = new FormData();
      formData.append('secretkey', apiKey);
      formData.append('question', question);

      // Try both folder_id and document_id approaches for better compatibility
      formData.append('folder_id', selectedFolder);

      // Also try with specific document_id if we have documents
      if (documents.length > 0) {
        console.log('Also trying with document_id:', documents[0].id);
        // Note: We'll try folder first, but this gives us debugging info
      }

      console.log('=== SENDING REQUEST TO DOCUMIND ===');
      console.log('Request details:');
      console.log('- API Key (first 10 chars):', apiKey.substring(0, 10) + '...');
      console.log('- Question:', question);
      console.log('- Folder ID:', selectedFolder);

      // Debug FormData contents
      console.log('FormData contents:');
      console.log('- secretkey: [REDACTED]');
      console.log('- question:', formData.get('question'));
      console.log('- folder_id:', formData.get('folder_id'));

      try {
        const response = await fetch('https://documind.onrender.com/api-ask-from-collection', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`Documind API error: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        // Debug logging - log the full response
        console.log('=== DOCUMIND API RESPONSE DEBUG ===');
        console.log('Full API Response:', JSON.stringify(result, null, 2));
        console.log('Response Status:', result.status);
        console.log('Response Data:', result.data);
        if (result.data) {
          console.log('Answer:', result.data.answer);
          console.log('Documents returned:', result.data.documents?.length || 0);
        }
        console.log('=== END DEBUG ===');

        // Check for successful response according to API docs
        if (result.status === 200 && result.data && result.data.answer) {
          const answer = result.data.answer;

          // Check if we got "No results found" but there are documents in the folder
          if (answer === "No results found" && result.data.documents && result.data.documents.length === 0) {
            console.log('⚠️ Got "No results found" but folder has documents. This might be due to:');
            console.log('   - Documents still processing');
            console.log('   - Question too generic or not related to document content');
            console.log('   - Language mismatch between question and document');
            console.log('   - Document content not matching the query');

            // Get document info from context
            const folderDocuments = context.getVariable('documind.folderDocuments') || [];
            const processingCount = context.getVariable('documind.processingCount') || 0;
            const readyCount = context.getVariable('documind.readyCount') || 0;
            const firstDoc = folderDocuments[0];

            let helpfulResponse = '';

            // Check if some documents are still processing
            if (processingCount > 0) {
              helpfulResponse = `I found ${folderDocuments.length} document(s) in your folder, but ${processingCount} ${processingCount === 1 ? 'is' : 'are'} still being processed.`;

              if (readyCount > 0) {
                helpfulResponse += ` ${readyCount} document(s) are ready but didn't contain relevant content for your question "${question}".`;
              }

              helpfulResponse += '\n\nDocument status:';
              folderDocuments.forEach((doc: any) => {
                const status = doc.status === 200 && doc.chunks_count > 0 ? '✅ Ready' : '⏳ Processing';
                helpfulResponse += `\n• ${doc.file_name} - ${status} (${doc.status_message})`;
              });

              if (processingCount > 0) {
                helpfulResponse += '\n\nPlease wait a few minutes for processing to complete, then try your question again.';
              }
            } else {
              // All documents are ready but no results found - try direct document query
              console.log('🔄 TRYING DIRECT DOCUMENT QUERY AS FALLBACK...');

              if (firstDoc && firstDoc.id) {
                try {
                  const docFormData = new FormData();
                  docFormData.append('secretkey', apiKey);
                  docFormData.append('question', question);
                  docFormData.append('document_id', firstDoc.id);

                  console.log('Trying direct document query with ID:', firstDoc.id);

                  // Also try a query without any folder/document restrictions
                  console.log('🔄 ALSO TRYING GLOBAL QUERY (no folder/document restrictions)...');
                  const globalFormData = new FormData();
                  globalFormData.append('secretkey', apiKey);
                  globalFormData.append('question', question);

                  const globalResponse = await fetch('https://documind.onrender.com/api-ask-from-collection', {
                    method: 'POST',
                    body: globalFormData
                  });

                  if (globalResponse.ok) {
                    const globalResult = await globalResponse.json();
                    console.log('🌍 Global query result:', JSON.stringify(globalResult, null, 2));

                    if (globalResult.status === 200 && globalResult.data && globalResult.data.answer && globalResult.data.answer !== 'No results found') {
                      console.log('✅ Global query succeeded!');
                      const globalAnswer = globalResult.data.answer;

                      await this.sendMessageThroughChannel(
                        channelConnection,
                        contact,
                        globalAnswer,
                        conversation,
                        true
                      );

                      // Set context variables
                      context.setVariable('documind.answer', globalAnswer);
                      context.setVariable('documind.response', globalAnswer);
                      context.setVariable('documind.question', question);
                      context.setVariable('documind.operation', operation);
                      context.setVariable('documind.folder', selectedFolder);
                      context.setVariable('documind.lastExecution', new Date().toISOString());

                      return;
                    }
                  }

                  const docResponse = await fetch('https://documind.onrender.com/api-ask-from-collection', {
                    method: 'POST',
                    body: docFormData
                  });

                  if (docResponse.ok) {
                    const docResult = await docResponse.json();
                    console.log('📄 Direct document query result:', JSON.stringify(docResult, null, 2));

                    if (docResult.status === 200 && docResult.data && docResult.data.answer && docResult.data.answer !== 'No results found') {
                      console.log('✅ Direct document query succeeded!');
                      const directAnswer = docResult.data.answer;

                      await this.sendMessageThroughChannel(
                        channelConnection,
                        contact,
                        directAnswer,
                        conversation,
                        true
                      );

                      // Set context variables
                      context.setVariable('documind.answer', directAnswer);
                      context.setVariable('documind.response', directAnswer);
                      context.setVariable('documind.question', question);
                      context.setVariable('documind.operation', operation);
                      context.setVariable('documind.folder', selectedFolder);
                      context.setVariable('documind.lastExecution', new Date().toISOString());

                      return;
                    }
                  }
                } catch (docError) {
                  console.log('❌ Direct document query failed:', docError);
                }

                // If all queries failed, try with a suggested question to test if the document is actually queryable
                if (firstDoc.suggestions && firstDoc.suggestions.length > 0) {
                  console.log('🔄 TESTING WITH SUGGESTED QUESTION...');
                  const testQuestion = firstDoc.suggestions[0];
                  console.log('Test question:', testQuestion);

                  try {
                    const testFormData = new FormData();
                    testFormData.append('secretkey', apiKey);
                    testFormData.append('question', testQuestion);

                    const testResponse = await fetch('https://documind.onrender.com/api-ask-from-collection', {
                      method: 'POST',
                      body: testFormData
                    });

                    if (testResponse.ok) {
                      const testResult = await testResponse.json();
                      console.log('🧪 Suggested question test result:', JSON.stringify(testResult, null, 2));

                      if (testResult.status === 200 && testResult.data && testResult.data.answer && testResult.data.answer !== 'No results found') {
                        console.log('✅ Suggested question worked! Document is queryable.');
                        console.log('❌ Issue is likely with the user\'s question being too generic or not matching document content.');
                      } else {
                        console.log('❌ Even suggested questions don\'t work. There might be a deeper API issue.');
                      }
                    }
                  } catch (testError) {
                    console.log('❌ Suggested question test failed:', testError);
                  }

                  // Try alternative parameter formats as a last resort
                  console.log('🔄 TRYING ALTERNATIVE API PARAMETER FORMATS...');

                  try {
                    // Try with 'query' instead of 'question'
                    const altFormData1 = new FormData();
                    altFormData1.append('secretkey', apiKey);
                    altFormData1.append('query', testQuestion);

                    const altResponse1 = await fetch('https://documind.onrender.com/api-ask-from-collection', {
                      method: 'POST',
                      body: altFormData1
                    });

                    if (altResponse1.ok) {
                      const altResult1 = await altResponse1.json();
                      console.log('🔄 Alternative format 1 (query param):', JSON.stringify(altResult1, null, 2));
                    }

                    // Try with 'collection_id' instead of 'folder_id'
                    const altFormData2 = new FormData();
                    altFormData2.append('secretkey', apiKey);
                    altFormData2.append('question', testQuestion);
                    altFormData2.append('collection_id', selectedFolder);

                    const altResponse2 = await fetch('https://documind.onrender.com/api-ask-from-collection', {
                      method: 'POST',
                      body: altFormData2
                    });

                    if (altResponse2.ok) {
                      const altResult2 = await altResponse2.json();
                      console.log('🔄 Alternative format 2 (collection_id):', JSON.stringify(altResult2, null, 2));
                    }
                  } catch (altError) {
                    console.log('❌ Alternative parameter testing failed:', altError);
                  }
                }
              }

              // If direct document query also failed, show helpful message
              helpfulResponse = `I found ${folderDocuments.length} document(s) in your folder but couldn't find relevant content for your question "${question}".`;

              if (firstDoc) {
                helpfulResponse += `\n\nDocument found: "${firstDoc.file_name}"`;

                // Include suggestions if available
                if (firstDoc.suggestions && firstDoc.suggestions.length > 0) {
                  helpfulResponse += '\n\nTry asking questions like:';
                  firstDoc.suggestions.forEach((suggestion: string) => {
                    helpfulResponse += `\n• ${suggestion}`;
                  });
                } else {
                  helpfulResponse += '\n\nTry asking more specific questions about the document content, or general questions like:';
                  helpfulResponse += '\n• "What is this document about?"';
                  helpfulResponse += '\n• "Summarize the main content"';
                  helpfulResponse += '\n• "What are the key points?"';
                }
              }
            }

            await this.sendMessageThroughChannel(
              channelConnection,
              contact,
              helpfulResponse,
              conversation,
              true
            );

            // Set context variables
            context.setVariable('documind.response', helpfulResponse);
            context.setVariable('documind.question', question);
            context.setVariable('documind.operation', operation);
            context.setVariable('documind.folder', selectedFolder);
            context.setVariable('documind.lastExecution', new Date().toISOString());

            return;
          }

          // console.log('✅ Successfully extracted answer:', answer);

          // Set context variables
          context.setVariable('documind.response', answer);
          context.setVariable('documind.question', question);
          context.setVariable('documind.operation', operation);
          context.setVariable('documind.folder', selectedFolder);
          context.setVariable('documind.lastExecution', new Date().toISOString());

          // Send the response to the user
          await this.sendMessageThroughChannel(
            channelConnection,
            contact,
            answer,
            conversation,
            true
          );

        } else {
          // Enhanced error handling
          let errorMessage = 'Failed to get response from Documind API';

          if (result.status !== 200) {
            errorMessage = `API returned status ${result.status}`;
          } else if (!result.data) {
            errorMessage = 'API response missing data field';
          } else if (!result.data.answer) {
            errorMessage = 'API response missing answer field';
          }

          // Check for specific error messages in the response
          if (result.error) {
            errorMessage = result.error;
          } else if (result.message) {
            errorMessage = result.message;
          } else if (result.data && typeof result.data === 'string') {
            errorMessage = result.data;
          }

          // Check if this might be a processing issue
          const isProcessingError = (
            errorMessage.toLowerCase().includes('processing') ||
            errorMessage.toLowerCase().includes('extracting') ||
            errorMessage.toLowerCase().includes('not ready') ||
            result.status === 102
          );

          if (isProcessingError) {
            console.log('❌ DOCUMENTS STILL PROCESSING - API CONFIRMED');
            const processingMessage = `Your document${documents.length > 1 ? 's are' : ' is'} still being processed by Documind. Please wait a few minutes and try again.

Document${documents.length > 1 ? 's' : ''} found:
${documents.map((doc: any) => `• ${doc.file_name} - Status: ${doc.status_message}`).join('\n')}

Processing usually takes 1-3 minutes depending on document size.`;

            context.setVariable('documind.error', processingMessage);

            await this.sendMessageThroughChannel(
              channelConnection,
              contact,
              processingMessage,
              conversation,
              true
            );
          } else {
            console.log('❌ Documind API Error:', errorMessage);
            context.setVariable('documind.error', errorMessage);

            await this.sendMessageThroughChannel(
              channelConnection,
              contact,
              `Error: ${errorMessage}`,
              conversation,
              true
            );
          }
        }

      } catch (apiError) {
        console.error('=== DOCUMIND API REQUEST ERROR ===');
        console.error('Error details:', apiError);
        console.error('API Key (first 10 chars):', apiKey.substring(0, 10) + '...');
        console.error('Question:', question);
        console.error('Folder ID:', selectedFolder);
        console.error('=== END ERROR DEBUG ===');

        const errorMessage = `Documind API request failed: ${apiError instanceof Error ? apiError.message : 'Unknown error'}`;
        context.setVariable('documind.error', errorMessage);

        await this.sendMessageThroughChannel(
          channelConnection,
          contact,
          errorMessage,
          conversation,
          true
        );
      }

    } catch (error) {
      console.error('Error executing Documind node with context:', error);
      const errorMessage = `Documind execution error: ${error instanceof Error ? error.message : 'Unknown error'}`;
      context.setVariable('documind.error', errorMessage);

      try {
        await this.sendMessageThroughChannel(
          channelConnection,
          contact,
          errorMessage,
          conversation,
          true
        );
      } catch (sendError) {
        console.error('Failed to send error message:', sendError);
      }
    }
  }

  /**
   * Execute Google Calendar node with execution context
   */
  private async executeGoogleCalendarNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {
      const data = node.data || {};
      const calendarAction = data.action || data.calendarAction || 'create_event';

      const tempMessage: Message = {
        id: 0,
        conversationId: conversation.id,
        externalId: null,
        direction: 'inbound',
        type: 'text',
        content: context.getVariable('message.content') || '',
        metadata: null,
        senderId: contact.id,
        senderType: 'contact',
        status: 'received',
        sentAt: new Date(),
        readAt: null,
        isFromBot: false,
        mediaUrl: null,
        createdAt: new Date(),
        groupParticipantJid: null,
        groupParticipantName: null,
        emailMessageId: null,
        emailInReplyTo: null,
        emailReferences: null,
        emailSubject: null,
        emailFrom: null,
        emailTo: null,
        emailCc: null,
        emailBcc: null,
        emailHtml: null,
        emailPlainText: null,
        emailHeaders: null
      };

      switch (calendarAction) {
        case 'create_event':
          await this.executeGoogleCalendarEventNode(node, tempMessage, conversation, contact, channelConnection);
          context.setVariable('calendar.action', 'create_event');
          context.setVariable('calendar.lastAction', 'create_event');
          break;

        case 'check_availability':
          await this.executeGoogleCalendarAvailabilityNode(node, tempMessage, conversation, contact, channelConnection);
          context.setVariable('calendar.action', 'check_availability');
          context.setVariable('calendar.lastAction', 'check_availability');
          break;

        default:
          
          context.setVariable('calendar.error', `Unknown action: ${calendarAction}`);
      }

      context.setVariable('calendar.lastExecution', new Date().toISOString());

    } catch (error) {
      context.setVariable('calendar.error', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Execute Update Pipeline Stage node with execution context
   */
  private async executeUpdatePipelineStageNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {
      const tempMessage: Message = {
        id: 0,
        conversationId: conversation.id,
        externalId: null,
        direction: 'inbound',
        type: 'text',
        content: context.getVariable('message.content') || '',
        metadata: null,
        senderId: contact.id,
        senderType: 'contact',
        status: 'received',
        sentAt: new Date(),
        readAt: null,
        isFromBot: false,
        mediaUrl: null,
        createdAt: new Date(),
        groupParticipantJid: null,
        groupParticipantName: null,
        emailMessageId: null,
        emailInReplyTo: null,
        emailReferences: null,
        emailSubject: null,
        emailFrom: null,
        emailTo: null,
        emailCc: null,
        emailBcc: null,
        emailHtml: null,
        emailPlainText: null,
        emailHeaders: null
      };

      await this.executeUpdatePipelineStageNode(node, tempMessage, conversation, contact, channelConnection);

      const data = node.data || {};
      const stageId = data.stageId;

      context.setVariable('pipeline.lastStageId', stageId);
      context.setVariable('pipeline.lastExecution', new Date().toISOString());
      context.setVariable('pipeline.action', 'update_stage');

    } catch (error) {
      console.error('Error executing Update Pipeline Stage node with context:', error);
      context.setVariable('pipeline.error', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Execute Translation node with execution context
   */
  private async executeTranslationNodeWithContext(
    node: any,
    context: FlowExecutionContext,
    conversation: Conversation,
    contact: Contact,
    channelConnection: ChannelConnection
  ): Promise<void> {
    try {
      const data = node.data || {};
      const message = context.getVariable('message') as Message;

      if (!message || !message.content) {
        console.log('Translation Node: No message content to translate');
        return;
      }


      if (!data.enabled) {
        console.log('Translation Node: Translation is disabled');
        return;
      }

      const apiKey = data.apiKey || process.env.OPENAI_API_KEY || '';
      const targetLanguage = data.targetLanguage || 'en';
      const translationMode = data.translationMode || 'separate';
      const detectLanguage = data.detectLanguage !== undefined ? data.detectLanguage : true;


      if (!apiKey) {
        console.error('Translation Node: No OpenAI API key provided');
        context.setVariable('translation.error', 'No API key provided');
        return;
      }


      const aiAssistantService = (await import('./ai-assistant')).default;
      const translationService = (aiAssistantService as any).translationService;

      if (!translationService) {
        console.error('Translation Node: Translation service not available');
        return;
      }


      const translationResult = await translationService.processTranslation(
        message.content,
        targetLanguage,
        'openai',
        apiKey
      );

      if (!translationResult.needsTranslation) {
        console.log('Translation Node: No translation needed - message is already in target language');
        context.setVariable('translation.skipped', true);
        context.setVariable('translation.reason', 'already_target_language');
        return;
      }

      if (!translationResult.translatedText) {
        console.log('Translation Node: Translation failed or returned empty result');
        context.setVariable('translation.error', 'Translation failed');
        return;
      }


      context.setVariable('translation.originalText', message.content);
      context.setVariable('translation.translatedText', translationResult.translatedText);
      context.setVariable('translation.detectedLanguage', translationResult.detectedLanguage);
      context.setVariable('translation.targetLanguage', targetLanguage);
      context.setVariable('translation.mode', translationMode);
      context.setVariable('translation.lastExecution', new Date().toISOString());


      if (translationMode === 'replace') {

        const updatedMessage = { ...message, content: translationResult.translatedText };
        context.setVariable('message', updatedMessage);

        console.log(`Translation Node: Replaced message content with translation (${translationResult.detectedLanguage} → ${targetLanguage})`);
      } else if (translationMode === 'append') {

        const appendedContent = `${message.content}\n\n🌐 Translation: ${translationResult.translatedText}`;
        const updatedMessage = { ...message, content: appendedContent };
        context.setVariable('message', updatedMessage);

        console.log(`Translation Node: Appended translation to message (${translationResult.detectedLanguage} → ${targetLanguage})`);
      } else if (translationMode === 'separate') {

        const translationText = `🌐 Translation: ${translationResult.translatedText}`;

        try {
          if ((channelConnection.channelType === 'whatsapp' || channelConnection.channelType === 'whatsapp_unofficial') && contact.identifier) {
            const whatsAppService = (await import('./channels/whatsapp')).default;

            if (channelConnection.channelType === 'whatsapp_unofficial') {
              await whatsAppService.sendMessage(
                channelConnection.id,
                channelConnection.userId,
                contact.identifier,
                translationText,
                true,
                conversation.id
              );
            } else {
              await whatsAppService.sendWhatsAppMessage(
                channelConnection.id,
                channelConnection.userId,
                contact.identifier,
                translationText,
                true,
                conversation.id
              );
            }
          } else {

            const insertTranslationMessage = {
              conversationId: conversation.id,
              contactId: contact.id,
              channelType: channelConnection.channelType,
              type: 'text',
              content: translationText,
              direction: 'outbound',
              status: 'sent',
              isFromBot: true,
              mediaUrl: null,
              timestamp: new Date(),
              metadata: JSON.stringify({
                isTranslation: true,
                originalText: message.content,
                detectedLanguage: translationResult.detectedLanguage,
                targetLanguage: targetLanguage,
                nodeId: node.id
              })
            };

            await storage.createMessage(insertTranslationMessage);
          }

          console.log(`Translation Node: Sent separate translation message (${translationResult.detectedLanguage} → ${targetLanguage})`);
        } catch (sendError) {
          console.error('Translation Node: Error sending translation message:', sendError);
          context.setVariable('translation.sendError', sendError instanceof Error ? sendError.message : 'Unknown error');
        }
      }

    } catch (error) {
      console.error('Error executing Translation node with context:', error);
      context.setVariable('translation.error', error instanceof Error ? error.message : 'Unknown error');
    }
  }
}

const flowExecutor = new FlowExecutor();
export default flowExecutor;
