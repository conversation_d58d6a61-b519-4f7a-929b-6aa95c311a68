import {
  makeWASocket,
  DisconnectReason,
  useMultiFileAuthState,
  makeCacheableSignalKeyStore,
  Browsers,
  WASocket,
  WAMessage,
  downloadMediaMessage
} from 'baileys';
import { Boom } from '@hapi/boom';
import path from 'path';
import fs from 'fs';
import * as fsPromises from 'fs/promises';
import fsExtra from 'fs-extra';
import axios from 'axios';
import { storage } from '../../storage';
import {
  InsertMessage,
  InsertConversation,
  InsertContact,
  Message,
  PERMISSIONS
} from '@shared/schema';
import pino from 'pino';
import { EventEmitter } from 'events';
import crypto from 'crypto';
import { logger } from '../../utils/logger';
import { getUserPermissions } from '../../middleware';
import { eventEmitterMonitor } from '../../utils/event-emitter-monitor';
import {
  convertAudioForWhatsAppWithFallback,
  needsConversionForWhatsApp,
  getWhatsAppMimeType,
  cleanupTempAudioFiles
} from '../../utils/audio-converter';

interface ConnectionState {
  socket: WASocket | null;
  status: 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error' | 'qr_code';
  lastConnected: Date | null;
  reconnectAttempts: number;
  lastReconnectAttempt: Date | null;
  healthScore: number;
  errorCount: number;
  lastError: string | null;
  rateLimitInfo: {
    messagesSent: number;
    lastReset: Date;
    isLimited: boolean;
  };
}

interface ReconnectionConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  healthCheckInterval: number;
  rateLimitWindow: number;
  maxMessagesPerWindow: number;
}

const activeConnections = new Map<number, WASocket>();
const connectionStates = new Map<number, ConnectionState>();

const RECONNECTION_CONFIG: ReconnectionConfig = {
  maxAttempts: 10,
  baseDelay: 2000,
  maxDelay: 300000,
  backoffMultiplier: 1.5,
  healthCheckInterval: 30000,
  rateLimitWindow: 60000,        // 60 seconds window
  maxMessagesPerWindow: 100      // Increased from 20 to 100 messages per minute
};

interface TypingConfig {
  enabled: boolean;
  wordsPerMinute: number;
  minDelay: number;
  maxDelay: number;
  randomnessFactor: number;
  recordingMinDelay: number;
  recordingMaxDelay: number;
}

interface MessageSplittingConfig {
  enabled: boolean;
  maxLength: number;
  splitMethod: 'sentences' | 'paragraphs' | 'characters';
  delayBetweenMessages: number;
  randomDelayFactor: number;
  preserveFormatting: boolean;
  minChunkSize: number;
  smartBoundaries: boolean;
  prioritizeSentences: boolean;
}

interface MessageDebouncingConfig {
  enabled: boolean;
  debounceDelay: number;
  maxDebounceDelay: number;
}

interface DebouncedMessage {
  message: any;
  conversation: any;
  contact: any;
  channelConnection: any;
  timeoutId: NodeJS.Timeout;
  createdAt: Date;
}

const TYPING_CONFIG: TypingConfig = {
  enabled: true,
  wordsPerMinute: 50,
  minDelay: 1000,
  maxDelay: 5000,
  randomnessFactor: 0.6,
  recordingMinDelay: 2000,
  recordingMaxDelay: 4000,
};

const MESSAGE_SPLITTING_CONFIG: MessageSplittingConfig = {
  enabled: true,
  maxLength: 300,
  splitMethod: 'sentences',
  delayBetweenMessages: 2000,
  randomDelayFactor: 0.5,
  preserveFormatting: true,
  minChunkSize: 20,
  smartBoundaries: true,
  prioritizeSentences: true,
};

const MESSAGE_DEBOUNCING_CONFIG: MessageDebouncingConfig = {
  enabled: true,
  debounceDelay: 5000,
  maxDebounceDelay: 30000,
};

/**
 * Check if a user has permission to access a WhatsApp connection
 * Integrates with the company's permission system
 */
export async function checkConnectionPermission(
  user: any,
  connection: any,
  conversationId?: number,
  connectionId?: number
): Promise<boolean> {


  if (user.isSuperAdmin) {
    return true;
  }

  if (connection.userId === user.id) {
    return true;
  }

  let connectionCompanyId = connection.companyId;

  if (!connectionCompanyId && connection.userId) {
    try {
      const connectionOwner = await storage.getUser(connection.userId);
      connectionCompanyId = connectionOwner?.companyId;

    } catch (error) {
      console.error('Error getting connection owner:', error);
    }
  }

  if (user.companyId !== connectionCompanyId) {
    return false;
  }

  const userPermissions = await getUserPermissions(user);


  if (userPermissions[PERMISSIONS.MANAGE_CHANNELS]) {
    return true;
  }

  if (userPermissions[PERMISSIONS.MANAGE_CONVERSATIONS]) {

    const targetConnectionId = connectionId || connection.id;

    if (conversationId) {

      try {
        const conversation = await storage.getConversation(conversationId);


        if (!conversation) {

          return false;
        }

        const isAssignedToUser = conversation.assignedToUserId === user.id;
        const isCorrectChannel = conversation.channelId === targetConnectionId;



        if (conversation && isAssignedToUser && isCorrectChannel) {

          return true;
        } else {

        }
      } catch (error) {
        console.error('Error checking conversation assignment:', error);
      }
    } else {

      try {
        const { conversations: allConversations } = await storage.getConversations({
          companyId: user.companyId
        });


        const assignedConversations = allConversations.filter(
          (conv: any) => conv.assignedToUserId === user.id
        );


        const conversationsOnConnection = assignedConversations.filter(
          (conv: any) => conv.channelId === targetConnectionId
        );


        if (conversationsOnConnection.length > 0) {

          return true;
        }
      } catch (error) {
        console.error('Error checking agent conversation assignments:', error);
      }
    }
  } else {

  }



  if (user.role === 'agent') {




  }

  return false;
}

const debouncedMessages = new Map<string, DebouncedMessage>();

interface QueuedMessage {
  id: string;
  phoneNumber: string;
  connectionId: number;
  chunks: string[];
  currentChunkIndex: number;
  timeoutIds: NodeJS.Timeout[];
  createdAt: Date;
  sock: any;
  cancelled: boolean;
}

const messageQueues = new Map<string, QueuedMessage[]>();

/**
 * Generate a unique queue ID for a user's message queue
 */
function getQueueKey(phoneNumber: string, connectionId: number): string {
  return `${connectionId}_${phoneNumber}`;
}

/**
 * Cancel all queued messages for a specific user
 */
function cancelQueuedMessages(phoneNumber: string, connectionId: number): void {
  const queueKey = getQueueKey(phoneNumber, connectionId);
  const userQueue = messageQueues.get(queueKey);

  if (!userQueue || userQueue.length === 0) {

    return;
  }



  let totalTimeoutsCancelled = 0;
  let totalChunksCancelled = 0;

  for (const queuedMessage of userQueue) {
    const remainingChunks = queuedMessage.chunks.length - queuedMessage.currentChunkIndex;
    totalChunksCancelled += remainingChunks;



    queuedMessage.cancelled = true;

    for (const timeoutId of queuedMessage.timeoutIds) {
      clearTimeout(timeoutId);
      totalTimeoutsCancelled++;

    }

    queuedMessage.timeoutIds.length = 0;
  }

  messageQueues.delete(queueKey);


}

/**
 * Add a message to the queue for delayed sending
 */
function addToMessageQueue(
  phoneNumber: string,
  connectionId: number,
  chunks: string[],
  messageId: string,
  sock: any
): QueuedMessage {
  const queueKey = getQueueKey(phoneNumber, connectionId);

  if (!messageQueues.has(queueKey)) {
    messageQueues.set(queueKey, []);
  }

  const queuedMessage: QueuedMessage = {
    id: messageId,
    phoneNumber,
    connectionId,
    chunks,
    currentChunkIndex: 0,
    timeoutIds: [],
    createdAt: new Date(),
    sock,
    cancelled: false
  };

  messageQueues.get(queueKey)!.push(queuedMessage);



  return queuedMessage;
}

/**
 * Remove a specific message from the queue
 */
function removeFromMessageQueue(phoneNumber: string, connectionId: number, messageId: string): void {
  const queueKey = getQueueKey(phoneNumber, connectionId);
  const userQueue = messageQueues.get(queueKey);

  if (!userQueue) {
    return;
  }

  const messageIndex = userQueue.findIndex(msg => msg.id === messageId);
  if (messageIndex !== -1) {
    const queuedMessage = userQueue[messageIndex];
    for (const timeoutId of queuedMessage.timeoutIds) {
      clearTimeout(timeoutId);
    }
    userQueue.splice(messageIndex, 1);

    if (userQueue.length === 0) {
      messageQueues.delete(queueKey);
    }


  }
}

const healthCheckIntervals = new Map<number, NodeJS.Timeout>();
const reconnectionTimeouts = new Map<number, NodeJS.Timeout>();
const connectionAttempts = new Map<number, boolean>(); // Track ongoing connection attempts

/**
 * Get a WhatsApp connection by ID
 * @param connectionId The ID of the connection
 * @returns The WhatsApp socket or undefined if not found
 */
export function getConnection(connectionId: number): WASocket | undefined {
  return activeConnections.get(connectionId);
}

/**
 * Initialize connection state for enterprise-grade management
 */
function initializeConnectionState(connectionId: number): ConnectionState {
  const state: ConnectionState = {
    socket: null,
    status: 'disconnected',
    lastConnected: null,
    reconnectAttempts: 0,
    lastReconnectAttempt: null,
    healthScore: 100,
    errorCount: 0,
    lastError: null,
    rateLimitInfo: {
      messagesSent: 0,
      lastReset: new Date(),
      isLimited: false
    }
  };

  connectionStates.set(connectionId, state);
  return state;
}

/**
 * Get or create connection state
 */
function getConnectionState(connectionId: number): ConnectionState {
  let state = connectionStates.get(connectionId);
  if (!state) {
    state = initializeConnectionState(connectionId);
  }
  return state;
}

/**
 * Calculate exponential backoff delay
 */
function calculateBackoffDelay(attempt: number): number {
  const delay = RECONNECTION_CONFIG.baseDelay * Math.pow(RECONNECTION_CONFIG.backoffMultiplier, attempt - 1);
  return Math.min(delay, RECONNECTION_CONFIG.maxDelay);
}

/**
 * Update connection health score based on events
 */
function updateHealthScore(connectionId: number, event: 'success' | 'error' | 'timeout'): void {
  const state = getConnectionState(connectionId);

  switch (event) {
    case 'success':
      state.healthScore = Math.min(100, state.healthScore + 10);
      state.errorCount = Math.max(0, state.errorCount - 1);
      break;
    case 'error':
      state.healthScore = Math.max(0, state.healthScore - 20);
      state.errorCount++;
      break;
    case 'timeout':
      state.healthScore = Math.max(0, state.healthScore - 15);
      state.errorCount++;
      break;
  }


}

/**
 * Check if connection should attempt reconnection based on health and limits
 */
function shouldAttemptReconnection(connectionId: number): boolean {
  const state = getConnectionState(connectionId);

  if (state.reconnectAttempts >= RECONNECTION_CONFIG.maxAttempts) {

    return false;
  }

  if (state.healthScore < 20) {

    return false;
  }



  return true;
}

/**
 * Start health monitoring for a connection
 */
function startHealthMonitoring(connectionId: number): void {
  const existingInterval = healthCheckIntervals.get(connectionId);
  if (existingInterval) {
    clearInterval(existingInterval);
  }

  const interval = setInterval(async () => {
    const sock = activeConnections.get(connectionId);
    const state = getConnectionState(connectionId);

    if (!sock || state.status !== 'connected') {
      return;
    }

    try {
      const isHealthy = sock.user?.id && sock.authState?.keys;

      if (isHealthy) {
        updateHealthScore(connectionId, 'success');

        eventEmitter.emit('connectionHealth', {
          connectionId,
          healthScore: state.healthScore,
          status: 'healthy'
        });
      } else {
        updateHealthScore(connectionId, 'error');


        if (state.healthScore < 30) {

          await scheduleReconnection(connectionId, await storage.getChannelConnection(connectionId));
        }
      }
    } catch (error) {
      updateHealthScore(connectionId, 'error');

    }
  }, RECONNECTION_CONFIG.healthCheckInterval);

  healthCheckIntervals.set(connectionId, interval);
}

/**
 * Stop health monitoring for a connection
 */
function stopHealthMonitoring(connectionId: number): void {
  const interval = healthCheckIntervals.get(connectionId);
  if (interval) {
    clearInterval(interval);
    healthCheckIntervals.delete(connectionId);
  }
}

/**
 * Schedule intelligent reconnection with exponential backoff
 */
async function scheduleReconnection(connectionId: number, connection: any): Promise<void> {
  if (!connection) {

    return;
  }

  const state = getConnectionState(connectionId);

  if (!shouldAttemptReconnection(connectionId)) {

    await updateConnectionStatus(connectionId, 'error');
    return;
  }

  const existingTimeout = reconnectionTimeouts.get(connectionId);
  if (existingTimeout) {
    clearTimeout(existingTimeout);
  }

  state.reconnectAttempts++;
  state.lastReconnectAttempt = new Date();
  state.status = 'reconnecting';

  const delay = calculateBackoffDelay(state.reconnectAttempts);



  await updateConnectionStatus(connectionId, 'reconnecting');

  eventEmitter.emit('connectionReconnecting', {
    connectionId,
    attempt: state.reconnectAttempts,
    maxAttempts: RECONNECTION_CONFIG.maxAttempts,
    nextAttemptIn: delay,
    healthScore: state.healthScore
  });

  const timeout = setTimeout(async () => {
    try {


      await cleanupConnection(connectionId);

      await connectToWhatsApp(connectionId, connection.userId);

      state.reconnectAttempts = 0;
      state.lastConnected = new Date();
      updateHealthScore(connectionId, 'success');



    } catch (error) {


      state.lastError = error instanceof Error ? error.message : 'Unknown error';
      updateHealthScore(connectionId, 'error');

      if (state.reconnectAttempts < RECONNECTION_CONFIG.maxAttempts) {
        await scheduleReconnection(connectionId, connection);
      } else {

        await updateConnectionStatus(connectionId, 'error');

        eventEmitter.emit('connectionFailed', {
          connectionId,
          error: 'Max reconnection attempts exceeded',
          totalAttempts: state.reconnectAttempts
        });
      }
    }
  }, delay);

  reconnectionTimeouts.set(connectionId, timeout);
}

/**
 * Update connection status in database and emit events
 */
async function updateConnectionStatus(connectionId: number, status: string): Promise<void> {
  try {
    await storage.updateChannelConnectionStatus(connectionId, status);

    const state = getConnectionState(connectionId);
    state.status = status as any;

    eventEmitter.emit('connectionStatusUpdate', {
      connectionId,
      status,
    });
  } catch (error) {

  }
}

/**
 * Clean up connection resources
 */
async function cleanupConnection(connectionId: number): Promise<void> {
  try {
    stopHealthMonitoring(connectionId);

    const timeout = reconnectionTimeouts.get(connectionId);
    if (timeout) {
      clearTimeout(timeout);
      reconnectionTimeouts.delete(connectionId);
    }

    const existingSocket = activeConnections.get(connectionId);
    if (existingSocket) {
      try {

        if (existingSocket.ev) {

          existingSocket.ev.removeAllListeners('connection.update');
          existingSocket.ev.removeAllListeners('creds.update');
          existingSocket.ev.removeAllListeners('messages.upsert');
          existingSocket.ev.removeAllListeners('messages.update');
          existingSocket.ev.removeAllListeners('messages.reaction');
          existingSocket.ev.removeAllListeners('contacts.upsert');
          existingSocket.ev.removeAllListeners('groups.upsert');
          existingSocket.ev.removeAllListeners('group-participants.update');
          existingSocket.ev.removeAllListeners('blocklist.set');
          existingSocket.ev.removeAllListeners('blocklist.update');
          existingSocket.ev.removeAllListeners('call');
          existingSocket.ev.removeAllListeners('presence.update');


          (existingSocket.ev as any).removeAllListeners('chats.set');
          (existingSocket.ev as any).removeAllListeners('contacts.set');
          (existingSocket.ev as any).removeAllListeners('messaging-history.set');
          (existingSocket.ev as any).removeAllListeners('labels.association');
          (existingSocket.ev as any).removeAllListeners('labels.edit');
        }

        existingSocket.ws?.close();
      } catch (error) {
        console.error('Error cleaning up socket listeners:', error);
      }
      activeConnections.delete(connectionId);
    }


    connectionStates.delete(connectionId);


    connectionAttempts.delete(connectionId);

  } catch (error) {
    console.error('Error during connection cleanup:', error);
  }
}



/**
 * Get connection diagnostics for troubleshooting
 */
export function getConnectionDiagnostics(connectionId: number): any {
  const state = getConnectionState(connectionId);
  const socket = activeConnections.get(connectionId);

  return {
    connectionId,
    status: state.status,
    healthScore: state.healthScore,
    reconnectAttempts: state.reconnectAttempts,
    lastConnected: state.lastConnected,
    lastReconnectAttempt: state.lastReconnectAttempt,
    errorCount: state.errorCount,
    lastError: state.lastError,
    rateLimitInfo: state.rateLimitInfo,
    socketConnected: !!socket,
    hasUser: !!socket?.user?.id,
    hasAuthState: !!socket?.authState,
    hasKeys: !!socket?.authState?.keys,
    sessionExists: fs.existsSync(path.join(SESSION_DIR, `session-${connectionId}`))
  };
}

const eventEmitter = new EventEmitter();

eventEmitter.setMaxListeners(50);

// Register with monitor for debugging
eventEmitterMonitor.register('whatsapp-service', eventEmitter);

const baileysPinoLogger = pino({ level: 'warn' });

const SESSION_DIR = path.join(process.cwd(), 'whatsapp-sessions');
if (!fs.existsSync(SESSION_DIR)) {
  fs.mkdirSync(SESSION_DIR, { recursive: true });
}

const MEDIA_DIR = path.join(process.cwd(), 'public', 'media');
fsExtra.ensureDirSync(MEDIA_DIR);

const mediaCache = new Map<string, string>();

/**
 * Calculate realistic typing delay based on message length
 * Simulates human typing speed with randomness
 */
function calculateTypingDelay(message: string): number {
  if (!TYPING_CONFIG.enabled) {
    return 0;
  }

  const words = message.split(' ').length;
  const baseDelay = (words / TYPING_CONFIG.wordsPerMinute) * 60 * 1000;

  const randomFactor = 0.7 + Math.random() * TYPING_CONFIG.randomnessFactor;
  const calculatedDelay = Math.min(Math.max(baseDelay * randomFactor, TYPING_CONFIG.minDelay), TYPING_CONFIG.maxDelay);


  return calculatedDelay;
}

/**
 * Calculate realistic recording delay for voice messages
 * Simulates time needed to record a voice message
 */
function calculateRecordingDelay(): number {
  if (!TYPING_CONFIG.enabled) {
    return 0;
  }

  const delay = TYPING_CONFIG.recordingMinDelay + Math.random() * (TYPING_CONFIG.recordingMaxDelay - TYPING_CONFIG.recordingMinDelay);

  return delay;
}

/**
 * Send typing indicator and wait for realistic typing time
 * @param sock WhatsApp socket connection
 * @param jid Recipient JID (phone number with @s.whatsapp.net or group JID)
 * @param message Message content to calculate typing time
 */
async function simulateTyping(sock: WASocket, jid: string, message: string): Promise<void> {
  if (!TYPING_CONFIG.enabled) {
    return;
  }

  try {


    await sock.sendPresenceUpdate('composing', jid);

    const delay = calculateTypingDelay(message);
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }


  } catch (error) {

  }
}

/**
 * Send recording indicator and wait for realistic recording time
 * @param sock WhatsApp socket connection
 * @param jid Recipient JID (phone number with @s.whatsapp.net or group JID)
 */
async function simulateRecording(sock: WASocket, jid: string): Promise<void> {
  if (!TYPING_CONFIG.enabled) {
    return;
  }

  try {


    await sock.sendPresenceUpdate('recording', jid);

    const delay = calculateRecordingDelay();
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }


  } catch (error) {

  }
}

/**
 * Stop presence indicators (typing/recording)
 * @param sock WhatsApp socket connection
 * @param jid Recipient JID
 */
async function stopPresenceIndicators(sock: WASocket, jid: string): Promise<void> {
  if (!TYPING_CONFIG.enabled) {
    return;
  }

  try {
    await sock.sendPresenceUpdate('paused', jid);

  } catch (error) {

  }
}

/**
 * Split a long message into smaller, natural chunks
 * @param message The message to split
 * @returns Array of message chunks
 */
function splitMessage(message: string): string[] {
  if (!MESSAGE_SPLITTING_CONFIG.enabled || message.length <= MESSAGE_SPLITTING_CONFIG.maxLength) {
    return [message];
  }



  let chunks: string[] = [];

  switch (MESSAGE_SPLITTING_CONFIG.splitMethod) {
    case 'sentences':
      chunks = splitBySentences(message);
      break;
    case 'paragraphs':
      chunks = splitByParagraphs(message);
      break;
    case 'characters':
      chunks = splitByCharacters(message);
      break;
    default:
      chunks = [message];
  }

  const finalChunks: string[] = [];
  for (const chunk of chunks) {
    if (chunk.length <= MESSAGE_SPLITTING_CONFIG.maxLength) {
      finalChunks.push(chunk);
    } else {
      finalChunks.push(...splitByCharacters(chunk));
    }
  }



  finalChunks.forEach((chunk, index) => {

  });

  return finalChunks.filter(chunk => chunk.trim().length > 0);
}

/**
 * Split message by sentences with improved logic
 */
function splitBySentences(message: string): string[] {
  const chunks: string[] = [];
  let remainingText = message.trim();

  while (remainingText.length > 0) {
    if (remainingText.length <= MESSAGE_SPLITTING_CONFIG.maxLength) {
      chunks.push(remainingText);
      break;
    }

    const chunk = findOptimalSplit(remainingText, MESSAGE_SPLITTING_CONFIG.maxLength);
    chunks.push(chunk);

    remainingText = remainingText.substring(chunk.length).trim();
  }

  return chunks.filter(chunk => chunk.trim().length > 0);
}

/**
 * Find the optimal split point for a message chunk
 * Priority: sentence endings > clause boundaries > word boundaries > character limit
 */
function findOptimalSplit(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }

  const minChunkSize = MESSAGE_SPLITTING_CONFIG.minChunkSize;
  const smartBoundaries = MESSAGE_SPLITTING_CONFIG.smartBoundaries;
  const prioritizeSentences = MESSAGE_SPLITTING_CONFIG.prioritizeSentences;

  if (!smartBoundaries) {
    return splitAtWordBoundary(text, maxLength, minChunkSize);
  }

  const boundaryPatterns = [
    ...(prioritizeSentences ? [{ pattern: /[.!?]+\s+/g, minChunkSize: Math.max(minChunkSize, 20), description: 'sentence' }] : []),
    { pattern: /[;:,]\s+/g, minChunkSize: Math.max(minChunkSize, 30), description: 'clause' },
    { pattern: /\n\s*/g, minChunkSize: minChunkSize, description: 'line break' },
    { pattern: /\s+/g, minChunkSize: Math.max(minChunkSize, 10), description: 'word' }
  ];

  for (const { pattern, minChunkSize: patternMinSize, description } of boundaryPatterns) {
    const matches = Array.from(text.matchAll(pattern));

    let bestMatch = null;
    let bestPosition = -1;

    for (const match of matches) {
      const position = match.index! + match[0].length;

      if (position <= maxLength && position >= patternMinSize) {
        bestMatch = match;
        bestPosition = position;
      }
    }

    if (bestMatch && bestPosition > 0) {
      const chunk = text.substring(0, bestPosition).trim();

      if (chunk.length >= patternMinSize) {

        return chunk;
      }
    }
  }

  return splitAtWordBoundary(text, maxLength, minChunkSize);
}

/**
 * Split text at word boundary with minimum chunk size consideration
 */
function splitAtWordBoundary(text: string, maxLength: number, minChunkSize: number): string {
  const words = text.split(/\s+/);
  let chunk = '';

  for (const word of words) {
    const testChunk = chunk + (chunk ? ' ' : '') + word;
    if (testChunk.length <= maxLength) {
      chunk = testChunk;
    } else {
      break;
    }
  }

  if (chunk.length < minChunkSize && words.length > 1) {
    const nextWord = words[chunk.split(' ').length];
    if (nextWord) {
      const forcedChunk = chunk + (chunk ? ' ' : '') + nextWord;
      if (forcedChunk.length <= maxLength * 1.1) {
        chunk = forcedChunk;
      }
    }
  }

  if (!chunk && words.length > 0) {
    chunk = text.substring(0, Math.max(maxLength - 3, minChunkSize)) + '...';

  }

  return chunk || text.substring(0, maxLength);
}

/**
 * Split message by paragraphs with improved logic
 */
function splitByParagraphs(message: string): string[] {
  const paragraphs = message.split(/\n\s*\n/).filter(p => p.trim().length > 0);
  const chunks: string[] = [];
  let currentChunk = '';

  for (const paragraph of paragraphs) {
    const trimmedParagraph = paragraph.trim();
    const separator = currentChunk ? '\n\n' : '';
    const testChunk = currentChunk + separator + trimmedParagraph;

    if (testChunk.length <= MESSAGE_SPLITTING_CONFIG.maxLength) {
      currentChunk = testChunk;
    } else {
      if (currentChunk) {
        chunks.push(currentChunk);
      }

      if (trimmedParagraph.length > MESSAGE_SPLITTING_CONFIG.maxLength) {
        const paragraphChunks = splitBySentences(trimmedParagraph);
        chunks.push(...paragraphChunks);
        currentChunk = '';
      } else {
        currentChunk = trimmedParagraph;
      }
    }
  }

  if (currentChunk) {
    chunks.push(currentChunk);
  }

  return chunks.filter(chunk => chunk.trim().length > 0);
}

/**
 * Split message by character count with smart word boundaries
 */
function splitByCharacters(message: string): string[] {
  const chunks: string[] = [];
  let remainingText = message.trim();

  while (remainingText.length > 0) {
    if (remainingText.length <= MESSAGE_SPLITTING_CONFIG.maxLength) {
      chunks.push(remainingText);
      break;
    }

    let splitPoint = MESSAGE_SPLITTING_CONFIG.maxLength;

    for (let i = MESSAGE_SPLITTING_CONFIG.maxLength - 1; i >= Math.max(20, MESSAGE_SPLITTING_CONFIG.maxLength * 0.7); i--) {
      if (remainingText[i] === ' ') {
        splitPoint = i;
        break;
      }
    }

    let chunk = remainingText.substring(0, splitPoint).trim();

    if (chunk.length < 20 && remainingText.length > MESSAGE_SPLITTING_CONFIG.maxLength) {
      chunk = remainingText.substring(0, MESSAGE_SPLITTING_CONFIG.maxLength).trim();
      splitPoint = chunk.length;
    }

    chunks.push(chunk);
    remainingText = remainingText.substring(splitPoint).trim();
  }

  return chunks.filter(chunk => chunk.length > 0);
}

/**
 * Calculate delay between split messages
 */
function calculateSplitMessageDelay(): number {
  const baseDelay = MESSAGE_SPLITTING_CONFIG.delayBetweenMessages;
  const randomFactor = 1 + (Math.random() - 0.5) * MESSAGE_SPLITTING_CONFIG.randomDelayFactor;
  const delay = Math.max(baseDelay * randomFactor, 1000);


  return delay;
}

/**
 * Configure typing indicator behavior
 * @param config Partial typing configuration to update
 */
export function configureTypingBehavior(config: Partial<TypingConfig>): void {
  Object.assign(TYPING_CONFIG, config);

}

/**
 * Configure message debouncing behavior
 * @param config Partial message debouncing configuration to update
 */
export function configureMessageDebouncing(config: Partial<MessageDebouncingConfig>): void {
  Object.assign(MESSAGE_DEBOUNCING_CONFIG, config);

}

/**
 * Get current message debouncing configuration
 * @returns Current message debouncing configuration
 */
export function getMessageDebouncingConfiguration(): MessageDebouncingConfig {
  return { ...MESSAGE_DEBOUNCING_CONFIG };
}

/**
 * Generate a unique debounce key for a user's message processing
 */
function getDebounceKey(remoteJid: string, connectionId: number): string {
  return `${connectionId}_${remoteJid}`;
}

/**
 * Cancel any existing debounced message processing for a user
 */
function cancelDebouncedMessage(remoteJid: string, connectionId: number): void {
  const debounceKey = getDebounceKey(remoteJid, connectionId);
  const existingDebounce = debouncedMessages.get(debounceKey);

  if (existingDebounce) {
    clearTimeout(existingDebounce.timeoutId);
    debouncedMessages.delete(debounceKey);
  }
}

/**
 * Schedule debounced processing of a user message
 */
function scheduleDebounceProcessing(
  message: any,
  conversation: any,
  contact: any,
  channelConnection: any,
  remoteJid: string,
  connectionId: number
): void {
  const debounceKey = getDebounceKey(remoteJid, connectionId);

  cancelDebouncedMessage(remoteJid, connectionId);

  const timeoutId = setTimeout(async () => {
    try {
      debouncedMessages.delete(debounceKey);

      await processMessageThroughFlowExecutor(message, conversation, contact, channelConnection);
    } catch (error) {

      debouncedMessages.delete(debounceKey);
    }
  }, MESSAGE_DEBOUNCING_CONFIG.debounceDelay);

  const debouncedMessage: DebouncedMessage = {
    message,
    conversation,
    contact,
    channelConnection,
    timeoutId,
    createdAt: new Date()
  };

  debouncedMessages.set(debounceKey, debouncedMessage);
}

/**
 * Process a message through the flow executor (extracted for reuse in debouncing)
 */
async function processMessageThroughFlowExecutor(
  message: any,
  conversation: any,
  contact: any,
  channelConnection: any
): Promise<void> {
  try {
    const flowExecutorModule = await import('../flow-executor');
    const flowExecutor = flowExecutorModule.default;

    if (contact) {
      await flowExecutor.processIncomingMessage(message, conversation, contact, channelConnection);
    }
  } catch (error) {

    throw error;
  }
}

/**
 * Clean up old debounced messages that may have been orphaned
 */
function cleanupOldDebouncedMessages(): void {
  const now = new Date();
  const maxAge = MESSAGE_DEBOUNCING_CONFIG.maxDebounceDelay * 2;

  const keysToDelete: string[] = [];

  debouncedMessages.forEach((debouncedMessage, key) => {
    const age = now.getTime() - debouncedMessage.createdAt.getTime();
    if (age > maxAge) {
      clearTimeout(debouncedMessage.timeoutId);
      keysToDelete.push(key);
    }
  });

  keysToDelete.forEach(key => debouncedMessages.delete(key));
}

/**
 * Get debouncing status for debugging
 */
export function getDebouncingStatus(): any {
  return {
    enabled: MESSAGE_DEBOUNCING_CONFIG.enabled,
    debounceDelay: MESSAGE_DEBOUNCING_CONFIG.debounceDelay,
    maxDebounceDelay: MESSAGE_DEBOUNCING_CONFIG.maxDebounceDelay,
    activeDebouncedMessages: debouncedMessages.size,
    debouncedUsers: Array.from(debouncedMessages.keys())
  };
}

/**
 * Configure message splitting behavior
 * @param config Partial message splitting configuration to update
 */
export function configureMessageSplitting(config: Partial<MessageSplittingConfig>): void {
  Object.assign(MESSAGE_SPLITTING_CONFIG, config);

}

/**
 * Get current typing configuration
 * @returns Current typing configuration
 */
export function getTypingConfiguration(): TypingConfig {
  return { ...TYPING_CONFIG };
}

/**
 * Get current message splitting configuration
 * @returns Current message splitting configuration
 */
export function getMessageSplittingConfiguration(): MessageSplittingConfig {
  return { ...MESSAGE_SPLITTING_CONFIG };
}



/**
 * Send a message with automatic splitting if needed
 * @param sock WhatsApp socket connection
 * @param phoneNumber Recipient phone number
 * @param message Message content
 * @param connectionId Connection ID for logging
 * @returns Array of sent message info
 */
async function sendMessageWithSplitting(
  sock: WASocket,
  phoneNumber: string,
  message: string,
  connectionId: number
): Promise<any[]> {
  const chunks = splitMessage(message);
  const sentMessages: any[] = [];

  if (chunks.length === 1) {
    try {
      await simulateTyping(sock, phoneNumber, chunks[0]);
      const sentMessageInfo = await sock.sendMessage(phoneNumber, { text: chunks[0] });
      await stopPresenceIndicators(sock, phoneNumber);
      sentMessages.push(sentMessageInfo);

      return sentMessages;
    } catch (error) {

      throw error;
    }
  }

  const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;



  const queuedMessage = addToMessageQueue(phoneNumber, connectionId, chunks, messageId, sock);


  try {
    await simulateTyping(sock, phoneNumber, chunks[0]);
    const firstMessageInfo = await sock.sendMessage(phoneNumber, { text: chunks[0] });
    sentMessages.push(firstMessageInfo);
    await stopPresenceIndicators(sock, phoneNumber);


    queuedMessage.currentChunkIndex = 1;

    for (let i = 1; i < chunks.length; i++) {
      const chunkIndex = i;
      const chunk = chunks[i];
      const isLastChunk = i === chunks.length - 1;
      const delay = calculateSplitMessageDelay();

      const actualDelay = delay * i;


      const timeoutId = setTimeout(async () => {
        try {


          const queueKey = getQueueKey(phoneNumber, connectionId);
          const currentQueue = messageQueues.get(queueKey);
          const currentMessage = currentQueue?.find(msg => msg.id === messageId);

          if (!currentMessage) {

            return;
          }

          if (currentMessage.cancelled) {

            return;
          }

          if (!currentMessage.timeoutIds.includes(timeoutId)) {

            return;
          }



          await simulateTyping(sock, phoneNumber, chunk);
          const sentMessageInfo = await sock.sendMessage(phoneNumber, { text: chunk });
          await stopPresenceIndicators(sock, phoneNumber);



          currentMessage.currentChunkIndex = chunkIndex + 1;

          if (isLastChunk) {
            removeFromMessageQueue(phoneNumber, connectionId, messageId);

          }

        } catch (error) {

          removeFromMessageQueue(phoneNumber, connectionId, messageId);
        }
      }, actualDelay);

      queuedMessage.timeoutIds.push(timeoutId);


    }

  } catch (error) {

    removeFromMessageQueue(phoneNumber, connectionId, messageId);
    throw error;
  }




  return sentMessages;
}

/**
 * Helper function to resolve media URL to actual file system path
 * Converts URLs like '/media/flow-media/filename.jpg' to actual file paths
 * For external URLs, returns the URL as-is for download handling
 */
function resolveMediaPath(mediaUrlOrPath: string): string {
  if (mediaUrlOrPath.startsWith('http://') || mediaUrlOrPath.startsWith('https://')) {
    return mediaUrlOrPath;
  }

  if (mediaUrlOrPath.startsWith('/media/flow-media/')) {
    const filename = path.basename(mediaUrlOrPath);
    return path.join(process.cwd(), 'uploads', 'flow-media', filename);
  }

  if (mediaUrlOrPath.startsWith('/media/')) {
    return path.join(process.cwd(), 'public', mediaUrlOrPath.substring(1));
  }

  if (mediaUrlOrPath.startsWith('/uploads/')) {
    return path.join(process.cwd(), mediaUrlOrPath.substring(1));
  }

  if (path.isAbsolute(mediaUrlOrPath) && !mediaUrlOrPath.startsWith('/media/') && !mediaUrlOrPath.startsWith('/uploads/')) {
    return mediaUrlOrPath;
  }

  return path.resolve(mediaUrlOrPath);
}

/**
 * Download media from a WhatsApp message and save it to disk
 * @param message The WhatsApp message containing media
 * @param sock The WhatsApp socket
 * @returns The URL path to the saved media file or null if failed
 */
export async function downloadAndSaveMedia(message: WAMessage, sock: WASocket): Promise<string | null> {
  const messageId = message.key?.id || '';
  try {
    if (!message.message) return null;

    const timestamp = message.messageTimestamp || Date.now();
    const mediaKey = crypto.createHash('md5').update(`${messageId}_${timestamp}`).digest('hex');

    if (mediaCache.has(mediaKey)) {
      const cachedUrl = mediaCache.get(mediaKey);
      if (cachedUrl) {


        const filePath = path.join(process.cwd(), 'public', cachedUrl.substring(1));
        if (await fsExtra.pathExists(filePath)) {
          return cachedUrl;
        }


      }
      mediaCache.delete(mediaKey);
    }

    let extension = '';
    let mimeType = '';
    let mediaType = '';

    if (message.message.imageMessage) {
      extension = '.jpg';
      mimeType = message.message.imageMessage.mimetype || 'image/jpeg';
      mediaType = 'image';
    }
    else if (message.message.videoMessage) {
      extension = '.mp4';
      mimeType = message.message.videoMessage.mimetype || 'video/mp4';
      mediaType = 'video';
    }
    else if (message.message.audioMessage) {
      extension = '.mp3';
      mimeType = message.message.audioMessage.mimetype || 'audio/mpeg';
      mediaType = 'audio';
    }
    else if (message.message.stickerMessage) {
      extension = '.webp';
      mimeType = 'image/webp';
      mediaType = 'sticker';
    }
    else if (message.message.documentMessage) {
      const fileName = message.message.documentMessage.fileName || '';
      const fileExt = path.extname(fileName);
      extension = fileExt || '.bin';
      mimeType = message.message.documentMessage.mimetype || 'application/octet-stream';
      mediaType = 'document';
    }
    else {
      return null;
    }

    const mediaTypeDir = path.join(MEDIA_DIR, mediaType);
    await fsExtra.ensureDir(mediaTypeDir);

    const filename = `${mediaKey}${extension}`;
    const filepath = path.join(mediaTypeDir, filename);
    const mediaUrl = `/media/${mediaType}/${filename}`;



    if (await fsExtra.pathExists(filepath)) {

      mediaCache.set(mediaKey, mediaUrl);
      return mediaUrl;
    }


    const buffer = await downloadMediaMessage(
      message,
      'buffer',
      {},
      {
        logger: baileysPinoLogger,
        reuploadRequest: sock.updateMediaMessage
      }
    );

    if (!buffer || buffer.length === 0) {
      console.error(`Downloaded buffer is empty for message ${messageId}`);
      return null;
    }


    await fsExtra.writeFile(filepath, buffer);


    mediaCache.set(mediaKey, mediaUrl);

    return mediaUrl;
  } catch (error) {
    console.error(`Error downloading media for message ${messageId}:`, error);
    return null;
  }
}

/**
 * Connects to WhatsApp using the Baileys library
 * @param connectionId The ID of the channel connection
 * @param userId The user ID who owns this connection
 */
export async function connectToWhatsApp(connectionId: number, userId: number): Promise<void> {
  try {
    const connection = await storage.getChannelConnection(connectionId);
    if (!connection) {
      throw new Error(`Connection with ID ${connectionId} not found`);
    }

    const user = await storage.getUser(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const hasConnectionAccess = await checkConnectionPermission(user, connection, undefined, connectionId);

    if (!hasConnectionAccess) {
      throw new Error('You do not have permission to access this connection');
    }

    if (activeConnections.has(connectionId)) {
      console.log(`WhatsApp connection ${connectionId} already exists, skipping duplicate connection attempt`);
      return;
    }


    if (connectionAttempts.get(connectionId)) {
      console.log(`WhatsApp connection attempt ${connectionId} already in progress, skipping duplicate attempt`);
      return;
    }


    connectionAttempts.set(connectionId, true);

    try {

      await cleanupConnection(connectionId);

      const sessionDir = path.join(SESSION_DIR, `session-${connectionId}`);
      if (!fs.existsSync(sessionDir)) {
        fs.mkdirSync(sessionDir, { recursive: true });
      }

      const { state: authState, saveCreds } = await useMultiFileAuthState(sessionDir);

      const sock = makeWASocket({
        auth: {
          creds: authState.creds,
          keys: makeCacheableSignalKeyStore(authState.keys, baileysPinoLogger),
        },
        printQRInTerminal: false,
        browser: Browsers.macOS('Chrome'),
        logger: baileysPinoLogger,
        markOnlineOnConnect: true,
        getMessage: async (key) => {
          try {
            const messageId = `${key.remoteJid}_${key.id}`;


            return { conversation: 'Message content not found' };
          } catch (error) {
            console.error('Error fetching message:', error);
            return { conversation: 'Error fetching message' };
          }
        },
        cachedGroupMetadata: async (_jid) => {
          return undefined;
        },
      });

      activeConnections.set(connectionId, sock);

      const connState = getConnectionState(connectionId);
      connState.socket = sock;
      connState.status = 'connecting';

      sock.ev.on('connection.update', async (update) => {
        const { connection, lastDisconnect, qr } = update;

        if (qr) {


          connState.status = 'qr_code';
          await updateConnectionStatus(connectionId, 'qr_code');

          setTimeout(() => {

            eventEmitter.emit('qrCode', {
              connectionId,
              qrCode: qr,
            });

            const qrInterval = setInterval(() => {
              if (activeConnections.has(connectionId)) {

                eventEmitter.emit('qrCode', {
                  connectionId,
                  qrCode: qr,
                });
              } else {
                clearInterval(qrInterval);
              }
            }, 10000);

            setTimeout(() => clearInterval(qrInterval), 30000);
          }, 1000);
        }

        if (connection === 'close') {
          const statusCode = (lastDisconnect?.error as Boom)?.output?.statusCode;
          const shouldReconnect = statusCode !== DisconnectReason.loggedOut;



          if (shouldReconnect) {

            connState.status = 'reconnecting';
            updateHealthScore(connectionId, 'error');

            const connection = await storage.getChannelConnection(connectionId);
            if (connection) {
              await scheduleReconnection(connectionId, connection);
            }
          } else {
            await updateConnectionStatus(connectionId, 'logged_out');

            await cleanupConnection(connectionId);

            try {
              const sessionDir = path.join(SESSION_DIR, `session-${connectionId}`);
              if (fs.existsSync(sessionDir)) {
                fs.rmSync(sessionDir, { recursive: true, force: true });

              }
            } catch (error) {
              console.error('Error removing session directory:', error);
            }
          }
        } else if (connection === 'open') {


          connState.status = 'connected';
          connState.lastConnected = new Date();
          connState.reconnectAttempts = 0;
          connState.lastError = null;
          updateHealthScore(connectionId, 'success');

          await updateConnectionStatus(connectionId, 'active');

          startHealthMonitoring(connectionId);

          eventEmitter.emit('connectionStatusUpdate', {
            connectionId,
            status: 'connected',
          });
        }
      });

      sock.ev.on('creds.update', (_creds) => {
        saveCreds();

      });



      (sock.ev as any).on('chats.set', async ({ chats }: { chats: any[] }) => {


        try {
          for (const chat of chats) {
            try {
              if (chat.id && !chat.id.includes('status@broadcast')) {
                if (chat.id.includes('@broadcast') || chat.id === 'status@broadcast') {
                  continue;
                }

                const phoneNumber = chat.id.split('@')[0];
                let contact = await storage.getContactByIdentifier(phoneNumber, 'whatsapp');

                if (!contact) {
                  const user = await storage.getUser(userId);
                  const companyId = user?.companyId;

                  const contactData: InsertContact = {
                    companyId: companyId,
                    name: chat.name || phoneNumber,
                    phone: phoneNumber,
                    email: null,
                    avatarUrl: null,
                    identifier: phoneNumber,
                    identifierType: 'whatsapp',
                    source: 'whatsapp',
                    notes: null
                  };

                  contact = await storage.createContact(contactData);

                }

                const existingConversation = await storage.getConversationByContactAndChannel(
                  contact.id,
                  connectionId
                );

                if (!existingConversation) {
                  const user = await storage.getUser(userId);
                  const companyId = user?.companyId;

                  const connection = await storage.getChannelConnection(connectionId);
                  const channelType = connection?.channelType || 'whatsapp_unofficial';

                  const conversationData: InsertConversation = {
                    companyId: companyId,
                    contactId: contact.id,
                    channelId: connectionId,
                    channelType: channelType,
                    status: 'active',
                    lastMessageAt: chat.conversationTimestamp ? new Date(typeof chat.conversationTimestamp === 'object' ? chat.conversationTimestamp.toNumber() : Number(chat.conversationTimestamp)) : new Date()
                  };

                  const conversation = await storage.createConversation(conversationData);


                  if ((global as any).broadcastToAllClients) {
                    (global as any).broadcastToAllClients({
                      type: 'newConversation',
                      data: {
                        ...conversation,
                        contact
                      }
                    });
                  }
                }
              }
            } catch (error) {
              console.error('Error processing chat:', error);
            }
          }


          eventEmitter.emit('whatsappHistorySyncComplete', {
            connectionId,
            contactsCount: chats.length,
            messagesCount: 0
          });
        } catch (error) {
          console.error('Error processing chats:', error);
        }
      });

      (sock.ev as any).on('messages.upsert', async ({ messages, type }: { messages: any[], type: string }) => {


        for (const waMsg of messages) {
          try {
            if (waMsg.key && waMsg.key.remoteJid) {
              await handleIncomingMessage(waMsg, connectionId, userId);
            }
          } catch (error) {
            console.error('Error handling message:', error);
          }
        }
      });

      (sock.ev as any).on('messages.update', async (updates: any[]) => {


      });

      (sock.ev as any).on('messages.reaction', async (reactions: any[]) => {


      });


      await updateConnectionStatus(connectionId, 'connecting');

    } catch (error: any) {
      console.error('Error connecting to WhatsApp:', error);
      await storage.updateChannelConnectionStatus(connectionId, 'error');

      eventEmitter.emit('connectionError', {
        connectionId,
        error: error.message || 'Unknown error connecting to WhatsApp',
      });

      throw error;
    } finally {

      connectionAttempts.delete(connectionId);
    }
  } catch (error) {


    console.error(`Error in connectToWhatsApp setup for connection ${connectionId}:`, error);
    await storage.updateChannelConnectionStatus(connectionId, 'error');
    eventEmitter.emit('connectionError', {
      connectionId,
      error: (error as any).message || 'Unknown error during connection setup',
    });

    connectionAttempts.delete(connectionId);
    throw error;
  }
}

/**
 * Handle incoming WhatsApp messages
 */
async function handleIncomingMessage(waMsg: WAMessage, connectionId: number, userId: number): Promise<void> {
  try {
    if (!waMsg.key || !waMsg.key.remoteJid || !waMsg.key.id) {

      return;
    }

    if (waMsg.key.remoteJid.includes('@broadcast') || waMsg.key.remoteJid === 'status@broadcast') {

      return;
    }

    const user = await storage.getUser(userId);
    if (!user) {
      console.error(`User with ID ${userId} not found`);
      return;
    }

    const remoteJid = waMsg.key.remoteJid;
    const companyId = user.companyId;

    const isFromMe = waMsg.key.fromMe === true;

    if (isFromMe) {

      return;
    }

    if (!isFromMe) {
      const queueKey = getQueueKey(remoteJid, connectionId);
      const existingQueue = messageQueues.get(queueKey);
      if (existingQueue && existingQueue.length > 0) {
        const totalPendingChunks = existingQueue.reduce((total, msg) => {
          return total + (msg.chunks.length - msg.currentChunkIndex);
        }, 0);



        existingQueue.forEach(msg => {
          const remainingChunks = msg.chunks.length - msg.currentChunkIndex;

        });

        cancelQueuedMessages(remoteJid, connectionId);


      } else {

      }
    } else {

    }

    const isGroupChat = waMsg.key.remoteJid.endsWith('@g.us');


    let messageType = 'text';
    let messageContent = '';
    let mediaUrl: string | null = null;

    let phoneNumber: string;
    let groupJid: string | null = null;
    let participantJid: string | null = null;
    let participantName: string | null = null;

    if (isGroupChat) {
      groupJid = waMsg.key.remoteJid;

      if (isFromMe) {
        phoneNumber = groupJid.split('@')[0];
      } else {
        participantJid = waMsg.key.participant || waMsg.key.remoteJid;
        phoneNumber = participantJid.split('@')[0];

        if (waMsg.pushName) {
          participantName = waMsg.pushName;
        }
      }
    } else {
      phoneNumber = waMsg.key.remoteJid.split('@')[0];
    }

    if (waMsg.message) {
      if (waMsg.message.conversation) {
        messageContent = waMsg.message.conversation;
      }
      else if (waMsg.message.extendedTextMessage) {
        messageContent = waMsg.message.extendedTextMessage.text || '';
      }
      else if (waMsg.message.imageMessage) {
        messageType = 'image';
        messageContent = waMsg.message.imageMessage.caption || 'Image message';

        const sock = activeConnections.get(connectionId);
        if (sock) {

          mediaUrl = await downloadAndSaveMedia(waMsg, sock);
          if (mediaUrl) {

          } else {
            console.error('Failed to download image');
          }
        }
      }
      else if (waMsg.message.videoMessage) {
        messageType = 'video';
        messageContent = waMsg.message.videoMessage.caption || 'Video message';

        const sock = activeConnections.get(connectionId);
        if (sock) {

          mediaUrl = await downloadAndSaveMedia(waMsg, sock);
          if (mediaUrl) {

          } else {
            console.error('Failed to download video');
          }
        }
      }
      else if (waMsg.message.audioMessage) {
        messageType = 'audio';
        messageContent = 'Audio message';

        const sock = activeConnections.get(connectionId);
        if (sock) {

          mediaUrl = await downloadAndSaveMedia(waMsg, sock);
          if (mediaUrl) {

          } else {
            console.error('Failed to download audio');
          }
        }
      }
      else if (waMsg.message.documentMessage) {
        messageType = 'document';
        messageContent = waMsg.message.documentMessage.fileName || 'Document message';

        const sock = activeConnections.get(connectionId);
        if (sock) {

          mediaUrl = await downloadAndSaveMedia(waMsg, sock);
          if (mediaUrl) {

          } else {
            console.error('Failed to download document');
          }
        }
      }
      else if (waMsg.message.contactMessage) {
        messageType = 'contact';
        messageContent = 'Contact shared';
      }
      else if (waMsg.message.locationMessage) {
        messageType = 'location';
        messageContent = 'Location shared';
      }
      else if (waMsg.message.stickerMessage) {
        messageType = 'sticker';
        messageContent = 'Sticker message';

        const sock = activeConnections.get(connectionId);
        if (sock) {

          mediaUrl = await downloadAndSaveMedia(waMsg, sock);
          if (mediaUrl) {

          } else {
            console.error('Failed to download sticker');
          }
        }
      }
      else {
        messageType = 'unknown';
        messageContent = 'Unsupported message type';

      }
    } else {

      return;
    }



    let contact = null;
    let conversation = null;

    if (isGroupChat) {
      conversation = await storage.getConversationByGroupJid(groupJid!);

      if (!conversation) {
        const connection = await storage.getChannelConnection(connectionId);
        const channelType = connection?.channelType || 'whatsapp_unofficial';

        let groupName = groupJid!.split('@')[0];
        let groupMetadata = null;

        try {
          const sock = activeConnections.get(connectionId);
          if (sock) {
            const metadata = await sock.groupMetadata(groupJid!);
            groupName = metadata.subject || groupName;
            groupMetadata = {
              subject: metadata.subject,
              desc: metadata.desc,
              participants: metadata.participants,
              creation: metadata.creation,
              owner: metadata.owner
            };
          }
        } catch (error) {

        }

        const conversationData: InsertConversation = {
          companyId: companyId,
          contactId: null,
          channelId: connectionId,
          channelType: channelType,
          status: 'active',
          lastMessageAt: new Date(),
          isGroup: true,
          groupJid: groupJid!,
          groupName: groupName,
          groupDescription: groupMetadata?.desc || null,
          groupParticipantCount: groupMetadata?.participants?.length || 0,
          groupCreatedAt: groupMetadata?.creation ? new Date(groupMetadata.creation * 1000) : new Date(),
          groupMetadata: groupMetadata
        };

        conversation = await storage.createConversation(conversationData);


        if ((global as any).broadcastToAllClients) {
          (global as any).broadcastToAllClients({
            type: 'newConversation',
            data: {
              ...conversation,
              contact: null
            }
          });
        }
      }

      if (!isFromMe && participantJid) {
        contact = await storage.getContactByIdentifier(phoneNumber, 'whatsapp');

        if (!contact) {
          const contactData: InsertContact = {
            companyId: companyId,
            name: participantName || phoneNumber,
            phone: phoneNumber,
            email: null,
            avatarUrl: null,
            identifier: phoneNumber,
            identifierType: 'whatsapp',
            source: 'whatsapp',
            notes: null
          };

          contact = await storage.createContact(contactData);

        }

        await storage.upsertGroupParticipant({
          conversationId: conversation.id,
          contactId: contact.id,
          participantJid: participantJid,
          participantName: participantName || contact.name,
          isActive: true
        });
      }
    } else {
      contact = await storage.getContactByIdentifier(phoneNumber, 'whatsapp');

      if (!contact) {
        let name = phoneNumber;
        let profilePictureUrl = null;

        try {
          const sock = activeConnections.get(connectionId);
          if (sock) {


            profilePictureUrl = await fetchProfilePicture(connectionId, phoneNumber);

            if (profilePictureUrl) {

            } else {

            }
          }
        } catch (e) {

        }

        const contactData: InsertContact = {
          companyId: companyId,
          name,
          phone: phoneNumber,
          email: null,
          avatarUrl: profilePictureUrl,
          identifier: phoneNumber,
          identifierType: 'whatsapp',
          source: 'whatsapp',
          notes: null
        };

        contact = await storage.createContact(contactData);

      }

      conversation = await storage.getConversationByContactAndChannel(
        contact.id,
        connectionId
      );

      if (!conversation) {
        const connection = await storage.getChannelConnection(connectionId);
        const channelType = connection?.channelType || 'whatsapp_unofficial';

        const conversationData: InsertConversation = {
          companyId: companyId,
          contactId: contact.id,
          channelId: connectionId,
          channelType: channelType,
          status: 'active',
          lastMessageAt: new Date(),
          isGroup: false
        };

        conversation = await storage.createConversation(conversationData);


        if ((global as any).broadcastToAllClients) {
          (global as any).broadcastToAllClients({
            type: 'newConversation',
            data: {
              ...conversation,
              contact
            }
          });
        }
      } else {
        conversation = await storage.updateConversation(conversation.id, {
          lastMessageAt: new Date()
        });
      }
    }

    if (conversation) {
      conversation = await storage.updateConversation(conversation.id, {
        lastMessageAt: new Date()
      });
    }

    const direction = isFromMe ? 'outbound' : 'inbound';
    const senderType = isFromMe ? 'user' : 'contact';
    const senderId = isFromMe ? userId : (contact?.id || null);

    const messageData: InsertMessage = {
      conversationId: conversation.id,
      content: messageContent,
      direction,
      type: messageType,
      sentAt: waMsg.messageTimestamp
        ? new Date((typeof waMsg.messageTimestamp === 'object'
          ? waMsg.messageTimestamp.toNumber()
          : Number(waMsg.messageTimestamp)) * 1000)
        : new Date(),
      senderId,
      senderType,
      status: 'delivered',
      mediaUrl,
      externalId: waMsg.key.id,

      groupParticipantJid: isGroupChat && !isFromMe ? participantJid : null,
      groupParticipantName: isGroupChat && !isFromMe ? participantName : null,

      metadata: JSON.stringify({
        messageId: waMsg.key.id,
        remoteJid: waMsg.key.remoteJid,
        fromMe: isFromMe,
        isGroupChat,
        ...(isGroupChat && { groupJid, participantJid, participantName }),
        ...(mediaUrl && { mediaUrl }),
        whatsappMessage: {
          key: waMsg.key,
          message: waMsg.message,
          messageTimestamp: waMsg.messageTimestamp
        }
      }),
    };

    const message = await storage.createMessage(messageData);


    if (!isFromMe) {
      try {
        const unreadCount = await storage.getUnreadCount(conversation.id);

        if ((global as any).broadcastToAllClients) {
          (global as any).broadcastToAllClients({
            type: 'unreadCountUpdated',
            data: {
              conversationId: conversation.id,
              unreadCount
            }
          });
        }
      } catch (error) {
        console.error('Error broadcasting unread count update:', error);
      }

      eventEmitter.emit('messageReceived', {
        message,
        conversation,
        contact,
      });

      if (!isGroupChat) {
        try {
          const channelConnection = await storage.getChannelConnection(connectionId);

          if (channelConnection && contact) {
            if (MESSAGE_DEBOUNCING_CONFIG.enabled && !isFromMe) {
              scheduleDebounceProcessing(message, conversation, contact, channelConnection, remoteJid, connectionId);
            } else {
              await processMessageThroughFlowExecutor(message, conversation, contact, channelConnection);
            }
          }
        } catch (error) {
          console.error('Error processing message through flow executor:', error);
        }
      } else {

      }
    }

  } catch (error: any) {
    console.error('Error handling incoming message:', error);
  }
}

/**
 * Disconnect from WhatsApp with enterprise-grade cleanup
 */
export async function disconnectWhatsApp(connectionId: number, userId: number): Promise<boolean> {
  try {
    const connection = await storage.getChannelConnection(connectionId);
    if (!connection) {
      throw new Error('Connection not found');
    }

    const user = await storage.getUser(userId);
    if (!user) {
      throw new Error('User not found');
    }





    const connState = getConnectionState(connectionId);
    connState.status = 'disconnected';
    connState.lastError = null;

    await updateConnectionStatus(connectionId, 'disconnected');

    const sock = activeConnections.get(connectionId);
    if (!sock) {


      await cleanupConnection(connectionId);

      eventEmitter.emit('connectionStatusUpdate', {
        connectionId,
        status: 'disconnected',
      });

      return true;
    }

    try {
      await sock.logout();

    } catch (logoutError) {


      try {
        sock.ws?.close();
      } catch (closeError) {

      }
    }

    await cleanupConnection(connectionId);

    eventEmitter.emit('connectionStatusUpdate', {
      connectionId,
      status: 'disconnected',
    });


    return true;
  } catch (error: any) {


    const connState = getConnectionState(connectionId);
    connState.lastError = error.message;
    updateHealthScore(connectionId, 'error');

    return false;
  }
}

/**
 * Send an audio message via WhatsApp
 */
export async function sendWhatsAppAudioMessage(
  connectionId: number,
  userId: number,
  to: string,
  audioPath: string,
  isFromBot: boolean = false,
  conversationId?: number
): Promise<Message | null> {

  let convertedAudioPath: string | null = null;

  try {
    const connection = await storage.getChannelConnection(connectionId);
    if (!connection) {
      throw new Error('Connection not found');
    }

    const user = await storage.getUser(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const hasConnectionAccess = await checkConnectionPermission(user, connection, conversationId, connectionId);

    if (!hasConnectionAccess) {
      throw new Error('You do not have permission to access this connection');
    }

    let sock = activeConnections.get(connectionId);
    if (!sock) {


      try {
        await connectToWhatsApp(connectionId, userId);
        await new Promise(resolve => setTimeout(resolve, 3000));

        sock = activeConnections.get(connectionId);
        if (!sock) {
          throw new Error(`Failed to establish WhatsApp connection for ID ${connectionId} after reconnection attempt`);
        }


      } catch (reconnectError) {
        console.error(`Failed to reconnect WhatsApp connection ${connectionId}:`, reconnectError);
        throw new Error(`No active connection found for ID ${connectionId} and reconnection failed: ${(reconnectError as any).message}`);
      }
    }

    if (!sock.user?.id) {

      throw new Error(`WhatsApp connection is not properly authenticated`);
    }

    const isGroupChat = to.endsWith('@g.us');
    let phoneNumber = to;

    if (!phoneNumber.includes('@')) {
      phoneNumber = phoneNumber.replace(/\D/g, '');
      phoneNumber = `${phoneNumber}@s.whatsapp.net`;
    }


    const fileExtension = path.extname(audioPath).toLowerCase();
    const originalBuffer = await fsPromises.readFile(audioPath);


    let mimeType = 'audio/mpeg'; // Default
    if (fileExtension === '.webm') {
      mimeType = 'audio/webm';
    } else if (fileExtension === '.ogg') {
      mimeType = 'audio/ogg';
    } else if (fileExtension === '.aac') {
      mimeType = 'audio/aac';
    } else if (fileExtension === '.m4a') {
      mimeType = 'audio/mp4';
    }

    let finalAudioPath = audioPath;
    let finalMimeType = mimeType; // Use detected MIME type
    let audioBuffer = originalBuffer;


    if (needsConversionForWhatsApp(mimeType, fileExtension)) {
      const tempDir = path.join(process.cwd(), 'temp', 'audio');
      await fsExtra.ensureDir(tempDir);

      try {
        const conversionResult = await convertAudioForWhatsAppWithFallback(
          audioPath,
          tempDir,
          path.basename(audioPath)
        );

        convertedAudioPath = conversionResult.outputPath;
        finalAudioPath = convertedAudioPath;
        audioBuffer = await fsPromises.readFile(convertedAudioPath);
        finalMimeType = conversionResult.mimeType; // Use the successful conversion format
      } catch (conversionError) {

        audioBuffer = originalBuffer;
        finalMimeType = 'audio/mpeg'; // Fallback MIME type
      }
    } else {
      finalMimeType = getWhatsAppMimeType(fileExtension.substring(1));
    }

    let sentMessageInfo;
    try {
      await simulateRecording(sock, phoneNumber);

      sentMessageInfo = await sock.sendMessage(phoneNumber, {
        audio: audioBuffer,
        mimetype: finalMimeType,
        ptt: true
      });

      await stopPresenceIndicators(sock, phoneNumber);

      updateHealthScore(connectionId, 'success');

      if (!sentMessageInfo) {
        throw new Error('Failed to send audio message: No response from WhatsApp');
      }
    } catch (sendError) {
      console.error('WhatsApp Service: Error in sock.sendMessage for audio:', sendError);
      updateHealthScore(connectionId, 'error');
      throw sendError;
    }

    let contact = null;
    let conversation = null;

    if (isGroupChat) {
      const groupJid = phoneNumber;
      conversation = await storage.getConversationByGroupJid(groupJid);

      if (!conversation) {
        const user = await storage.getUser(userId);
        const companyId = user?.companyId;

        let groupName = groupJid.split('@')[0];
        let groupMetadata = null;

        try {
          const metadata = await sock.groupMetadata(groupJid);
          groupName = metadata.subject || groupName;
          groupMetadata = {
            subject: metadata.subject,
            desc: metadata.desc,
            participants: metadata.participants,
            creation: metadata.creation,
            owner: metadata.owner
          };
        } catch (error) {

        }

        const conversationData: InsertConversation = {
          companyId: companyId,
          contactId: null,
          channelId: connectionId,
          channelType: 'whatsapp_unofficial',
          status: 'active',
          lastMessageAt: new Date(),
          isGroup: true,
          groupJid: groupJid,
          groupName: groupName,
          groupDescription: groupMetadata?.desc || null,
          groupParticipantCount: groupMetadata?.participants?.length || 0,
          groupCreatedAt: groupMetadata?.creation ? new Date(groupMetadata.creation * 1000) : new Date(),
          groupMetadata: groupMetadata
        };

        conversation = await storage.createConversation(conversationData);

      } else {
        conversation = await storage.updateConversation(conversation.id, {
          lastMessageAt: new Date()
        });
      }
    } else {
      const identifier = to.includes('@') ? to.split('@')[0] : to;
      contact = await storage.getContactByIdentifier(identifier, 'whatsapp');

      if (!contact) {
        let profilePictureUrl = null;

        try {
          profilePictureUrl = await fetchProfilePicture(connectionId, identifier);
        } catch (fetchError) {

        }

        const user = await storage.getUser(userId);
        const companyId = user?.companyId;

        const contactData: InsertContact = {
          companyId: companyId,
          name: identifier,
          phone: identifier,
          email: null,
          avatarUrl: profilePictureUrl,
          identifier,
          identifierType: 'whatsapp',
          source: 'whatsapp',
          notes: null
        };

        contact = await storage.createContact(contactData);
      }

      conversation = await storage.getConversationByContactAndChannel(
        contact.id,
        connectionId
      );

      if (!conversation) {
        const user = await storage.getUser(userId);
        const companyId = user?.companyId;

        const conversationData: InsertConversation = {
          companyId: companyId,
          contactId: contact.id,
          channelId: connectionId,
          channelType: 'whatsapp_unofficial',
          status: 'active',
          lastMessageAt: new Date(),
          isGroup: false
        };

        conversation = await storage.createConversation(conversationData);
      } else {
        conversation = await storage.updateConversation(conversation.id, {
          lastMessageAt: new Date()
        });
      }
    }


    const MEDIA_DIR = path.join(process.cwd(), 'public', 'media');
    const uniqueId = crypto.randomBytes(16).toString('hex');
    const fileExt = path.extname(finalAudioPath) || '.webm';
    const filename = `${uniqueId}${fileExt}`;

    const mediaTypeDir = path.join(MEDIA_DIR, 'audio');
    await fsExtra.ensureDir(mediaTypeDir);

    const mediaFilePath = path.join(mediaTypeDir, filename);
    await fsExtra.copy(finalAudioPath, mediaFilePath);

    const mediaUrl = `/media/audio/${filename}`;

    const whatsappMessageId = sentMessageInfo?.key?.id;
    const messageData: InsertMessage = {
      conversationId: conversation.id,
      content: 'Voice message',
      direction: 'outbound',
      type: 'audio',
      sentAt: new Date(),
      senderId: isFromBot ? null : userId,
      senderType: isFromBot ? null : 'user',
      isFromBot: isFromBot,
      status: 'sent',
      externalId: whatsappMessageId,
      groupParticipantJid: null,
      groupParticipantName: null,
      metadata: JSON.stringify({
        messageId: whatsappMessageId,
        remoteJid: sentMessageInfo?.key?.remoteJid,
        fromMe: true,
        isGroupChat,
        audioPath: finalAudioPath,
        originalPath: audioPath,
        mimeType: finalMimeType,
        ...(isGroupChat && { groupJid: phoneNumber }),
        whatsappMessage: sentMessageInfo ? {
          key: sentMessageInfo.key,
          message: { audioMessage: { mimetype: finalMimeType, ptt: true } },
          messageTimestamp: Date.now()
        } : null
      }),
      mediaUrl: mediaUrl,
    };

    const savedMessage = await storage.createMessage(messageData);


    eventEmitter.emit('messageSent', {
      message: savedMessage,
      conversation,
      contact,
    });

    return savedMessage;
  } catch (error: any) {
    console.error('Error sending WhatsApp audio message:', error);
    return null;
  } finally {

    if (convertedAudioPath) {
      try {
        await cleanupTempAudioFiles([convertedAudioPath]);
      } catch (cleanupError) {
        console.error('Error cleaning up converted audio file:', cleanupError);
      }
    }
  }
}

export async function sendWhatsAppMessage(
  connectionId: number,
  userId: number,
  to: string,
  message: string,
  isFromBot: boolean = false,
  conversationId?: number
): Promise<Message | null> {

  const startTime = Date.now();

  try {



    const [connection, user] = await Promise.all([
      storage.getChannelConnection(connectionId),
      storage.getUser(userId)
    ]);

    if (!connection) {
      throw new Error('Connection not found');
    }

    if (!user) {
      throw new Error('User not found');
    }

    const hasConnectionAccess = await checkConnectionPermission(user, connection, conversationId, connectionId);

    if (!hasConnectionAccess) {
      throw new Error('You do not have permission to access this connection');
    }

    let connectionCompanyId = connection.companyId;
    if (!connectionCompanyId) {
      const connectionOwner = await storage.getUser(connection.userId);
      if (connectionOwner) {
        connectionCompanyId = connectionOwner.companyId;
      }
    }



    let sock = activeConnections.get(connectionId);
    if (!sock) {

      if (connectionAttempts.get(connectionId)) {

        await new Promise(resolve => setTimeout(resolve, 100));
        sock = activeConnections.get(connectionId);

        if (!sock) {
          throw new Error(`Connection attempt in progress for ID ${connectionId}, please retry`);
        }
      } else {
        try {
          await connectToWhatsApp(connectionId, userId);


          await new Promise(resolve => setTimeout(resolve, 500));

          sock = activeConnections.get(connectionId);
          if (!sock) {
            throw new Error(`Failed to establish WhatsApp connection for ID ${connectionId} after reconnection attempt`);
          }

        } catch (reconnectError) {
          console.error(`Failed to reconnect WhatsApp connection ${connectionId}:`, reconnectError);
          throw new Error(`No active connection found for ID ${connectionId} and reconnection failed: ${(reconnectError as any).message}`);
        }
      }
    }


    if (!sock.user?.id) {
      throw new Error(`WhatsApp connection is not properly authenticated`);
    }


    const connState = getConnectionState(connectionId);
    if (connState.status === 'disconnected' || connState.status === 'error') {
      throw new Error(`WhatsApp connection is not ready (status: ${connState.status})`);
    }

    const isGroupChat = to.endsWith('@g.us');
    let phoneNumber = to;

    if (!phoneNumber.includes('@')) {
      phoneNumber = phoneNumber.replace(/\D/g, '');
      phoneNumber = `${phoneNumber}@s.whatsapp.net`;
    }



    let sentMessageInfo;
    try {


      const chunks = splitMessage(message);

      if (chunks.length > 1) {

        const sentMessages = await sendMessageWithSplitting(sock, phoneNumber, message, connectionId);
        sentMessageInfo = sentMessages[0];


      } else {

        const typingPromise = simulateTyping(sock, phoneNumber, message);
        const messagePromise = sock.sendMessage(phoneNumber, { text: message });


        sentMessageInfo = await messagePromise;


        await stopPresenceIndicators(sock, phoneNumber);


        typingPromise.catch(error => {
          console.warn('Typing simulation error (non-blocking):', error);
        });
      }


      updateHealthScore(connectionId, 'success');

      if (!sentMessageInfo) {

      }
    } catch (sendError) {
      console.error('WhatsApp Service: Error in sock.sendMessage:', sendError);
      updateHealthScore(connectionId, 'error');
      throw sendError;
    }

    let contact = null;
    let conversation = null;

    if (isGroupChat) {
      const groupJid = phoneNumber;

      conversation = await storage.getConversationByGroupJid(groupJid);

      if (!conversation) {
        const user = await storage.getUser(userId);
        const companyId = user?.companyId;

        let groupName = groupJid.split('@')[0];
        let groupMetadata = null;

        try {
          const metadata = await sock.groupMetadata(groupJid);
          groupName = metadata.subject || groupName;
          groupMetadata = {
            subject: metadata.subject,
            desc: metadata.desc,
            participants: metadata.participants,
            creation: metadata.creation,
            owner: metadata.owner
          };
        } catch (error) {

        }

        const conversationData: InsertConversation = {
          companyId: companyId,
          contactId: null,
          channelId: connectionId,
          channelType: 'whatsapp_unofficial',
          status: 'active',
          lastMessageAt: new Date(),
          isGroup: true,
          groupJid: groupJid,
          groupName: groupName,
          groupDescription: groupMetadata?.desc || null,
          groupParticipantCount: groupMetadata?.participants?.length || 0,
          groupCreatedAt: groupMetadata?.creation ? new Date(groupMetadata.creation * 1000) : new Date(),
          groupMetadata: groupMetadata
        };

        conversation = await storage.createConversation(conversationData);


        if ((global as any).broadcastToAllClients) {
          (global as any).broadcastToAllClients({
            type: 'newConversation',
            data: {
              ...conversation,
              contact: null
            }
          });
        }
      } else {
        conversation = await storage.updateConversation(conversation.id, {
          lastMessageAt: new Date()
        });
      }
    } else {
      const identifier = to.includes('@') ? to.split('@')[0] : to;
      contact = await storage.getContactByIdentifier(identifier, 'whatsapp');

      if (!contact) {
        let profilePictureUrl = null;

        try {
          profilePictureUrl = await fetchProfilePicture(connectionId, identifier);

          if (profilePictureUrl) {

          }
        } catch (fetchError) {

        }

        const user = await storage.getUser(userId);
        const companyId = user?.companyId;

        const contactData: InsertContact = {
          companyId: companyId,
          name: identifier,
          phone: identifier,
          email: null,
          avatarUrl: profilePictureUrl,
          identifier,
          identifierType: 'whatsapp',
          source: 'whatsapp',
          notes: null
        };

        contact = await storage.createContact(contactData);
      }

      conversation = await storage.getConversationByContactAndChannel(
        contact.id,
        connectionId
      );

      if (!conversation) {
        const user = await storage.getUser(userId);
        const companyId = user?.companyId;

        const conversationData: InsertConversation = {
          companyId: companyId,
          contactId: contact.id,
          channelId: connectionId,
          channelType: 'whatsapp_unofficial',
          status: 'active',
          lastMessageAt: new Date(),
          isGroup: false
        };

        conversation = await storage.createConversation(conversationData);

        if ((global as any).broadcastToAllClients) {
          (global as any).broadcastToAllClients({
            type: 'newConversation',
            data: {
              ...conversation,
              contact
            }
          });
        }
      } else {
        conversation = await storage.updateConversation(conversation.id, {
          lastMessageAt: new Date()
        });
      }
    }

    const whatsappMessageId = sentMessageInfo?.key?.id;
    if (whatsappMessageId) {
      const existingMessage = await storage.getMessageByWhatsAppId(conversation.id, whatsappMessageId);
      if (existingMessage) {

        return existingMessage;
      }
    }

    const messageData: InsertMessage = {
      conversationId: conversation.id,
      content: message,
      direction: 'outbound',
      type: 'text',
      sentAt: new Date(),
      senderId: isFromBot ? null : userId,
      senderType: isFromBot ? null : 'user',
      isFromBot: isFromBot,
      status: 'sent',
      externalId: whatsappMessageId,

      groupParticipantJid: null,
      groupParticipantName: null,

      metadata: JSON.stringify({
        messageId: whatsappMessageId,
        remoteJid: sentMessageInfo?.key?.remoteJid,
        fromMe: true,
        isGroupChat,
        ...(isGroupChat && { groupJid: phoneNumber }),
        whatsappMessage: sentMessageInfo ? {
          key: sentMessageInfo.key,
          message: { conversation: message },
          messageTimestamp: Date.now()
        } : null
      }),
      mediaUrl: null,
    };

    if (isFromBot && whatsappMessageId) {
      const existingMessage = await storage.getMessageByWhatsAppId(conversation.id, whatsappMessageId);
      if (existingMessage) {

        return existingMessage;
      }
    }

    if (isFromBot) {
      const recentMessages = await storage.getMessagesByConversationPaginated(conversation.id, 5, 0);
      const duplicateMessage = recentMessages.find(m =>
        m.content === message &&
        m.direction === 'outbound' &&
        m.isFromBot === true &&
        m.createdAt &&
        Math.abs(new Date().getTime() - new Date(m.createdAt).getTime()) < 30000
      );

      if (duplicateMessage) {
        return duplicateMessage;
      }
    }

    let savedMessage;
    try {
      savedMessage = await storage.createMessage(messageData);

    } catch (error: any) {
      if (error.message?.includes('duplicate') || error.message?.includes('unique')) {

        if (whatsappMessageId) {
          const existingMessage = await storage.getMessageByWhatsAppId(conversation.id, whatsappMessageId);
          if (existingMessage) {

            return existingMessage;
          }
        }
      }
      throw error;
    }

    eventEmitter.emit('messageSent', {
      message: savedMessage,
      conversation,
      contact,
    });


    const deliveryTime = Date.now() - startTime;
    if (deliveryTime > 2000) {
      console.warn(`WhatsApp message delivery took ${deliveryTime}ms (connectionId: ${connectionId})`);
    } else {
      console.log(`WhatsApp message delivered in ${deliveryTime}ms (connectionId: ${connectionId})`);
    }

    return savedMessage;
  } catch (error: any) {
    const deliveryTime = Date.now() - startTime;
    console.error(`Error sending WhatsApp message after ${deliveryTime}ms:`, error);
    return null;
  }
}

/**
 * Alias for sendWhatsAppMessage to maintain compatibility with flow executor
 * This ensures existing code that calls sendMessage() will work properly
 */
export const sendMessage = sendWhatsAppMessage;

/**
 * Alias for sendWhatsAppAudioMessage to maintain compatibility with flow executor
 * This ensures existing code that calls sendAudioMessage() will work properly
 */
export const sendAudioMessage = sendWhatsAppAudioMessage;

/**
 * Alias for sendWhatsAppMediaMessage to maintain compatibility with flow executor
 * This ensures existing code that calls sendMedia() will work properly
 */
export const sendMedia = sendWhatsAppMediaMessage;

/**
 * Delete a WhatsApp message for everyone using Baileys
 * @param connectionId WhatsApp connection ID
 * @param userId User ID of the sender
 * @param to Recipient phone number (remoteJid)
 * @param messageKey The message key object containing remoteJid, fromMe, and id
 * @returns Success status and any error message
 */
export async function deleteWhatsAppMessage(
  connectionId: number,
  userId: number,
  to: string,
  messageKey: { remoteJid?: string; fromMe?: boolean; id: string },
  conversationId?: number
): Promise<{ success: boolean; error?: string }> {
  try {
    const connection = await storage.getChannelConnection(connectionId);
    if (!connection) {
      return { success: false, error: 'Connection not found' };
    }

    const user = await storage.getUser(userId);
    if (!user) {
      return { success: false, error: 'User not found' };
    }

    const hasConnectionAccess = await checkConnectionPermission(user, connection, conversationId, connectionId);

    if (!hasConnectionAccess) {
      return { success: false, error: 'You do not have permission to access this connection' };
    }

    const sock = activeConnections.get(connectionId);
    if (!sock) {
      return { success: false, error: 'WhatsApp connection not found or not active' };
    }

    const isGroupChat = to.includes('@g.us');
    let recipient = to;

    if (!isGroupChat) {
      let phoneNumber = to.replace(/[^0-9]/g, '');
      if (!phoneNumber.includes('@')) {
        phoneNumber = phoneNumber + '@s.whatsapp.net';
      }
      recipient = phoneNumber;
    }

    const fullMessageKey = {
      remoteJid: messageKey.remoteJid || recipient,
      fromMe: messageKey.fromMe !== undefined ? messageKey.fromMe : true,
      id: messageKey.id
    };



    await sock.sendMessage(recipient, {
      delete: fullMessageKey
    });


    return { success: true };
  } catch (error: any) {
    console.error('Error deleting WhatsApp message:', error);

    if (error.message?.includes('too old') || error.message?.includes('time limit')) {
      return {
        success: false,
        error: 'Message is too old to be deleted. WhatsApp only allows deletion within 72 minutes of sending.'
      };
    }

    if (error.message?.includes('not found') || error.message?.includes('does not exist')) {
      return {
        success: false,
        error: 'Message not found or already deleted.'
      };
    }

    if (error.message?.includes('permission') || error.message?.includes('unauthorized')) {
      return {
        success: false,
        error: 'You do not have permission to delete this message.'
      };
    }

    if (error.message?.includes('group') && error.message?.includes('admin')) {
      return {
        success: false,
        error: 'Only group admins can delete messages in this group.'
      };
    }

    return {
      success: false,
      error: error.message || 'Failed to delete message from WhatsApp'
    };
  }
}

/**
 * Send a quoted message (reply) to WhatsApp using Baileys
 * @param connectionId WhatsApp connection ID
 * @param userId User ID of the sender
 * @param to Recipient phone number
 * @param quotedMessageData Object containing text and quoted message
 * @returns Saved message or null if failed
 */
export async function sendQuotedMessage(
  connectionId: number,
  userId: number,
  to: string,
  quotedMessageData: { text: string; quoted: any },
  isFromBot: boolean = false,
  conversationId?: number
): Promise<Message | null> {
  try {
    const connection = await storage.getChannelConnection(connectionId);
    if (!connection) {
      throw new Error('Connection not found');
    }

    const user = await storage.getUser(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const hasConnectionAccess = await checkConnectionPermission(user, connection, conversationId, connectionId);

    if (!hasConnectionAccess) {
      throw new Error('You do not have permission to access this connection');
    }

    let sock = activeConnections.get(connectionId);
    if (!sock) {


      try {
        await connectToWhatsApp(connectionId, userId);
        await new Promise(resolve => setTimeout(resolve, 3000));

        sock = activeConnections.get(connectionId);
        if (!sock) {
          throw new Error(`Failed to establish WhatsApp connection for ID ${connectionId} after reconnection attempt`);
        }


      } catch (reconnectError) {
        console.error(`Failed to reconnect WhatsApp connection ${connectionId}:`, reconnectError);
        throw new Error(`No active connection found for ID ${connectionId} and reconnection failed: ${(reconnectError as any).message}`);
      }
    }

    if (!sock.user?.id) {

      throw new Error(`WhatsApp connection is not properly authenticated`);
    }

    const isGroupChat = to.endsWith('@g.us');
    let phoneNumber = to;

    if (!phoneNumber.includes('@')) {
      phoneNumber = phoneNumber.replace(/\D/g, '');
      phoneNumber = `${phoneNumber}@s.whatsapp.net`;
    }



    if (!quotedMessageData.quoted || !quotedMessageData.quoted.key) {
      throw new Error('Invalid quoted message object - missing key');
    }

    const messageText = quotedMessageData.text?.trim();
    if (!messageText) {
      throw new Error('Message text cannot be empty');
    }

    await simulateTyping(sock, phoneNumber, messageText);

    const sentMessageInfo = await sock.sendMessage(phoneNumber, {
      text: messageText
    }, {
      quoted: quotedMessageData.quoted
    });

    await stopPresenceIndicators(sock, phoneNumber);

    let contact = null;
    let conversation = null;

    if (isGroupChat) {
      const groupJid = phoneNumber;

      conversation = await storage.getConversationByGroupJid(groupJid);

      if (!conversation) {
        const user = await storage.getUser(userId);
        const companyId = user?.companyId;

        let groupName = groupJid.split('@')[0];
        let groupMetadata = null;

        try {
          const metadata = await sock.groupMetadata(groupJid);
          groupName = metadata.subject || groupName;
          groupMetadata = {
            subject: metadata.subject,
            desc: metadata.desc,
            participants: metadata.participants,
            creation: metadata.creation,
            owner: metadata.owner
          };
        } catch (error) {

        }

        const conversationData: InsertConversation = {
          companyId: companyId,
          contactId: null,
          channelId: connectionId,
          channelType: 'whatsapp_unofficial',
          status: 'active',
          lastMessageAt: new Date(),
          isGroup: true,
          groupJid: groupJid,
          groupName: groupName,
          groupDescription: groupMetadata?.desc || null,
          groupParticipantCount: groupMetadata?.participants?.length || 0,
          groupCreatedAt: groupMetadata?.creation ? new Date(groupMetadata.creation * 1000) : new Date(),
          groupMetadata: groupMetadata
        };

        conversation = await storage.createConversation(conversationData);


        if ((global as any).broadcastToAllClients) {
          (global as any).broadcastToAllClients({
            type: 'newConversation',
            data: {
              ...conversation,
              contact: null
            }
          });
        }
      } else {
        conversation = await storage.updateConversation(conversation.id, {
          lastMessageAt: new Date()
        });
      }
    } else {
      const identifier = to.includes('@') ? to.split('@')[0] : to;
      contact = await storage.getContactByIdentifier(identifier, 'whatsapp');

      if (!contact) {
        let profilePictureUrl = null;

        try {
          profilePictureUrl = await fetchProfilePicture(connectionId, identifier);
          if (profilePictureUrl) {

          }
        } catch (fetchError) {

        }

        const user = await storage.getUser(userId);
        const companyId = user?.companyId;

        const contactData: InsertContact = {
          companyId: companyId,
          name: identifier,
          phone: identifier,
          email: null,
          avatarUrl: profilePictureUrl,
          identifier,
          identifierType: 'whatsapp',
          source: 'whatsapp',
          notes: null
        };

        contact = await storage.createContact(contactData);
      }

      conversation = await storage.getConversationByContactAndChannel(
        contact.id,
        connectionId
      );

      if (!conversation) {
        const user = await storage.getUser(userId);
        const companyId = user?.companyId;

        const conversationData: InsertConversation = {
          companyId: companyId,
          contactId: contact.id,
          channelId: connectionId,
          channelType: 'whatsapp_unofficial',
          status: 'active',
          lastMessageAt: new Date(),
          isGroup: false
        };

        conversation = await storage.createConversation(conversationData);

        if ((global as any).broadcastToAllClients) {
          (global as any).broadcastToAllClients({
            type: 'newConversation',
            data: {
              ...conversation,
              contact
            }
          });
        }
      } else {
        conversation = await storage.updateConversation(conversation.id, {
          lastMessageAt: new Date()
        });
      }
    }

    const whatsappMessageId = sentMessageInfo?.key?.id;
    if (whatsappMessageId) {
      const existingMessage = await storage.getMessageByWhatsAppId(conversation.id, whatsappMessageId);
      if (existingMessage) {

        return existingMessage;
      }
    }

    const messageData: InsertMessage = {
      conversationId: conversation.id,
      content: quotedMessageData.text,
      direction: 'outbound',
      type: 'text',
      sentAt: new Date(),
      senderId: isFromBot ? null : userId,
      senderType: isFromBot ? null : 'user',
      isFromBot: isFromBot,
      status: 'sent',
      externalId: whatsappMessageId,

      groupParticipantJid: null,
      groupParticipantName: null,

      metadata: JSON.stringify({
        messageId: whatsappMessageId,
        remoteJid: sentMessageInfo?.key?.remoteJid,
        fromMe: true,
        isGroupChat,
        isQuotedMessage: true,
        quotedMessageId: quotedMessageData.quoted?.key?.id,
        ...(isGroupChat && { groupJid: phoneNumber }),
        whatsappMessage: sentMessageInfo ? {
          key: sentMessageInfo.key,
          message: { conversation: quotedMessageData.text },
          messageTimestamp: Date.now()
        } : null
      }),
      mediaUrl: null,
    };

    let savedMessage;
    try {
      savedMessage = await storage.createMessage(messageData);

    } catch (error: any) {
      if (error.message?.includes('duplicate') || error.message?.includes('unique')) {

        if (whatsappMessageId) {
          const existingMessage = await storage.getMessageByWhatsAppId(conversation.id, whatsappMessageId);
          if (existingMessage) {

            return existingMessage;
          }
        }
      }
      throw error;
    }

    eventEmitter.emit('messageSent', {
      message: savedMessage,
      conversation,
      contact,
    });

    return savedMessage;
  } catch (error: any) {
    console.error('Error sending WhatsApp quoted message:', error);
    return null;
  }
}

/**
 * Get all active WhatsApp connections
 */
export function getActiveConnections(): number[] {
  return Array.from(activeConnections.keys());
}

/**
 * Check if a WhatsApp connection is active
 */
/**
 * Check if a WhatsApp connection is active and ready to send messages
 * @param connectionId The ID of the connection to check
 * @returns True if the connection is active and ready, false otherwise
 */
/**
 * Check if a WhatsApp connection is active and ready to send messages
 * Based on Baileys documentation and testing, this properly verifies connection status
 *
 * @param connectionId The ID of the connection to check
 * @returns True if the connection is active and ready, false otherwise
 */
/**
 * Check if a WhatsApp connection is active and ready to send messages
 * Based on Baileys documentation and testing, this properly verifies connection status
 *
 * @param connectionId The ID of the connection to check
 * @returns True if the connection is active and ready, false otherwise
 */
export function isConnectionActive(connectionId: number): boolean {
  const sock = activeConnections.get(connectionId);

  if (!sock) {

    return false;
  }

  try {
    const hasUser = !!sock.user?.id;

    const hasAuthState = !!sock.authState;

    const hasKeys = !!sock.authState?.keys;



    return hasUser && hasAuthState && hasKeys;
  } catch (error) {
    console.error(`Error checking WhatsApp connection status for ID ${connectionId}:`, error);
    return false;
  }
}

/**
 * Subscribe to WhatsApp events
 */
export function subscribeToWhatsAppEvents(
  event: string,
  callback: (data: any) => void
): () => void {
  eventEmitter.on(event, callback);
  return () => eventEmitter.off(event, callback);
}

/**
 * Send media message to WhatsApp
 * @param connectionId WhatsApp connection ID
 * @param userId User ID of the sender
 * @param to Recipient phone number
 * @param mediaType Type of media ('image', 'video', 'audio', 'document')
 * @param filePath Path to the file on the server
 * @param caption Optional caption for the media
 * @param fileName Optional filename for documents
 * @returns Saved message or null if failed
 */
export async function sendWhatsAppMediaMessage(
  connectionId: number,
  userId: number,
  to: string,
  mediaType: 'image' | 'video' | 'audio' | 'document',
  filePath: string,
  caption?: string,
  fileName?: string,
  isFromBot: boolean = false,
  conversationId?: number
): Promise<Message | null> {
  try {
    const connection = await storage.getChannelConnection(connectionId);
    if (!connection) {
      throw new Error('Connection not found');
    }

    const user = await storage.getUser(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const hasConnectionAccess = await checkConnectionPermission(user, connection, conversationId, connectionId);

    if (!hasConnectionAccess) {
      throw new Error('You do not have permission to access this connection');
    }

    let sock = activeConnections.get(connectionId);
    if (!sock) {


      try {
        await connectToWhatsApp(connectionId, userId);

        await new Promise(resolve => setTimeout(resolve, 3000));

        sock = activeConnections.get(connectionId);
        if (!sock) {
          throw new Error(`Failed to establish WhatsApp connection for ID ${connectionId} after reconnection attempt`);
        }


      } catch (reconnectError) {
        console.error(`Failed to reconnect WhatsApp connection ${connectionId}:`, reconnectError);
        throw new Error(`No active connection found for ID ${connectionId} and reconnection failed: ${(reconnectError as any).message}`);
      }
    }

    if (!sock.user?.id) {

      throw new Error(`WhatsApp connection is not properly authenticated`);
    }

    const isGroupChat = to.endsWith('@g.us');
    let phoneNumber = to;

    if (!phoneNumber.includes('@')) {
      phoneNumber = phoneNumber.replace(/\D/g, '');
      phoneNumber = `${phoneNumber}@s.whatsapp.net`;
    }



    const resolvedFilePath = resolveMediaPath(filePath);
    let fileContent: Buffer;

    if (resolvedFilePath.startsWith('http://') || resolvedFilePath.startsWith('https://')) {
      try {
        const response = await axios.get(resolvedFilePath, { responseType: 'arraybuffer' });
        fileContent = Buffer.from(response.data);
      } catch (downloadError: any) {
        console.error('Error downloading media from URL:', downloadError);
        throw new Error(`Failed to download media from URL: ${resolvedFilePath}. Error: ${downloadError.message}`);
      }
    } else {
      if (!await fsExtra.pathExists(resolvedFilePath)) {
        throw new Error(`Media file not found at path: ${resolvedFilePath}`);
      }
      fileContent = await fsExtra.readFile(resolvedFilePath);
    }
    let messageContent: any = {};

    switch (mediaType) {
      case 'image':
        messageContent = {
          image: fileContent,
          caption: caption || '',
        };
        break;
      case 'video':
        messageContent = {
          video: fileContent,
          caption: caption || '',
        };
        break;
      case 'audio':


        return await sendWhatsAppAudioMessage(
          connectionId,
          userId,
          to,
          resolvedFilePath,
          isFromBot,
          conversationId
        );
      case 'document':
        messageContent = {
          document: fileContent,
          mimetype: 'application/octet-stream',
          fileName: fileName || path.basename(resolvedFilePath),
          caption: caption || '',
        };
        break;
      default:
        throw new Error(`Unsupported media type: ${mediaType}`);
    }


    const simulationMessage = caption || `Sending ${mediaType}...`;
    await simulateTyping(sock, phoneNumber, simulationMessage);

    const sentMessageInfo = await sock.sendMessage(phoneNumber, messageContent);


    await stopPresenceIndicators(sock, phoneNumber);

    let contact = null;
    let conversation = null;

    if (isGroupChat) {
      const groupJid = phoneNumber;

      conversation = await storage.getConversationByGroupJid(groupJid);

      if (!conversation) {
        const user = await storage.getUser(userId);
        const companyId = user?.companyId;

        let groupName = groupJid.split('@')[0];
        let groupMetadata = null;

        try {
          const metadata = await sock.groupMetadata(groupJid);
          groupName = metadata.subject || groupName;
          groupMetadata = {
            subject: metadata.subject,
            desc: metadata.desc,
            participants: metadata.participants,
            creation: metadata.creation,
            owner: metadata.owner
          };
        } catch (error) {

        }

        const conversationData: InsertConversation = {
          companyId: companyId,
          contactId: null,
          channelId: connectionId,
          channelType: 'whatsapp_unofficial',
          status: 'active',
          lastMessageAt: new Date(),
          isGroup: true,
          groupJid: groupJid,
          groupName: groupName,
          groupDescription: groupMetadata?.desc || null,
          groupParticipantCount: groupMetadata?.participants?.length || 0,
          groupCreatedAt: groupMetadata?.creation ? new Date(groupMetadata.creation * 1000) : new Date(),
          groupMetadata: groupMetadata
        };

        conversation = await storage.createConversation(conversationData);


        if ((global as any).broadcastToAllClients) {
          (global as any).broadcastToAllClients({
            type: 'newConversation',
            data: {
              ...conversation,
              contact: null
            }
          });
        }
      } else {
        conversation = await storage.updateConversation(conversation.id, {
          lastMessageAt: new Date()
        });
      }
    } else {
      const identifier = to.includes('@') ? to.split('@')[0] : to;
      contact = await storage.getContactByIdentifier(identifier, 'whatsapp');

      if (!contact) {
        let profilePictureUrl = null;

        try {
          profilePictureUrl = await fetchProfilePicture(connectionId, identifier);

          if (profilePictureUrl) {

          }
        } catch (fetchError) {

        }

        const user = await storage.getUser(userId);
        const companyId = user?.companyId;

        const contactData: InsertContact = {
          companyId: companyId,
          name: identifier,
          phone: identifier,
          email: null,
          avatarUrl: profilePictureUrl,
          identifier,
          identifierType: 'whatsapp',
          source: 'whatsapp',
          notes: null
        };

        contact = await storage.createContact(contactData);
      }

      conversation = await storage.getConversationByContactAndChannel(
        contact.id,
        connectionId
      );

      if (!conversation) {
        const user = await storage.getUser(userId);
        const companyId = user?.companyId;

        const conversationData: InsertConversation = {
          companyId: companyId,
          contactId: contact.id,
          channelId: connectionId,
          channelType: 'whatsapp_unofficial',
          status: 'active',
          lastMessageAt: new Date(),
          isGroup: false
        };

        conversation = await storage.createConversation(conversationData);

        if ((global as any).broadcastToAllClients) {
          (global as any).broadcastToAllClients({
            type: 'newConversation',
            data: {
              ...conversation,
              contact
            }
          });
        }
      } else {
        conversation = await storage.updateConversation(conversation.id, {
          lastMessageAt: new Date()
        });
      }
    }

    const uniqueId = crypto.createHash('md5').update(`${Date.now()}`).digest('hex');

    let fileExt = '';
    if (resolvedFilePath.startsWith('http://') || resolvedFilePath.startsWith('https://')) {
      const urlPath = new URL(resolvedFilePath).pathname;
      fileExt = path.extname(urlPath);
      if (!fileExt) {
        switch (mediaType) {
          case 'image': fileExt = '.jpg'; break;
          case 'video': fileExt = '.mp4'; break;
          case 'document': fileExt = '.pdf'; break;
          default: fileExt = '.bin';
        }
      }
    } else {
      fileExt = path.extname(resolvedFilePath);
    }

    const filename = `${uniqueId}${fileExt}`;

    const mediaTypeDir = path.join(MEDIA_DIR, mediaType);
    await fsExtra.ensureDir(mediaTypeDir);

    const mediaFilePath = path.join(mediaTypeDir, filename);

    if (resolvedFilePath.startsWith('http://') || resolvedFilePath.startsWith('https://')) {
      await fsExtra.writeFile(mediaFilePath, fileContent);
    } else {
      await fsExtra.copy(resolvedFilePath, mediaFilePath);
    }

    const mediaUrl = `/media/${mediaType}/${filename}`;

    mediaCache.set(uniqueId, mediaUrl);

    const whatsappMessageId = sentMessageInfo?.key?.id;
    if (whatsappMessageId) {
      const existingMessage = await storage.getMessageByWhatsAppId(conversation.id, whatsappMessageId);
      if (existingMessage) {

        return existingMessage;
      }
    }

    const messageData: InsertMessage = {
      conversationId: conversation.id,
      content: caption || '',
      direction: 'outbound',
      type: mediaType,
      sentAt: new Date(),
      senderId: isFromBot ? null : userId,
      senderType: isFromBot ? null : 'user',
      isFromBot: isFromBot,
      status: 'sent',
      externalId: whatsappMessageId,

      groupParticipantJid: null,
      groupParticipantName: null,

      metadata: JSON.stringify({
        messageId: whatsappMessageId,
        remoteJid: sentMessageInfo?.key?.remoteJid,
        fromMe: true,
        isGroupChat,
        ...(isGroupChat && { groupJid: phoneNumber }),
        originalFileName: fileName || path.basename(resolvedFilePath),
        originalPath: filePath,
        resolvedPath: resolvedFilePath,
        mediaUrl,
        whatsappMessage: sentMessageInfo ? {
          key: sentMessageInfo.key,
          message: { [mediaType]: { caption: caption || '' } },
          messageTimestamp: Date.now()
        } : null
      }),
      mediaUrl,
    };

    let savedMessage;
    try {
      savedMessage = await storage.createMessage(messageData);

    } catch (error: any) {
      if (error.message?.includes('duplicate') || error.message?.includes('unique')) {

        if (whatsappMessageId) {
          const existingMessage = await storage.getMessageByWhatsAppId(conversation.id, whatsappMessageId);
          if (existingMessage) {

            return existingMessage;
          }
        }
      }
      throw error;
    }

    eventEmitter.emit('messageSent', {
      message: savedMessage,
      conversation,
      contact,
    });

    return savedMessage;
  } catch (error: any) {
    console.error('Error sending WhatsApp media message:', error);
    return null;
  }
}

/**
 * Fetch profile picture for a WhatsApp contact
 * @param connectionId The ID of the WhatsApp connection
 * @param phoneNumber The phone number of the contact
 * @param downloadAndSave Whether to download and save the image locally (default: true)
 * @returns URL to the profile picture or null if not available
 */
export async function fetchProfilePicture(connectionId: number, phoneNumber: string, downloadAndSave: boolean = true): Promise<string | null> {
  try {


    let sock = activeConnections.get(connectionId);
    if (!sock) {


      try {
        const connection = await storage.getChannelConnection(connectionId);
        if (!connection) {
          console.error(`Connection ${connectionId} not found in database`);
          return null;
        }

        await connectToWhatsApp(connectionId, connection.userId);

        await new Promise(resolve => setTimeout(resolve, 3000));

        sock = activeConnections.get(connectionId);
        if (!sock) {
          console.error(`Failed to establish WhatsApp connection for ID ${connectionId} after reconnection attempt`);
          return null;
        }


      } catch (reconnectError) {
        console.error(`Failed to reconnect WhatsApp connection ${connectionId}:`, reconnectError);
        return null;
      }
    }

    let jid = phoneNumber;

    const cleanPhoneNumber = phoneNumber.replace(/\D/g, '');

    if (!jid.includes('@')) {
      jid = `${cleanPhoneNumber}@s.whatsapp.net`;
    }



    try {
      const ppUrl = await sock.profilePictureUrl(jid, 'image');

      if (ppUrl) {


        if (!downloadAndSave) {

          return ppUrl;
        }

        const profilePicsDir = path.join(MEDIA_DIR, 'profile_pictures');
        await fsExtra.ensureDir(profilePicsDir);

        const sanitizedPhone = cleanPhoneNumber;
        const existingFiles = await fsExtra.readdir(profilePicsDir).catch(() => []);
        const existingFile = existingFiles.find(file => file.startsWith(`${sanitizedPhone}_`));

        if (existingFile) {
          const existingUrl = `/media/profile_pictures/${existingFile}`;

          return existingUrl;
        }

        const timestamp = Date.now();
        const filename = `${sanitizedPhone}_${timestamp}.jpg`;
        const filepath = path.join(profilePicsDir, filename);

        try {
          const response = await fetch(ppUrl);

          if (!response.ok) {
            console.error(`Failed to download profile picture: ${response.status} ${response.statusText}`);

            return ppUrl;
          }

          const buffer = await response.arrayBuffer();
          const nodeBuffer = Buffer.from(buffer);

          await fsExtra.writeFile(filepath, nodeBuffer);



          const publicUrl = `/media/profile_pictures/${filename}`;


          return publicUrl;
        } catch (downloadError) {
          console.error(`Error downloading profile picture for ${phoneNumber}:`, downloadError);

          return ppUrl;
        }
      } else {

        return null;
      }
    } catch (apiError: any) {
      if (apiError.message?.includes('item-not-found') || apiError.message?.includes('not-authorized')) {

      } else {
        console.error(`API error fetching profile picture for ${phoneNumber}:`, apiError.message);
      }
      return null;
    }
  } catch (error: any) {
    console.error(`Unexpected error fetching profile picture for ${phoneNumber}:`, error);
    return null;
  }
}

/**
 * Fetch group profile picture for a WhatsApp group
 * @param connectionId The ID of the WhatsApp connection
 * @param groupJid The JID of the group
 * @param downloadAndSave Whether to download and save the image locally (default: true)
 * @returns URL to the group profile picture or null if not available
 */
export async function fetchGroupProfilePicture(connectionId: number, groupJid: string, downloadAndSave: boolean = true): Promise<string | null> {
  try {




    const sock = activeConnections.get(connectionId);
    if (!sock) {
      console.error(`[fetchGroupProfilePicture] No active connection found for ID ${connectionId}`);
      return null;
    }



    try {
      const ppUrl = await sock.profilePictureUrl(groupJid, 'image');

      if (ppUrl) {


        if (!downloadAndSave) {

          return ppUrl;
        }

        const profilePicsDir = path.join(MEDIA_DIR, 'group_pictures');
        await fsExtra.ensureDir(profilePicsDir);

        const sanitizedGroupId = groupJid.replace(/[^a-zA-Z0-9]/g, '_');
        const timestamp = Date.now();
        const filename = `${sanitizedGroupId}_${timestamp}.jpg`;
        const filepath = path.join(profilePicsDir, filename);

        const existingFiles = await fsExtra.readdir(profilePicsDir).catch(() => []);
        const oldFiles = existingFiles.filter(file => file.startsWith(`${sanitizedGroupId}_`));
        for (const oldFile of oldFiles) {
          await fsExtra.remove(path.join(profilePicsDir, oldFile)).catch(() => {});
        }

        try {
          const response = await fetch(ppUrl);

          if (!response.ok) {
            console.error(`Failed to download group profile picture: ${response.status} ${response.statusText}`);

            return ppUrl;
          }

          const buffer = await response.arrayBuffer();
          const nodeBuffer = Buffer.from(buffer);

          await fsExtra.writeFile(filepath, nodeBuffer);



          const publicUrl = `/media/group_pictures/${filename}`;


          return publicUrl;
        } catch (downloadError) {
          console.error(`Error downloading group profile picture for ${groupJid}:`, downloadError);

          return ppUrl;
        }
      } else {

        return null;
      }
    } catch (apiError: any) {
      if (apiError.message?.includes('item-not-found') || apiError.message?.includes('not-authorized')) {

      } else {
        console.error(`API error fetching group profile picture for ${groupJid}:`, apiError.message);
      }
      return null;
    }
  } catch (error: any) {
    console.error(`Unexpected error fetching group profile picture for ${groupJid}:`, error);
    return null;
  }
}

/**
 * Get profile picture URL for a WhatsApp contact (without downloading)
 * @param connectionId The ID of the WhatsApp connection
 * @param phoneNumber The phone number of the contact
 * @returns Direct URL to the profile picture or null if not available
 */
export async function getProfilePictureUrl(connectionId: number, phoneNumber: string): Promise<string | null> {
  try {


    const sock = activeConnections.get(connectionId);
    if (!sock) {
      console.error(`No active connection found for ID ${connectionId}`);
      return null;
    }

    const cleanPhoneNumber = phoneNumber.replace(/\D/g, '');
    const jid = `${cleanPhoneNumber}@s.whatsapp.net`;



    try {
      const ppUrl = await sock.profilePictureUrl(jid, 'image');

      if (ppUrl) {

        return ppUrl;
      } else {

        return null;
      }
    } catch (apiError: any) {
      if (apiError.message?.includes('item-not-found') || apiError.message?.includes('not-authorized')) {

      } else {
        console.error(`API error fetching profile picture URL for ${phoneNumber}:`, apiError.message);
      }
      return null;
    }
  } catch (error: any) {
    console.error(`Unexpected error fetching profile picture URL for ${phoneNumber}:`, error);
    return null;
  }
}

/**
 * Fetch profile pictures for multiple participants in a group
 * @param connectionId The ID of the WhatsApp connection
 * @param participantJids Array of participant JIDs
 * @param downloadAndSave Whether to download and save images locally (default: false for performance)
 * @returns Map of JID to profile picture URL
 */
export async function fetchParticipantProfilePictures(
  connectionId: number,
  participantJids: string[],
  downloadAndSave: boolean = false
): Promise<Map<string, string | null>> {
  const results = new Map<string, string | null>();

  try {


    const sock = activeConnections.get(connectionId);
    if (!sock) {
      console.error(`No active connection found for ID ${connectionId}`);
      return results;
    }

    const batchSize = 5;
    for (let i = 0; i < participantJids.length; i += batchSize) {
      const batch = participantJids.slice(i, i + batchSize);

      await Promise.all(batch.map(async (jid) => {
        try {
          const phoneNumber = jid.split('@')[0];
          let profilePictureUrl: string | null = null;

          if (downloadAndSave) {
            profilePictureUrl = await fetchProfilePicture(connectionId, phoneNumber, true);
          } else {
            profilePictureUrl = await getProfilePictureUrl(connectionId, phoneNumber);
          }

          results.set(jid, profilePictureUrl);

        } catch (error) {
          console.error(`Error fetching profile picture for ${jid}:`, error);
          results.set(jid, null);
        }
      }));

      if (i + batchSize < participantJids.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }


    return results;
  } catch (error: any) {
    console.error(`Error fetching participant profile pictures:`, error);
    return results;
  }
}

/**
 * Get profile picture URL for a participant by JID (optimized for group participants)
 * @param connectionId The ID of the WhatsApp connection
 * @param participantJid The JID of the participant (e.g., "<EMAIL>")
 * @returns Direct URL to the profile picture or null if not available
 */
export async function getParticipantProfilePictureUrl(connectionId: number, participantJid: string): Promise<string | null> {
  try {


    const sock = activeConnections.get(connectionId);
    if (!sock) {
      console.error(`No active connection found for ID ${connectionId}`);
      return null;
    }

    try {
      const ppUrl = await sock.profilePictureUrl(participantJid, 'image');

      if (ppUrl) {

        return ppUrl;
      } else {

        return null;
      }
    } catch (apiError: any) {
      if (apiError.message?.includes('item-not-found') || apiError.message?.includes('not-authorized')) {

      } else {
        console.error(`API error fetching participant profile picture URL for ${participantJid}:`, apiError.message);
      }
      return null;
    }
  } catch (error: any) {
    console.error(`Unexpected error fetching participant profile picture URL for ${participantJid}:`, error);
    return null;
  }
}

/**
 * Check and recover lost WhatsApp connections
 * This function checks if database connections exist but are not in the active connections map
 */
export async function checkAndRecoverConnections(): Promise<void> {
  try {


    const allConnections = await storage.getChannelConnections(null);
    const whatsappConnections = allConnections.filter(conn =>
      (conn.channelType === 'whatsapp' || conn.channelType === 'whatsapp_unofficial') &&
      conn.status === 'active'
    );

    let recoveredCount = 0;

    for (const connection of whatsappConnections) {
      if (!activeConnections.has(connection.id)) {


        const sessionDir = path.join(SESSION_DIR, `session-${connection.id}`);
        const hasCredsFile = fs.existsSync(sessionDir) && fs.existsSync(path.join(sessionDir, 'creds.json'));

        if (hasCredsFile) {
          try {
            await connectToWhatsApp(connection.id, connection.userId);
            recoveredCount++;

          } catch (error) {
            console.error(`Failed to recover WhatsApp connection ${connection.id}:`, error);
            await storage.updateChannelConnectionStatus(connection.id, 'error');
          }
        } else {

          await storage.updateChannelConnectionStatus(connection.id, 'disconnected');
        }
      }
    }


  } catch (error) {
    console.error('Error during connection recovery check:', error);
  }
}

/**
 * Auto-reconnect all eligible WhatsApp connections on server start
 * This function should be called once during server initialization
 */
export async function autoReconnectWhatsAppSessions(): Promise<void> {
  try {


    const allConnections = await storage.getChannelConnections(null);

    const whatsappConnections = allConnections.filter(conn =>
      conn.channelType === 'whatsapp' ||
      conn.channelType === 'whatsapp_unofficial'
    );

    if (whatsappConnections.length === 0) {

      return;
    }



    const eligibleConnections = whatsappConnections.filter(conn => {
      const sessionDir = path.join(SESSION_DIR, `session-${conn.id}`);
      const sessionDirExists = fs.existsSync(sessionDir);

      const hasCredsFile = sessionDirExists && fs.existsSync(path.join(sessionDir, 'creds.json'));

      const hasValidStatus = conn.status &&
        conn.status !== 'error' &&
        conn.status !== 'logged_out' &&
        conn.status !== 'replaced';



      return hasValidStatus || hasCredsFile;
    });



    for (let i = 0; i < eligibleConnections.length; i++) {
      const connection = eligibleConnections[i];

      setTimeout(async () => {
        try {

          await connectToWhatsApp(connection.id, connection.userId);

        } catch (error) {
          console.error(`Failed to auto-reconnect WhatsApp connection ${connection.id}:`, error);
          await storage.updateChannelConnectionStatus(connection.id, 'error');
        }
      }, i * 5000);
    }
  } catch (error) {
    console.error('Error during WhatsApp auto-reconnection:', error);
  }
}

export default {
  connect: connectToWhatsApp,
  disconnect: disconnectWhatsApp,
  sendMessage: sendWhatsAppMessage,
  sendWhatsAppMessage,
  sendAudioMessage: sendWhatsAppAudioMessage,
  sendWhatsAppAudioMessage,
  sendQuotedMessage,
  sendMedia: sendWhatsAppMediaMessage,
  sendMediaMessage: sendWhatsAppMediaMessage,
  sendWhatsAppMediaMessage,
  getActiveConnections,
  isConnectionActive,
  fetchProfilePicture,
  fetchGroupProfilePicture,
  getProfilePictureUrl,
  fetchParticipantProfilePictures,
  getParticipantProfilePictureUrl,
  subscribeToEvents: subscribeToWhatsAppEvents,
  autoReconnect: autoReconnectWhatsAppSessions,
  checkAndRecover: checkAndRecoverConnections,
  configureTypingBehavior,
  getTypingConfiguration,
  configureMessageSplitting,
  getMessageSplittingConfiguration,
  configureMessageDebouncing,
  getMessageDebouncingConfiguration,
  getDebouncingStatus,
  getConnectionDiagnostics,
  cancelQueuedMessages,
  getQueueStatus: (phoneNumber: string, connectionId: number) => {
    const queueKey = getQueueKey(phoneNumber, connectionId);
    const userQueue = messageQueues.get(queueKey);
    return {
      hasQueue: !!userQueue,
      queueLength: userQueue?.length || 0,
      messages: userQueue?.map(msg => ({
        id: msg.id,
        chunksTotal: msg.chunks.length,
        currentChunkIndex: msg.currentChunkIndex,
        scheduledTimeouts: msg.timeoutIds.length,
        createdAt: msg.createdAt
      })) || []
    };
  },
  testMessageSplitting: (message: string) => {
    const chunks = splitMessage(message);
    return {
      originalLength: message.length,
      chunks: chunks.map((chunk, index) => ({
        index: index + 1,
        text: chunk,
        length: chunk.length
      })),
      totalChunks: chunks.length,
      config: MESSAGE_SPLITTING_CONFIG
    };
  }
};
