import { useEffect, useState, useRef, useCallback } from 'react';
import { useToast } from './use-toast';
import { useAuth } from './use-auth';
import { usePageVisibility } from './usePageVisibility';

type MessageHandler = (data: any) => void;

export default function useSocket(url: string) {
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState<any>(null);
  const socketRef = useRef<WebSocket | null>(null);
  const { toast } = useToast();
  const { user, isLoading } = useAuth();
  const { isVisible, wasHidden, getHiddenDuration } = usePageVisibility();
  const messageHandlers = useRef<Record<string, MessageHandler[]>>({});
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;
  const lastConnectAttemptRef = useRef(0);

  useEffect(() => {
    if (isLoading || !user) {
      return;
    }

    const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
    const wsUrl = `${protocol}//${window.location.host}${url}`;

    const socket = new WebSocket(wsUrl);
    socketRef.current = socket;

    socket.addEventListener('open', () => {
      setIsConnected(true);
      reconnectAttemptsRef.current = 0;
      lastConnectAttemptRef.current = Date.now();

      socket.send(JSON.stringify({
        type: 'authenticate',
        userId: user.id
      }));

      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }

      const heartbeatInterval = setInterval(() => {
        if (socket.readyState === WebSocket.OPEN) {
          socket.send(JSON.stringify({ type: 'ping' }));
        }
      }, 30000);

      (socket as any).heartbeatInterval = heartbeatInterval;
    });

    socket.addEventListener('close', (event) => {
      setIsConnected(false);

      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }

      if (user && reconnectAttemptsRef.current < maxReconnectAttempts) {
        reconnectAttemptsRef.current++;

        const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current - 1), 30000);

        reconnectTimeoutRef.current = setTimeout(() => {
          setLastMessage({ type: 'reconnect', timestamp: Date.now() });
        }, delay);
      } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
        toast({
          title: "Connection Lost",
          description: "Unable to maintain real-time connection. Please refresh the page.",
          variant: "destructive"
        });
      }
    });

    socket.addEventListener('error', (error) => {
      console.error('WebSocket error:', error);
    });

    socket.addEventListener('message', (event) => {
      try {
        const data = JSON.parse(event.data);
        setLastMessage(data);

        // Debug logging for email-related WebSocket messages
        if (data.type === 'newMessage' && data.data?.channelType === 'email') {
          console.log('📧 Received email message via WebSocket:', data);
        }
        if (data.type === 'conversationUpdated' && data.data?.channelType === 'email') {
          console.log('📧 Received email conversation update via WebSocket:', data);
        }

        if (data.type === 'authError') {
          toast({
            title: "Authentication Error",
            description: data.message || "Failed to authenticate WebSocket connection",
            variant: "destructive"
          });
          socket.close();
          return;
        }

        if (data.type && messageHandlers.current[data.type]) {
          messageHandlers.current[data.type].forEach(handler => {
            handler(data);
          });
        }
      } catch (err) {
        console.error('Error parsing WebSocket message:', err);
      }
    });

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }

      if ((socket as any).heartbeatInterval) {
        clearInterval((socket as any).heartbeatInterval);
      }

      if (socket.readyState === WebSocket.OPEN) {
        socket.close();
      }
    };
  }, [url, toast, user, isLoading]);

  // Handle page visibility changes for WebSocket reconnection
  useEffect(() => {
    if (!user || isLoading) return;

    if (isVisible && wasHidden) {
      const hiddenDuration = getHiddenDuration();
      const timeSinceLastConnect = Date.now() - lastConnectAttemptRef.current;

      // If page was hidden for more than 5 minutes or WebSocket is not connected
      // and it's been more than 30 seconds since last connect attempt, try to reconnect
      if ((hiddenDuration >= 5 * 60 * 1000 || !isConnected) && timeSinceLastConnect > 30000) {
        if (socketRef.current?.readyState !== WebSocket.OPEN) {
          lastConnectAttemptRef.current = Date.now();
          setLastMessage({ type: 'reconnect', timestamp: Date.now() });
        }
      }
    }
  }, [isVisible, wasHidden, getHiddenDuration, isConnected, user, isLoading]);

  const onMessage = useCallback((type: string, handler: MessageHandler) => {
    if (!messageHandlers.current[type]) {
      messageHandlers.current[type] = [];
    }

    messageHandlers.current[type].push(handler);

    return () => {
      messageHandlers.current[type] = messageHandlers.current[type].filter(h => h !== handler);
    };
  }, []);

  const sendMessage = useCallback((data: any) => {
    if (socketRef.current?.readyState === WebSocket.OPEN) {
      socketRef.current.send(JSON.stringify(data));
      return true;
    }
    return false;
  }, []);

  return { isConnected, lastMessage, sendMessage, onMessage };
}
