[{"key": "admin.languages.cannot_delete_default_desc", "value": "Please set another language as default first."}, {"key": "admin.languages.cannot_delete_default_title", "value": "Cannot delete default language"}, {"key": "admin.languages.confirm_delete", "value": "Are you sure you want to delete {{name}}?"}, {"key": "admin.languages.default_updated_desc", "value": "The default language has been updated successfully."}, {"key": "admin.languages.default_updated_title", "value": "Default language updated"}, {"key": "admin.languages.deleted_desc", "value": "The language has been deleted successfully."}, {"key": "admin.languages.deleted_title", "value": "Language deleted"}, {"key": "admin.settings.updates", "value": "System Updates"}, {"key": "settings.updates.title", "value": "System Updates"}, {"key": "settings.updates.current_version", "value": "Current Version"}, {"key": "settings.updates.checking", "value": "Checking..."}, {"key": "settings.updates.check_updates", "value": "Check for Updates"}, {"key": "settings.updates.update_available", "value": "Update Available: v{{version}}"}, {"key": "settings.updates.package_size", "value": "Package Size: {{size}}"}, {"key": "settings.updates.release_notes", "value": "Release Notes:"}, {"key": "settings.updates.starting", "value": "Starting..."}, {"key": "settings.updates.install_update", "value": "Install Update"}, {"key": "settings.updates.up_to_date", "value": "Your system is up to date"}, {"key": "settings.updates.update_progress", "value": "Update Progress"}, {"key": "settings.updates.preparing", "value": "Preparing update..."}, {"key": "settings.updates.update_warning", "value": "Do not close this page or restart the system during the update process."}, {"key": "settings.updates.history", "value": "Update History"}, {"key": "settings.updates.no_history", "value": "No update history available"}, {"key": "settings.updates.update_started", "value": "Update Started"}, {"key": "settings.updates.update_started_desc", "value": "System update has been initiated"}, {"key": "settings.updates.update_failed", "value": "Failed to start update"}, {"key": "settings.updates.status.pending", "value": "Pending"}, {"key": "settings.updates.status.downloading", "value": "Downloading"}, {"key": "settings.updates.status.validating", "value": "Validating"}, {"key": "settings.updates.status.applying", "value": "Applying"}, {"key": "settings.updates.status.completed", "value": "Completed"}, {"key": "settings.updates.status.failed", "value": "Failed"}, {"key": "settings.updates.status.rolled_back", "value": "Rolled Back"}, {"key": "trial.status_title", "value": "Trial Period"}, {"key": "trial.days_remaining", "value": "{{days}} days remaining"}, {"key": "trial.expires_today", "value": "Expires today"}, {"key": "trial.expired", "value": "Trial expired"}, {"key": "trial.upgrade_now", "value": "Upgrade now"}, {"key": "plans.trial_days", "value": "Trial Days"}, {"key": "plans.trial_period", "value": "Trial Period"}, {"key": "plans.has_trial", "value": "Has Trial"}, {"key": "plans.no_trial", "value": "No Trial"}, {"key": "plans.free_plan", "value": "Free Plan"}, {"key": "plans.free", "value": "Free"}, {"key": "plans.paid", "value": "Paid"}, {"key": "registration.trial_available", "value": "{{days}} day trial available"}, {"key": "registration.free_plan_available", "value": "Free plan"}, {"key": "admin.languages.description", "value": "Manage the languages available in your application. The default language will be used when a user's preferred language is not available."}, {"key": "admin.languages.table.actions", "value": "Actions"}, {"key": "admin.languages.table.active", "value": "Active"}, {"key": "admin.languages.table.code", "value": "Code"}, {"key": "admin.languages.table.default", "value": "<PERSON><PERSON><PERSON>"}, {"key": "admin.languages.table.direction", "value": "Direction"}, {"key": "admin.languages.table.language", "value": "Language"}, {"key": "admin.languages.title", "value": "Available Languages"}, {"key": "admin.languages.updated_desc", "value": "The language has been updated successfully."}, {"key": "admin.languages.updated_title", "value": "Language updated"}, {"key": "admin.namespaces.confirm_delete", "value": "Are you sure you want to delete the \"{{name}}\" namespace? This will delete all keys and translations in this namespace."}, {"key": "admin.namespaces.deleted_desc", "value": "The namespace has been deleted successfully."}, {"key": "admin.namespaces.deleted_title", "value": "Namespace deleted"}, {"key": "admin.namespaces.description", "value": "Namespaces help organize translations into logical groups."}, {"key": "admin.namespaces.edit_description", "value": "Update the namespace settings."}, {"key": "admin.namespaces.edit_title", "value": "Edit Namespace"}, {"key": "admin.namespaces.form.description", "value": "Description"}, {"key": "admin.namespaces.form.description_placeholder", "value": "Common translations used throughout the application"}, {"key": "admin.namespaces.form.name", "value": "Name"}, {"key": "admin.namespaces.form.name_placeholder", "value": "common"}, {"key": "admin.namespaces.table.actions", "value": "Actions"}, {"key": "admin.namespaces.table.description", "value": "Description"}, {"key": "admin.namespaces.table.name", "value": "Name"}, {"key": "admin.namespaces.title", "value": "Translation Namespaces"}, {"key": "admin.namespaces.update_button", "value": "Update Namespace"}, {"key": "admin.namespaces.updated_desc", "value": "The namespace has been updated successfully."}, {"key": "admin.namespaces.updated_title", "value": "Namespace updated"}, {"key": "admin.translations.description", "value": "Select a language and namespace to manage translations."}, {"key": "admin.translations.language_label", "value": "Language"}, {"key": "admin.translations.namespace_label", "value": "Namespace"}, {"key": "admin.translations.no_keys", "value": "No translation keys found in this namespace. Add a new key to get started."}, {"key": "admin.translations.no_translation", "value": "No translation"}, {"key": "admin.translations.saved_desc", "value": "The translation has been saved successfully."}, {"key": "admin.translations.saved_title", "value": "Translation saved"}, {"key": "admin.translations.select_both", "value": "Please select both a language and a namespace to manage translations."}, {"key": "admin.translations.select_language", "value": "Select language"}, {"key": "admin.translations.select_namespace", "value": "Select namespace"}, {"key": "admin.translations.table.actions", "value": "Actions"}, {"key": "admin.translations.table.key", "value": "Key"}, {"key": "admin.translations.table.translation", "value": "Translation"}, {"key": "admin.translations.title", "value": "Manage Translations"}, {"key": "common.edit", "value": "Edit"}, {"key": "common.error", "value": "Error"}, {"key": "common.updating", "value": "Updating..."}, {"key": "common.search", "value": "Search"}, {"key": "common.searching", "value": "Searching..."}, {"key": "common.search_placeholder", "value": "Search conversations, contacts, templates..."}, {"key": "search.no_results", "value": "No results found for \"{{query}}\""}, {"key": "search.conversations", "value": "Conversations"}, {"key": "search.contacts", "value": "Contacts"}, {"key": "search.templates", "value": "Templates"}, {"key": "emoji.picker_title", "value": "Select Emoji"}, {"key": "emoji.recent", "value": "Recently Used"}, {"key": "emoji.search_placeholder", "value": "Search emojis..."}, {"key": "messages.input.add_emoji", "value": "Add emoji"}, {"key": "quoted_message.unknown_sender", "value": "Unknown"}, {"key": "quoted_message.you", "value": "You"}, {"key": "quoted_message.assistant", "value": "Assistant"}, {"key": "quoted_message.contact", "value": "Contact"}, {"key": "quoted_message.image", "value": "📷 Image"}, {"key": "quoted_message.video", "value": "🎥 Video"}, {"key": "quoted_message.audio", "value": "🎵 Audio"}, {"key": "quoted_message.document", "value": "📄 Document"}, {"key": "quoted_message.message", "value": "Message"}, {"key": "quoted_message.deleted_sender", "value": "Unknown"}, {"key": "quoted_message.deleted_message", "value": "This message was deleted"}, {"key": "message_bubble.confirm_delete_whatsapp_title", "value": "Delete Message for Everyone"}, {"key": "message_bubble.confirm_delete_whatsapp_message", "value": "This message will be deleted from both {{appName}} and the recipient's WhatsApp chat. This action cannot be undone."}, {"key": "message_bubble.confirm_delete_whatsapp_old", "value": "This message is too old to be deleted from WhatsApp (72-minute limit). It will only be deleted from {{appName}}."}, {"key": "welcome.message", "value": "welcome to pointer"}, {"key": "admin.payments.title", "value": "Payment Management"}, {"key": "admin.payments.description", "value": "Comprehensive payment tracking and management dashboard"}, {"key": "admin.payments.dashboard.title", "value": "Payment Dashboard"}, {"key": "admin.payments.metrics.total_revenue", "value": "Total Revenue"}, {"key": "admin.payments.metrics.monthly_revenue", "value": "Monthly Revenue"}, {"key": "admin.payments.metrics.yearly_revenue", "value": "Yearly Revenue"}, {"key": "admin.payments.metrics.monthly_growth", "value": "Monthly Growth"}, {"key": "admin.payments.metrics.active_subscriptions", "value": "Active Subscriptions"}, {"key": "admin.payments.metrics.pending_payments", "value": "Pending Payments"}, {"key": "admin.payments.metrics.payment_success_rate", "value": "Payment Success Rate"}, {"key": "admin.payments.trends.title", "value": "Payment Trends"}, {"key": "admin.payments.trends.revenue", "value": "Revenue"}, {"key": "admin.payments.trends.transactions", "value": "Transactions"}, {"key": "admin.payments.trends.period.7days", "value": "Last 7 Days"}, {"key": "admin.payments.trends.period.30days", "value": "Last 30 Days"}, {"key": "admin.payments.trends.period.12months", "value": "Last 12 Months"}, {"key": "admin.payments.companies.title", "value": "Company Payment Details"}, {"key": "admin.payments.companies.search_placeholder", "value": "Search companies..."}, {"key": "admin.payments.companies.table.company", "value": "Company"}, {"key": "admin.payments.companies.table.plan", "value": "Plan"}, {"key": "admin.payments.companies.table.status", "value": "Status"}, {"key": "admin.payments.companies.table.last_payment", "value": "Last Payment"}, {"key": "admin.payments.companies.table.next_renewal", "value": "Next Renewal"}, {"key": "admin.payments.companies.table.payment_method", "value": "Payment Method"}, {"key": "admin.payments.companies.table.total_paid", "value": "Total Paid"}, {"key": "admin.payments.companies.table.actions", "value": "Actions"}, {"key": "admin.payments.companies.status.active", "value": "Active"}, {"key": "admin.payments.companies.status.pending", "value": "Pending"}, {"key": "admin.payments.companies.status.overdue", "value": "Overdue"}, {"key": "admin.payments.companies.status.cancelled", "value": "Cancelled"}, {"key": "admin.payments.transactions.title", "value": "Payment Transactions"}, {"key": "admin.payments.transactions.filter.all_methods", "value": "All Payment Methods"}, {"key": "admin.payments.transactions.filter.all_statuses", "value": "All Statuses"}, {"key": "admin.payments.transactions.filter.date_range", "value": "Date Range"}, {"key": "admin.payments.transactions.table.id", "value": "Transaction ID"}, {"key": "admin.payments.transactions.table.company", "value": "Company"}, {"key": "admin.payments.transactions.table.plan", "value": "Plan"}, {"key": "admin.payments.transactions.table.amount", "value": "Amount"}, {"key": "admin.payments.transactions.table.method", "value": "Method"}, {"key": "admin.payments.transactions.table.status", "value": "Status"}, {"key": "admin.payments.transactions.table.date", "value": "Date"}, {"key": "admin.payments.transactions.table.actions", "value": "Actions"}, {"key": "admin.payments.transactions.status.completed", "value": "Completed"}, {"key": "admin.payments.transactions.status.pending", "value": "Pending"}, {"key": "admin.payments.transactions.status.failed", "value": "Failed"}, {"key": "admin.payments.transactions.status.cancelled", "value": "Cancelled"}, {"key": "admin.payments.pending.title", "value": "Pending Payments"}, {"key": "admin.payments.pending.description", "value": "Manage overdue and pending payments"}, {"key": "admin.payments.pending.table.company", "value": "Company"}, {"key": "admin.payments.pending.table.plan", "value": "Plan"}, {"key": "admin.payments.pending.table.amount", "value": "Amount"}, {"key": "admin.payments.pending.table.days_overdue", "value": "Days Overdue"}, {"key": "admin.payments.pending.table.method", "value": "Method"}, {"key": "admin.payments.pending.table.actions", "value": "Actions"}, {"key": "admin.payments.actions.mark_received", "value": "<PERSON> as Received"}, {"key": "admin.payments.actions.send_reminder", "value": "Send Reminder"}, {"key": "admin.payments.actions.view_details", "value": "View Details"}, {"key": "admin.payments.performance.title", "value": "Payment Method Performance"}, {"key": "admin.payments.performance.table.method", "value": "Payment Method"}, {"key": "admin.payments.performance.table.transactions", "value": "Total Transactions"}, {"key": "admin.payments.performance.table.success_rate", "value": "Success Rate"}, {"key": "admin.payments.performance.table.revenue", "value": "Total Revenue"}, {"key": "admin.payments.performance.table.avg_amount", "value": "Average Amount"}, {"key": "admin.payments.export.title", "value": "Export Payment Data"}, {"key": "admin.payments.export.format.csv", "value": "CSV"}, {"key": "admin.payments.export.format.json", "value": "JSON"}, {"key": "admin.payments.export.button", "value": "Export Data"}, {"key": "admin.payments.reminders.title", "value": "Send Payment Reminder"}, {"key": "admin.payments.reminders.message_placeholder", "value": "Enter reminder message..."}, {"key": "admin.payments.reminders.send_button", "value": "Send Reminder"}, {"key": "admin.payments.reminders.success", "value": "<PERSON><PERSON><PERSON> sent successfully"}, {"key": "admin.payments.status_update.title", "value": "Update Payment Status"}, {"key": "admin.payments.status_update.notes_placeholder", "value": "Add notes (optional)..."}, {"key": "admin.payments.status_update.success", "value": "Payment status updated successfully"}, {"key": "common.save", "value": "Save"}, {"key": "common.close", "value": "Close"}, {"key": "common.hide", "value": "<PERSON>de"}, {"key": "common.something_went_wrong", "value": "Something went wrong"}, {"key": "flow_builder.send_message", "value": "Send Message"}, {"key": "flow_builder.enter_message", "value": "Enter your message..."}, {"key": "flow_builder.condition", "value": "Condition"}, {"key": "flow_builder.condition_example", "value": "if message.contains('hello')"}, {"key": "flow_builder.yes", "value": "Yes"}, {"key": "flow_builder.no", "value": "No"}, {"key": "flow_builder.input", "value": "Input"}, {"key": "flow_builder.collect_response", "value": "Collect user response"}, {"key": "flow_builder.action", "value": "Action"}, {"key": "flow_builder.perform_api_call", "value": "Perform API call"}, {"key": "flow_builder.add_node", "value": "Add Node"}, {"key": "flow_builder.message", "value": "Message"}, {"key": "flow_builder.error_loading_flow", "value": "Error loading flow"}, {"key": "flow_builder.parse_error", "value": "Could not parse flow data"}, {"key": "flow_builder.flow_created", "value": "Flow created"}, {"key": "flow_builder.flow_created_success", "value": "Your flow has been created successfully."}, {"key": "flow_builder.plan_limit_reached", "value": "Plan Limit Reached"}, {"key": "flow_builder.upgrade_plan_message", "value": "To create more flows, please contact your administrator to upgrade your plan."}, {"key": "flow_builder.error_creating_flow", "value": "Error creating flow"}, {"key": "flow_builder.flow_updated", "value": "Flow updated"}, {"key": "flow_builder.flow_updated_success", "value": "Your flow has been updated successfully."}, {"key": "flow_builder.error_updating_flow", "value": "Error updating flow"}, {"key": "flow_builder.name_required", "value": "Name required"}, {"key": "flow_builder.provide_name", "value": "Please provide a name for your flow"}, {"key": "flow_builder.flow_name_placeholder", "value": "Flow name"}, {"key": "flow_builder.active", "value": "Active"}, {"key": "flow_builder.draft", "value": "Draft"}, {"key": "flow_builder.creating_new_flow", "value": "Creating New Flow"}, {"key": "flow_builder.current_flow_status", "value": "Current flow status"}, {"key": "flow_builder.ai_assistant", "value": "AI Assistant"}, {"key": "flow_builder.ai_history", "value": "History"}, {"key": "flow_builder.ai_messages", "value": "messages"}, {"key": "flow_builder.ai_audio_enabled", "value": "Audio enabled"}, {"key": "flow_builder.ai_image_enabled", "value": "Image enabled"}, {"key": "flow_builder.ai_video_enabled", "value": "Video enabled"}, {"key": "flow_builder.ai_functions_enabled", "value": "Functions enabled"}, {"key": "flow_builder.ai_provider", "value": "AI Provider"}, {"key": "flow_builder.ai_select_provider", "value": "Select provider"}, {"key": "flow_builder.ai_model", "value": "Model"}, {"key": "flow_builder.ai_select_model", "value": "Select model"}, {"key": "flow_builder.ai_api_key", "value": "API Key"}, {"key": "flow_builder.ai_api_key_placeholder", "value": "Enter your {{provider}} API key"}, {"key": "flow_builder.ai_get_api_key", "value": "Get your API key here"}, {"key": "flow_builder.ai_system_prompt", "value": "Assistant Mode (System Prompt)"}, {"key": "flow_builder.ai_prompt_placeholder", "value": "Enter instructions for the AI"}, {"key": "flow_builder.ai_enable_history", "value": "Enable conversation history"}, {"key": "flow_builder.ai_history_limit", "value": "History messages limit"}, {"key": "flow_builder.ai_history_description", "value": "Sets how many previous messages to include for context."}, {"key": "flow_builder.ai_enable_audio", "value": "Enable audio processing"}, {"key": "flow_builder.ai_enable_image", "value": "Enable image processing"}, {"key": "flow_builder.ai_image_tooltip", "value": "Allows the AI to process images sent by the user. Note: Only available with multimodal models like Gemini 2.5."}, {"key": "flow_builder.ai_enable_video", "value": "Enable video processing"}, {"key": "flow_builder.ai_video_tooltip", "value": "Allows the AI to process video files sent by the user. Note: Only available with multimodal models like Gemini 2.5."}, {"key": "flow_builder.ai_enable_functions", "value": "Enable function calling"}, {"key": "flow_builder.ai_functions_tooltip", "value": "Only available with certain providers and models. Allows the AI to perform actions like scheduling appointments, checking data, etc."}, {"key": "flow_builder.ai_enable_task_execution", "value": "Enable Task Execution"}, {"key": "flow_builder.ai_task_execution_tooltip", "value": "Allows the AI to execute predefined tasks and trigger different flow paths based on user intent. Each task can connect to different nodes in the flow."}, {"key": "flow_builder.ai_tasks", "value": "Tasks"}, {"key": "flow_builder.ai_add_task", "value": "Add Task"}, {"key": "flow_builder.ai_no_tasks", "value": "No tasks configured. Add a task to enable AI function calling."}, {"key": "flow_builder.ai_tasks_description", "value": "Tasks allow the AI to execute specific functions when user intent is detected. Each task creates an output handle that can be connected to other nodes."}, {"key": "flow_builder.ai_tasks_enabled", "value": "Tasks"}, {"key": "flow_builder.ai_default_output", "value": "Default Output"}, {"key": "flow_builder.ai_default_output_desc", "value": "Used when no tasks are triggered or task execution is disabled"}, {"key": "flow_builder.ai_session_stopped", "value": "Session Stopped"}, {"key": "flow_builder.ai_session_stopped_desc", "value": "Triggered when user types the stop keyword to exit AI session"}, {"key": "flow_builder.ai_description", "value": "The AI Assistant node will process incoming messages and generate responses based on the context. It can access conversation history, contact data, and other variables from the flow."}, {"key": "flow_builder.http_request", "value": "HTTP Request"}, {"key": "flow_builder.http_no_url", "value": "No URL configured"}, {"key": "flow_builder.http_auth", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.http_header", "value": "header"}, {"key": "flow_builder.http_body_configured", "value": "Body configured"}, {"key": "flow_builder.http_retry", "value": "Retry"}, {"key": "flow_builder.http_mapping", "value": "mapping"}, {"key": "flow_builder.http_quick_templates", "value": "Quick Templates"}, {"key": "flow_builder.http_choose_template", "value": "Choose a template..."}, {"key": "flow_builder.http_method", "value": "HTTP Method"}, {"key": "flow_builder.http_select_method", "value": "Select method"}, {"key": "flow_builder.http_request_url", "value": "Request URL"}, {"key": "flow_builder.webhook", "value": "Webhook"}, {"key": "flow_builder.webhook_no_url", "value": "No URL configured"}, {"key": "flow_builder.webhook_body_configured", "value": "Body configured"}, {"key": "flow_builder.webhook_method", "value": "HTTP Method"}, {"key": "flow_builder.webhook_select_method", "value": "Select method"}, {"key": "flow_builder.webhook_url", "value": "Webhook URL"}, {"key": "flow_builder.webhook_url_placeholder", "value": "https://api.example.com/webhook"}, {"key": "flow_builder.webhook_test_tooltip", "value": "Test webhook with current configuration"}, {"key": "flow_builder.webhook_authentication", "value": "Authentication"}, {"key": "flow_builder.webhook_select_auth", "value": "Select auth type"}, {"key": "flow_builder.webhook_bearer_token", "value": "Bearer token"}, {"key": "flow_builder.webhook_username", "value": "Username"}, {"key": "flow_builder.webhook_password", "value": "Password"}, {"key": "flow_builder.calendar_event", "value": "Calendar Event"}, {"key": "flow_builder.calendar_invalid_email", "value": "Invalid email"}, {"key": "flow_builder.calendar_valid_email_required", "value": "Please enter a valid email address"}, {"key": "flow_builder.calendar_untitled_event", "value": "Untitled Event"}, {"key": "flow_builder.calendar_start", "value": "Start"}, {"key": "flow_builder.calendar_end", "value": "End"}, {"key": "flow_builder.calendar_attendees", "value": "Attendees"}, {"key": "flow_builder.calendar_event_title", "value": "Event Title"}, {"key": "flow_builder.calendar_enter_title", "value": "Enter event title"}, {"key": "flow_builder.calendar_description", "value": "Description"}, {"key": "flow_builder.calendar_enter_description", "value": "Enter event description"}, {"key": "flow_builder.duplicate_node", "value": "Duplicate node"}, {"key": "flow_builder.delete_node", "value": "Delete node"}, {"key": "flow_builder.hard_reset_keyword", "value": "Hard Reset Keyword"}, {"key": "flow_builder.hard_reset_keyword_placeholder", "value": "reset, restart, newchat, etc."}, {"key": "flow_builder.hard_reset_keyword_help", "value": "When bot is disabled, users can type this keyword to re-enable the bot and start fresh"}, {"key": "flow_builder.hard_reset_confirmation_message", "value": "Reset Confirmation Message"}, {"key": "flow_builder.hard_reset_confirmation_placeholder", "value": "<PERSON><PERSON> has been reactivated. Starting fresh conversation..."}, {"key": "flow_builder.hard_reset_confirmation_help", "value": "Message sent to user when hard reset is triggered"}, {"key": "flow_builder.hard_reset_label", "value": "Hard Reset"}, {"key": "admin.translations.select_both", "value": "Please select both a language and a namespace to manage translations."}, {"key": "errors.404_title", "value": "404 Page Not Found"}, {"key": "errors.404_description", "value": "Did you forget to add the page to the router?"}, {"key": "auth.welcome_team", "value": "Welcome to the team!"}, {"key": "auth.account_created_success", "value": "Your account has been created successfully. You are now logged in."}, {"key": "auth.validation.username_min_length", "value": "Username must be at least 3 characters long"}, {"key": "auth.validation.password_min_length", "value": "Password must be at least 6 characters long"}, {"key": "auth.validation.passwords_no_match", "value": "Passwords do not match"}, {"key": "auth.validation.full_name_required", "value": "Full name is required"}, {"key": "auth.invalid_invitation_title", "value": "Invalid Invitation Link"}, {"key": "auth.invalid_invitation_desc", "value": "The invitation link is missing or invalid. Please check your email and try again."}, {"key": "auth.go_to_login", "value": "Go to Login"}, {"key": "auth.verifying_invitation", "value": "Verifying invitation..."}, {"key": "auth.invitation_not_found_title", "value": "Invitation Not Found"}, {"key": "auth.invitation_not_found_desc", "value": "This invitation link is invalid, expired, or has already been used."}, {"key": "auth.accept_invitation_title", "value": "Accept Team Invitation"}, {"key": "auth.invited_as_role", "value": "You've been invited to join as a {{role}}"}, {"key": "auth.full_name", "value": "Full Name"}, {"key": "auth.enter_full_name", "value": "Enter your full name"}, {"key": "auth.username", "value": "Username"}, {"key": "auth.choose_username", "value": "Choose a username"}, {"key": "auth.password", "value": "Password"}, {"key": "auth.create_password", "value": "Create a password"}, {"key": "auth.confirm_password", "value": "Confirm Password"}, {"key": "auth.confirm_password_placeholder", "value": "Confirm your password"}, {"key": "auth.creating_account", "value": "Creating Account..."}, {"key": "auth.accept_invitation_button", "value": "Accept Invitation & Create Account"}, {"key": "auth.already_have_account", "value": "Already have an account?"}, {"key": "auth.sign_in_here", "value": "Sign in here"}, {"key": "auth.invitation_agreement", "value": "By accepting this invitation, you agree to join the team and follow the company's policies."}, {"key": "auth.login_success", "value": "Login successful"}, {"key": "auth.welcome_back", "value": "Welcome back, {{name}}!"}, {"key": "auth.login_failed", "value": "<PERSON><PERSON> failed"}, {"key": "auth.invalid_credentials", "value": "Invalid username or password"}, {"key": "auth.email", "value": "Email"}, {"key": "auth.email_placeholder", "value": "<EMAIL>"}, {"key": "auth.password_placeholder", "value": "â€¢â€¢â€¢â€¢â€¢â€¢â€¢â€¢"}, {"key": "auth.logging_in", "value": "Logging in..."}, {"key": "auth.login", "value": "<PERSON><PERSON>"}, {"key": "auth.welcome_title", "value": "Welcome to {{appName}}"}, {"key": "auth.welcome_subtitle", "value": "Multi-channel team inbox and AI chatbot platform"}, {"key": "auth.login_title", "value": "Login to your account"}, {"key": "auth.login_description", "value": "Enter your username and password to access your account"}, {"key": "auth.username_placeholder", "value": "Your username"}, {"key": "auth.register_company", "value": "Register a new company"}, {"key": "auth.hero_title", "value": "Unify Your Customer Communications"}, {"key": "auth.hero_subtitle", "value": "The all-in-one platform for managing customer conversations across WhatsApp, Facebook, Instagram, and more."}, {"key": "auth.feature_inbox_title", "value": "Multi-channel inbox"}, {"key": "auth.feature_inbox_desc", "value": "Manage all customer conversations in one unified inbox"}, {"key": "auth.feature_ai_title", "value": "AI-powered chatbots"}, {"key": "auth.feature_ai_desc", "value": "Automate responses with customizable AI chatbots"}, {"key": "auth.feature_team_title", "value": "Team collaboration"}, {"key": "auth.feature_team_desc", "value": "Assign conversations, leave notes, and collaborate effectively"}, {"key": "admin.login_title", "value": "<PERSON><PERSON>"}, {"key": "admin.login_description", "value": "Enter your credentials to access the admin panel"}, {"key": "admin.restricted_area", "value": "This area is restricted to administrators only"}, {"key": "nav.dashboard", "value": "Dashboard"}, {"key": "dashboard.total_conversations", "value": "Total Conversations"}, {"key": "dashboard.active_conversations", "value": "Active Conversations"}, {"key": "dashboard.response_time", "value": "Response Time"}, {"key": "nav.inbox", "value": "Inbox"}, {"key": "nav.flow_builder", "value": "Flow Builder"}, {"key": "nav.contacts", "value": "Contacts"}, {"key": "nav.pipeline", "value": "Pipeline"}, {"key": "nav.calendar", "value": "Calendar"}, {"key": "nav.campaigns", "value": "Campaigns"}, {"key": "nav.analytics", "value": "Analytics"}, {"key": "nav.channels", "value": "Channels"}, {"key": "nav.settings", "value": "Settings"}, {"key": "nav.help_support", "value": "Help & Support"}, {"key": "nav.company", "value": "Company"}, {"key": "nav.plan", "value": "Plan"}, {"key": "nav.profile", "value": "Profile"}, {"key": "admin.nav.dashboard", "value": "Dashboard"}, {"key": "admin.nav.companies", "value": "Companies"}, {"key": "admin.nav.users", "value": "Users"}, {"key": "admin.nav.plans", "value": "Plans"}, {"key": "admin.nav.analytics", "value": "Analytics"}, {"key": "admin.nav.translations", "value": "Translations"}, {"key": "admin.nav.settings", "value": "Settings"}, {"key": "admin.companies.new_company", "value": "New Company"}, {"key": "admin.companies.total_companies", "value": "Total Companies"}, {"key": "admin.companies.active_companies", "value": "Active Companies"}, {"key": "admin.companies.inactive_companies", "value": "Inactive Companies"}, {"key": "admin.companies.manage_description", "value": "Manage all companies in the system"}, {"key": "admin.companies.no_companies_found", "value": "No companies found. Create your first company to get started."}, {"key": "admin.companies.table.name", "value": "Name"}, {"key": "admin.companies.table.slug", "value": "Slug"}, {"key": "admin.companies.table.plan", "value": "Plan"}, {"key": "admin.companies.table.status", "value": "Status"}, {"key": "admin.companies.table.actions", "value": "Actions"}, {"key": "common.active", "value": "Active"}, {"key": "common.inactive", "value": "Inactive"}, {"key": "common.manage", "value": "Manage"}, {"key": "common.please_wait", "value": "Please wait..."}, {"key": "common.search", "value": "Search"}, {"key": "common.searching_for", "value": "Searching for: {{query}}"}, {"key": "common.search_placeholder", "value": "Search conversations, templates..."}, {"key": "admin.returning_to_admin", "value": "Returning to admin account"}, {"key": "admin.error_returning", "value": "Error returning to admin"}, {"key": "admin.trying_fallback", "value": "Trying fallback method..."}, {"key": "admin.returning", "value": "Returning..."}, {"key": "admin.return_to_admin", "value": "Return to Admin"}, {"key": "admin.impersonating", "value": "Impersonating"}, {"key": "auth.logged_out", "value": "Logged out"}, {"key": "auth.logged_out_success", "value": "You have been successfully logged out"}, {"key": "auth.logout_failed", "value": "Failed to logout: {{error}}"}, {"key": "auth.back_to_login", "value": "Back to Login"}, {"key": "registration.success_title", "value": "Registration successful!"}, {"key": "registration.approval_required", "value": "Your company registration has been submitted for approval. You will receive an email once approved."}, {"key": "registration.success_desc", "value": "Your company has been registered successfully. You can now log in."}, {"key": "registration.failed_title", "value": "Registration failed"}, {"key": "registration.unavailable_title", "value": "Registration Unavailable"}, {"key": "registration.unavailable_desc", "value": "Company registration is currently disabled. Please contact the administrator for more information."}, {"key": "registration.title", "value": "Company Registration"}, {"key": "registration.description", "value": "Fill in the details below to register your company and create an admin account"}, {"key": "registration.approval_notice", "value": "New registrations require admin approval. You will receive an email once your registration is approved."}, {"key": "registration.company_info", "value": "Company Information"}, {"key": "registration.company_name", "value": "Company Name"}, {"key": "registration.company_name_placeholder", "value": "Your Company Name"}, {"key": "registration.admin_details", "value": "Admin User Details"}, {"key": "registration.plan_selection", "value": "Plan Selection"}, {"key": "registration.registering", "value": "Registering..."}, {"key": "registration.register_button", "value": "Register Company"}, {"key": "registration.page_title", "value": "Register Your Company"}, {"key": "registration.page_subtitle", "value": "Create your company account and start managing conversations"}, {"key": "registration.company_slug", "value": "Company Slug"}, {"key": "registration.company_slug_placeholder", "value": "your-company"}, {"key": "registration.company_slug_description", "value": "This will be your unique identifier (e.g., your-company.app.com)"}, {"key": "registration.company_email", "value": "Company Email"}, {"key": "registration.company_email_placeholder", "value": "<EMAIL>"}, {"key": "registration.company_phone", "value": "Company Phone"}, {"key": "registration.company_phone_placeholder", "value": "+****************"}, {"key": "registration.company_website", "value": "Company Website"}, {"key": "registration.company_website_placeholder", "value": "https://yourcompany.com"}, {"key": "registration.admin_full_name", "value": "Full Name"}, {"key": "registration.admin_full_name_placeholder", "value": "<PERSON>"}, {"key": "registration.admin_email", "value": "Email Address"}, {"key": "registration.admin_email_placeholder", "value": "<EMAIL>"}, {"key": "registration.admin_username", "value": "Username"}, {"key": "registration.admin_username_placeholder", "value": "johndoe"}, {"key": "registration.admin_username_description", "value": "This will be used to log in to your account"}, {"key": "registration.admin_password", "value": "Password"}, {"key": "registration.admin_password_placeholder", "value": "Create a strong password"}, {"key": "registration.admin_confirm_password", "value": "Confirm Password"}, {"key": "registration.admin_confirm_password_placeholder", "value": "Confirm your password"}, {"key": "registration.select_plan", "value": "Select Plan"}, {"key": "registration.select_plan_placeholder", "value": "Choose a plan"}, {"key": "registration.plan_change_note", "value": "You can change your plan later from the settings"}, {"key": "registration.validation.company_name_min", "value": "Company name must be at least 2 characters"}, {"key": "registration.validation.company_slug_min", "value": "Company slug must be at least 3 characters"}, {"key": "registration.validation.company_slug_format", "value": "Slug can only contain lowercase letters, numbers, and hyphens"}, {"key": "registration.validation.company_email_invalid", "value": "Please enter a valid company email address"}, {"key": "registration.validation.company_website_invalid", "value": "Please enter a valid website URL"}, {"key": "registration.validation.admin_name_min", "value": "Full name must be at least 2 characters"}, {"key": "registration.validation.admin_email_invalid", "value": "Please enter a valid email address"}, {"key": "registration.validation.admin_username_min", "value": "Username must be at least 3 characters"}, {"key": "registration.validation.admin_password_min", "value": "Password must be at least 6 characters"}, {"key": "registration.validation.confirm_password_required", "value": "Please confirm your password"}, {"key": "registration.validation.passwords_no_match", "value": "Passwords don't match"}, {"key": "registration.validation.plan_required", "value": "Please select a plan"}, {"key": "registration.validation.slug_taken", "value": "This slug is already taken"}, {"key": "registration.error.status_check_failed", "value": "Failed to check registration status"}, {"key": "registration.error.plans_fetch_failed", "value": "Failed to fetch plans"}, {"key": "registration.error.slug_check_failed", "value": "Failed to check slug availability"}, {"key": "registration.error.register_failed", "value": "Failed to register company"}, {"key": "flow_builder.error.no_file", "value": "No file selected"}, {"key": "flow_builder.error.no_file_desc", "value": "Please select a file to upload"}, {"key": "flow_builder.error.invalid_file_type", "value": "Invalid file type"}, {"key": "flow_builder.error.invalid_image_type", "value": "Please select a valid image file (JPEG, PNG, GIF, WebP, SVG)"}, {"key": "flow_builder.error.file_too_large", "value": "File too large"}, {"key": "flow_builder.error.max_file_size", "value": "Maximum file size is 30MB"}, {"key": "flow_builder.error.upload_failed", "value": "Upload failed"}, {"key": "flow_builder.error.upload_error", "value": "An error occurred while uploading the file"}, {"key": "flow_builder.success.upload_complete", "value": "Upload complete"}, {"key": "flow_builder.success.image_uploaded", "value": "Image uploaded successfully"}, {"key": "flow_builder.success.image_removed", "value": "Image removed"}, {"key": "flow_builder.success.image_removed_desc", "value": "Image has been removed from the node"}, {"key": "flow_builder.remove_image", "value": "Remove Image"}, {"key": "flow_builder.preview.loading_audio", "value": "Loading audio..."}, {"key": "flow_builder.preview.loading_video", "value": "Loading video..."}, {"key": "flow_builder.preview.loading_image", "value": "Loading image..."}, {"key": "flow_builder.preview.failed_audio", "value": "Failed to load audio"}, {"key": "flow_builder.preview.failed_video", "value": "Failed to load video"}, {"key": "flow_builder.preview.failed_image", "value": "Failed to load image"}, {"key": "flow_builder.preview.duration", "value": "Duration"}, {"key": "flow_builder.preview.resolution", "value": "Resolution"}, {"key": "flow_builder.preview.click_to_download", "value": "Click to download/view"}, {"key": "flow_builder.preview.open_document", "value": "Open"}, {"key": "flow_builder.preview_audio", "value": "Preview Audio"}, {"key": "flow_builder.preview_video", "value": "Preview Video"}, {"key": "flow_builder.preview_document", "value": "Preview Document"}, {"key": "flow_builder.hide_preview", "value": "Hide Preview"}, {"key": "inbox.conversations", "value": "Conversations"}, {"key": "inbox.filtered_by_channel", "value": "Filtered by channel"}, {"key": "inbox.clear_channel_filter", "value": "Clear channel filter"}, {"key": "inbox.filter_conversations", "value": "Filter conversations"}, {"key": "inbox.start_new_conversation", "value": "Start new conversation"}, {"key": "inbox.search_conversations", "value": "Search conversations"}, {"key": "inbox.search_conversations_enhanced", "value": "Search by name, tag, phone, email..."}, {"key": "inbox.filter.all", "value": "All"}, {"key": "inbox.filter.unassigned", "value": "Unassigned"}, {"key": "inbox.filter.my_chats", "value": "My Chats"}, {"key": "inbox.filter.assigned", "value": "Assigned"}, {"key": "inbox.no_conversations_found", "value": "No conversations found"}, {"key": "inbox.no_conversation_selected", "value": "No Conversation Selected"}, {"key": "inbox.select_conversation_hint", "value": "Select a conversation from the list to view messages"}, {"key": "inbox.show_conversations", "value": "Show conversations"}, {"key": "inbox.close_conversations", "value": "Close conversations"}, {"key": "inbox.new_lead", "value": "New Lead"}, {"key": "inbox.last_active_time", "value": "Last active 10 min ago"}, {"key": "inbox.conversation_started_via", "value": "Conversation started via {{channel}}"}, {"key": "inbox.scroll_to_bottom", "value": "Scroll to bottom"}, {"key": "inbox.new_conversation", "value": "New Conversation"}, {"key": "inbox.not_connected", "value": "Not Connected"}, {"key": "inbox.cannot_send_message", "value": "Cannot send message, not connected to server"}, {"key": "inbox.failed_send_media", "value": "Failed to send media message"}, {"key": "contacts.redirecting_to_inbox", "value": "Redirecting to inbox"}, {"key": "contacts.opening_conversation_with", "value": "Opening conversation with {{name}}"}, {"key": "contacts.add_contact", "value": "Add Contact"}, {"key": "contacts.search_placeholder", "value": "Search contacts..."}, {"key": "contacts.all_channels", "value": "All Channels"}, {"key": "contacts.whatsapp_official", "value": "WhatsApp Official"}, {"key": "contacts.whatsapp_unofficial", "value": "WhatsApp Unofficial"}, {"key": "contacts.add.title", "value": "Add New Contact"}, {"key": "contacts.add.description", "value": "Create a new contact with the information below."}, {"key": "contacts.add.name_label", "value": "Name"}, {"key": "contacts.add.name_placeholder", "value": "Enter contact name"}, {"key": "contacts.add.name_required", "value": "Contact name is required"}, {"key": "contacts.add.email_label", "value": "Email"}, {"key": "contacts.add.email_placeholder", "value": "Enter email address"}, {"key": "contacts.add.phone_label", "value": "Phone"}, {"key": "contacts.add.phone_placeholder", "value": "Enter phone number"}, {"key": "contacts.add.company_label", "value": "Company"}, {"key": "contacts.add.company_placeholder", "value": "Enter company name"}, {"key": "contacts.add.channel_label", "value": "Channel"}, {"key": "contacts.add.select_channel_placeholder", "value": "Select channel"}, {"key": "contacts.add.channel.whatsapp_official", "value": "WhatsApp Official"}, {"key": "contacts.add.channel.whatsapp_unofficial", "value": "WhatsApp Unofficial"}, {"key": "contacts.add.channel.messenger", "value": "Facebook Messenger"}, {"key": "contacts.add.channel.instagram", "value": "Instagram"}, {"key": "contacts.add.channel_identifier_label", "value": "Channel Identifier"}, {"key": "contacts.add.channel_identifier_placeholder", "value": "Phone number or ID"}, {"key": "contacts.add.tags_label", "value": "Tags (comma separated)"}, {"key": "contacts.add.tags_placeholder", "value": "lead, customer, etc."}, {"key": "contacts.add.notes_label", "value": "Notes"}, {"key": "contacts.add.notes_placeholder", "value": "Additional notes about this contact..."}, {"key": "contacts.add.creating", "value": "Creating..."}, {"key": "contacts.add.create_button", "value": "Create Contact"}, {"key": "contacts.add.success_title", "value": "Contact created"}, {"key": "contacts.add.success_description", "value": "The contact has been successfully created."}, {"key": "contacts.add.error_title", "value": "Creation failed"}, {"key": "contacts.import.button", "value": "Import CSV"}, {"key": "contacts.import.title", "value": "Import Contacts from CSV"}, {"key": "contacts.import.description", "value": "Upload a CSV file to import multiple contacts at once. Download the template to see the required format."}, {"key": "contacts.import.download_template", "value": "Download Template"}, {"key": "contacts.import.file_label", "value": "CSV File"}, {"key": "contacts.import.file_help", "value": "Maximum file size: 10MB. Only CSV files are supported."}, {"key": "contacts.import.preview_label", "value": "Preview (first 5 rows)"}, {"key": "contacts.import.duplicate_handling_label", "value": "Duplicate Handling"}, {"key": "contacts.import.duplicate.skip", "value": "Skip duplicates"}, {"key": "contacts.import.duplicate.update", "value": "Update existing"}, {"key": "contacts.import.duplicate.create", "value": "Create new"}, {"key": "contacts.import.duplicate_help", "value": "How to handle contacts with duplicate email addresses"}, {"key": "contacts.import.importing", "value": "Importing..."}, {"key": "contacts.import.results_label", "value": "Import Results"}, {"key": "contacts.import.successful", "value": "Successfully imported: {{count}}"}, {"key": "contacts.import.failed", "value": "Failed to import: {{count}}"}, {"key": "contacts.import.errors", "value": "Errors:"}, {"key": "contacts.import.more_errors", "value": "And {{count}} more errors..."}, {"key": "contacts.import.import_button", "value": "Import Contacts"}, {"key": "contacts.import.no_file_selected", "value": "Please select a CSV file to import"}, {"key": "contacts.import.success_title", "value": "Import completed"}, {"key": "contacts.import.success_description", "value": "Successfully imported {{count}} contacts"}, {"key": "contacts.import.error_title", "value": "Import failed"}, {"key": "contacts.bulk_actions.selected_count", "value": "{{count}} contacts selected"}, {"key": "contacts.bulk_actions.clear_selection", "value": "Clear Selection"}, {"key": "contacts.bulk_actions.delete_selected", "value": "Delete Selected"}, {"key": "contacts.bulk_actions.deleting", "value": "Deleting..."}, {"key": "contacts.bulk_actions.select_all", "value": "Select all contacts"}, {"key": "contacts.bulk_actions.select_contact", "value": "Select contact {{name}}"}, {"key": "contacts.bulk_delete.title", "value": "Delete {{count}} Contacts"}, {"key": "contacts.bulk_delete.warning", "value": "This will permanently delete these contacts and all associated conversations, messages, and notes. This action cannot be undone."}, {"key": "contacts.bulk_delete.confirm", "value": "Delete {{count}} Contacts"}, {"key": "contacts.bulk_delete.deleting", "value": "Deleting..."}, {"key": "contacts.bulk_delete.success_title", "value": "Contacts deleted"}, {"key": "contacts.bulk_delete.success_description", "value": "Successfully deleted {{count}} of {{total}} contacts"}, {"key": "contacts.bulk_delete.partial_failure_title", "value": "Some deletions failed"}, {"key": "contacts.bulk_delete.partial_failure_description", "value": "{{count}} contacts could not be deleted"}, {"key": "contacts.bulk_delete.error_title", "value": "Bulk delete failed"}, {"key": "contacts.facebook_messenger", "value": "Facebook Messenger"}, {"key": "contacts.instagram", "value": "Instagram"}, {"key": "contacts.no_contacts_found", "value": "No contacts found"}, {"key": "contacts.try_adjusting_filters", "value": "Try adjusting your search or filters"}, {"key": "contacts.add_first_contact", "value": "Add your first contact to get started"}, {"key": "contacts.table.name", "value": "Name"}, {"key": "contacts.table.contact_info", "value": "Contact Info"}, {"key": "contacts.table.company", "value": "Company"}, {"key": "contacts.table.channel", "value": "Channel"}, {"key": "contacts.table.tags", "value": "Tags"}, {"key": "contacts.table.last_updated", "value": "Last Updated"}, {"key": "contacts.table.actions", "value": "Actions"}, {"key": "contacts.message", "value": "Message"}, {"key": "contacts.delete_contact", "value": "Delete Contact"}, {"key": "contacts.delete_warning", "value": "This will permanently delete this contact and all associated conversations, messages, and notes. This action cannot be undone."}, {"key": "common.today", "value": "Today"}, {"key": "common.unknown_contact", "value": "Unknown Contact"}, {"key": "common.cancel", "value": "Cancel"}, {"key": "common.delete", "value": "Delete"}, {"key": "common.filter", "value": "Filter"}, {"key": "common.success", "value": "Success"}, {"key": "common.missing_information", "value": "Missing Information"}, {"key": "common.fill_required_fields", "value": "Please fill in all required fields"}, {"key": "pipeline.stage_created_success", "value": "Stage created successfully"}, {"key": "pipeline.stage_create_failed", "value": "Failed to create stage: {{error}}"}, {"key": "pipeline.stage_updated_success", "value": "Stage updated successfully"}, {"key": "pipeline.stage_update_failed", "value": "Failed to update stage: {{error}}"}, {"key": "pipeline.stage_deleted_success", "value": "Stage deleted successfully"}, {"key": "pipeline.stage_delete_failed", "value": "Failed to delete stage: {{error}}"}, {"key": "pipeline.deal_update_failed", "value": "Failed to update deal stage: {{error}}"}, {"key": "pipeline.stage_name_required", "value": "Stage name cannot be empty"}, {"key": "pipeline.add_stage", "value": "Add Stage"}, {"key": "pipeline.add_deal", "value": "Add Deal"}, {"key": "pipeline.no_stages", "value": "No Pipeline Stages"}, {"key": "pipeline.create_first_stage", "value": "Create your first stage to get started"}, {"key": "pipeline.add_first_stage", "value": "Add First Stage"}, {"key": "pipeline.add_stage_title", "value": "Add Pipeline Stage"}, {"key": "pipeline.add_stage_description", "value": "Create a new stage for your pipeline. Stages help organize your deals and track their progress."}, {"key": "pipeline.stage_name", "value": "Stage Name"}, {"key": "pipeline.stage_name_placeholder", "value": "e.g., Discovery, Negotiation, Proposal"}, {"key": "pipeline.stage_color", "value": "Stage Color"}, {"key": "pipeline.create_stage", "value": "Create Stage"}, {"key": "pipeline.edit_stage_title", "value": "Edit Pipeline Stage"}, {"key": "pipeline.edit_stage_description", "value": "Update the stage name and color."}, {"key": "pipeline.update_stage", "value": "Update Stage"}, {"key": "pipeline.delete_stage_title", "value": "Delete Pipeline Stage"}, {"key": "pipeline.stage_contains_deals", "value": "This stage contains {{count}} deals. Where would you like to move them?"}, {"key": "pipeline.delete_stage_confirmation", "value": "Are you sure you want to delete this stage? This action cannot be undone."}, {"key": "pipeline.move_deals_to", "value": "Move deals to"}, {"key": "pipeline.select_stage", "value": "Select a stage"}, {"key": "pipeline.select_target_stage_warning", "value": "You must select a target stage or the deals in this stage will be lost."}, {"key": "pipeline.delete_stage", "value": "Delete Stage"}, {"key": "calendar.event_created", "value": "Event Created"}, {"key": "calendar.appointment_created_success", "value": "Your appointment has been successfully created"}, {"key": "calendar.event_create_failed", "value": "Failed to create event: {{error}}"}, {"key": "calendar.event_updated", "value": "Event Updated"}, {"key": "calendar.appointment_updated_success", "value": "Your appointment has been successfully updated"}, {"key": "calendar.event_update_failed", "value": "Failed to update event: {{error}}"}, {"key": "calendar.event_canceled", "value": "Event Canceled"}, {"key": "calendar.appointment_canceled_success", "value": "Your appointment has been successfully canceled"}, {"key": "calendar.event_cancel_failed", "value": "Failed to cancel event: {{error}}"}, {"key": "calendar.not_connected", "value": "Google Calendar Not Connected"}, {"key": "calendar.connect_first", "value": "Please connect your Google Calendar in Settings first"}, {"key": "calendar.enter_schedule_name", "value": "Please enter a schedule name"}, {"key": "calendar.schedule_added", "value": "Schedule Added"}, {"key": "calendar.schedule_added_success", "value": "New schedule \"{{name}}\" has been added"}, {"key": "calendar.schedule_updated", "value": "Schedule Updated"}, {"key": "calendar.schedule_updated_success", "value": "Schedule \"{{name}}\" has been updated"}, {"key": "calendar.schedule_deleted", "value": "Schedule Deleted"}, {"key": "calendar.schedule_deleted_success", "value": "Schedule \"{{name}}\" has been deleted"}, {"key": "calendar.more", "value": "more"}, {"key": "calendar.personal_teams", "value": "Personal, Teams"}, {"key": "calendar.today", "value": "Today"}, {"key": "calendar.month", "value": "Month"}, {"key": "calendar.week", "value": "Week"}, {"key": "calendar.day", "value": "Day"}, {"key": "calendar.my_schedules", "value": "My Schedules"}, {"key": "calendar.categories", "value": "Categories"}, {"key": "calendar.sun", "value": "Sun"}, {"key": "calendar.mon", "value": "Mon"}, {"key": "calendar.tue", "value": "<PERSON><PERSON>"}, {"key": "calendar.wed", "value": "Wed"}, {"key": "calendar.thu", "value": "<PERSON>hu"}, {"key": "calendar.fri", "value": "<PERSON><PERSON>"}, {"key": "calendar.sat", "value": "Sat"}, {"key": "calendar.check_availability", "value": "Check Availability"}, {"key": "calendar.schedule_appointment", "value": "Schedule Appointment"}, {"key": "calendar.schedule_new_appointment", "value": "Schedule New Appointment"}, {"key": "calendar.create_new_event", "value": "Create a new event on your calendar."}, {"key": "calendar.title", "value": "Title"}, {"key": "calendar.description", "value": "Description"}, {"key": "calendar.location", "value": "Location"}, {"key": "calendar.category", "value": "Category"}, {"key": "calendar.select_category", "value": "Select category"}, {"key": "campaigns.completed", "value": "Campaign Completed"}, {"key": "campaigns.finished_processing", "value": "Campaign \"{{name}}\" has finished processing."}, {"key": "campaigns.fetch_failed", "value": "Failed to fetch campaigns"}, {"key": "campaigns.stats_fetch_failed", "value": "Failed to fetch campaign statistics"}, {"key": "campaigns.action_failed", "value": "Failed to {{action}} campaign"}, {"key": "campaigns.deleted_successfully", "value": "Campaign \"{{name}}\" deleted successfully"}, {"key": "campaigns.delete_failed", "value": "Failed to delete campaign"}, {"key": "campaigns.stats_recalculated", "value": "Stats Recalculated"}, {"key": "campaigns.stats_updated", "value": "Campaign statistics have been updated."}, {"key": "campaigns.stats_recalc_failed", "value": "Failed to recalculate stats: {{error}}"}, {"key": "campaigns.network_error", "value": "Network error: {{error}}"}, {"key": "campaigns.dashboard_description", "value": "Manage and monitor your mass messaging campaigns"}, {"key": "campaigns.live_updates", "value": "Live updates"}, {"key": "campaigns.polling_mode", "value": "Polling mode"}, {"key": "campaigns.refresh", "value": "Refresh"}, {"key": "campaigns.create_campaign", "value": "Create Campaign"}, {"key": "campaigns.total_campaigns", "value": "Total Campaigns"}, {"key": "campaigns.active_campaigns", "value": "Active Campaigns"}, {"key": "campaigns.total_recipients", "value": "Total Recipients"}, {"key": "campaigns.messages_delivered", "value": "Messages Delivered"}, {"key": "campaigns.delivery_rate", "value": "Delivery Rate"}, {"key": "campaigns.no_campaigns_found", "value": "No campaigns found"}, {"key": "campaigns.get_started_message", "value": "Get started by creating your first mass messaging campaign"}, {"key": "campaigns.details", "value": "Details"}, {"key": "campaigns.recipients", "value": "Recipients"}, {"key": "campaigns.progress", "value": "Progress"}, {"key": "campaigns.delivered", "value": "Delivered"}, {"key": "campaigns.failed", "value": "Failed"}, {"key": "campaigns.processed", "value": "processed"}, {"key": "campaigns.total", "value": "total"}, {"key": "campaigns.delete_campaign", "value": "Delete Campaign"}, {"key": "campaigns.delete_confirmation", "value": "Are you sure you want to delete the campaign \"{{name}}\"? This action cannot be undone and all campaign data will be permanently removed."}, {"key": "campaigns.start", "value": "Start"}, {"key": "campaigns.pause", "value": "Pause"}, {"key": "campaigns.resume", "value": "Resume"}, {"key": "campaigns.status.draft", "value": "Draft"}, {"key": "campaigns.status.scheduled", "value": "Scheduled"}, {"key": "campaigns.status.running", "value": "Running"}, {"key": "campaigns.status.paused", "value": "Paused"}, {"key": "campaigns.status.completed", "value": "Completed"}, {"key": "campaigns.status.cancelled", "value": "Cancelled"}, {"key": "campaigns.status.failed", "value": "Failed"}, {"key": "campaigns.filter.all", "value": "All"}, {"key": "campaigns.filter.draft", "value": "Draft"}, {"key": "campaigns.filter.running", "value": "Running"}, {"key": "campaigns.filter.paused", "value": "Paused"}, {"key": "campaigns.filter.completed", "value": "Completed"}, {"key": "flows.flow_assigned", "value": "Flow assigned"}, {"key": "flows.flow_assigned_success", "value": "Flow has been assigned to channel successfully."}, {"key": "flows.error_assigning_flow", "value": "Error assigning flow"}, {"key": "flows.assignment_updated", "value": "Assignment updated"}, {"key": "flows.assignment_updated_success", "value": "Flow assignment status has been updated."}, {"key": "flows.error_updating_assignment", "value": "Error updating assignment"}, {"key": "flows.assign", "value": "Assign"}, {"key": "flows.manage_flow_assignments", "value": "Manage Flow Assignments"}, {"key": "flows.assign_flow_description", "value": "Assign \"{{flowName}}\" to channels and manage active assignments."}, {"key": "flows.active_assignments", "value": "Active Assignments"}, {"key": "flows.channel", "value": "Channel"}, {"key": "flows.status", "value": "Status"}, {"key": "flows.actions", "value": "Actions"}, {"key": "flows.active", "value": "Active"}, {"key": "flows.inactive", "value": "Inactive"}, {"key": "flows.deactivate", "value": "Deactivate"}, {"key": "flows.activate", "value": "Activate"}, {"key": "flows.no_assignments_yet", "value": "No assignments yet"}, {"key": "flows.available_channels", "value": "Available Channels"}, {"key": "flows.no_channels_available", "value": "No channels available"}, {"key": "flows.flow_deleted", "value": "Flow deleted"}, {"key": "flows.flow_deleted_success", "value": "Flow has been deleted successfully."}, {"key": "flows.error_deleting_flow", "value": "Error deleting flow"}, {"key": "flows.delete_flow", "value": "Delete Flow"}, {"key": "flows.delete_flow_confirmation", "value": "Are you sure you want to delete \"{{flowName}}\"? This action cannot be undone."}, {"key": "flows.draft", "value": "Draft"}, {"key": "flows.archived", "value": "Archived"}, {"key": "flows.page_description", "value": "Create and manage automated conversation flows for your channels."}, {"key": "flows.flows", "value": "flows"}, {"key": "flows.limit_reached", "value": "Limit reached"}, {"key": "flows.contact_admin_upgrade", "value": "Contact your administrator to upgrade your plan"}, {"key": "flows.plan_limit_tooltip", "value": "You've reached your plan's flow limit"}, {"key": "flows.new_flow", "value": "New Flow"}, {"key": "flows.your_flows", "value": "Your Flows"}, {"key": "flows.flows_description", "value": "Flows can be assigned to one or more channels to handle conversations automatically."}, {"key": "flows.name", "value": "Name"}, {"key": "flows.updated", "value": "Updated"}, {"key": "flows.sorted_descending", "value": "Sorted descending"}, {"key": "flows.sorted_ascending", "value": "Sorted ascending"}, {"key": "flows.version", "value": "Version"}, {"key": "flows.no_flows_yet", "value": "No flows yet"}, {"key": "flows.create_first_flow", "value": "Create your first flow to start automating conversations."}, {"key": "flows.create_flow", "value": "Create Flow"}, {"key": "common.language_selector.title", "value": "Select Language"}, {"key": "common.language_selector.no_languages", "value": "No languages available"}, {"key": "common.language_selector.changing", "value": "Changing language..."}, {"key": "admin.backup.title", "value": "Backup Management"}, {"key": "admin.backup.loading", "value": "Loading backup system..."}, {"key": "admin.backup.tabs.overview", "value": "Overview"}, {"key": "admin.backup.tabs.backups", "value": "Backups"}, {"key": "admin.backup.tabs.schedules", "value": "Schedules"}, {"key": "admin.backup.tabs.settings", "value": "Settings"}, {"key": "admin.backup.stats.total_backups", "value": "Total Backups"}, {"key": "admin.backup.stats.total_size", "value": "Total Size"}, {"key": "admin.backup.stats.local_backups", "value": "Local Backups"}, {"key": "admin.backup.stats.cloud_backups", "value": "Cloud Backups"}, {"key": "admin.backup.quick_actions.title", "value": "Quick Actions"}, {"key": "admin.backup.quick_actions.description", "value": "Create manual backups and manage your backup system"}, {"key": "admin.backup.quick_actions.backup_description", "value": "Backup Description"}, {"key": "admin.backup.quick_actions.backup_description_placeholder", "value": "Enter backup description..."}, {"key": "admin.backup.quick_actions.storage_locations", "value": "Storage Locations"}, {"key": "admin.backup.storage.local", "value": "Local Storage"}, {"key": "admin.backup.storage.google_drive", "value": "Google Drive"}, {"key": "admin.backup.storage.drive", "value": "Drive"}, {"key": "admin.backup.storage.not_connected", "value": "(Not Connected)"}, {"key": "admin.backup.actions.create_backup", "value": "Create Backup"}, {"key": "admin.backup.history.title", "value": "Backup History"}, {"key": "admin.backup.history.description", "value": "View and manage all database backups"}, {"key": "admin.backup.table.filename", "value": "Filename"}, {"key": "admin.backup.table.type", "value": "Type"}, {"key": "admin.backup.table.size", "value": "Size"}, {"key": "admin.backup.table.status", "value": "Status"}, {"key": "admin.backup.table.storage", "value": "Storage"}, {"key": "admin.backup.table.created", "value": "Created"}, {"key": "admin.backup.table.actions", "value": "Actions"}, {"key": "admin.backup.status.creating", "value": "Creating"}, {"key": "admin.backup.status.completed", "value": "Completed"}, {"key": "admin.backup.status.failed", "value": "Failed"}, {"key": "admin.backup.status.uploading", "value": "Uploading"}, {"key": "admin.backup.status.uploaded", "value": "Uploaded"}, {"key": "admin.backup.empty.title", "value": "No Backups Found"}, {"key": "admin.backup.empty.description", "value": "Create your first backup to get started"}, {"key": "admin.backup.messages.config_saved", "value": "Backup configuration saved successfully"}, {"key": "admin.backup.messages.backup_started", "value": "Backup creation started successfully"}, {"key": "admin.backup.schedules.title", "value": "Backup Schedules"}, {"key": "admin.backup.schedules.description", "value": "Configure automated backup schedules"}, {"key": "admin.backup.schedules.new_schedule", "value": "New Schedule"}, {"key": "admin.backup.schedules.active_title", "value": "Active Schedules"}, {"key": "admin.backup.schedules.active_description", "value": "Manage your automated backup schedules"}, {"key": "admin.backup.schedules.table.name", "value": "Schedule Name"}, {"key": "admin.backup.schedules.table.frequency", "value": "Frequency"}, {"key": "admin.backup.schedules.table.time", "value": "Time"}, {"key": "admin.backup.schedules.table.next_run", "value": "Next Run"}, {"key": "admin.backup.schedules.table.storage", "value": "Storage"}, {"key": "admin.backup.schedules.table.status", "value": "Status"}, {"key": "admin.backup.schedules.table.actions", "value": "Actions"}, {"key": "admin.backup.schedules.status.enabled", "value": "Enabled"}, {"key": "admin.backup.schedules.status.disabled", "value": "Disabled"}, {"key": "admin.backup.schedules.empty.title", "value": "No Schedules Configured"}, {"key": "admin.backup.schedules.empty.description", "value": "Create your first automated backup schedule to get started"}, {"key": "admin.backup.schedules.actions.create", "value": "Create Schedule"}, {"key": "admin.backup.restore.title", "value": "Database Restoration"}, {"key": "admin.backup.restore.warning_step", "value": "Review the backup details and understand the implications"}, {"key": "admin.backup.restore.confirmation_step", "value": "Confirm your intention to restore the database"}, {"key": "admin.backup.restore.progress_step", "value": "Database restoration in progress"}, {"key": "admin.backup.restore.backup_info", "value": "Backup Information"}, {"key": "admin.backup.restore.filename", "value": "Filename"}, {"key": "admin.backup.restore.created", "value": "Created"}, {"key": "admin.backup.restore.size", "value": "Size"}, {"key": "admin.backup.restore.type", "value": "Type"}, {"key": "admin.backup.restore.description", "value": "Description"}, {"key": "admin.backup.restore.storage", "value": "Storage"}, {"key": "admin.backup.restore.warning_title", "value": "Critical Warning"}, {"key": "admin.backup.restore.warning_replace", "value": "This action will completely replace your current database"}, {"key": "admin.backup.restore.warning_data_lost", "value": "All current data will be permanently lost"}, {"key": "admin.backup.restore.warning_disconnect", "value": "All users will be disconnected during the restoration"}, {"key": "admin.backup.restore.warning_undone", "value": "The restoration process cannot be undone"}, {"key": "admin.backup.restore.warning_backup", "value": "Make sure you have a recent backup of the current state if needed"}, {"key": "admin.backup.restore.process_title", "value": "Restoration Process"}, {"key": "admin.backup.restore.process_download", "value": "Backup file will be downloaded if stored in cloud"}, {"key": "admin.backup.restore.process_verify", "value": "Backup integrity will be verified"}, {"key": "admin.backup.restore.process_clean", "value": "Current database will be cleaned"}, {"key": "admin.backup.restore.process_restore", "value": "Backup data will be restored"}, {"key": "admin.backup.restore.process_ready", "value": "System will be ready for use"}, {"key": "admin.backup.restore.confirmation_title", "value": "Final Confirmation Required"}, {"key": "admin.backup.restore.confirmation_text", "value": "To proceed with the database restoration, please type the backup filename exactly as shown below:"}, {"key": "admin.backup.restore.confirmation_label", "value": "Confirmation Text"}, {"key": "admin.backup.restore.confirmation_placeholder", "value": "Type \"{filename}\" to confirm"}, {"key": "admin.backup.restore.confirmation_help", "value": "This confirmation ensures you understand the consequences of this action."}, {"key": "admin.backup.restore.progress_title", "value": "Restoration in Progress"}, {"key": "admin.backup.restore.progress_processing", "value": "Processing..."}, {"key": "admin.backup.restore.completed_title", "value": "Restoration Completed"}, {"key": "admin.backup.restore.completed_message", "value": "Database has been successfully restored. The dialog will close automatically."}, {"key": "admin.backup.restore.failed_title", "value": "Restoration Failed"}, {"key": "admin.backup.restore.cancel", "value": "Cancel"}, {"key": "admin.backup.restore.understand_continue", "value": "I Understand, Continue"}, {"key": "admin.backup.restore.back", "value": "Back"}, {"key": "admin.backup.restore.restore_database", "value": "Restore Database"}, {"key": "admin.backup.restore.close", "value": "Close"}, {"key": "admin.backup.schedule_dialog.create_title", "value": "Create Backup Schedule"}, {"key": "admin.backup.schedule_dialog.edit_title", "value": "Edit Backup Schedule"}, {"key": "admin.backup.schedule_dialog.description", "value": "Configure automated backup schedule settings"}, {"key": "admin.backup.schedule_dialog.name_label", "value": "Schedule Name"}, {"key": "admin.backup.schedule_dialog.name_placeholder", "value": "e.g., Daily Backup, Weekly Archive"}, {"key": "admin.backup.schedule_dialog.name_help", "value": "A descriptive name for this backup schedule"}, {"key": "admin.backup.schedule_dialog.frequency_label", "value": "Backup Frequency"}, {"key": "admin.backup.schedule_dialog.frequency_daily", "value": "Daily"}, {"key": "admin.backup.schedule_dialog.frequency_weekly", "value": "Weekly"}, {"key": "admin.backup.schedule_dialog.frequency_monthly", "value": "Monthly"}, {"key": "admin.backup.schedule_dialog.time_label", "value": "Backup Time"}, {"key": "admin.backup.schedule_dialog.time_help", "value": "Time when the backup will be executed (24-hour format)"}, {"key": "admin.backup.schedule_dialog.day_of_week", "value": "Day of Week"}, {"key": "admin.backup.schedule_dialog.day_of_month", "value": "Day of Month"}, {"key": "admin.backup.schedule_dialog.day_of_month_help", "value": "Day of the month when backup will be executed (1-31)"}, {"key": "admin.backup.schedule_dialog.storage_label", "value": "Storage Locations"}, {"key": "admin.backup.schedule_dialog.storage_help", "value": "Where to store the backup files"}, {"key": "admin.backup.schedule_dialog.enable_title", "value": "Enable Schedule"}, {"key": "admin.backup.schedule_dialog.enable_help", "value": "Start this schedule immediately after creation"}, {"key": "admin.backup.schedule_dialog.update_button", "value": "Update Schedule"}, {"key": "admin.backup.schedule_dialog.create_button", "value": "Create Schedule"}, {"key": "admin.backup.days.sunday", "value": "Sunday"}, {"key": "admin.backup.days.monday", "value": "Monday"}, {"key": "admin.backup.days.tuesday", "value": "Tuesday"}, {"key": "admin.backup.days.wednesday", "value": "Wednesday"}, {"key": "admin.backup.days.thursday", "value": "Thursday"}, {"key": "admin.backup.days.friday", "value": "Friday"}, {"key": "admin.backup.days.saturday", "value": "Saturday"}, {"key": "admin.backup.messages.schedule_created", "value": "Schedule created successfully"}, {"key": "admin.backup.messages.schedule_updated", "value": "Schedule updated successfully"}, {"key": "admin.backup.messages.schedule_deleted", "value": "Schedule deleted successfully"}, {"key": "admin.backup.messages.backup_deleted", "value": "Backup deleted successfully"}, {"key": "admin.backup.messages.backup_valid", "value": "Backup Valid"}, {"key": "admin.backup.messages.backup_invalid", "value": "Backup Invalid"}, {"key": "admin.backup.messages.restore_successful", "value": "Restore Successful"}, {"key": "admin.backup.messages.restore_failed", "value": "Restore Failed"}, {"key": "admin.backup.messages.restore_error", "value": "<PERSON><PERSON>"}, {"key": "admin.backup.messages.settings_saved", "value": "Setting<PERSON> saved successfully"}, {"key": "admin.backup.messages.connection_successful", "value": "Connection Successful"}, {"key": "admin.backup.messages.connection_failed", "value": "Connection Failed"}, {"key": "admin.backup.actions.reset_defaults", "value": "Reset to Defaults"}, {"key": "admin.backup.actions.save_settings", "value": "Save Settings"}, {"key": "common.cancel", "value": "Cancel"}, {"key": "common.validation_error", "value": "Validation Error"}, {"key": "common.file_size.bytes", "value": "Bytes"}, {"key": "common.file_size.kb", "value": "KB"}, {"key": "common.file_size.mb", "value": "MB"}, {"key": "common.file_size.gb", "value": "GB"}, {"key": "common.file_size.tb", "value": "TB"}, {"key": "admin.backup.messages.validation_successful", "value": "Validation Successful"}, {"key": "admin.backup.messages.validation_failed", "value": "Validation Failed"}, {"key": "admin.backup.validation.schedule_name_required", "value": "Schedule name is required"}, {"key": "admin.backup.validation.oauth_credentials_required", "value": "Client ID and Client Secret are required"}, {"key": "admin.backup.default_description", "value": "Manual backup"}, {"key": "admin.backup.restore.preparing", "value": "Preparing restoration..."}, {"key": "admin.backup.oauth.credentials_configured", "value": "OAuth Credentials Configured"}, {"key": "admin.backup.oauth.client_id", "value": "Client ID"}, {"key": "admin.backup.oauth.redirect_uri", "value": "Redirect URI"}, {"key": "admin.backup.oauth.source", "value": "Source"}, {"key": "admin.backup.oauth.admin_interface", "value": "Admin Interface"}, {"key": "admin.backup.oauth.environment_variables", "value": "Environment Variables"}, {"key": "admin.backup.oauth.configured", "value": "Configured"}, {"key": "admin.backup.actions.download_backup", "value": "Download backup"}, {"key": "admin.backup.actions.verify_backup", "value": "Verify backup integrity"}, {"key": "admin.backup.actions.restore_backup", "value": "Restore database from this backup"}, {"key": "admin.backup.actions.delete_backup", "value": "Delete backup"}, {"key": "admin.backup.actions.edit_schedule", "value": "Edit schedule"}, {"key": "admin.backup.actions.disable_schedule", "value": "Disable schedule"}, {"key": "admin.backup.actions.enable_schedule", "value": "Enable schedule"}, {"key": "admin.backup.actions.delete_schedule", "value": "Delete schedule"}, {"key": "admin.backup.actions.upload_backup", "value": "Upload Backup"}, {"key": "admin.backup.upload.title", "value": "Upload Database Backup"}, {"key": "admin.backup.upload.description", "value": "Upload an external database backup file to add it to your backup collection. Supported formats: .sql, .backup, .dump, .bak"}, {"key": "admin.backup.upload.file_label", "value": "Backup File"}, {"key": "admin.backup.upload.file_help", "value": "Maximum file size: 500MB. Supported formats: .sql, .backup, .dump, .bak"}, {"key": "admin.backup.upload.description_label", "value": "Description (Optional)"}, {"key": "admin.backup.upload.description_placeholder", "value": "Enter a description for this backup..."}, {"key": "admin.backup.upload.storage_label", "value": "Storage Locations"}, {"key": "admin.backup.upload.storage_help", "value": "Choose where to store the uploaded backup"}, {"key": "admin.backup.upload.uploading", "value": "Uploading..."}, {"key": "admin.backup.upload.upload_button", "value": "Upload Backup"}, {"key": "admin.backup.upload.no_file_selected", "value": "Please select a backup file to upload"}, {"key": "admin.backup.messages.backup_uploaded", "value": "Backup uploaded successfully"}, {"key": "admin.backup.dialogs.delete_backup_title", "value": "Delete Backup"}, {"key": "admin.backup.dialogs.delete_backup_description", "value": "Are you sure you want to delete this backup? This action cannot be undone."}, {"key": "admin.backup.dialogs.delete_schedule_title", "value": "Delete Schedule"}, {"key": "admin.backup.dialogs.delete_schedule_description", "value": "Are you sure you want to delete this backup schedule? This action cannot be undone."}, {"key": "admin.backup.settings.default_storage_locations", "value": "Default Storage Locations"}, {"key": "admin.backup.settings.default_storage_help", "value": "Default storage locations for new backups"}, {"key": "admin.backup.google_drive.title", "value": "Google Drive Integration"}, {"key": "admin.backup.google_drive.description", "value": "Configure OAuth 2.0 credentials and cloud storage backup options"}, {"key": "admin.backup.google_drive.connection_title", "value": "Google Drive Connection"}, {"key": "admin.backup.google_drive.connection_description", "value": "Authorize {{appName}} to access your Google Drive for backup storage."}, {"key": "admin.backup.google_drive.connect_drive", "value": "Connect Google Drive"}, {"key": "admin.backup.google_drive.test_connection", "value": "Test Connection"}, {"key": "admin.backup.oauth.configuration_title", "value": "OAuth 2.0 Configuration"}, {"key": "admin.backup.oauth.configuration_description", "value": "Configure Google OAuth credentials for Drive access"}, {"key": "admin.backup.oauth.configured_status", "value": "Configured"}, {"key": "admin.backup.oauth.not_configured_status", "value": "Not Configured"}, {"key": "admin.backup.oauth.validate_credentials", "value": "Validate Credentials"}, {"key": "admin.backup.oauth.clear_credentials", "value": "Clear Credentials"}, {"key": "admin.backup.oauth.setup_required", "value": "OAuth Setup Required"}, {"key": "admin.backup.oauth.setup_description", "value": "To enable Google Drive backups, you need to configure OAuth 2.0 credentials from Google Cloud Console."}, {"key": "admin.backup.oauth.setup_instructions", "value": "Setup Instructions"}, {"key": "admin.backup.oauth.step_1", "value": "Go to"}, {"key": "admin.backup.oauth.google_cloud_console", "value": "Google Cloud Console"}, {"key": "admin.backup.oauth.step_2", "value": "Create a new project or select existing one"}, {"key": "admin.backup.oauth.step_3", "value": "Enable the Google Drive API"}, {"key": "admin.backup.oauth.step_4", "value": "Create OAuth 2.0 credentials (Web application)"}, {"key": "admin.backup.oauth.step_5", "value": "Add your redirect URI to authorized redirect URIs"}, {"key": "admin.backup.oauth.step_6", "value": "Copy the Client ID and Client Secret"}, {"key": "admin.backup.oauth.configure_credentials", "value": "Configure OAuth Credentials"}, {"key": "admin.backup.oauth.client_id_label", "value": "Google Client ID"}, {"key": "admin.backup.oauth.client_id_placeholder", "value": "Enter your Google OAuth Client ID"}, {"key": "admin.backup.oauth.client_id_help", "value": "The Client ID from your Google Cloud Console OAuth credentials"}, {"key": "admin.backup.oauth.client_secret_label", "value": "Google Client Secret"}, {"key": "admin.backup.oauth.client_secret_placeholder", "value": "Enter your Google OAuth Client Secret"}, {"key": "admin.backup.oauth.client_secret_help", "value": "The Client Secret from your Google Cloud Console OAuth credentials"}, {"key": "admin.backup.oauth.redirect_uri_label", "value": "Redirect URI"}, {"key": "admin.backup.oauth.redirect_uri_help", "value": "This URI must be added to your Google OAuth authorized redirect URIs"}, {"key": "admin.backup.oauth.save_credentials", "value": "Save Credentials"}, {"key": "admin.backup.dialogs.clear_oauth_title", "value": "Clear OAuth Credentials"}, {"key": "admin.backup.dialogs.clear_oauth_description", "value": "This will remove all stored Google OAuth credentials and disable Google Drive integration. You will need to reconfigure credentials to use Google Drive backups."}, {"key": "common.delete", "value": "Delete"}, {"key": "admin.backup.storage.local", "value": "Local Storage"}, {"key": "admin.backup.storage.google_drive", "value": "Google Drive"}, {"key": "admin.backup.storage.not_connected", "value": "(Not Connected)"}, {"key": "admin.backup.oauth.credentials_form_title", "value": "OAuth 2.0 Credentials"}, {"key": "admin.backup.google_drive.enable_title", "value": "Enable Google Drive Backups"}, {"key": "admin.backup.google_drive.enable_description", "value": "Store backups in Google Drive for cloud redundancy"}, {"key": "admin.backup.google_drive.connected_title", "value": "Google Drive Connected"}, {"key": "admin.backup.google_drive.connected_description", "value": "Your Google Drive account is connected and ready for backups."}, {"key": "admin.backup.google_drive.oauth_required_title", "value": "OAuth Configuration Required"}, {"key": "admin.backup.google_drive.oauth_required_description", "value": "Please configure OAuth 2.0 credentials above before enabling Google Drive backups."}, {"key": "admin.backup.settings.general_title", "value": "General Settings"}, {"key": "admin.backup.settings.general_description", "value": "Configure global backup system preferences"}, {"key": "admin.backup.settings.retention_label", "value": "Backup Retention Period (days)"}, {"key": "admin.backup.settings.retention_help", "value": "Backups older than this will be automatically deleted"}, {"key": "admin.backup.oauth.update_credentials", "value": "Update Credentials"}, {"key": "common.back", "value": "Back"}, {"key": "admin.company_deletion.title", "value": "Delete Company"}, {"key": "admin.company_deletion.messages.success_title", "value": "Company Deleted"}, {"key": "admin.company_deletion.messages.error_title", "value": "Deletion Failed"}, {"key": "admin.company_deletion.steps.preview_description", "value": "Review what will be permanently deleted"}, {"key": "admin.company_deletion.steps.confirm_description", "value": "Confirm the deletion by typing the company name"}, {"key": "admin.company_deletion.steps.final_description", "value": "Final confirmation - this action cannot be undone"}, {"key": "admin.company_deletion.data_types.users", "value": "Users"}, {"key": "admin.company_deletion.data_types.conversations", "value": "Conversations"}, {"key": "admin.company_deletion.data_types.messages", "value": "Messages"}, {"key": "admin.company_deletion.data_types.contacts", "value": "Contacts"}, {"key": "admin.company_deletion.data_types.flows", "value": "Flows"}, {"key": "admin.company_deletion.data_types.deals", "value": "Deals"}, {"key": "admin.company_deletion.data_types.payment_records", "value": "Payment Records"}, {"key": "admin.company_deletion.data_types.media_files", "value": "Media Files"}, {"key": "admin.company_deletion.data_types.whatsapp_sessions", "value": "WhatsApp Sessions"}, {"key": "admin.company_deletion.preview.warning_title", "value": "Warning: Irreversible Action"}, {"key": "admin.company_deletion.preview.warning_description", "value": "This will permanently delete the company and ALL associated data. This action cannot be undone."}, {"key": "admin.company_deletion.preview.data_title", "value": "Data to be permanently deleted"}, {"key": "admin.company_deletion.preview.warnings_title", "value": "Critical Warnings"}, {"key": "admin.company_deletion.preview.load_error", "value": "Failed to load deletion preview"}, {"key": "admin.company_deletion.confirm.title", "value": "Type Company Name to Continue"}, {"key": "admin.company_deletion.confirm.description", "value": "To confirm deletion, type the exact company name"}, {"key": "admin.company_deletion.confirm.label", "value": "Company Name"}, {"key": "admin.company_deletion.confirm.placeholder", "value": "Type \"{{name}}\" to confirm"}, {"key": "admin.company_deletion.final.title", "value": "Final Confirmation"}, {"key": "admin.company_deletion.final.description", "value": "You are about to permanently delete {{name}} and all its data. This action is irreversible and will immediately remove all associated information."}, {"key": "admin.company_deletion.final.consequences_title", "value": "What happens next"}, {"key": "admin.company_deletion.final.consequence_users", "value": "All user accounts will be deleted"}, {"key": "admin.company_deletion.final.consequence_conversations", "value": "All conversations and messages will be removed"}, {"key": "admin.company_deletion.final.consequence_contacts", "value": "All contacts and their data will be deleted"}, {"key": "admin.company_deletion.final.consequence_media", "value": "All media files will be permanently removed"}, {"key": "admin.company_deletion.final.consequence_whatsapp", "value": "All WhatsApp sessions will be terminated"}, {"key": "admin.company_deletion.final.consequence_payments", "value": "All payment records will be deleted"}, {"key": "admin.company_deletion.final.consequence_company", "value": "The company will be completely removed from the system"}, {"key": "admin.company_deletion.buttons.continue_to_confirmation", "value": "Continue to Confirmation"}, {"key": "admin.company_deletion.buttons.proceed_to_final", "value": "Proceed to Final Step"}, {"key": "admin.company_deletion.buttons.deleting", "value": "Deleting..."}, {"key": "admin.company_deletion.buttons.delete_permanently", "value": "Delete Company Permanently"}, {"key": "campaigns.builder.steps.basic", "value": "Basic Info"}, {"key": "campaigns.builder.steps.audience", "value": "Audience"}, {"key": "campaigns.builder.steps.content", "value": "Content"}, {"key": "campaigns.builder.steps.settings", "value": "Settings"}, {"key": "campaigns.builder.steps.antiban", "value": "Anti-Ban"}, {"key": "campaigns.builder.steps.review", "value": "Review"}, {"key": "campaigns.builder.messages.load_error", "value": "Failed to load campaign data"}, {"key": "campaigns.builder.messages.segment_created", "value": "Segment \"{{name}}\" created and selected"}, {"key": "campaigns.builder.messages.template_created", "value": "Template \"{{name}}\" created and selected"}, {"key": "campaigns.builder.messages.update_success", "value": "Campaign updated successfully"}, {"key": "campaigns.builder.messages.save_success", "value": "Campaign saved as draft"}, {"key": "campaigns.builder.messages.update_error", "value": "Failed to update campaign"}, {"key": "campaigns.builder.messages.save_error", "value": "Failed to save campaign"}, {"key": "campaigns.builder.messages.launch_success_title", "value": "Campaign Launched Successfully! 🚀"}, {"key": "campaigns.builder.messages.launch_success_description", "value": "\"{{name}}\" is now running and will send messages to {{count}} contacts"}, {"key": "campaigns.builder.messages.launch_error_title", "value": "Launch Failed"}, {"key": "campaigns.builder.messages.launch_error_description", "value": "Failed to launch campaign. Please try again."}, {"key": "campaigns.builder.validation.error_title", "value": "Validation Error"}, {"key": "campaigns.builder.validation.name_required", "value": "Campaign name is required"}, {"key": "campaigns.builder.validation.connection_required", "value": "At least one WhatsApp connection is required"}, {"key": "campaigns.builder.validation.segment_required", "value": "Audience segment is required"}, {"key": "campaigns.builder.validation.content_required", "value": "Message content is required"}, {"key": "campaigns.builder.validation.schedule_required", "value": "Scheduled date and time is required for scheduled campaigns"}, {"key": "campaigns.builder.basic.name_label", "value": "Campaign Name"}, {"key": "campaigns.builder.basic.name_placeholder", "value": "Enter campaign name"}, {"key": "campaigns.builder.basic.description_label", "value": "Description"}, {"key": "campaigns.builder.basic.description_placeholder", "value": "Describe your campaign"}, {"key": "campaigns.builder.basic.connections_label", "value": "WhatsApp Connections"}, {"key": "campaigns.builder.basic.connections_description", "value": "Select multiple WhatsApp accounts for better distribution and anti-ban protection"}, {"key": "campaigns.builder.basic.no_connections", "value": "No WhatsApp connections available"}, {"key": "campaigns.builder.basic.setup_connection", "value": "Please set up a WhatsApp connection in Settings > Channel Connections first"}, {"key": "campaigns.builder.basic.accounts_selected", "value": "account(s) selected for distribution"}, {"key": "campaigns.builder.basic.select_all", "value": "Select All"}, {"key": "campaigns.builder.basic.clear_all", "value": "Clear All"}, {"key": "campaigns.builder.basic.type_label", "value": "Campaign Type"}, {"key": "campaigns.builder.basic.type_immediate", "value": "Send Immediately"}, {"key": "campaigns.builder.basic.type_scheduled", "value": "Schedule for Later"}, {"key": "campaigns.builder.basic.type_drip", "value": "Drip Campaign"}, {"key": "campaigns.builder.basic.schedule_label", "value": "Scheduled Date & Time"}, {"key": "campaigns.builder.audience.segment_label", "value": "Select Audience Segment"}, {"key": "campaigns.builder.audience.create_segment", "value": "Create New Segment"}, {"key": "campaigns.builder.audience.segment_placeholder", "value": "Choose a segment"}, {"key": "campaigns.builder.audience.contacts", "value": "contacts"}, {"key": "campaigns.builder.audience.will_receive", "value": "contacts will receive this campaign"}, {"key": "campaigns.builder.content.template_label", "value": "Use Template (Optional)"}, {"key": "campaigns.builder.content.create_template", "value": "Create New Template"}, {"key": "campaigns.builder.content.template_placeholder", "value": "Choose a template"}, {"key": "campaigns.builder.content.message_label", "value": "Message Content"}, {"key": "campaigns.builder.content.message_placeholder", "value": "Enter your message content. Click 'Insert Variable' to add personalization..."}, {"key": "campaigns.builder.content.validation_title", "value": "Content Validation"}, {"key": "campaigns.builder.content.validation_score", "value": "Score"}, {"key": "campaigns.builder.content.validation_issues", "value": "Issues"}, {"key": "campaigns.builder.header.edit", "value": "Edit Campaign"}, {"key": "campaigns.builder.header.create", "value": "Create Campaign"}, {"key": "campaigns.builder.header.step_progress", "value": "Step {{current}} of {{total}}: {{title}}"}, {"key": "campaigns.builder.navigation.previous", "value": "Previous"}, {"key": "campaigns.builder.navigation.next", "value": "Next"}, {"key": "campaigns.builder.navigation.save_draft", "value": "Save Draft"}, {"key": "campaigns.builder.navigation.launch", "value": "Launch Campaign"}, {"key": "campaigns.builder.navigation.launching", "value": "Launching..."}, {"key": "campaigns.builder.launch.confirmation_title", "value": "Launch Campaign Confirmation"}, {"key": "campaigns.builder.launch.confirmation_description", "value": "Are you sure you want to launch this campaign? This action cannot be undone."}, {"key": "campaigns.builder.launch.summary_title", "value": "Campaign Summary"}, {"key": "campaigns.builder.launch.summary_name", "value": "Name"}, {"key": "campaigns.builder.launch.summary_type", "value": "Type"}, {"key": "campaigns.builder.launch.summary_recipients", "value": "Recipients"}, {"key": "campaigns.builder.launch.summary_scheduled", "value": "Scheduled for"}, {"key": "campaigns.builder.launch.warning_title", "value": "Important"}, {"key": "campaigns.builder.launch.warning_description", "value": "Once launched, this campaign will start sending messages immediately and cannot be stopped."}, {"key": "campaigns.builder.launch.confirm_button", "value": "Yes, Launch"}, {"key": "campaigns.builder.settings.rate_limiting_title", "value": "Rate Limiting & Anti-Ban Settings"}, {"key": "campaigns.builder.settings.messages_per_minute", "value": "Messages per Minute"}, {"key": "campaigns.builder.settings.messages_per_hour", "value": "Messages per Hour"}, {"key": "campaigns.builder.settings.delay_between_messages", "value": "Delay Between Messages (seconds)"}, {"key": "campaigns.builder.antiban.title", "value": "Anti-Ban Protection"}, {"key": "campaigns.builder.antiban.enable_label", "value": "Enable Anti-Ban Protection"}, {"key": "campaigns.builder.antiban.enable_description", "value": "Automatically apply intelligent rate limiting and account rotation"}, {"key": "campaigns.builder.antiban.mode_label", "value": "Protection Mode"}, {"key": "campaigns.builder.antiban.mode_description", "value": "Choose how aggressive the anti-ban protection should be"}, {"key": "campaigns.builder.antiban.mode_conservative", "value": "Conservative"}, {"key": "campaigns.builder.antiban.mode_conservative_desc", "value": "Slowest, safest"}, {"key": "campaigns.builder.antiban.mode_moderate", "value": "Moderate"}, {"key": "campaigns.builder.antiban.mode_moderate_desc", "value": "Balanced approach"}, {"key": "campaigns.builder.antiban.mode_aggressive", "value": "Aggressive"}, {"key": "campaigns.builder.antiban.mode_aggressive_desc", "value": "Faster, higher risk"}, {"key": "campaigns.builder.antiban.business_hours_label", "value": "Business Hours Only"}, {"key": "campaigns.builder.antiban.business_hours_desc", "value": "Send only during 9 AM - 6 PM"}, {"key": "campaigns.builder.antiban.respect_weekends_label", "value": "Respect Weekends"}, {"key": "campaigns.builder.antiban.respect_weekends_desc", "value": "Pause on weekends"}, {"key": "campaigns.builder.antiban.randomize_delays_label", "value": "Randomize Delays"}, {"key": "campaigns.builder.antiban.randomize_delays_desc", "value": "Add human-like variance to message timing"}, {"key": "campaigns.builder.antiban.min_delay_label", "value": "<PERSON> (seconds)"}, {"key": "campaigns.builder.antiban.max_delay_label", "value": "<PERSON> (seconds)"}, {"key": "campaigns.builder.antiban.account_rotation_label", "value": "Smart Account Rotation"}, {"key": "campaigns.builder.antiban.account_rotation_desc", "value": "Distribute messages across selected accounts"}, {"key": "campaigns.builder.antiban.cooldown_period_label", "value": "Account Cooldown Period (minutes)"}, {"key": "campaigns.builder.antiban.cooldown_period_desc", "value": "Rest time between high-volume sending for each account"}, {"key": "campaigns.builder.antiban.message_variation_label", "value": "Message Variation"}, {"key": "campaigns.builder.antiban.message_variation_desc", "value": "Add slight variations to avoid identical content"}, {"key": "campaigns.builder.antiban.account_health_title", "value": "Account Health Status"}, {"key": "campaigns.builder.antiban.last_active", "value": "Last active"}, {"key": "campaigns.builder.antiban.account_status_healthy", "value": "Healthy"}, {"key": "campaigns.builder.review.campaign_summary_title", "value": "Campaign Summary"}, {"key": "campaigns.builder.review.basic_info_title", "value": "Basic Information"}, {"key": "campaigns.builder.review.name_label", "value": "Name"}, {"key": "campaigns.builder.review.description_label", "value": "Description"}, {"key": "campaigns.builder.review.channel_label", "value": "Channel"}, {"key": "campaigns.builder.review.type_label", "value": "Type"}, {"key": "campaigns.builder.review.scheduled_for_label", "value": "Scheduled for"}, {"key": "campaigns.builder.review.whatsapp_accounts_title", "value": "WhatsApp Accounts"}, {"key": "campaigns.builder.review.accounts_selected_for_distribution", "value": "account(s) selected for distribution"}, {"key": "campaigns.builder.review.single_account_label", "value": "Single Account"}, {"key": "campaigns.builder.review.no_account_selected", "value": "No WhatsApp account selected"}, {"key": "campaigns.builder.review.audience_title", "value": "Audience"}, {"key": "campaigns.builder.review.segment_label", "value": "Segment"}, {"key": "campaigns.builder.review.no_segment_selected", "value": "No segment selected"}, {"key": "campaigns.builder.review.antiban_title", "value": "Anti-Ban Protection"}, {"key": "campaigns.builder.review.antiban_mode_label", "value": "Mode"}, {"key": "campaigns.builder.review.random_delays_badge", "value": "Random Delays"}, {"key": "campaigns.builder.review.account_rotation_badge", "value": "Account Rotation"}, {"key": "campaigns.builder.review.antiban_disabled", "value": "Anti-ban protection disabled"}, {"key": "campaigns.builder.review.rate_limiting_title", "value": "Rate Limiting"}, {"key": "campaigns.builder.review.per_minute_label", "value": "Per <PERSON>"}, {"key": "campaigns.builder.review.per_hour_label", "value": "Per Hour"}, {"key": "campaigns.builder.review.per_day_label", "value": "Per Day"}, {"key": "campaigns.builder.review.base_delay_label", "value": "Base Delay"}, {"key": "campaigns.builder.review.messages_unit", "value": "messages"}, {"key": "campaigns.builder.review.seconds_unit", "value": "seconds"}, {"key": "campaigns.builder.review.message_content_title", "value": "Message Content"}, {"key": "campaigns.builder.review.media_attachments_label", "value": "Media Attachments"}, {"key": "campaigns.builder.review.media_item_label", "value": "Media"}, {"key": "campaigns.details.title", "value": "Campaign Details"}, {"key": "campaigns.details.fetch_failed", "value": "Failed to fetch campaign details"}, {"key": "campaigns.details.export_failed", "value": "Failed to export Excel"}, {"key": "campaigns.details.export_success", "value": "Campaign data exported to Excel"}, {"key": "campaigns.details.export_excel", "value": "Excel"}, {"key": "campaigns.details.search_placeholder", "value": "Search by contact name or phone number..."}, {"key": "campaigns.details.filter_by_status", "value": "Filter by status"}, {"key": "campaigns.details.all_statuses", "value": "All Statuses"}, {"key": "campaigns.details.status.pending", "value": "Pending"}, {"key": "campaigns.details.status.sent", "value": "<PERSON><PERSON>"}, {"key": "campaigns.details.status.delivered", "value": "Delivered"}, {"key": "campaigns.details.status.failed", "value": "Failed"}, {"key": "campaigns.details.status.cancelled", "value": "Cancelled"}, {"key": "campaigns.details.no_data", "value": "No campaign data found"}, {"key": "campaigns.details.no_results", "value": "No results match your search criteria"}, {"key": "campaigns.details.table.contact_name", "value": "Contact Name"}, {"key": "campaigns.details.table.phone_number", "value": "Phone Number"}, {"key": "campaigns.details.table.whatsapp_account", "value": "WhatsApp Account"}, {"key": "campaigns.details.table.status", "value": "Status"}, {"key": "campaigns.details.table.sent_at", "value": "<PERSON><PERSON>"}, {"key": "campaigns.details.table.message_content", "value": "Message Content"}, {"key": "campaigns.details.table.error", "value": "Error"}, {"key": "campaigns.details.pagination.showing", "value": "Showing {{start}} to {{end}} of {{total}} results"}, {"key": "campaigns.details.pagination.previous", "value": "Previous"}, {"key": "campaigns.details.pagination.next", "value": "Next"}, {"key": "campaigns.details.pagination.page_info", "value": "Page {{current}} of {{total}}"}, {"key": "segments.create.title", "value": "Create Contact Segment"}, {"key": "segments.create.name_label", "value": "Segment Name"}, {"key": "segments.create.name_placeholder", "value": "e.g., VIP Customers, New Leads"}, {"key": "segments.create.description_label", "value": "Description (Optional)"}, {"key": "segments.create.description_placeholder", "value": "Describe this segment..."}, {"key": "segments.create.filter_criteria_title", "value": "<PERSON><PERSON>"}, {"key": "segments.create.contact_tags_label", "value": "Contact Tags"}, {"key": "segments.create.tag_placeholder", "value": "Enter tag name"}, {"key": "segments.create.tags_description", "value": "Contacts must have ALL selected tags"}, {"key": "segments.create.created_after_label", "value": "Created After"}, {"key": "segments.create.created_before_label", "value": "Created Before"}, {"key": "segments.create.contact_preview_title", "value": "Contact Preview"}, {"key": "segments.create.showing_first_50", "value": "Showing first 50 of"}, {"key": "segments.create.contacts", "value": "contacts"}, {"key": "segments.create.contacts_match_criteria", "value": "contacts match these criteria"}, {"key": "segments.create.excluded", "value": "excluded"}, {"key": "segments.create.loading_preview", "value": "Loading contact preview..."}, {"key": "segments.create.table.contact_name", "value": "Contact Name"}, {"key": "segments.create.table.phone", "value": "Phone"}, {"key": "segments.create.table.email", "value": "Email"}, {"key": "segments.create.table.tags", "value": "Tags"}, {"key": "segments.create.table.created", "value": "Created"}, {"key": "segments.create.table.last_activity", "value": "Last Activity"}, {"key": "segments.create.table.actions", "value": "Actions"}, {"key": "segments.create.table.unknown", "value": "Unknown"}, {"key": "segments.create.table.no_activity", "value": "No activity"}, {"key": "segments.create.exclude_contact_tooltip", "value": "Exclude contact from segment"}, {"key": "segments.create.all_contacts_excluded", "value": "All contacts have been excluded from this segment"}, {"key": "segments.create.restore_all_contacts", "value": "Restore all contacts"}, {"key": "segments.create.no_contacts_match", "value": "No contacts match the current criteria"}, {"key": "segments.create.try_adjusting_filters", "value": "Try adjusting your filters"}, {"key": "segments.create.add_filter_criteria", "value": "Add filter criteria to preview contacts"}, {"key": "segments.create.select_tags_or_dates", "value": "Select tags or date ranges to see matching contacts"}, {"key": "segments.create.excluded_contacts_title", "value": "Excluded Contacts"}, {"key": "segments.create.restore_all", "value": "Restore All"}, {"key": "segments.create.restore_contact_tooltip", "value": "Restore contact"}, {"key": "segments.create.create_button", "value": "Create Segment"}, {"key": "segments.create.contact_excluded_title", "value": "Contact excluded"}, {"key": "segments.create.contact_excluded_desc", "value": "Contact has been removed from this segment preview"}, {"key": "segments.create.contact_restored_title", "value": "Contact restored"}, {"key": "segments.create.contact_restored_desc", "value": "Contact has been added back to the segment preview"}, {"key": "segments.create.name_required", "value": "Please enter a segment name"}, {"key": "segments.create.criteria_required", "value": "Please add at least one filter criteria"}, {"key": "segments.create.success", "value": "Segment created successfully"}, {"key": "segments.create.failed", "value": "Failed to create segment"}, {"key": "segments.edit.title", "value": "Edit Contact Segment"}, {"key": "segments.edit.loading_segment", "value": "Loading segment..."}, {"key": "segments.edit.load_failed", "value": "Failed to load segment"}, {"key": "segments.edit.name_label", "value": "Segment Name"}, {"key": "segments.edit.name_placeholder", "value": "e.g., VIP Customers, New Leads"}, {"key": "segments.edit.description_label", "value": "Description (Optional)"}, {"key": "segments.edit.description_placeholder", "value": "Describe this segment..."}, {"key": "segments.edit.filter_criteria_title", "value": "<PERSON><PERSON>"}, {"key": "segments.edit.contact_tags_label", "value": "Contact Tags"}, {"key": "segments.edit.tag_placeholder", "value": "Add a tag..."}, {"key": "segments.edit.created_after_label", "value": "Created After"}, {"key": "segments.edit.created_before_label", "value": "Created Before"}, {"key": "segments.edit.contact_preview_title", "value": "Contact Preview"}, {"key": "segments.edit.contact_excluded_title", "value": "Contact excluded"}, {"key": "segments.edit.contact_excluded_desc", "value": "Contact has been removed from this segment preview"}, {"key": "segments.edit.contact_restored_title", "value": "Contact restored"}, {"key": "segments.edit.contact_restored_desc", "value": "Contact has been added back to the segment preview"}, {"key": "segments.edit.validation_error", "value": "Validation Error"}, {"key": "segments.edit.name_required", "value": "Segment name is required"}, {"key": "segments.edit.update_success", "value": "Segment updated successfully"}, {"key": "segments.edit.update_failed", "value": "Failed to update segment"}, {"key": "segments.edit.delete_success", "value": "Segment deleted successfully"}, {"key": "segments.edit.delete_failed", "value": "Failed to delete segment"}, {"key": "segments.edit.delete_button", "value": "Delete Segment"}, {"key": "segments.edit.updating", "value": "Updating..."}, {"key": "segments.edit.update_button", "value": "Update Segment"}, {"key": "segments.edit.delete_confirm_title", "value": "Delete Segment"}, {"key": "segments.edit.delete_confirm_message", "value": "Are you sure you want to delete this segment? This action cannot be undone."}, {"key": "segments.edit.delete_confirm_button", "value": "Delete Segment"}, {"key": "segments.edit.contacts", "value": "contacts"}, {"key": "segments.edit.showing_first_50", "value": "Showing first 50 of"}, {"key": "segments.edit.contacts_match_criteria", "value": "contacts match these criteria"}, {"key": "segments.edit.excluded", "value": "excluded"}, {"key": "segments.edit.loading_preview", "value": "Loading contact preview..."}, {"key": "segments.edit.table.contact_name", "value": "Contact Name"}, {"key": "segments.edit.table.phone", "value": "Phone"}, {"key": "segments.edit.table.email", "value": "Email"}, {"key": "segments.edit.table.tags", "value": "Tags"}, {"key": "segments.edit.table.created", "value": "Created"}, {"key": "segments.edit.table.last_activity", "value": "Last Activity"}, {"key": "segments.edit.table.actions", "value": "Actions"}, {"key": "segments.edit.table.unknown", "value": "Unknown"}, {"key": "segments.edit.table.no_activity", "value": "No activity"}, {"key": "segments.edit.exclude_contact_tooltip", "value": "Exclude contact from segment"}, {"key": "segments.edit.all_contacts_excluded", "value": "All contacts have been excluded from this segment"}, {"key": "segments.edit.restore_all_contacts", "value": "Restore all contacts"}, {"key": "segments.edit.no_contacts_match", "value": "No contacts match the current criteria"}, {"key": "segments.edit.try_adjusting_filters", "value": "Try adjusting your filters"}, {"key": "segments.edit.add_filter_criteria", "value": "Add filter criteria to preview contacts"}, {"key": "segments.edit.select_tags_or_dates", "value": "Select tags or date ranges to see matching contacts"}, {"key": "segments.edit.excluded_contacts_title", "value": "Excluded Contacts"}, {"key": "segments.edit.restore_all", "value": "Restore All"}, {"key": "segments.edit.restore_contact_tooltip", "value": "Restore contact"}, {"key": "templates.create.title", "value": "Create Template"}, {"key": "templates.create.name_label", "value": "Template Name"}, {"key": "templates.create.name_placeholder", "value": "e.g., Welcome Message"}, {"key": "templates.create.category_label", "value": "Category"}, {"key": "templates.create.description_label", "value": "Description (Optional)"}, {"key": "templates.create.description_placeholder", "value": "Describe this template..."}, {"key": "templates.create.content_label", "value": "Template Content"}, {"key": "templates.create.content_placeholder", "value": "Enter your template content. Click 'Insert Variable' to add personalization..."}, {"key": "templates.create.media_files_label", "value": "Media Files (Optional)"}, {"key": "templates.create.add_media", "value": "Add Media"}, {"key": "templates.create.supported_files", "value": "Supported: Images (JPEG, PNG, WebP), Videos (MP4, 3GP), Audio (MP3, AAC, OGG), Documents (PDF, DOC, DOCX). Max 10MB per file."}, {"key": "templates.create.create_button", "value": "Create Template"}, {"key": "templates.create.file_size_error", "value": "File size must be less than 10MB"}, {"key": "templates.create.image_type_error", "value": "Only JPEG, PNG, and WebP images are allowed"}, {"key": "templates.create.video_type_error", "value": "Only MP4 and 3GP videos are allowed"}, {"key": "templates.create.audio_type_error", "value": "Only MP3, AAC, and OGG audio files are allowed"}, {"key": "templates.create.document_type_error", "value": "Only PDF, DOC, and DOCX documents are allowed"}, {"key": "templates.create.invalid_file", "value": "Invalid File"}, {"key": "templates.create.upload_failed", "value": "Upload failed"}, {"key": "templates.create.upload_file_failed", "value": "Failed to upload {{filename}}"}, {"key": "templates.create.name_required", "value": "Please enter a template name"}, {"key": "templates.create.content_required", "value": "Please enter template content"}, {"key": "templates.create.success", "value": "Template created successfully"}, {"key": "templates.create.failed", "value": "Failed to create template"}, {"key": "templates.edit.title", "value": "Edit Template"}, {"key": "templates.edit.loading_template", "value": "Loading template..."}, {"key": "templates.edit.load_failed", "value": "Failed to load template"}, {"key": "templates.edit.name_label", "value": "Template Name"}, {"key": "templates.edit.name_placeholder", "value": "e.g., Welcome Message, Promotion Alert"}, {"key": "templates.edit.description_label", "value": "Description (Optional)"}, {"key": "templates.edit.description_placeholder", "value": "Describe this template..."}, {"key": "templates.edit.category_label", "value": "Category"}, {"key": "templates.edit.category.general", "value": "General"}, {"key": "templates.edit.category.marketing", "value": "Marketing"}, {"key": "templates.edit.category.support", "value": "Support"}, {"key": "templates.edit.category.notification", "value": "Notification"}, {"key": "templates.edit.category.welcome", "value": "Welcome"}, {"key": "templates.edit.content_label", "value": "Message Content"}, {"key": "templates.edit.content_placeholder", "value": "Enter your message content here... Click 'Insert Variable' to add personalization..."}, {"key": "templates.edit.attached_media", "value": "Attached Media"}, {"key": "templates.edit.validation_error", "value": "Validation Error"}, {"key": "templates.edit.name_content_required", "value": "Name and content are required"}, {"key": "templates.edit.update_success", "value": "Template updated successfully"}, {"key": "templates.edit.update_failed", "value": "Failed to update template"}, {"key": "templates.edit.delete_success", "value": "Template deleted successfully"}, {"key": "templates.edit.delete_failed", "value": "Failed to delete template"}, {"key": "templates.edit.delete_button", "value": "Delete Template"}, {"key": "templates.edit.updating", "value": "Updating..."}, {"key": "templates.edit.update_button", "value": "Update Template"}, {"key": "templates.edit.delete_confirm_title", "value": "Delete Template"}, {"key": "templates.edit.delete_confirm_message", "value": "Are you sure you want to delete this template? This action cannot be undone."}, {"key": "templates.edit.delete_confirm_button", "value": "Delete Template"}, {"key": "variables.contact_name", "value": "Contact Name"}, {"key": "variables.contact_name_desc", "value": "Full name of the contact"}, {"key": "variables.phone_number", "value": "Phone Number"}, {"key": "variables.phone_number_desc", "value": "Contact phone number"}, {"key": "variables.email_address", "value": "Email Address"}, {"key": "variables.email_address_desc", "value": "Contact email address"}, {"key": "variables.company_name", "value": "Company Name"}, {"key": "variables.company_name_desc", "value": "Contact company or organization"}, {"key": "variables.current_date", "value": "Current Date"}, {"key": "variables.current_date_desc", "value": "Today's date"}, {"key": "variables.current_time", "value": "Current Time"}, {"key": "variables.current_time_desc", "value": "Current time"}, {"key": "variables.custom_variable_desc", "value": "Custom variable: {{variable}}"}, {"key": "variables.category.contact", "value": "Contact Information"}, {"key": "variables.category.custom", "value": "Custom Variables"}, {"key": "variables.category.system", "value": "System Variables"}, {"key": "variables.category.other", "value": "Other"}, {"key": "variables.insert_variable", "value": "Insert Variable"}, {"key": "variables.search_placeholder", "value": "Search variables..."}, {"key": "variables.no_variables_found", "value": "No variables found."}, {"key": "variables.help_text", "value": "Use variables like"}, {"key": "variables.for_personalization", "value": "for personalization"}, {"key": "contacts.avatar.refresh_tooltip", "value": "Refresh WhatsApp profile picture"}, {"key": "contacts.edit.title", "value": "Edit Contact"}, {"key": "contacts.edit.description", "value": "Make changes to the contact information below."}, {"key": "contacts.edit.contact_id_missing", "value": "Contact ID is missing"}, {"key": "contacts.edit.update_failed", "value": "Failed to update contact"}, {"key": "contacts.edit.success_title", "value": "Contact updated"}, {"key": "contacts.edit.success_description", "value": "The contact has been successfully updated."}, {"key": "contacts.edit.error_title", "value": "Update failed"}, {"key": "contacts.edit.name_label", "value": "Name"}, {"key": "contacts.edit.email_label", "value": "Email"}, {"key": "contacts.edit.phone_label", "value": "Phone"}, {"key": "contacts.edit.company_label", "value": "Company"}, {"key": "contacts.edit.channel_label", "value": "Channel"}, {"key": "contacts.edit.select_channel_placeholder", "value": "Select channel"}, {"key": "contacts.edit.channel.whatsapp_official", "value": "WhatsApp Official"}, {"key": "contacts.edit.channel.whatsapp_unofficial", "value": "WhatsApp Unofficial"}, {"key": "contacts.edit.channel.messenger", "value": "Facebook Messenger"}, {"key": "contacts.edit.channel.instagram", "value": "Instagram"}, {"key": "contacts.edit.channel_identifier_label", "value": "Channel Identifier"}, {"key": "contacts.edit.channel_identifier_placeholder", "value": "Phone number or ID"}, {"key": "contacts.edit.tags_label", "value": "Tags (comma separated)"}, {"key": "contacts.edit.tags_placeholder", "value": "lead, customer, etc."}, {"key": "contacts.edit.notes_label", "value": "Notes"}, {"key": "contacts.edit.saving", "value": "Saving..."}, {"key": "contacts.edit.save_changes", "value": "Save Changes"}, {"key": "agents.fetch_failed", "value": "Failed to fetch agents: {{status}} {{error}}"}, {"key": "agents.assign_failed", "value": "Failed to assign conversation"}, {"key": "agents.unassign_failed", "value": "Failed to unassign conversation"}, {"key": "agents.assigned_title", "value": "Conversation Assigned"}, {"key": "agents.unassigned_title", "value": "Conversation Unassigned"}, {"key": "agents.assigned_description", "value": "Conversation assigned to {{<PERSON><PERSON><PERSON>}}"}, {"key": "agents.unassigned_description", "value": "Conversation has been unassigned"}, {"key": "agents.assignment_failed_title", "value": "Assignment Failed"}, {"key": "agents.assignment_failed_description", "value": "Failed to update assignment"}, {"key": "agents.reassign_conversation", "value": "Reassign Conversation"}, {"key": "agents.assign_conversation", "value": "Assign Conversation"}, {"key": "agents.loading_agents", "value": "Loading agents..."}, {"key": "agents.error_loading_agents", "value": "Error loading agents"}, {"key": "agents.no_agents_available", "value": "No agents available"}, {"key": "agents.unassign", "value": "Unassign"}, {"key": "agents.unassigned", "value": "Unassigned"}, {"key": "agents.assign", "value": "Assign"}, {"key": "contacts.details.contact_updated_title", "value": "Contact updated"}, {"key": "contacts.details.contact_updated_description", "value": "Contact information has been updated."}, {"key": "contacts.details.fetch_notes_failed", "value": "Failed to fetch notes"}, {"key": "contacts.details.save_note_failed", "value": "Failed to save note"}, {"key": "contacts.details.note_saved_successfully", "value": "Note saved successfully"}, {"key": "contacts.details.channel.whatsapp", "value": "WhatsApp"}, {"key": "contacts.details.channel.whatsapp_business", "value": "WhatsApp Business"}, {"key": "contacts.details.channel.whatsapp_unofficial", "value": "WhatsApp (Unofficial)"}, {"key": "contacts.details.channel.messenger", "value": "<PERSON>"}, {"key": "contacts.details.channel.instagram", "value": "Instagram"}, {"key": "contacts.details.channel.chat", "value": "Cha<PERSON>"}, {"key": "contacts.details.unknown", "value": "Unknown"}, {"key": "contacts.details.close_details", "value": "Close contact details"}, {"key": "contacts.details.show_details", "value": "Show contact details"}, {"key": "contacts.details.title", "value": "Contact Details"}, {"key": "contacts.details.contact_information", "value": "Contact Information"}, {"key": "contacts.details.full_name", "value": "Full Name"}, {"key": "contacts.details.phone", "value": "Phone"}, {"key": "contacts.details.email", "value": "Email"}, {"key": "contacts.details.company", "value": "Company"}, {"key": "contacts.details.not_provided", "value": "Not provided"}, {"key": "contacts.details.edit_details", "value": "Edit details"}, {"key": "contacts.details.tags", "value": "Tags"}, {"key": "contacts.details.no_tags_added", "value": "No tags added"}, {"key": "contacts.details.conversation_details", "value": "Conversation Details"}, {"key": "contacts.details.first_contacted", "value": "First contacted"}, {"key": "contacts.details.channel", "value": "Channel"}, {"key": "messages.input.microphone_access_error", "value": "Could not access microphone. Please check permissions."}, {"key": "messages.input.microphone_denied", "value": "Microphone access denied. Please allow microphone access and try again."}, {"key": "messages.input.microphone_not_found", "value": "No microphone found. Please connect a microphone and try again."}, {"key": "messages.input.recording_error", "value": "Recording Error"}, {"key": "messages.input.no_recording", "value": "No recording to send"}, {"key": "messages.input.recording_too_short", "value": "Recording is too short or empty"}, {"key": "messages.input.voice_message_sent", "value": "Voice message sent"}, {"key": "messages.input.voice_message_failed", "value": "Failed to send voice message"}, {"key": "messages.input.send_message_failed", "value": "Failed to send message"}, {"key": "messages.input.bot_disabled", "value": "<PERSON><PERSON> Disabled"}, {"key": "messages.input.bot_enabled", "value": "Bot Enabled"}, {"key": "messages.input.messages_as_you", "value": "Messages will now be sent as you"}, {"key": "messages.input.messages_as_bot", "value": "Messages will now be sent as the bot assistant"}, {"key": "messages.input.file_too_large", "value": "File Too Large"}, {"key": "messages.input.max_file_size", "value": "Maximum file size is 10MB"}, {"key": "messages.input.record_voice_message", "value": "Record voice message"}, {"key": "messages.input.type_message", "value": "Type a message..."}, {"key": "messages.input.resume", "value": "Resume"}, {"key": "messages.input.pause", "value": "Pause"}, {"key": "messages.input.stop", "value": "Stop"}, {"key": "messages.input.send", "value": "Send"}, {"key": "conversations.item.channel.whatsapp", "value": "WhatsApp"}, {"key": "conversations.item.channel.messenger", "value": "<PERSON>"}, {"key": "conversations.item.channel.instagram", "value": "Instagram"}, {"key": "conversations.item.channel.email", "value": "Email"}, {"key": "conversations.item.channel.sms", "value": "SMS"}, {"key": "conversations.item.channel.web_chat", "value": "Web Chat"}, {"key": "conversations.item.channel.chat", "value": "Cha<PERSON>"}, {"key": "conversations.item.yesterday", "value": "Yesterday"}, {"key": "conversations.item.no_messages_yet", "value": "No messages yet"}, {"key": "conversations.item.sent_image", "value": "📷 Sent an image"}, {"key": "conversations.item.image", "value": "📷 Image"}, {"key": "conversations.item.sent_video", "value": "🎥 Sent a video"}, {"key": "conversations.item.video", "value": "🎥 Video"}, {"key": "conversations.item.sent_audio", "value": "🎵 Sent an audio"}, {"key": "conversations.item.audio", "value": "🎵 Audio"}, {"key": "conversations.item.sent_document", "value": "📄 Sent a document"}, {"key": "conversations.item.document", "value": "📄 Document"}, {"key": "conversations.item.conversation_with", "value": "Conversation with"}, {"key": "conversations.item.unread_messages", "value": "unread messages"}, {"key": "conversations.item.more", "value": "more"}, {"key": "conversations.item.new_lead", "value": "New Lead"}, {"key": "conversations.item.bot_active", "value": "Bo<PERSON> active"}, {"key": "conversations.item.awaiting_reply", "value": "Awaiting reply"}, {"key": "conversations.item.bot", "value": "Bot"}, {"key": "conversations.item.waiting", "value": "Waiting"}, {"key": "conversations.view.channel.whatsapp", "value": "WhatsApp"}, {"key": "conversations.view.channel.messenger", "value": "<PERSON>"}, {"key": "conversations.view.channel.instagram", "value": "Instagram"}, {"key": "conversations.view.channel.email", "value": "Email"}, {"key": "conversations.view.channel.sms", "value": "SMS"}, {"key": "conversations.view.channel.web_chat", "value": "Web Chat"}, {"key": "conversations.view.channel.chat", "value": "Cha<PERSON>"}, {"key": "conversations.view.add_contact", "value": "Add Contact"}, {"key": "conversations.view.start_call", "value": "Start Call"}, {"key": "conversations.view.schedule", "value": "Schedule"}, {"key": "conversations.view.more_options", "value": "More options"}, {"key": "contacts.edit_dialog.contact_id_missing", "value": "Contact ID is missing"}, {"key": "contacts.edit_dialog.update_failed", "value": "Failed to update contact"}, {"key": "contacts.edit_dialog.success_title", "value": "Contact updated"}, {"key": "contacts.edit_dialog.success_description", "value": "The contact has been successfully updated."}, {"key": "contacts.edit_dialog.error_title", "value": "Update failed"}, {"key": "contacts.edit_dialog.invalid_file_type", "value": "Invalid file type"}, {"key": "contacts.edit_dialog.invalid_file_type_desc", "value": "Please select a valid image file (JPEG, PNG, GIF, or WebP)."}, {"key": "contacts.edit_dialog.file_too_large", "value": "File too large"}, {"key": "contacts.edit_dialog.file_too_large_desc", "value": "File size is {{sizeMB}}MB. Please select an image smaller than 5MB."}, {"key": "contacts.edit_dialog.image_too_large", "value": "Image too large"}, {"key": "contacts.edit_dialog.image_too_large_desc", "value": "Image dimensions are {{width}}x{{height}}. Please use an image smaller than {{maxDimension}}x{{maxDimension}} pixels."}, {"key": "contacts.edit_dialog.image_selected", "value": "Image selected"}, {"key": "contacts.edit_dialog.image_selected_desc", "value": "Profile picture has been selected successfully."}, {"key": "contacts.edit_dialog.error_reading_file", "value": "Error reading file"}, {"key": "contacts.edit_dialog.error_reading_file_desc", "value": "Failed to read the selected image file."}, {"key": "contacts.edit_dialog.invalid_image", "value": "Invalid image"}, {"key": "contacts.edit_dialog.invalid_image_desc", "value": "The selected file is not a valid image."}, {"key": "contacts.edit_dialog.unexpected_error", "value": "An unexpected error occurred while processing the image."}, {"key": "contacts.edit_dialog.image_removed", "value": "Image removed"}, {"key": "contacts.edit_dialog.image_removed_desc", "value": "Profile picture has been removed."}, {"key": "contacts.edit_dialog.upload_disabled", "value": "Upload disabled"}, {"key": "contacts.edit_dialog.upload_disabled_desc", "value": "File upload is currently disabled."}, {"key": "contacts.edit_dialog.upload_error", "value": "Upload error"}, {"key": "contacts.edit_dialog.upload_not_available", "value": "File upload is not available. Please try refreshing the page."}, {"key": "contacts.edit_dialog.upload_failed", "value": "Failed to open file picker. Please try again."}, {"key": "contacts.edit_dialog.validation_error", "value": "Validation error"}, {"key": "contacts.edit_dialog.name_required", "value": "Contact name is required."}, {"key": "contacts.edit_dialog.invalid_email", "value": "Please enter a valid email address."}, {"key": "contacts.edit_dialog.invalid_phone", "value": "Please enter a valid phone number (7-20 digits)."}, {"key": "contacts.edit_dialog.title", "value": "Edit Contact Details"}, {"key": "contacts.edit_dialog.processing", "value": "Processing..."}, {"key": "contacts.edit_dialog.upload_photo", "value": "Upload Photo"}, {"key": "contacts.edit_dialog.sync_from_whatsapp", "value": "Sync from WhatsApp"}, {"key": "contacts.edit_dialog.full_name_required", "value": "Full Name *"}, {"key": "contacts.edit_dialog.enter_full_name", "value": "Enter full name"}, {"key": "contacts.edit_dialog.email", "value": "Email"}, {"key": "contacts.edit_dialog.enter_email", "value": "Enter email address"}, {"key": "contacts.edit_dialog.phone_number", "value": "Phone Number"}, {"key": "contacts.edit_dialog.enter_phone", "value": "Enter phone number"}, {"key": "contacts.edit_dialog.company", "value": "Company"}, {"key": "contacts.edit_dialog.enter_company", "value": "Enter company name"}, {"key": "contacts.edit_dialog.primary_channel", "value": "Primary Channel"}, {"key": "contacts.edit_dialog.select_channel", "value": "Select channel"}, {"key": "contacts.edit_dialog.whatsapp_official", "value": "WhatsApp Official"}, {"key": "contacts.edit_dialog.whatsapp_unofficial", "value": "WhatsApp Unofficial"}, {"key": "contacts.edit_dialog.facebook_messenger", "value": "Facebook Messenger"}, {"key": "contacts.edit_dialog.instagram", "value": "Instagram"}, {"key": "contacts.edit_dialog.channel_identifier", "value": "Channel Identifier"}, {"key": "contacts.edit_dialog.phone_username_id", "value": "Phone number, username, or ID"}, {"key": "contacts.edit_dialog.tags", "value": "Tags"}, {"key": "contacts.edit_dialog.enter_tags", "value": "Enter tags separated by commas (e.g., lead, customer, vip)"}, {"key": "contacts.edit_dialog.tags_help", "value": "Separate multiple tags with commas"}, {"key": "contacts.edit_dialog.notes", "value": "Notes"}, {"key": "contacts.edit_dialog.add_notes", "value": "Add any additional notes about this contact..."}, {"key": "contacts.edit_dialog.saving", "value": "Saving..."}, {"key": "contacts.edit_dialog.save_changes", "value": "Save Changes"}, {"key": "contacts.edit_dialog.unsaved_changes", "value": "Unsaved Changes"}, {"key": "contacts.edit_dialog.unsaved_changes_message", "value": "You have unsaved changes. Are you sure you want to close without saving?"}, {"key": "contacts.edit_dialog.continue_editing", "value": "Continue Editing"}, {"key": "contacts.edit_dialog.discard_changes", "value": "Discard Changes"}, {"key": "contacts.edit_dialog.upload_profile_picture_failed", "value": "Failed to upload profile picture"}, {"key": "contacts.edit_dialog.profile_picture_updated", "value": "Profile picture updated"}, {"key": "contacts.edit_dialog.profile_picture_uploaded", "value": "The profile picture has been uploaded successfully."}, {"key": "contacts.edit_dialog.profile_picture_upload_failed", "value": "Profile picture upload failed"}, {"key": "contacts.edit_dialog.contact_updated_upload_failed", "value": "Contact was updated but profile picture upload failed."}, {"key": "contacts.edit_dialog.profile_preview", "value": "Profile preview"}, {"key": "media_upload.no_file_selected", "value": "No media file selected"}, {"key": "media_upload.sent_successfully", "value": "Media message sent successfully"}, {"key": "media_upload.error_sending", "value": "Error Sending Media"}, {"key": "media_upload.send_failed", "value": "Failed to send media message"}, {"key": "media_upload.image_preview", "value": "Image preview"}, {"key": "media_upload.file_size_kb", "value": "{{size}} KB"}, {"key": "media_upload.title", "value": "Send Media"}, {"key": "media_upload.description", "value": "Preview and send media files through WhatsApp"}, {"key": "media_upload.caption_placeholder", "value": "Add a caption (optional)"}, {"key": "media_upload.sending", "value": "Sending..."}, {"key": "media_upload.send", "value": "Send"}, {"key": "message_bubble.download_failed", "value": "Failed to download media"}, {"key": "message_bubble.media_downloaded", "value": "Media downloaded"}, {"key": "message_bubble.download_success", "value": "Media has been downloaded successfully"}, {"key": "message_bubble.download_failed_title", "value": "Download failed"}, {"key": "message_bubble.image_message", "value": "Image message"}, {"key": "message_bubble.downloading", "value": "Downloading..."}, {"key": "message_bubble.download_image", "value": "Download Image"}, {"key": "message_bubble.download_video", "value": "Download Video"}, {"key": "message_bubble.video_not_supported", "value": "Your browser does not support the video element."}, {"key": "message_bubble.audio_not_supported", "value": "Your browser does not support the audio element."}, {"key": "message_bubble.audio_message", "value": "Audio message"}, {"key": "message_bubble.loading", "value": "Loading..."}, {"key": "message_bubble.download", "value": "Download"}, {"key": "message_bubble.document", "value": "Document"}, {"key": "message_bubble.open", "value": "Open"}, {"key": "message_bubble.reply", "value": "Reply to this message"}, {"key": "message_bubble.delete", "value": "Delete this message"}, {"key": "message_bubble.delete_failed", "value": "Failed to delete message"}, {"key": "message_bubble.delete_failed_title", "value": "Delete failed"}, {"key": "message_bubble.message_deleted", "value": "Message deleted"}, {"key": "message_bubble.delete_success", "value": "Message has been deleted successfully"}, {"key": "message_bubble.confirm_delete_title", "value": "Delete Message"}, {"key": "message_bubble.confirm_delete_message", "value": "Are you sure you want to delete this message? This action cannot be undone."}, {"key": "message_bubble.media_message", "value": "Media message"}, {"key": "message_bubble.deleting", "value": "Deleting..."}, {"key": "message_bubble.delete_confirm", "value": "Delete"}, {"key": "message_input.replying_to", "value": "Replying to"}, {"key": "message_input.cancel_reply", "value": "<PERSON><PERSON> reply"}, {"key": "message_input.reply_failed", "value": "Failed to send reply"}, {"key": "message_input.reply_sent", "value": "Reply sent"}, {"key": "message_input.reply_success", "value": "Your reply has been sent successfully"}, {"key": "channel.reply_not_supported", "value": "Replies are not supported for this channel"}, {"key": "channel.delete_not_supported", "value": "Message deletion is not supported for this channel"}, {"key": "channel.message_too_old", "value": "Message is too old to be deleted"}, {"key": "channel.whatsapp_delete_limit", "value": "WhatsApp messages can only be deleted within 3 days"}, {"key": "message_bubble.sticker", "value": "<PERSON>er"}, {"key": "message_bubble.link_preview", "value": "Link preview"}, {"key": "message_bubble.contact_avatar", "value": "Contact avatar"}, {"key": "message_bubble.bot", "value": "Bot"}, {"key": "message_bubble.powerchat_assistant", "value": "{{appName}} Assistant"}, {"key": "clear_history.confirm_message", "value": "Are you sure you want to clear all chat history with \"{{conversationName}}\"? This will permanently delete all messages and media files from {{appName}}."}, {"key": "clear_history.confirm_group_message", "value": "Are you sure you want to clear all chat history for \"{{conversationName}}\"? This will permanently delete all messages and media files from {{appName}}."}, {"key": "clear_history.warning_title", "value": "Important:"}, {"key": "clear_history.warning_irreversible", "value": "This action cannot be undone"}, {"key": "clear_history.warning_local_only", "value": "Messages will only be deleted from {{appName}}, not from WhatsApp"}, {"key": "clear_history.warning_media", "value": "All associated media files will be permanently deleted"}, {"key": "new_conversation.name_required", "value": "Contact name is required"}, {"key": "new_conversation.phone_required", "value": "Phone number is required"}, {"key": "new_conversation.phone_invalid", "value": "Please enter a valid phone number with at least 10 digits"}, {"key": "new_conversation.connection_required", "value": "Please select a WhatsApp connection"}, {"key": "new_conversation.no_connections", "value": "No active WhatsApp connections found. Please connect WhatsApp in Settings first."}, {"key": "new_conversation.create_failed", "value": "Failed to create conversation"}, {"key": "new_conversation.success_message", "value": "WhatsApp conversation initiated successfully."}, {"key": "new_conversation.create_error", "value": "Failed to create conversation. Please try again."}, {"key": "new_conversation.no_connection_selected", "value": "No Connection Selected"}, {"key": "new_conversation.select_connection", "value": "Please select a WhatsApp connection."}, {"key": "new_conversation.title", "value": "Start New WhatsApp Conversation"}, {"key": "new_conversation.description", "value": "Enter contact details to initiate a new WhatsApp conversation."}, {"key": "new_conversation.contact_name_required", "value": "Contact Name *"}, {"key": "new_conversation.enter_contact_name", "value": "Enter contact's full name"}, {"key": "new_conversation.phone_number_required", "value": "Phone Number *"}, {"key": "new_conversation.enter_phone_number", "value": "Enter phone number (e.g., +1234567890)"}, {"key": "new_conversation.include_country_code", "value": "Include country code for international numbers"}, {"key": "new_conversation.whatsapp_connection_required", "value": "WhatsApp Connection *"}, {"key": "new_conversation.loading_connections", "value": "Loading connections..."}, {"key": "new_conversation.select_whatsapp_connection", "value": "Select WhatsApp connection"}, {"key": "new_conversation.no_connections_available", "value": "No active WhatsApp connections available"}, {"key": "new_conversation.initial_message_optional", "value": "Initial Message (Optional)"}, {"key": "new_conversation.enter_initial_message", "value": "Enter an optional first message to send..."}, {"key": "new_conversation.initial_message_help", "value": "This message will be sent immediately after creating the conversation"}, {"key": "new_conversation.creating", "value": "Creating..."}, {"key": "new_conversation.start_conversation", "value": "Start Conversation"}, {"key": "inbox.failed_load_messages", "value": "Failed to load conversation messages"}, {"key": "inbox.conversation_assigned", "value": "Conversation Assigned"}, {"key": "inbox.conversation_assigned_to_agent", "value": "Conversation has been assigned to an agent"}, {"key": "inbox.conversation_unassigned", "value": "Conversation has been unassigned"}, {"key": "inbox.conversation_unassigned_desc", "value": "Conversation has been unassigned"}, {"key": "inbox.new_conversation", "value": "New Conversation"}, {"key": "inbox.not_connected", "value": "Not Connected"}, {"key": "inbox.cannot_send_message", "value": "Cannot send message, not connected to server"}, {"key": "inbox.cannot_send_media", "value": "Cannot send media, not connected to server"}, {"key": "inbox.not_connected_server", "value": "Not connected to server"}, {"key": "inbox.conversation_not_found", "value": "Conversation not found"}, {"key": "inbox.network_error", "value": "Network error"}, {"key": "inbox.failed_send_media", "value": "Failed to send media message"}, {"key": "inbox.server_error", "value": "Server error ({{status}}): {{statusText}}"}, {"key": "inbox.send_message_failed", "value": "Failed to send message"}, {"key": "flow_builder.duplicate_node", "value": "Duplicate node"}, {"key": "flow_builder.delete_node", "value": "Delete node"}, {"key": "flow_builder.send_message", "value": "Send Message"}, {"key": "flow_builder.default_message", "value": "Hello! How can I help you?"}, {"key": "flow_builder.var_contact_name", "value": "<PERSON>'s name"}, {"key": "flow_builder.var_contact_phone", "value": "Contact's phone number"}, {"key": "flow_builder.var_message_content", "value": "Received message content"}, {"key": "flow_builder.var_date_today", "value": "Current date"}, {"key": "flow_builder.var_time_now", "value": "Current time"}, {"key": "flow_builder.var_availability", "value": "Google Calendar availability data from previous node"}, {"key": "flow_builder.type_message_placeholder", "value": "Type your message here..."}, {"key": "flow_builder.insert_variable", "value": "Insert Variable:"}, {"key": "flow_builder.variables_help", "value": "Variables will be replaced with actual values when message is sent."}, {"key": "flow_builder.condition", "value": "Condition"}, {"key": "flow_builder.condition_type", "value": "Condition Type:"}, {"key": "flow_builder.advanced", "value": "Advanced"}, {"key": "flow_builder.enter_custom_condition", "value": "Enter custom condition"}, {"key": "flow_builder.condition_examples", "value": "Examples: Contains('help'), IsMedia(), ExactMatch('hello')"}, {"key": "flow_builder.enter_text_value", "value": "Enter text value"}, {"key": "flow_builder.case_sensitive", "value": "Case sensitive"}, {"key": "flow_builder.has_media_desc", "value": "Checks if the message has any attached media."}, {"key": "flow_builder.media_type_desc", "value": "Checks if the message contains media of the selected type."}, {"key": "flow_builder.before", "value": "Before"}, {"key": "flow_builder.after", "value": "After"}, {"key": "flow_builder.between", "value": "Between"}, {"key": "flow_builder.time_format_between", "value": "Use format: HH:MM,HH:MM (24h)"}, {"key": "flow_builder.time_format_single", "value": "Use format: HH:MM (24h)"}, {"key": "flow_builder.name", "value": "Name"}, {"key": "flow_builder.phone", "value": "Phone"}, {"key": "flow_builder.email", "value": "Email"}, {"key": "flow_builder.tags", "value": "Tags"}, {"key": "flow_builder.attribute_value", "value": "Attribute value"}, {"key": "flow_builder.yes", "value": "Yes"}, {"key": "flow_builder.no", "value": "No"}, {"key": "flow_builder.input", "value": "Input"}, {"key": "flow_builder.collect_user_response", "value": "Collect user response"}, {"key": "flow_builder.enter_input_prompt", "value": "Enter input prompt"}, {"key": "flow_builder.action", "value": "Action"}, {"key": "flow_builder.perform_api_call", "value": "Perform API call"}, {"key": "flow_builder.enter_action", "value": "Enter action"}, {"key": "flow_builder.action_examples", "value": "Examples: CreateTicket(), TagContact('VIP'), AddToGroup('leads')"}, {"key": "flow_builder.search_nodes", "value": "Search nodes..."}, {"key": "flow_builder.add_node", "value": "Add Node"}, {"key": "flow_builder.no_nodes_found", "value": "No nodes found"}, {"key": "flow_builder.try_different_search", "value": "Try a different search term"}, {"key": "flow_builder.checking", "value": "Checking..."}, {"key": "flow_builder.connected", "value": "Connected"}, {"key": "flow_builder.connect", "value": "Connect"}, {"key": "flow_builder.connect_google_calendar_first", "value": "Connect to Google Calendar first"}, {"key": "flow_builder.name_required", "value": "Name required"}, {"key": "flow_builder.provide_flow_name", "value": "Please provide a name for your flow"}, {"key": "flow_builder.error_loading_flow", "value": "Error loading flow"}, {"key": "flow_builder.could_not_parse_flow_data", "value": "Could not parse flow data"}, {"key": "flow_builder.flow_created", "value": "Flow created"}, {"key": "flow_builder.flow_created_successfully", "value": "Your flow has been created successfully."}, {"key": "flow_builder.error_creating_flow", "value": "Error creating flow"}, {"key": "flow_builder.something_went_wrong", "value": "Something went wrong"}, {"key": "flow_builder.flow_updated", "value": "Flow updated"}, {"key": "flow_builder.flow_updated_successfully", "value": "Your flow has been updated successfully."}, {"key": "flow_builder.error_updating_flow", "value": "Error updating flow"}, {"key": "flow_builder.node_deleted", "value": "Node deleted"}, {"key": "flow_builder.node_connections_removed", "value": "Node and its connections have been removed."}, {"key": "flow_builder.node_duplicated", "value": "Node duplicated"}, {"key": "flow_builder.node_copy_created", "value": "A copy of the node has been created."}, {"key": "flow_builder.flow_name", "value": "Flow name"}, {"key": "flow_builder.node_selection", "value": "Node Selection"}, {"key": "flow_builder.active", "value": "Active"}, {"key": "flow_builder.draft", "value": "Draft"}, {"key": "flow_builder.creating_new_flow", "value": "Creating New Flow"}, {"key": "flow_builder.current_flow_status", "value": "Current flow status"}, {"key": "flow_builder.send_image", "value": "Send Image"}, {"key": "flow_builder.image_label", "value": "Image:"}, {"key": "flow_builder.or_enter_image_url", "value": "Or enter image URL:"}, {"key": "flow_builder.enter_image_url", "value": "Enter image URL or path"}, {"key": "flow_builder.caption_optional", "value": "Caption (optional):"}, {"key": "flow_builder.add_caption_image", "value": "Add a caption to your image..."}, {"key": "flow_builder.insert_variable_caption", "value": "Insert Variable in Caption:"}, {"key": "flow_builder.no_image_provided", "value": "No image provided"}, {"key": "flow_builder.caption_label", "value": "Caption:"}, {"key": "flow_builder.send_video", "value": "Send Video"}, {"key": "flow_builder.video_url_label", "value": "Video URL:"}, {"key": "flow_builder.enter_video_url", "value": "Enter video URL or path"}, {"key": "flow_builder.add_caption_video", "value": "Add a caption to your video..."}, {"key": "flow_builder.no_url_provided", "value": "No URL provided"}, {"key": "flow_builder.send_audio", "value": "Send Audio"}, {"key": "flow_builder.audio_url_label", "value": "Audio URL:"}, {"key": "flow_builder.enter_audio_url", "value": "Enter audio URL or path"}, {"key": "flow_builder.add_caption_audio", "value": "Add a caption to your audio..."}, {"key": "flow_builder.send_document", "value": "Send Document"}, {"key": "flow_builder.document_url_label", "value": "Document URL:"}, {"key": "flow_builder.enter_document_url", "value": "Enter document URL or path"}, {"key": "flow_builder.file_name_optional", "value": "File Name (optional):"}, {"key": "flow_builder.enter_file_name", "value": "Enter file name (e.g. report.pdf)"}, {"key": "flow_builder.add_caption_document", "value": "Add a caption to your document..."}, {"key": "flow_builder.file_name_label", "value": "File Name:"}, {"key": "flow_builder.message_received", "value": "Message Received"}, {"key": "flow_builder.when", "value": "When"}, {"key": "flow_builder.message_lowercase", "value": "message"}, {"key": "flow_builder.that", "value": "that"}, {"key": "flow_builder.any_message", "value": "Any Message"}, {"key": "flow_builder.contains_word", "value": "Contains Word"}, {"key": "flow_builder.exact_match", "value": "Exact Match"}, {"key": "flow_builder.regex_pattern", "value": "Regex Pattern"}, {"key": "flow_builder.has_media", "value": "Has Media"}, {"key": "flow_builder.changes_saved_automatically", "value": "Changes are saved automatically when you save the flow."}, {"key": "flow_builder.default_video_caption", "value": "Watch this video!"}, {"key": "flow_builder.default_audio_caption", "value": "Listen to this audio!"}, {"key": "flow_builder.default_document_caption", "value": "Check out this document!"}, {"key": "flow_builder.default_image_caption", "value": "Check out this image!"}, {"key": "flow_builder.default_document_caption_full", "value": "Here is the document you requested."}, {"key": "analytics.title", "value": "Analytics"}, {"key": "analytics.select_period", "value": "Select time period"}, {"key": "analytics.period.today", "value": "Today"}, {"key": "analytics.period.yesterday", "value": "Yesterday"}, {"key": "analytics.period.7days", "value": "Last 7 days"}, {"key": "analytics.period.30days", "value": "Last 30 days"}, {"key": "analytics.period.90days", "value": "Last 90 days"}, {"key": "analytics.period.custom", "value": "Custom Range"}, {"key": "analytics.pick_date", "value": "Pick a date"}, {"key": "analytics.export", "value": "Export"}, {"key": "analytics.vs_last_period", "value": "vs last period"}, {"key": "analytics.cards.total_conversations", "value": "Total Conversations"}, {"key": "analytics.cards.total_contacts", "value": "Total Contacts"}, {"key": "analytics.cards.total_messages", "value": "Total Messages"}, {"key": "analytics.cards.response_rate", "value": "Response Rate"}, {"key": "analytics.charts.conversations_by_channel", "value": "Conversations by Channel"}, {"key": "analytics.charts.avg_response_time", "value": "Average Response Time"}, {"key": "analytics.charts.channel_distribution", "value": "Channel Distribution"}, {"key": "analytics.charts.bot_performance", "value": "Bot <PERSON>"}, {"key": "analytics.response_time", "value": "Response Time"}, {"key": "analytics.channels.whatsapp_official", "value": "WhatsApp Official"}, {"key": "analytics.channels.whatsapp_unofficial", "value": "WhatsApp Unofficial"}, {"key": "analytics.channels.messenger", "value": "<PERSON>"}, {"key": "analytics.channels.instagram", "value": "Instagram"}, {"key": "analytics.tabs.overview", "value": "Overview"}, {"key": "analytics.tabs.top_intents", "value": "Top Intents"}, {"key": "analytics.tabs.flow_performance", "value": "Flow Performance"}, {"key": "analytics.bot.handling_title", "value": "<PERSON><PERSON>"}, {"key": "analytics.bot.handling_description", "value": "65% of all conversations were successfully handled by the AI assistant without human intervention."}, {"key": "analytics.bot.escalation_title", "value": "Human Escalation"}, {"key": "analytics.bot.escalation_description", "value": "35% of conversations required escalation to a human agent."}, {"key": "analytics.bot.escalation_reasons_title", "value": "Top Reasons for Escalation"}, {"key": "analytics.bot.reason_1", "value": "Complex product questions"}, {"key": "analytics.bot.reason_2", "value": "Pricing negotiations"}, {"key": "analytics.bot.reason_3", "value": "Technical support issues"}, {"key": "analytics.intents.product_info", "value": "Product Information"}, {"key": "analytics.intents.pricing", "value": "Pricing Inquiries"}, {"key": "analytics.intents.scheduling", "value": "Appointment Scheduling"}, {"key": "analytics.intents.support", "value": "Support Requests"}, {"key": "analytics.intents.other", "value": "Other"}, {"key": "analytics.flows.welcome", "value": "Welcome Flow"}, {"key": "analytics.flows.product_info", "value": "Product Information"}, {"key": "analytics.flows.support_ticket", "value": "Support Ticket"}, {"key": "analytics.flows.completion_rate", "value": "{{rate}}% completion"}, {"key": "analytics.flows.conversations_count", "value": "{{count}} conversations"}, {"key": "analytics.export.success", "value": "Export successful"}, {"key": "analytics.export.success_description", "value": "Analytics data has been exported successfully."}, {"key": "analytics.export.error", "value": "Export failed"}, {"key": "analytics.export.error_description", "value": "Failed to export analytics data."}, {"key": "groups.unnamed_group", "value": "Unnamed Group"}, {"key": "groups.participants", "value": "participants"}, {"key": "groups.group_info", "value": "Group Info"}, {"key": "groups.close_group_info", "value": "Close group info"}, {"key": "groups.description", "value": "Description"}, {"key": "groups.group_id", "value": "Group ID"}, {"key": "groups.created_date", "value": "Created"}, {"key": "groups.admin", "value": "Admin"}, {"key": "groups.super_admin", "value": "Group Admin"}, {"key": "groups.show_more", "value": "Show More"}, {"key": "groups.show_less", "value": "Show Less"}, {"key": "groups.unknown_participant", "value": "Unknown Participant"}, {"key": "groups.avatar.refresh_tooltip", "value": "Refresh group profile picture"}, {"key": "common.unknown", "value": "Unknown"}, {"key": "admin.settings.platform", "value": "Platform"}, {"key": "admin.settings.partnerapi", "value": "Partner API"}, {"key": "admin.settings.title", "value": "Settings"}, {"key": "admin.settings.branding", "value": "Branding"}, {"key": "admin.settings.branding_description", "value": "Customize the appearance of your application"}, {"key": "admin.settings.payment_gateways", "value": "Payment Gateways"}, {"key": "admin.settings.email", "value": "<PERSON><PERSON>s"}, {"key": "admin.settings.general", "value": "General"}, {"key": "admin.settings.registration", "value": "Registration"}, {"key": "admin.settings.backup", "value": "Database Backup"}, {"key": "admin.settings.app_name", "value": "Application Name"}, {"key": "admin.settings.primary_color", "value": "Primary Color"}, {"key": "admin.settings.secondary_color", "value": "Secondary Color"}, {"key": "admin.settings.save_branding", "value": "Save Branding Settings"}, {"key": "admin.settings.platform.title", "value": "Platform Configuration"}, {"key": "admin.settings.platform.description", "value": "Configure platform-wide partner API integrations and Tech Provider credentials"}, {"key": "admin.settings.platform.360dialog.title", "value": "360Dialog Partner API"}, {"key": "admin.settings.platform.360dialog.description", "value": "Configure 360Dialog Partner credentials for company onboarding"}, {"key": "admin.settings.platform.meta.title", "value": "Meta WhatsApp Business API"}, {"key": "admin.settings.platform.meta.description", "value": "Configure Meta Tech Provider credentials for embedded signup"}, {"key": "admin.settings.platform.meta.benefits.embedded_signup", "value": "Tech Provider embedded signup integration"}, {"key": "admin.settings.platform.meta.benefits.streamlined_onboarding", "value": "Streamlined WhatsApp Business account onboarding"}, {"key": "admin.settings.platform.meta.benefits.automatic_provisioning", "value": "Automatic phone number provisioning"}, {"key": "admin.settings.platform.configure_button", "value": "Configure"}, {"key": "admin.settings.platform.coming_soon", "value": "Coming Soon"}, {"key": "admin.settings.platform.additional_partners.title", "value": "Additional Partner APIs"}, {"key": "admin.settings.platform.additional_partners.description", "value": "Future partner integrations will appear here"}, {"key": "meta.partner.config.title", "value": "Meta WhatsApp Business API Partner Configuration"}, {"key": "meta.partner.config.loading", "value": "Loading configuration..."}, {"key": "meta.partner.config.tech_provider_credentials", "value": "Tech Provider Credentials"}, {"key": "meta.partner.config.tech_provider_description", "value": "Configure your Meta Tech Provider credentials for embedded signup"}, {"key": "meta.partner.config.app_id", "value": "App ID"}, {"key": "meta.partner.config.app_id_placeholder", "value": "Your Meta App ID"}, {"key": "meta.partner.config.app_secret", "value": "App Secret"}, {"key": "meta.partner.config.app_secret_placeholder", "value": "Your Meta App Secret"}, {"key": "meta.partner.config.business_manager_id", "value": "Business Manager ID"}, {"key": "meta.partner.config.business_manager_id_placeholder", "value": "Your Business Manager ID"}, {"key": "meta.partner.config.webhook_verify_token", "value": "Webhook Verify Token"}, {"key": "meta.partner.config.webhook_verify_token_placeholder", "value": "Webhook verification token"}, {"key": "meta.partner.config.access_token", "value": "System User Access Token"}, {"key": "meta.partner.config.access_token_placeholder", "value": "System user access token"}, {"key": "meta.partner.config.webhook_url", "value": "Webhook URL"}, {"key": "meta.partner.config.webhook_url_placeholder", "value": "https://yourdomain.com/api/webhooks/meta-whatsapp"}, {"key": "meta.partner.config.company_profile", "value": "Company Profile"}, {"key": "meta.partner.config.company_profile_description", "value": "This information will be shown to companies during onboarding"}, {"key": "meta.partner.config.company_name", "value": "Company Name"}, {"key": "meta.partner.config.company_name_placeholder", "value": "Your company name"}, {"key": "meta.partner.config.logo_url", "value": "Logo URL"}, {"key": "meta.partner.config.logo_url_placeholder", "value": "https://example.com/logo.png"}, {"key": "meta.partner.config.test_configuration", "value": "Test Configuration"}, {"key": "meta.partner.config.save_configuration", "value": "Save Configuration"}, {"key": "meta.partner.config.update_configuration", "value": "Update Configuration"}, {"key": "meta.partner.config.validation.required_fields", "value": "App ID, App Secret, and Business Manager ID are required"}, {"key": "meta.partner.config.validation.success", "value": "Meta Partner API credentials are valid!"}, {"key": "meta.partner.config.validation.failed", "value": "Invalid Meta Partner API credentials"}, {"key": "meta.partner.config.validation.error", "value": "Failed to validate configuration"}, {"key": "meta.partner.config.save.success", "value": "Meta Partner API configuration updated successfully"}, {"key": "meta.partner.config.save.error", "value": "Failed to save configuration"}, {"key": "meta.partner.config.load.error", "value": "Failed to load existing configuration"}, {"key": "meta.whatsapp.onboarding.title", "value": "Meta WhatsApp Business API - Easy Setup"}, {"key": "meta.whatsapp.onboarding.description", "value": "Connect your WhatsApp Business account in just a few clicks using our integrated onboarding flow."}, {"key": "meta.whatsapp.onboarding.connection_name", "value": "Connection Name"}, {"key": "meta.whatsapp.onboarding.connection_name_placeholder", "value": "e.g., Main WhatsApp Business"}, {"key": "meta.whatsapp.onboarding.connection_name_help", "value": "Give this connection a memorable name"}, {"key": "meta.whatsapp.onboarding.what_happens_next", "value": "What happens next:"}, {"key": "meta.whatsapp.onboarding.step_connect_business", "value": "Connect your Meta Business account"}, {"key": "meta.whatsapp.onboarding.step_select_account", "value": "Select your WhatsApp Business account"}, {"key": "meta.whatsapp.onboarding.step_choose_numbers", "value": "Choose phone numbers to integrate"}, {"key": "meta.whatsapp.onboarding.step_automatic_config", "value": "Automatic configuration and setup"}, {"key": "meta.whatsapp.onboarding.sdk_initializing", "value": "Initializing Facebook SDK..."}, {"key": "meta.whatsapp.onboarding.start_easy_setup", "value": "Start Easy Setup"}, {"key": "meta.whatsapp.onboarding.processing", "value": "Processing..."}, {"key": "meta.whatsapp.onboarding.cancel", "value": "Cancel"}, {"key": "meta.whatsapp.onboarding.checking_config", "value": "Checking configuration..."}, {"key": "meta.whatsapp.onboarding.config_required.title", "value": "Configuration Required"}, {"key": "meta.whatsapp.onboarding.config_required.description", "value": "Meta WhatsApp Business API Partner integration is not configured. Please contact your system administrator to set up the Partner API credentials."}, {"key": "meta.whatsapp.onboarding.config_required.close", "value": "Close"}, {"key": "meta.whatsapp.onboarding.success", "value": "WhatsApp Business account connected successfully. {{count}} phone number(s) configured."}, {"key": "meta.whatsapp.onboarding.error.connection_name_required", "value": "Please enter a connection name"}, {"key": "meta.whatsapp.onboarding.error.sdk_not_initialized", "value": "Facebook SDK not initialized"}, {"key": "meta.whatsapp.onboarding.error.launch_signup", "value": "Failed to launch WhatsApp signup"}, {"key": "meta.whatsapp.onboarding.error.signup_failed", "value": "Failed to process WhatsApp Business account signup"}, {"key": "meta.whatsapp.onboarding.error.process_signup", "value": "Failed to process signup"}, {"key": "meta.whatsapp.onboarding.error.sdk_init", "value": "Failed to initialize Facebook SDK"}, {"key": "channels.whatsapp.meta.easy_setup", "value": "Easy Setup"}, {"key": "channels.whatsapp.meta.title", "value": "WhatsApp Business API (Meta)"}, {"key": "channels.whatsapp.meta.description", "value": "Official Meta WhatsApp Business API"}, {"key": "channels.whatsapp.meta.embedded.title", "value": "WhatsApp Business Embedded"}, {"key": "channels.whatsapp.meta.embedded.description", "value": "Quick setup with embedded signup"}, {"key": "channels.whatsapp.meta.connection_info", "value": "Official WhatsApp Business API (Meta)"}, {"key": "channels.whatsapp.meta.connection_description", "value": "This connection uses the official WhatsApp Business API from Meta. It provides reliable messaging with advanced features and compliance."}, {"key": "common.success", "value": "Success"}, {"key": "common.error", "value": "Error"}, {"key": "common.validation_failed", "value": "Validation Failed"}, {"key": "common.signup_error", "value": "Signup Error"}, {"key": "common.sdk_error", "value": "SDK Error"}, {"key": "common.configuration_error", "value": "Configuration Error"}, {"key": "partner.config.success.created", "value": "Partner API configuration created successfully"}, {"key": "partner.config.success.updated", "value": "Partner API configuration updated successfully"}, {"key": "partner.config.error.load_failed", "value": "Failed to load existing configuration"}, {"key": "partner.config.error.save_failed", "value": "Failed to save configuration"}, {"key": "partner.config.error.validation_failed", "value": "Failed to validate configuration"}, {"key": "api.access.title", "value": "API Access"}, {"key": "api.access.description", "value": "Manage API keys and programmatic access to your channels"}, {"key": "api.access.create_key", "value": "Create API Key"}, {"key": "api.access.no_keys.title", "value": "No API Keys"}, {"key": "api.access.no_keys.description", "value": "Create your first API key to start sending messages programmatically"}, {"key": "api.access.tabs.keys", "value": "API Keys"}, {"key": "api.access.tabs.usage", "value": "Usage Statistics"}, {"key": "api.access.tabs.docs", "value": "Documentation"}, {"key": "api.access.key.active", "value": "Active"}, {"key": "api.access.key.inactive", "value": "Inactive"}, {"key": "api.access.key.activate", "value": "Activate"}, {"key": "api.access.key.deactivate", "value": "Deactivate"}, {"key": "api.access.key.delete", "value": "Delete"}, {"key": "api.access.key.created", "value": "Created"}, {"key": "api.access.key.last_used", "value": "Last Used"}, {"key": "api.access.key.never_used", "value": "Never"}, {"key": "api.access.key.rate_limits", "value": "Rate Limits"}, {"key": "api.access.key.permissions", "value": "Permissions"}, {"key": "api.access.create.title", "value": "Create New API Key"}, {"key": "api.access.create.description", "value": "Create a new API key to access the messaging API programmatically."}, {"key": "api.access.create.name_label", "value": "API Key Name"}, {"key": "api.access.create.name_placeholder", "value": "e.g., Production Bot, Marketing Automation"}, {"key": "api.access.create.creating", "value": "Creating..."}, {"key": "api.access.create.cancel", "value": "Cancel"}, {"key": "api.access.show_key.title", "value": "API Key Created"}, {"key": "api.access.show_key.description", "value": "Your API key has been created. Copy it now as it won't be shown again."}, {"key": "api.access.show_key.warning", "value": "Store this API key securely. You won't be able to see it again."}, {"key": "api.access.show_key.saved", "value": "I've Saved the Key"}, {"key": "api.access.usage.total_requests", "value": "Total Requests"}, {"key": "api.access.usage.successful", "value": "Successful"}, {"key": "api.access.usage.failed", "value": "Failed"}, {"key": "api.access.usage.avg_duration", "value": "Avg Duration"}, {"key": "api.access.usage.data_transfer", "value": "Data Transfer"}, {"key": "api.access.docs.title", "value": "API Documentation"}, {"key": "api.access.docs.description", "value": "Learn how to integrate with our messaging API"}, {"key": "api.access.docs.base_url", "value": "Base URL"}, {"key": "api.access.docs.endpoints", "value": "Available Endpoints"}, {"key": "api.access.docs.authentication", "value": "Authentication"}, {"key": "api.access.docs.auth_description", "value": "Include your API key in the Authorization header:"}, {"key": "api.access.error.name_required", "value": "Please enter a name for the API key"}, {"key": "api.access.error.create_failed", "value": "Failed to create API key"}, {"key": "api.access.error.delete_failed", "value": "Failed to delete API key"}, {"key": "api.access.error.update_failed", "value": "Failed to update API key"}, {"key": "api.access.error.load_failed", "value": "Failed to load API keys"}, {"key": "api.access.success.created", "value": "API key created successfully"}, {"key": "api.access.success.deleted", "value": "API key deleted successfully"}, {"key": "api.access.success.activated", "value": "API key activated successfully"}, {"key": "api.access.success.deactivated", "value": "API key deactivated successfully"}, {"key": "api.access.success.copied", "value": "API key copied to clipboard"}]