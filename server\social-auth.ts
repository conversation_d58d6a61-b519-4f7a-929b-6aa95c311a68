import { Express, Request, Response } from "express";
import { storage } from "./storage";
import { OAuth2Client } from 'google-auth-library';
import crypto from 'crypto';

interface GoogleUserInfo {
  id: string;
  email: string;
  name: string;
  picture?: string;
}

interface FacebookUserInfo {
  id: string;
  email: string;
  name: string;
  picture?: {
    data: {
      url: string;
    };
  };
}


function encryptToken(token: string): string {
  const algorithm = 'aes-256-gcm';
  const secretKey = process.env.ENCRYPTION_KEY || 'default-key-change-in-production';
  const key = crypto.scryptSync(secretKey, 'salt', 32);
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(algorithm, key);
  
  let encrypted = cipher.update(token, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return `${iv.toString('hex')}:${encrypted}`;
}


function decryptToken(encryptedToken: string): string {
  const algorithm = 'aes-256-gcm';
  const secretKey = process.env.ENCRYPTION_KEY || 'default-key-change-in-production';
  const key = crypto.scryptSync(secretKey, 'salt', 32);
  
  const [ivHex, encrypted] = encryptedToken.split(':');
  const iv = Buffer.from(ivHex, 'hex');
  const decipher = crypto.createDecipher(algorithm, key);
  
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
}

export function setupSocialAuth(app: Express) {
  

  app.get("/api/auth/google", async (req: Request, res: Response) => {
    try {
      const googleConfig = await storage.getAppSetting('social_login_google');
      
      if (!googleConfig || !(googleConfig.value as any).enabled) {
        return res.status(400).json({ error: "Google OAuth is not enabled" });
      }
      
      const config = googleConfig.value as any;
      const oauth2Client = new OAuth2Client(
        config.client_id,
        config.client_secret,
        `${req.protocol}://${req.get('host')}/api/auth/google/callback`
      );
      
      const scopes = [
        'https://www.googleapis.com/auth/userinfo.profile',
        'https://www.googleapis.com/auth/userinfo.email'
      ];
      
      const authUrl = oauth2Client.generateAuthUrl({
        access_type: 'offline',
        scope: scopes,
        prompt: 'consent'
      });
      
      res.redirect(authUrl);
    } catch (error) {
      console.error("Error initiating Google OAuth:", error);
      res.status(500).json({ error: "Failed to initiate Google OAuth" });
    }
  });


  app.get("/api/auth/google/callback", async (req: Request, res: Response) => {
    try {
      const { code, error } = req.query;
      
      if (error) {
        return res.redirect('/auth?error=oauth_cancelled');
      }
      
      if (!code) {
        return res.redirect('/auth?error=oauth_failed');
      }
      
      const googleConfig = await storage.getAppSetting('social_login_google');
      if (!googleConfig || !(googleConfig.value as any).enabled) {
        return res.redirect('/auth?error=oauth_disabled');
      }
      
      const config = googleConfig.value as any;
      const oauth2Client = new OAuth2Client(
        config.client_id,
        config.client_secret,
        `${req.protocol}://${req.get('host')}/api/auth/google/callback`
      );
      

      const { tokens } = await oauth2Client.getToken(code as string);
      oauth2Client.setCredentials(tokens);
      

      const userInfoResponse = await fetch(
        `https://www.googleapis.com/oauth2/v2/userinfo?access_token=${tokens.access_token}`
      );
      const userInfo: GoogleUserInfo = await userInfoResponse.json();
      

      let user = await storage.getUserBySocialAccount('google', userInfo.id);
      
      if (!user) {

        user = await storage.getUserByEmail(userInfo.email);
        
        if (user) {

          await storage.createUserSocialAccount({
            userId: user.id,
            provider: 'google',
            providerUserId: userInfo.id,
            providerEmail: userInfo.email,
            providerName: userInfo.name,
            providerAvatarUrl: userInfo.picture,
            accessToken: encryptToken(tokens.access_token || ''),
            refreshToken: tokens.refresh_token ? encryptToken(tokens.refresh_token) : null,
            tokenExpiresAt: tokens.expiry_date ? new Date(tokens.expiry_date) : null,
            providerData: { userInfo }
          });
        } else {

          const newUser = await storage.createUserFromSocialLogin({
            email: userInfo.email,
            fullName: userInfo.name,
            avatarUrl: userInfo.picture,
            provider: 'google',
            providerUserId: userInfo.id,
            providerData: { userInfo }
          });
          
          user = newUser;
          

          await storage.createUserSocialAccount({
            userId: user.id,
            provider: 'google',
            providerUserId: userInfo.id,
            providerEmail: userInfo.email,
            providerName: userInfo.name,
            providerAvatarUrl: userInfo.picture,
            accessToken: encryptToken(tokens.access_token || ''),
            refreshToken: tokens.refresh_token ? encryptToken(tokens.refresh_token) : null,
            tokenExpiresAt: tokens.expiry_date ? new Date(tokens.expiry_date) : null,
            providerData: { userInfo }
          });
        }
      }
      

      req.logIn(user, (err) => {
        if (err) {
          console.error("Error logging in user:", err);
          return res.redirect('/auth?error=login_failed');
        }
        
        res.redirect('/');
      });
      
    } catch (error) {
      console.error("Error in Google OAuth callback:", error);
      res.redirect('/auth?error=oauth_failed');
    }
  });


  app.get("/api/auth/facebook", async (req: Request, res: Response) => {
    try {
      const facebookConfig = await storage.getAppSetting('social_login_facebook');
      
      if (!facebookConfig || !(facebookConfig.value as any).enabled) {
        return res.status(400).json({ error: "Facebook OAuth is not enabled" });
      }
      
      const config = facebookConfig.value as any;
      const redirectUri = `${req.protocol}://${req.get('host')}/api/auth/facebook/callback`;
      
      const authUrl = `https://www.facebook.com/v22.0/dialog/oauth?` +
        `client_id=${config.app_id}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `scope=email,public_profile&` +
        `response_type=code`;
      
      res.redirect(authUrl);
    } catch (error) {
      console.error("Error initiating Facebook OAuth:", error);
      res.status(500).json({ error: "Failed to initiate Facebook OAuth" });
    }
  });


  app.get("/api/auth/facebook/callback", async (req: Request, res: Response) => {
    try {
      const { code, error } = req.query;
      
      if (error) {
        return res.redirect('/auth?error=oauth_cancelled');
      }
      
      if (!code) {
        return res.redirect('/auth?error=oauth_failed');
      }
      
      const facebookConfig = await storage.getAppSetting('social_login_facebook');
      if (!facebookConfig || !(facebookConfig.value as any).enabled) {
        return res.redirect('/auth?error=oauth_disabled');
      }
      
      const config = facebookConfig.value as any;
      const redirectUri = `${req.protocol}://${req.get('host')}/api/auth/facebook/callback`;
      

      const tokenResponse = await fetch(
        `https://graph.facebook.com/v22.0/oauth/access_token?` +
        `client_id=${config.app_id}&` +
        `client_secret=${config.app_secret}&` +
        `code=${code}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}`
      );
      
      const tokenData = await tokenResponse.json();
      
      if (tokenData.error) {
        console.error("Facebook token exchange error:", tokenData.error);
        return res.redirect('/auth?error=oauth_failed');
      }
      

      const userInfoResponse = await fetch(
        `https://graph.facebook.com/me?fields=id,name,email,picture&access_token=${tokenData.access_token}`
      );
      const userInfo: FacebookUserInfo = await userInfoResponse.json();
      

      let user = await storage.getUserBySocialAccount('facebook', userInfo.id);
      
      if (!user) {

        user = await storage.getUserByEmail(userInfo.email);
        
        if (user) {

          await storage.createUserSocialAccount({
            userId: user.id,
            provider: 'facebook',
            providerUserId: userInfo.id,
            providerEmail: userInfo.email,
            providerName: userInfo.name,
            providerAvatarUrl: userInfo.picture?.data?.url,
            accessToken: encryptToken(tokenData.access_token),
            refreshToken: null,
            tokenExpiresAt: tokenData.expires_in ? new Date(Date.now() + tokenData.expires_in * 1000) : null,
            providerData: { userInfo }
          });
        } else {

          const newUser = await storage.createUserFromSocialLogin({
            email: userInfo.email,
            fullName: userInfo.name,
            avatarUrl: userInfo.picture?.data?.url,
            provider: 'facebook',
            providerUserId: userInfo.id,
            providerData: { userInfo }
          });
          
          user = newUser;
          

          await storage.createUserSocialAccount({
            userId: user.id,
            provider: 'facebook',
            providerUserId: userInfo.id,
            providerEmail: userInfo.email,
            providerName: userInfo.name,
            providerAvatarUrl: userInfo.picture?.data?.url,
            accessToken: encryptToken(tokenData.access_token),
            refreshToken: null,
            tokenExpiresAt: tokenData.expires_in ? new Date(Date.now() + tokenData.expires_in * 1000) : null,
            providerData: { userInfo }
          });
        }
      }
      

      req.logIn(user, (err) => {
        if (err) {
          console.error("Error logging in user:", err);
          return res.redirect('/auth?error=login_failed');
        }
        
        res.redirect('/');
      });
      
    } catch (error) {
      console.error("Error in Facebook OAuth callback:", error);
      res.redirect('/auth?error=oauth_failed');
    }
  });


  app.get("/api/auth/apple", async (req: Request, res: Response) => {
    try {
      const appleConfig = await storage.getAppSetting('social_login_apple');

      if (!appleConfig || !(appleConfig.value as any).enabled) {
        return res.status(400).json({ error: "Apple OAuth is not enabled" });
      }

      const config = appleConfig.value as any;
      const redirectUri = `${req.protocol}://${req.get('host')}/api/auth/apple/callback`;


      const state = crypto.randomBytes(32).toString('hex');

      const authUrl = `https://appleid.apple.com/auth/authorize?` +
        `client_id=${config.client_id}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `response_type=code&` +
        `scope=name email&` +
        `response_mode=form_post&` +
        `state=${state}`;

      res.redirect(authUrl);
    } catch (error) {
      console.error("Error initiating Apple OAuth:", error);
      res.status(500).json({ error: "Failed to initiate Apple OAuth" });
    }
  });


  app.post("/api/auth/apple/callback", async (req: Request, res: Response) => {
    try {
      const { code, error, state } = req.body;

      if (error) {
        return res.redirect('/auth?error=oauth_cancelled');
      }

      if (!code) {
        return res.redirect('/auth?error=oauth_failed');
      }

      const appleConfig = await storage.getAppSetting('social_login_apple');
      if (!appleConfig || !(appleConfig.value as any).enabled) {
        return res.redirect('/auth?error=oauth_disabled');
      }








      res.redirect('/auth?error=apple_oauth_not_fully_implemented');

    } catch (error) {
      console.error("Error in Apple OAuth callback:", error);
      res.redirect('/auth?error=oauth_failed');
    }
  });
}
