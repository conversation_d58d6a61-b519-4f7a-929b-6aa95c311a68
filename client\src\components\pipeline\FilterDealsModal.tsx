import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription
} from '@/components/ui/dialog';
import {
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { X } from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

interface FilterDealsModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentFilter: {
    priority: string | null;
    assignedToUserId: number | null;
    titleSearch: string | null;
    tags: string[] | null;
    contactPhone: string | null;
    contactName: string | null;
  };
  onApplyFilter: (filter: {
    priority: string | null;
    assignedToUserId: number | null;
    titleSearch: string | null;
    tags: string[] | null;
    contactPhone: string | null;
    contactName: string | null;
  }) => void;
}

export default function FilterDealsModal({
  isOpen,
  onClose,
  currentFilter,
  onApplyFilter
}: FilterDealsModalProps) {
  const [filter, setFilter] = useState({
    priority: currentFilter.priority,
    assignedToUserId: currentFilter.assignedToUserId,
    titleSearch: currentFilter.titleSearch,
    tags: currentFilter.tags || [],
    contactPhone: currentFilter.contactPhone,
    contactName: currentFilter.contactName,
  });

  const [selectedTags, setSelectedTags] = useState<string[]>(currentFilter.tags || []);

  useEffect(() => {
    if (isOpen) {
      setFilter({
        priority: currentFilter.priority,
        assignedToUserId: currentFilter.assignedToUserId,
        titleSearch: currentFilter.titleSearch,
        tags: currentFilter.tags || [],
        contactPhone: currentFilter.contactPhone,
        contactName: currentFilter.contactName,
      });
      setSelectedTags(currentFilter.tags || []);
    }
  }, [isOpen, currentFilter]);

  const { data: teamMembers = [] } = useQuery({
    queryKey: ['/api/team-members'],
    queryFn: () => apiRequest('GET', '/api/team-members')
      .then(res => res.json()),
  });

  const { data: availableTags = [] } = useQuery({
    queryKey: ['/api/deals/tags'],
    queryFn: () => apiRequest('GET', '/api/deals/tags')
      .then(res => res.json()),
  });

  const handleApplyFilter = () => {
    const filterToApply = {
      ...filter,
      tags: selectedTags.length > 0 ? selectedTags : null,
    };
    onApplyFilter(filterToApply);
    onClose();
  };

  const handleClearFilter = () => {
    const clearedFilter = {
      priority: null,
      assignedToUserId: null,
      titleSearch: null,
      tags: null,
      contactPhone: null,
      contactName: null,
    };
    setFilter({
      priority: null,
      assignedToUserId: null,
      titleSearch: null,
      tags: [],
      contactPhone: null,
      contactName: null,
    });
    setSelectedTags([]);
    onApplyFilter(clearedFilter);
    onClose();
  };

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const removeTag = (tagToRemove: string) => {
    setSelectedTags(prev => prev.filter(tag => tag !== tagToRemove));
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Filter Deals</DialogTitle>
          <DialogDescription>
            Filter deals by priority, assignee, title, tags, and contact information.
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[60vh] pr-4">
          <div className="space-y-6 py-4">
            {/* Priority Filter */}
            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <Select
                value={filter.priority || 'all'}
                onValueChange={(value) => setFilter({ ...filter, priority: value === 'all' ? null : value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All priorities" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All priorities</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Assigned To Filter */}
            <div className="space-y-2">
              <Label htmlFor="assignee">Assigned To</Label>
              <Select
                value={filter.assignedToUserId?.toString() || 'all'}
                onValueChange={(value) => setFilter({
                  ...filter,
                  assignedToUserId: value === 'all' ? null : parseInt(value)
                })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All team members" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All team members</SelectItem>
                  {teamMembers.map((user: any) => (
                    <SelectItem key={user.id} value={user.id.toString()}>
                      {user.fullName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Deal Title Search */}
            <div className="space-y-2">
              <Label htmlFor="titleSearch">Search by Deal Title</Label>
              <Input
                id="titleSearch"
                placeholder="Enter deal title to search..."
                value={filter.titleSearch || ''}
                onChange={(e) => setFilter({ ...filter, titleSearch: e.target.value || null })}
              />
            </div>

            {/* Tags Filter */}
            <div className="space-y-2">
              <Label>Filter by Tags</Label>
              {availableTags.length > 0 ? (
                <div className="space-y-3">
                  {/* Selected Tags Display */}
                  {selectedTags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {selectedTags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                          {tag}
                          <X
                            className="h-3 w-3 cursor-pointer hover:text-destructive"
                            onClick={() => removeTag(tag)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}

                  {/* Available Tags */}
                  <div className="max-h-32 overflow-y-auto border rounded-md p-2">
                    <div className="space-y-2">
                      {availableTags.map((tag: string) => (
                        <div key={tag} className="flex items-center space-x-2">
                          <Checkbox
                            id={`tag-${tag}`}
                            checked={selectedTags.includes(tag)}
                            onCheckedChange={() => handleTagToggle(tag)}
                          />
                          <Label htmlFor={`tag-${tag}`} className="text-sm cursor-pointer">
                            {tag}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No tags available</p>
              )}
            </div>

            {/* Contact Phone Search */}
            <div className="space-y-2">
              <Label htmlFor="contactPhone">Search by Contact Phone</Label>
              <Input
                id="contactPhone"
                placeholder="Enter contact phone number..."
                value={filter.contactPhone || ''}
                onChange={(e) => setFilter({ ...filter, contactPhone: e.target.value || null })}
              />
            </div>

            {/* Contact Name Search */}
            <div className="space-y-2">
              <Label htmlFor="contactName">Search by Contact Name</Label>
              <Input
                id="contactName"
                placeholder="Enter contact name..."
                value={filter.contactName || ''}
                onChange={(e) => setFilter({ ...filter, contactName: e.target.value || null })}
              />
            </div>
          </div>
        </ScrollArea>

        <DialogFooter className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={handleClearFilter}
          >
            Clear Filters
          </Button>
          <Button
            type="button"
            className="btn-brand-primary"
            onClick={handleApplyFilter}
          >
            Apply Filters
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}