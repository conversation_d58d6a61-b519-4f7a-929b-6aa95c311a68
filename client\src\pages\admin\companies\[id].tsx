import { useEffect, useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useLocation, useRoute } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import { usePlans } from "@/hooks/use-plans";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, ArrowLeft, Save, Trash, LogIn, UserCog } from "lucide-react";
import AdminLayout from "@/components/admin/AdminLayout";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Company {
  id: number;
  name: string;
  slug: string;
  logo?: string;
  primaryColor: string;
  active: boolean;
  plan: string;
  planId?: number;
  maxUsers: number;
  createdAt: string;
  updatedAt: string;
}

interface User {
  id: number;
  username: string;
  fullName: string;
  email: string;
  role: string;
  avatarUrl?: string;
  companyId: number;
  createdAt: string;
}

const companySchema = z.object({
  name: z.string().min(1, "Company name is required"),
  slug: z.string().min(1, "Slug is required").regex(/^[a-z0-9-]+$/, "Slug can only contain lowercase letters, numbers, and hyphens"),
  logo: z.string().optional(),
  primaryColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Must be a valid hex color"),
  active: z.boolean(),
  planId: z.number().int().min(1, "Plan is required"),
  maxUsers: z.number().int().min(1, "Must have at least 1 user")
});

type CompanyFormValues = z.infer<typeof companySchema>;

export default function CompanyDetailPage() {
  const { user, isLoading: isLoadingAuth, impersonateCompanyMutation } = useAuth();
  const { plans, isLoading: isLoadingPlans } = usePlans();
  const [_, navigate] = useLocation();
  const [match, params] = useRoute<{ id: string }>("/admin/companies/:id");
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("details");
  const [isImpersonateDialogOpen, setIsImpersonateDialogOpen] = useState(false);

  useEffect(() => {
    if (!isLoadingAuth && user && !user.isSuperAdmin) {
      navigate("/");
    }
  }, [user, isLoadingAuth, navigate]);

  const companyId = match ? parseInt(params.id) : null;

  const { data: company, isLoading: isLoadingCompany } = useQuery<Company>({
    queryKey: [`/api/admin/companies/${companyId}`],
    queryFn: async () => {
      const res = await apiRequest("GET", `/api/admin/companies/${companyId}`);
      if (!res.ok) throw new Error("Failed to fetch company");
      return res.json();
    },
    enabled: !!companyId && !!user?.isSuperAdmin
  });

  const { data: companyUsers, isLoading: isLoadingUsers } = useQuery<User[]>({
    queryKey: [`/api/admin/companies/${companyId}/users`],
    queryFn: async () => {
      const res = await apiRequest("GET", `/api/admin/companies/${companyId}/users`);
      if (!res.ok) throw new Error("Failed to fetch company users");
      return res.json();
    },
    enabled: !!companyId && !!user?.isSuperAdmin
  });

  const findPlanId = (company: Company) => {
    // First try to get plan by planId (more accurate after upgrades)
    if (company.planId) {
      return company.planId;
    }

    // Fallback to plan name lookup
    if (company.plan) {
      const plan = plans.find(p => p.name.toLowerCase() === company.plan.toLowerCase());
      return plan?.id || 0;
    }

    return 0;
  };

  const form = useForm<CompanyFormValues>({
    resolver: zodResolver(companySchema),
    defaultValues: {
      name: "",
      slug: "",
      logo: "",
      primaryColor: "#333235",
      active: true,
      planId: 0,
      maxUsers: 5
    }
  });

  useEffect(() => {
    if (company && plans.length > 0) {
      const planId = findPlanId(company);

      form.reset({
        name: company.name,
        slug: company.slug,
        logo: company.logo || "",
        primaryColor: company.primaryColor,
        active: company.active,
        planId: planId,
        maxUsers: company.maxUsers
      });
    }
  }, [company, plans, form]);

  const updateMutation = useMutation({
    mutationFn: async (data: CompanyFormValues) => {
      const res = await apiRequest("PUT", `/api/admin/companies/${companyId}`, data);
      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to update company");
      }
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "Company updated",
        description: "The company has been updated successfully",
      });
      queryClient.invalidateQueries({ queryKey: [`/api/admin/companies/${companyId}`] });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/companies'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Update failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const deactivateMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("DELETE", `/api/admin/companies/${companyId}`);
      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to deactivate company");
      }
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "Company deactivated",
        description: "The company has been deactivated successfully",
      });
      navigate("/admin/dashboard");
    },
    onError: (error: Error) => {
      toast({
        title: "Deactivation failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const onSubmit = (data: CompanyFormValues) => {
    updateMutation.mutate(data);
  };

  const handleImpersonateCompany = () => {
    if (companyId) {
      impersonateCompanyMutation.mutate(companyId);
      setIsImpersonateDialogOpen(false);
    }
  };

  if (isLoadingAuth) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!user?.isSuperAdmin) {
    return null;
  }

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" onClick={() => navigate("/admin/dashboard")} className="mr-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <h1 className="text-2xl">
              {isLoadingCompany ? "Loading..." : `Company: ${company?.name}`}
            </h1>
          </div>

          {!isLoadingCompany && company && (
            <AlertDialog open={isImpersonateDialogOpen} onOpenChange={setIsImpersonateDialogOpen}>
              <AlertDialogTrigger asChild>
                <Button className="ml-auto btn-brand-primary">
                  <LogIn className="h-4 w-4 mr-2" />
                  Impersonate Company
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent className="max-w-md max-h-[90vh]">
                <AlertDialogHeader>
                  <AlertDialogTitle>Impersonate Company</AlertDialogTitle>
                  <AlertDialogDescription>
                    You are about to log in as an admin user for {company.name}.
                    This will allow you to access the company dashboard and perform actions as a company admin.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className="mt-4">
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction className="btn-brand-primary" onClick={handleImpersonateCompany}>
                    {impersonateCompanyMutation.isPending ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Impersonating...
                      </>
                    ) : (
                      <>
                        <UserCog className="h-4 w-4 mr-2" />
                        Impersonate
                      </>
                    )}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-6">
            <TabsTrigger value="details">Company Details</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="data">Data & Usage</TabsTrigger>
          </TabsList>

          <TabsContent value="details">
            <Card>
              <CardHeader>
                <CardTitle>Company Information</CardTitle>
                <CardDescription>
                  Manage company details and settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingCompany ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <>
                   
                  </>
                )}
                {!isLoadingCompany && (
                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Company Name</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="slug"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Slug</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormDescription>
                                Used for URL and identification
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="logo"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Logo URL</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="primaryColor"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Primary Color</FormLabel>
                              <div className="flex items-center space-x-2">
                                <div
                                  className="w-6 h-6 rounded-full border cursor-pointer"
                                  style={{ backgroundColor: field.value }}
                                  onClick={() => {
                                    const colorInput = document.getElementById('primaryColorPicker') as HTMLInputElement;
                                    if (colorInput) colorInput.click();
                                  }}
                                />
                                <FormControl>
                                  <Input
                                    id="primaryColorPicker"
                                    type="color"
                                    {...field}
                                    className="w-16 h-10 p-1 border rounded cursor-pointer"
                                  />
                                </FormControl>
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder="#333235"
                                    className="flex-1"
                                  />
                                </FormControl>
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="planId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Plan</FormLabel>
                              <Select
                                onValueChange={(value) => {
                                  field.onChange(parseInt(value));
                                  const selectedPlan = plans.find(p => p.id === parseInt(value));
                                  if (selectedPlan) {
                                    form.setValue('maxUsers', selectedPlan.maxUsers);
                                  }
                                }}
                                value={field.value?.toString() || ""}
                                disabled={isLoadingPlans || plans.length === 0}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select a plan" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {isLoadingPlans ? (
                                    <SelectItem value="loading" disabled>
                                      <div className="flex items-center">
                                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                        Loading plans...
                                      </div>
                                    </SelectItem>
                                  ) : plans.length === 0 ? (
                                    <SelectItem value="none" disabled>No plans available</SelectItem>
                                  ) : (
                                    plans.map((plan) => (
                                      <SelectItem key={plan.id} value={plan.id.toString()}>
                                        {plan.name} (${plan.price}/month)
                                      </SelectItem>
                                    ))
                                  )}
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                {field.value ? (
                                  <>
                                    {plans.find(p => p.id === field.value)?.description || ""}
                                  </>
                                ) : "Select a subscription plan for this company"}
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="maxUsers"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Max Users</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value))}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="active"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Active Status</FormLabel>
                              <FormDescription>
                                When inactive, users cannot access the platform
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <div className="flex justify-between">
                        <Button
                          type="button"
                          variant="destructive"
                          onClick={() => {
                            if (confirm("Are you sure you want to deactivate this company? This will prevent all users from accessing the platform.")) {
                              deactivateMutation.mutate();
                            }
                          }}
                          disabled={deactivateMutation.isPending}
                        >
                          {deactivateMutation.isPending ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Deactivating...
                            </>
                          ) : (
                            <>
                              <Trash className="mr-2 h-4 w-4" />
                              Deactivate Company
                            </>
                          )}
                        </Button>

                        <Button
                          type="submit"
                          disabled={updateMutation.isPending}
                          variant="brand"
                          className="btn-brand-primary"
                        >
                          {updateMutation.isPending ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Saving...
                            </>
                          ) : (
                            <>
                              <Save className="mr-2 h-4 w-4" />
                              Save Changes
                            </>
                          )}
                        </Button>
                      </div>
                    </form>
                  </Form>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="users">
            <Card>
              <CardHeader>
                <CardTitle>Company Users</CardTitle>
                <CardDescription>
                  Manage users for this company
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingUsers ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : !companyUsers || companyUsers.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No users found for this company.
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <div className="grid grid-cols-4 p-4 font-medium border-b">
                      <div>Name</div>
                      <div>Email</div>
                      <div>Role</div>
                      <div>Actions</div>
                    </div>
                    <div className="divide-y">
                      {companyUsers.map((user) => (
                        <div key={user.id} className="grid grid-cols-4 p-4 items-center">
                          <div className="font-medium">{user.fullName}</div>
                          <div className="text-muted-foreground">{user.email}</div>
                          <div className="capitalize">{user.role}</div>
                          <div>
                            <Button
                              variant="brand"
                              size="sm"
                            >
                              Edit
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="data">
            <Card>
              <CardHeader>
                <CardTitle>Data & Usage</CardTitle>
                <CardDescription>
                  View company data usage and statistics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  Data usage statistics will be available soon.
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}
