/**
 * Test script to verify Google Calendar integration with AI Assistant
 * This script simulates the AI function calling flow and tests the backend APIs
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:5000';
const TEST_USER_ID = 1;
const TEST_COMPANY_ID = 1;

// Test data
const testEventData = {
  summary: 'Test AI Assistant Booking',
  description: 'This is a test event created by the AI Assistant integration test',
  location: 'Virtual Meeting',
  startDateTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
  endDateTime: new Date(Date.now() + 24 * 60 * 60 * 1000 + 30 * 60 * 1000).toISOString(), // Tomorrow + 30 minutes
  attendees: ['<EMAIL>']
};

async function testCalendarIntegration() {
  console.log('🧪 Starting Google Calendar Integration Test...\n');

  try {
    // Test 1: Check availability
    console.log('📅 Test 1: Checking availability...');
    const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    
    try {
      const availabilityResponse = await axios.get(`${BASE_URL}/api/google/calendar/availability`, {
        params: {
          date: tomorrow,
          duration: 30
        },
        headers: {
          'Authorization': 'Bearer test-token', // You'll need a valid token
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Availability check successful:', availabilityResponse.data);
    } catch (error) {
      console.log('❌ Availability check failed:', error.response?.data || error.message);
    }

    // Test 2: Create calendar event
    console.log('\n📝 Test 2: Creating calendar event...');
    
    try {
      const createResponse = await axios.post(`${BASE_URL}/api/google/calendar/events`, testEventData, {
        headers: {
          'Authorization': 'Bearer test-token', // You'll need a valid token
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Event creation successful:', createResponse.data);
      
      // Store event ID for cleanup
      const eventId = createResponse.data.eventId;
      
      // Test 3: List events to verify creation
      console.log('\n📋 Test 3: Listing events to verify creation...');
      
      const startOfDay = new Date(tomorrow + 'T00:00:00Z').toISOString();
      const endOfDay = new Date(tomorrow + 'T23:59:59Z').toISOString();
      
      try {
        const listResponse = await axios.get(`${BASE_URL}/api/google/calendar/events`, {
          params: {
            timeMin: startOfDay,
            timeMax: endOfDay,
            maxResults: 10
          },
          headers: {
            'Authorization': 'Bearer test-token',
            'Content-Type': 'application/json'
          }
        });
        
        console.log('✅ Event listing successful:', listResponse.data);
        
        // Check if our test event is in the list
        const events = listResponse.data.items || [];
        const testEvent = events.find(event => event.id === eventId);
        
        if (testEvent) {
          console.log('✅ Test event found in calendar:', testEvent.summary);
        } else {
          console.log('⚠️ Test event not found in calendar list');
        }
        
      } catch (error) {
        console.log('❌ Event listing failed:', error.response?.data || error.message);
      }
      
      // Test 4: Clean up - delete the test event
      console.log('\n🗑️ Test 4: Cleaning up test event...');
      
      try {
        const deleteResponse = await axios.delete(`${BASE_URL}/api/google/calendar/events/${eventId}`, {
          headers: {
            'Authorization': 'Bearer test-token',
            'Content-Type': 'application/json'
          }
        });
        
        console.log('✅ Event deletion successful');
        
      } catch (error) {
        console.log('❌ Event deletion failed:', error.response?.data || error.message);
      }
      
    } catch (error) {
      console.log('❌ Event creation failed:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }

  console.log('\n🏁 Test completed!');
}

// Function to simulate AI Assistant function calling
function simulateAIFunctionCall() {
  console.log('\n🤖 Simulating AI Assistant Function Call...\n');
  
  const calendarFunctions = [
    {
      name: 'check_availability',
      arguments: {
        date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        duration: 30
      }
    },
    {
      name: 'book_appointment',
      arguments: {
        title: 'AI Assistant Test Meeting',
        description: 'Test meeting booked by AI Assistant',
        location: 'Virtual',
        start_time: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        end_time: new Date(Date.now() + 24 * 60 * 60 * 1000 + 30 * 60 * 1000).toISOString(),
        attendees: ['<EMAIL>'],
        duration: 30
      }
    }
  ];
  
  console.log('📋 AI Function Calls that should be executed:');
  calendarFunctions.forEach((func, index) => {
    console.log(`${index + 1}. Function: ${func.name}`);
    console.log(`   Arguments:`, JSON.stringify(func.arguments, null, 2));
    console.log('');
  });
  
  console.log('💡 These function calls should be processed by the AI Assistant node');
  console.log('   and executed through the executeCalendarFunction method.');
}

// Run tests
if (require.main === module) {
  console.log('Choose test to run:');
  console.log('1. Test Calendar API endpoints directly');
  console.log('2. Simulate AI function calling');
  console.log('3. Both\n');
  
  const testType = process.argv[2] || '3';
  
  switch (testType) {
    case '1':
      testCalendarIntegration();
      break;
    case '2':
      simulateAIFunctionCall();
      break;
    case '3':
    default:
      simulateAIFunctionCall();
      setTimeout(() => testCalendarIntegration(), 2000);
      break;
  }
}

module.exports = {
  testCalendarIntegration,
  simulateAIFunctionCall
};
