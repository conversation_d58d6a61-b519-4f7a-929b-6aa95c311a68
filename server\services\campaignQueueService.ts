import {
  campaignQueue,
  campaignRecipients,
  campaigns,
  channelConnections,
  contacts
} from '@shared/schema';
import { and, asc, eq, inArray, lte, sql } from 'drizzle-orm';
import path from 'path';
import { db } from '../db';
import {
  Campaign,
  CampaignProcessingResult,
  CampaignQueueMetadata, CampaignStats
} from '../types/campaign';
import { CampaignEventEmitter } from '../utils/websocket';
import { CampaignService } from './campaignService';
import whatsappService from './channels/whatsapp';
import whatsappOfficialService from './channels/whatsapp-official';





interface QueueItem {
  id: number;
  campaign_id: number;
  recipient_id: number;
  account_id: number | null;
  scheduled_for: Date;
  attempts: number | null;
  max_attempts: number | null;
  metadata: unknown;
  priority: number | null;
  campaign_status: string | null;
  campaign_company_id: number | null;
}

interface ChannelConnection {
  id: number;
  userId: number;
  companyId: number | null;
  channelType: string;
  status: string;
}

interface RecipientData {
  id: number;
  contactId: number;
  variables: Record<string, any> | null;
  phone: string | null;
  name: string | null;
  email: string | null;
}





export class CampaignQueueService {
  private campaignService: CampaignService;
  private isProcessing: boolean;
  private processingInterval: NodeJS.Timeout | null;
  private accountRotation: Map<string, number>;

  constructor() {
    this.campaignService = new CampaignService();
    this.isProcessing = false;
    this.processingInterval = null;
    this.accountRotation = new Map<string, number>();
  }

  
  
  

  public startQueueProcessor(): void {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    
    this.processingInterval = setInterval(async () => {
      try {
        await this.processQueue();
        await this.checkCampaignCompletion(); 
      } catch (error) {
        console.error('Queue processing error:', error);
      }
    }, 5000);

    
    setInterval(async () => {
      try {
        await this.recordAnalyticsSnapshots();
      } catch (error) {
        console.error('Analytics recording error:', error);
      }
    }, 300000);
  }

  public stopQueueProcessor(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
    this.isProcessing = false;
  }

  private async processQueue(): Promise<void> {
    try {
      
      const queueItems = await db.select({
        id: campaignQueue.id,
        campaign_id: campaignQueue.campaignId,
        recipient_id: campaignQueue.recipientId,
        account_id: campaignQueue.accountId,
        scheduled_for: campaignQueue.scheduledFor,
        attempts: campaignQueue.attempts,
        max_attempts: campaignQueue.maxAttempts,
        metadata: campaignQueue.metadata,
        priority: campaignQueue.priority,
        campaign_status: campaigns.status,
        campaign_company_id: campaigns.companyId
      })
      .from(campaignQueue)
      .leftJoin(campaigns, eq(campaignQueue.campaignId, campaigns.id))
      .where(and(
        eq(campaignQueue.status, 'pending'),
        lte(campaignQueue.scheduledFor, new Date()),
        eq(campaigns.status, 'running')
      ))
      .orderBy(asc(campaignQueue.priority), asc(campaignQueue.scheduledFor))
      .limit(50);

      if (queueItems.length === 0) {
        return;
      }


      
      const itemsByCompany = this.groupItemsByCompany(queueItems);


      for (const [companyId, items] of Array.from(itemsByCompany.entries())) {
        await this.processCompanyItems(companyId, items);
      }

    } catch (error) {
      console.error('Failed to process queue:', error);
    }
  }

  private groupItemsByCompany(queueItems: QueueItem[]): Map<number, QueueItem[]> {
    const itemsByCompany = new Map<number, QueueItem[]>();

    queueItems.forEach(item => {
      const companyId = item.campaign_company_id;
      if (companyId !== null) {
        if (!itemsByCompany.has(companyId)) {
          itemsByCompany.set(companyId, []);
        }
        itemsByCompany.get(companyId)!.push(item);
      }
    });

    return itemsByCompany;
  }

  private groupItemsByCampaign(queueItems: QueueItem[]): Map<number, QueueItem[]> {
    const itemsByCampaign = new Map<number, QueueItem[]>();

    queueItems.forEach(item => {
      const campaignId = item.campaign_id;
      if (!itemsByCampaign.has(campaignId)) {
        itemsByCampaign.set(campaignId, []);
      }
      itemsByCampaign.get(campaignId)!.push(item);
    });

    return itemsByCampaign;
  }

  private async getCampaignChannelConnection(campaignId: number): Promise<ChannelConnection | null> {
    try {
      
      const [campaign] = await db.select({
        id: campaigns.id,
        channelId: campaigns.channelId,
        channelIds: campaigns.channelIds,
        channelType: campaigns.channelType,
        companyId: campaigns.companyId,
        antiBanSettings: campaigns.antiBanSettings
      })
      .from(campaigns)
      .where(eq(campaigns.id, campaignId));

      if (!campaign) {
        return null;
      }

      const channelIds = campaign.channelIds as number[] || [];
      const antiBanSettings = campaign.antiBanSettings as any || {};

      
     
      
      if (channelIds.length > 0 && antiBanSettings.accountRotation) {
      
        const selectedChannelId = await this.selectOptimalAccount(channelIds, campaign.companyId, antiBanSettings);

        if (!selectedChannelId) {
    
          
          if (campaign.channelId) {
            const [fallbackConnection] = await db.select()
              .from(channelConnections)
              .where(and(
                eq(channelConnections.id, campaign.channelId),
                eq(channelConnections.status, 'active')
              ));

            if (fallbackConnection && fallbackConnection.channelType.includes('whatsapp')) {
              return fallbackConnection as ChannelConnection;
            }
          }

          return null;
        }

        
        const [channelConnection] = await db.select()
          .from(channelConnections)
          .where(and(
            eq(channelConnections.id, selectedChannelId),
            eq(channelConnections.status, 'active')
          ));

        if (!channelConnection) {
          return null;
        }

        return channelConnection as ChannelConnection;
      }

      
      if (!campaign.channelId) {
        return null;
      }

      
      const [channelConnection] = await db.select()
        .from(channelConnections)
        .where(and(
          eq(channelConnections.id, campaign.channelId),
          eq(channelConnections.status, 'active')
        ));

      if (!channelConnection) {
        return null;
      }

      
      if (!channelConnection.channelType.includes('whatsapp')) {
        return null;
      }

      return channelConnection as ChannelConnection;
    } catch (error) {
      console.error(`Failed to get channel connection for campaign ${campaignId}:`, error);
      return null;
    }
  }

  private async selectOptimalAccount(channelIds: number[], companyId: number, antiBanSettings: any): Promise<number | null> {
    try {

      if (!channelIds || channelIds.length === 0) {
        
        return null;
      }


      const availableAccounts: Array<{
        id: number;
        accountName: string;
        status: string;
        updatedAt: Date;
      }> = [];

      for (const channelId of channelIds) {
        try {
          const account = await db.select()
            .from(channelConnections)
            .where(and(
              eq(channelConnections.id, channelId),
              eq(channelConnections.channelType, 'whatsapp_unofficial'),
              eq(channelConnections.status, 'active')
            ))
            .limit(1);

          if (account.length > 0) {
            availableAccounts.push({
              id: account[0].id,
              accountName: account[0].accountName || '',
              status: account[0].status || '',
              updatedAt: account[0].updatedAt || new Date()
            });
          }
        } catch (error) {
          console.error(`Failed to fetch account ${channelId}:`, error);
        }
      }

      if (availableAccounts.length === 0) {
        
        return null;
      }


      
      if (!antiBanSettings.mode || antiBanSettings.mode === 'simple') {
        const selectedAccount = availableAccounts[Math.floor(Math.random() * availableAccounts.length)];
        return selectedAccount.id;
      }

      
      const accountsWithStats = await Promise.all(
        availableAccounts.map(async (account) => {
          try {
            
            const allMessages = await db.select({
              completedAt: campaignQueue.completedAt
            })
            .from(campaignQueue)
            .leftJoin(campaigns, eq(campaignQueue.campaignId, campaigns.id))
            .where(and(
              eq(campaignQueue.accountId, account.id),
              eq(campaignQueue.status, 'completed'),
              eq(campaigns.companyId, companyId)
            ));

            
            const now = new Date();
            const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const hourStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours());

            let messageCountToday = 0;
            let messageCountHour = 0;
            let lastMessageAt: Date | null = null;

            for (const message of allMessages) {
              if (message.completedAt) {
                const completedAt = new Date(message.completedAt);

                
                if (completedAt >= todayStart) {
                  messageCountToday++;
                }

                
                if (completedAt >= hourStart) {
                  messageCountHour++;
                }

                
                if (!lastMessageAt || completedAt > lastMessageAt) {
                  lastMessageAt = completedAt;
                }
              }
            }

            return {
              ...account,
              messageCountToday,
              messageCountHour,
              lastMessageAt
            };
          } catch (error) {
            console.error(`Failed to get stats for account ${account.id}:`, error);
            return {
              ...account,
              messageCountToday: 0,
              messageCountHour: 0,
              lastMessageAt: null
            };
          }
        })
      );

      if (accountsWithStats.length === 0) {
        
        return null;
      }

      
      const scoredAccounts = accountsWithStats.map(account => {
        let score = 100; 

        const messageCountToday = account.messageCountToday || 0;
        const messageCountHour = account.messageCountHour || 0;
        const lastMessageAt = account.lastMessageAt ? new Date(account.lastMessageAt) : null;

        
        const rateLimits = this.getRateLimitsForMode(antiBanSettings.mode);


        
        if (messageCountToday > rateLimits.maxPerDay * 0.8) {
          score -= 50; 
        } else if (messageCountToday > rateLimits.maxPerDay * 0.6) {
          score -= 25; 
        }

        
        if (messageCountHour > rateLimits.maxPerHour * 0.8) {
          score -= 40;
        } else if (messageCountHour > rateLimits.maxPerHour * 0.6) {
          score -= 20;
        }

        
        if (lastMessageAt) {
          const timeSinceLastMessage = Date.now() - lastMessageAt.getTime();
          const cooldownPeriod = (antiBanSettings.cooldownPeriod || 30) * 60 * 1000; 

          if (timeSinceLastMessage > cooldownPeriod) {
            score += 20; 
          } else if (timeSinceLastMessage < cooldownPeriod * 0.5) {
            score -= 15; 
          }
        } else {
          score += 30; 
        }

        return {
          ...account,
          score,
          messageCountToday,
          messageCountHour,
          lastMessageAt
        };
      });

      
      const eligibleAccounts = scoredAccounts.filter(account => {
        const rateLimits = this.getRateLimitsForMode(antiBanSettings.mode);
        return account.messageCountToday < rateLimits.maxPerDay &&
               account.messageCountHour < rateLimits.maxPerHour;
      });

      if (eligibleAccounts.length === 0) {
        

        
        if (scoredAccounts.length > 0) {
          scoredAccounts.sort((a, b) => a.messageCountToday - b.messageCountToday);
          const fallbackAccount = scoredAccounts[0];

          return fallbackAccount.id;
        }

        return null;
      }

      
      eligibleAccounts.sort((a, b) => b.score - a.score);
      const selectedAccount = eligibleAccounts[0];


      return selectedAccount.id;
    } catch (error) {
      console.error('❌ Failed to select optimal account:', error);

      
      try {

        
        for (const channelId of channelIds) {
          try {
            const fallbackAccount = await db.select()
              .from(channelConnections)
              .where(and(
                eq(channelConnections.id, channelId),
                eq(channelConnections.channelType, 'whatsapp_unofficial'),
                eq(channelConnections.status, 'active')
              ))
              .limit(1);

            if (fallbackAccount.length > 0) {
              const account = fallbackAccount[0];
              return account.id;
            }
          } catch (singleAccountError) {
            console.error(`Failed to check account ${channelId}:`, singleAccountError);
          }
        }

      } catch (fallbackError) {
        console.error('Emergency fallback also failed:', fallbackError);
      }

      return null;
    }
  }

  private getRateLimitsForMode(mode: string): { maxPerDay: number; maxPerHour: number; maxPerMinute: number } {
    switch (mode) {
      case 'conservative':
        return { maxPerDay: 500, maxPerHour: 50, maxPerMinute: 2 };
      case 'moderate':
        return { maxPerDay: 1000, maxPerHour: 100, maxPerMinute: 5 };
      case 'aggressive':
        return { maxPerDay: 2000, maxPerHour: 200, maxPerMinute: 10 };
      default:
        return { maxPerDay: 1000, maxPerHour: 100, maxPerMinute: 5 };
    }
  }

  private async processCompanyItems(companyId: number, items: QueueItem[]): Promise<void> {
    try {

      
      const itemsByCampaign = this.groupItemsByCampaign(items);


      for (const [campaignId, campaignItems] of Array.from(itemsByCampaign.entries())) {
        await this.processCampaignItemsBatch(campaignId, campaignItems);
      }

    } catch (error) {
      console.error(`Failed to process company ${companyId} items:`, error);
    }
  }

  /**
   * Process campaign items in batches to improve performance and reduce database load
   */
  private async processCampaignItemsBatch(campaignId: number, items: QueueItem[]): Promise<void> {
    const BATCH_SIZE = 10; 
    const BATCH_DELAY = 1000; 

    try {

      
      const channelConnection = await this.getCampaignChannelConnection(campaignId);
      if (!channelConnection) {
        await this.markItemsAsFailed(items.map(item => item.id), 'No channel connection available');
        return;
      }

      
      if (channelConnection.status !== 'active') {
        await this.markItemsAsFailed(items.map(item => item.id), 'WhatsApp connection is not active');
        return;
      }


      
      const recipientIds = items.map(item => item.recipient_id);
      const recipientData = await this.getRecipientsData(recipientIds);
      const recipientMap = new Map(recipientData.map(r => [r.id, r]));

      
      const [campaignForSettings] = await db.select({
        antiBanSettings: campaigns.antiBanSettings
      })
      .from(campaigns)
      .where(eq(campaigns.id, campaignId));

      const antiBanSettings = campaignForSettings?.antiBanSettings as any || {};

      
      for (let i = 0; i < items.length; i += BATCH_SIZE) {
        const batch = items.slice(i, i + BATCH_SIZE);


        
        for (const item of batch) {
          try {
            const recipient = recipientMap.get(item.recipient_id);
            if (!recipient) {
              
              await this.markItemAsFailed(item.id, 'Recipient not found');
              continue;
            }

            
            await db.update(campaignQueue)
              .set({
                accountId: channelConnection.id,
                status: 'processing',
                startedAt: new Date()
              })
              .where(eq(campaignQueue.id, item.id));

            
            await this.processQueueItemWithData({
              ...item,
              account_id: channelConnection.id
            }, channelConnection, recipient);

            
            await this.addConnectionDelay(channelConnection.id, antiBanSettings);

          } catch (error) {
            console.error(`Error processing queue item ${item.id}:`, error);
            await this.handleItemFailure(item, error instanceof Error ? error.message : 'Unknown error');
          }
        }

        
        if (i + BATCH_SIZE < items.length) {
          await new Promise(resolve => setTimeout(resolve, BATCH_DELAY));
        }
      }

    } catch (error) {
      console.error(`Failed to process campaign ${campaignId} items in batches:`, error);

      await this.markItemsAsFailed(items.map(item => item.id), `Batch processing error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async processQueueItem(queueItem: QueueItem): Promise<CampaignProcessingResult> {
    try {
      
      const [campaign] = await db.select()
        .from(campaigns)
        .where(eq(campaigns.id, queueItem.campaign_id));

      const [recipient] = await db.select({
        id: campaignRecipients.id,
        contactId: campaignRecipients.contactId,
        variables: campaignRecipients.variables,
        phone: contacts.phone,
        name: contacts.name,
        email: contacts.email
      })
        .from(campaignRecipients)
        .leftJoin(contacts, eq(campaignRecipients.contactId, contacts.id))
        .where(eq(campaignRecipients.id, queueItem.recipient_id));

      if (!campaign || !recipient) {
        throw new Error('Campaign or recipient not found');
      }

      const campaignData = campaign;
      const recipientData = recipient as RecipientData;




      const allVariables = {

        name: recipientData.name || '',
        phone: recipientData.phone || '',
        email: recipientData.email || '',

        ...(recipientData.variables || {})
      };


      let personalizedContent = await this.campaignService.personalizeMessage(
        campaignData.content,
        allVariables
      );

      
      const updatedMetadata: CampaignQueueMetadata = {
        ...(queueItem.metadata as CampaignQueueMetadata || {}),
        content: personalizedContent,
        recipient_phone: recipientData.phone || '',
        recipient_name: recipientData.name || ''
      };

      await db.update(campaignQueue)
        .set({ metadata: updatedMetadata })
        .where(eq(campaignQueue.id, queueItem.id));

      
      
      const [channelConnection] = await db.select()
        .from(channelConnections)
        .where(eq(channelConnections.id, queueItem.account_id!));

      if (!channelConnection) {
        throw new Error(`Channel connection not found for connection ${queueItem.account_id}`);
      }

      const connection = channelConnection as ChannelConnection;


      const mediaUrls = (campaignData.mediaUrls as string[]) || [];
      const hasMedia = mediaUrls.length > 0;
      let result: any;

      if (hasMedia) {

        for (const mediaUrl of mediaUrls) {
          const mediaType = this.getMediaTypeFromUrl(mediaUrl);
          const mediaPath = this.getMediaPathFromUrl(mediaUrl);


          if (connection.channelType === 'whatsapp_unofficial') {
            result = await whatsappService.sendMedia(
              connection.id,
              connection.userId,
              recipientData.phone || '',
              mediaType,
              mediaPath,
              personalizedContent,
              path.basename(mediaPath)
            );
          } else if (connection.channelType === 'whatsapp_official') {
            if (!connection.companyId) {
              throw new Error('Company ID is required for WhatsApp Official media messages');
            }
            result = await whatsappOfficialService.sendMedia(
              connection.id,
              connection.userId,
              connection.companyId,
              recipientData.phone || '',
              mediaType,
              mediaUrl,
              personalizedContent,
              undefined,
              undefined,
              true // isFromBot = true for campaign messages
            );
          } else {
            throw new Error(`Unsupported channel type: ${connection.channelType}`);
          }

          
          personalizedContent = '';
        }

        
        if (personalizedContent.trim()) {
          if (connection.channelType === 'whatsapp_unofficial') {
            result = await whatsappService.sendWhatsAppMessage(
              connection.id,
              connection.userId,
              recipientData.phone || '',
              personalizedContent
            );
          } else if (connection.channelType === 'whatsapp_official') {
            if (!connection.companyId) {
              throw new Error('Company ID is required for WhatsApp Official messages');
            }
            result = await whatsappOfficialService.sendMessage(
              connection.id,
              connection.userId,
              connection.companyId,
              recipientData.phone || '',
              personalizedContent
            );
          }
        }
      } else {

        if (connection.channelType === 'whatsapp_unofficial') {
          result = await whatsappService.sendWhatsAppMessage(
            connection.id,
            connection.userId,
            recipientData.phone || '',
            personalizedContent
          );
        } else if (connection.channelType === 'whatsapp_official') {
          if (!connection.companyId) {
            throw new Error('Company ID is required for WhatsApp Official messages');
          }
          result = await whatsappOfficialService.sendMessage(
            connection.id,
            connection.userId,
            connection.companyId,
            recipientData.phone || '',
            personalizedContent
          );
        } else {
          throw new Error(`Unsupported channel type: ${connection.channelType}`);
        }
      }


      
      await db.update(campaignQueue)
        .set({
          status: 'completed',
          completedAt: new Date()
        })
        .where(eq(campaignQueue.id, queueItem.id));

      
      await db.update(campaignRecipients)
        .set({
          status: 'sent',
          sentAt: new Date()
        })
        .where(eq(campaignRecipients.id, queueItem.recipient_id));

      
      await this.updateCampaignStatistics(queueItem.campaign_id);

      
      const progressStats = await this.getCampaignProgress(queueItem.campaign_id);


      CampaignEventEmitter.emitMessageSent(
        queueItem.campaign_id,
        campaignData.companyId,
        progressStats,
        {
          campaignName: campaignData.name,
          recipientPhone: recipientData.phone || '',
          recipientName: recipientData.name || '',
          messageId: result?.messageId || result?.id,
          sentAt: new Date()
        }
      );

      return {
        success: true,
        messageId: result?.messageId || result?.id,
        timestamp: new Date(),
        accountId: queueItem.account_id || undefined,
        recipientPhone: recipientData.phone || ''
      };

    } catch (error) {
      console.error(`Failed to process queue item ${queueItem.id}:`, error);
      throw error;
    }
  }

  /**
   * Process queue item with pre-fetched data to improve performance
   */
  private async processQueueItemWithData(
    queueItem: QueueItem,
    channelConnection: ChannelConnection,
    recipientData: RecipientData
  ): Promise<CampaignProcessingResult> {
    try {
      
      const [campaign] = await db.select()
        .from(campaigns)
        .where(eq(campaigns.id, queueItem.campaign_id));

      if (!campaign) {
        throw new Error('Campaign not found');
      }

      const campaignData = campaign;


      const allVariables = {

        name: recipientData.name || '',
        phone: recipientData.phone || '',
        email: recipientData.email || '',

        ...(recipientData.variables || {})
      };


      let personalizedContent = await this.campaignService.personalizeMessage(
        campaignData.content,
        allVariables
      );


      const updatedMetadata: CampaignQueueMetadata = {
        ...(queueItem.metadata as CampaignQueueMetadata || {}),
        content: personalizedContent,
        recipient_phone: recipientData.phone || '',
        recipient_name: recipientData.name || ''
      };

      await db.update(campaignQueue)
        .set({ metadata: updatedMetadata })
        .where(eq(campaignQueue.id, queueItem.id));


      const mediaUrls = (campaignData.mediaUrls as string[]) || [];
      const hasMedia = mediaUrls.length > 0;
      let result: any;

      if (hasMedia) {

        for (const mediaUrl of mediaUrls) {
          const mediaType = this.getMediaTypeFromUrl(mediaUrl);
          const mediaPath = this.getMediaPathFromUrl(mediaUrl);


          if (channelConnection.channelType === 'whatsapp_unofficial') {
            result = await whatsappService.sendMedia(
              channelConnection.id,
              channelConnection.userId,
              recipientData.phone || '',
              mediaType,
              mediaPath,
              personalizedContent,
              path.basename(mediaPath)
            );
          } else if (channelConnection.channelType === 'whatsapp_official') {
            if (!channelConnection.companyId) {
              throw new Error('Company ID is required for WhatsApp Official media messages');
            }
            result = await whatsappOfficialService.sendMedia(
              channelConnection.id,
              channelConnection.userId,
              channelConnection.companyId,
              recipientData.phone || '',
              mediaType,
              mediaUrl,
              personalizedContent,
              undefined,
              undefined,
              true // isFromBot = true for campaign messages
            );
          } else {
            throw new Error(`Unsupported channel type: ${channelConnection.channelType}`);
          }

          personalizedContent = '';
        }

        
        if (personalizedContent.trim()) {
          if (channelConnection.channelType === 'whatsapp_unofficial') {
            result = await whatsappService.sendWhatsAppMessage(
              channelConnection.id,
              channelConnection.userId,
              recipientData.phone || '',
              personalizedContent
            );
          } else if (channelConnection.channelType === 'whatsapp_official') {
            if (!channelConnection.companyId) {
              throw new Error('Company ID is required for WhatsApp Official messages');
            }
            result = await whatsappOfficialService.sendMessage(
              channelConnection.id,
              channelConnection.userId,
              channelConnection.companyId,
              recipientData.phone || '',
              personalizedContent
            );
          }
        }
      } else {

        if (channelConnection.channelType === 'whatsapp_unofficial') {
          result = await whatsappService.sendWhatsAppMessage(
            channelConnection.id,
            channelConnection.userId,
            recipientData.phone || '',
            personalizedContent
          );
        } else if (channelConnection.channelType === 'whatsapp_official') {
          if (!channelConnection.companyId) {
            throw new Error('Company ID is required for WhatsApp Official messages');
          }
          result = await whatsappOfficialService.sendMessage(
            channelConnection.id,
            channelConnection.userId,
            channelConnection.companyId,
            recipientData.phone || '',
            personalizedContent
          );
        } else {
          throw new Error(`Unsupported channel type: ${channelConnection.channelType}`);
        }
      }


      
      await db.update(campaignQueue)
        .set({
          status: 'completed',
          completedAt: new Date()
        })
        .where(eq(campaignQueue.id, queueItem.id));

      
      await db.update(campaignRecipients)
        .set({
          status: 'sent',
          sentAt: new Date()
        })
        .where(eq(campaignRecipients.id, queueItem.recipient_id));

      
      await this.updateCampaignStatistics(queueItem.campaign_id);

      
      const progressStats = await this.getCampaignProgress(queueItem.campaign_id);


      CampaignEventEmitter.emitMessageSent(
        queueItem.campaign_id,
        campaignData.companyId,
        progressStats,
        {
          campaignName: campaignData.name,
          recipientPhone: recipientData.phone || '',
          recipientName: recipientData.name || '',
          messageId: result?.messageId || result?.id,
          sentAt: new Date()
        }
      );

      return {
        success: true,
        messageId: result?.messageId || result?.id,
        timestamp: new Date(),
        accountId: queueItem.account_id || undefined,
        recipientPhone: recipientData.phone || ''
      };

    } catch (error) {
      console.error(`Failed to process queue item ${queueItem.id}:`, error);
      throw error;
    }
  }

  /**
   * Get recipient data for multiple recipients in a single query
   */
  private async getRecipientsData(recipientIds: number[]): Promise<RecipientData[]> {
    try {
      if (recipientIds.length === 0) return [];

      const recipients = await db.select({
        id: campaignRecipients.id,
        contactId: campaignRecipients.contactId,
        variables: campaignRecipients.variables,
        phone: contacts.phone,
        name: contacts.name,
        email: contacts.email
      })
      .from(campaignRecipients)
      .leftJoin(contacts, eq(campaignRecipients.contactId, contacts.id))
      .where(inArray(campaignRecipients.id, recipientIds));

      return recipients.map(r => ({
        id: r.id,
        contactId: r.contactId,
        variables: r.variables as Record<string, any> | null,
        phone: r.phone || '',
        name: r.name || '',
        email: r.email || ''
      }));
    } catch (error) {
      console.error('Error fetching recipients data:', error);
      return [];
    }
  }

  /**
   * Mark multiple items as failed in a single query
   */
  private async markItemsAsFailed(itemIds: number[], errorMessage: string): Promise<void> {
    try {
      if (itemIds.length === 0) return;

      await db.update(campaignQueue)
        .set({
          status: 'failed',
          errorMessage: errorMessage,
          completedAt: new Date(),
          attempts: sql`attempts + 1`
        })
        .where(inArray(campaignQueue.id, itemIds));

    } catch (error) {
      console.error('Error marking items as failed:', error);
    }
  }

  /**
   * Mark a single item as failed
   */
  private async markItemAsFailed(itemId: number, errorMessage: string): Promise<void> {
    await this.markItemsAsFailed([itemId], errorMessage);
  }

  private async handleItemFailure(queueItem: QueueItem, errorMessage: string): Promise<void> {
    try {
      const attempts = (queueItem.attempts || 0) + 1;
      const maxAttempts = queueItem.max_attempts || 3;

      if (attempts >= maxAttempts) {
        
        await db.update(campaignQueue)
          .set({
            status: 'failed',
            errorMessage: errorMessage,
            attempts: attempts,
            lastErrorAt: new Date()
          })
          .where(eq(campaignQueue.id, queueItem.id));

        
        await db.update(campaignRecipients)
          .set({
            status: 'failed',
            failedAt: new Date(),
            errorMessage: errorMessage
          })
          .where(eq(campaignRecipients.id, queueItem.recipient_id));

        
        await this.updateCampaignStatistics(queueItem.campaign_id);

        
        const [campaignData] = await db.select({
          id: campaigns.id,
          name: campaigns.name,
          companyId: campaigns.companyId
        })
        .from(campaigns)
        .where(eq(campaigns.id, queueItem.campaign_id));

        if (campaignData) {
          
          const progressStats = await this.getCampaignProgress(queueItem.campaign_id);


          CampaignEventEmitter.emitMessageFailed(
            queueItem.campaign_id,
            campaignData.companyId,
            progressStats,
            errorMessage,
            {
              campaignName: campaignData.name
            }
          );
        }

      } else {
        
        const retryDelay = Math.pow(2, attempts) * 60000; 
        const retryTime = new Date(Date.now() + retryDelay);

        await db.update(campaignQueue)
          .set({
            status: 'pending',
            scheduledFor: retryTime,
            errorMessage: errorMessage,
            attempts: attempts,
            lastErrorAt: new Date()
          })
          .where(eq(campaignQueue.id, queueItem.id));
      }

    } catch (error) {
      console.error('Failed to handle item failure:', error);
    }
  }

  private async addConnectionDelay(_connectionId: number, antiBanSettings?: any): Promise<void> {
    try {
      let baseDelay = 6000; 
      let randomRange = [1000, 3000]; 

      
      if (antiBanSettings) {
        if (antiBanSettings.randomizeDelay) {
          const minDelay = (antiBanSettings.minDelay || 3) * 1000; 
          const maxDelay = (antiBanSettings.maxDelay || 15) * 1000; 
          randomRange = [minDelay, maxDelay];
          baseDelay = 0; 
        }

        
        switch (antiBanSettings.mode) {
          case 'conservative':
            baseDelay = Math.max(baseDelay, 10000); 
            break;
          case 'moderate':
            baseDelay = Math.max(baseDelay, 6000); 
            break;
          case 'aggressive':
            baseDelay = Math.max(baseDelay, 3000); 
            break;
        }

        
        if (antiBanSettings.businessHoursOnly) {
          const now = new Date();
          const hour = now.getHours();

          
          if (hour < 9 || hour >= 18) {
            baseDelay += 300000; 
          }
        }

        
        if (antiBanSettings.respectWeekends) {
          const now = new Date();
          const dayOfWeek = now.getDay();

          
          if (dayOfWeek === 0 || dayOfWeek === 6) {
            baseDelay += 600000; 
          }
        }
      }

      const randomDelay = Math.floor(
        Math.random() * (randomRange[1] - randomRange[0] + 1)
      ) + randomRange[0];

      const totalDelay = baseDelay + randomDelay;

      await new Promise<void>(resolve => setTimeout(resolve, totalDelay));
    } catch (error) {
      console.error('Failed to add connection delay:', error);
    }
  }

  
  
  

  private async recordAnalyticsSnapshots(): Promise<void> {
    try {
      
      const runningCampaigns = await db.select()
        .from(campaigns)
        .where(eq(campaigns.status, 'running'));

      for (const campaign of runningCampaigns) {
        try {
          await this.campaignService.recordAnalyticsSnapshot(campaign.id);
        } catch (error) {
          console.error(`Failed to record analytics for campaign ${campaign.id}:`, error);
        }
      }

    } catch (error) {
      console.error('Failed to record analytics snapshots:', error);
    }
  }

  public async checkCampaignCompletion(): Promise<void> {
    try {
      
      const potentiallyCompletedCampaigns = await db.select()
        .from(campaigns)
        .where(eq(campaigns.status, 'running'));
      for (const campaign of potentiallyCompletedCampaigns) {
        try {
          
          const queueStats = await db.select({
            total: sql`COUNT(*)`,
            pending: sql`COUNT(*) FILTER (WHERE status = 'pending')`,
            processing: sql`COUNT(*) FILTER (WHERE status = 'processing')`,
            completed: sql`COUNT(*) FILTER (WHERE status = 'completed')`,
            failed: sql`COUNT(*) FILTER (WHERE status = 'failed')`,
            cancelled: sql`COUNT(*) FILTER (WHERE status = 'cancelled')`
          })
          .from(campaignQueue)
          .where(eq(campaignQueue.campaignId, campaign.id));

          const stats = queueStats[0];
          if (!stats) {
            continue; // Skip if no stats found
          }

          const totalItems = parseInt(String(stats.total));
          const pendingItems = parseInt(String(stats.pending));
          const processingItems = parseInt(String(stats.processing));
          const completedItems = parseInt(String(stats.completed));
          const failedItems = parseInt(String(stats.failed));


          
          const activePendingItems = pendingItems + processingItems;

          if (totalItems > 0 && activePendingItems === 0) {
            
            await db.update(campaigns)
              .set({
                status: 'completed',
                completedAt: new Date()
              })
              .where(eq(campaigns.id, campaign.id));

            
            await this.campaignService.recordAnalyticsSnapshot(campaign.id);


            
            CampaignEventEmitter.emitCampaignCompleted(
              campaign.id,
              campaign.companyId,
              campaign.name,
              {
                totalRecipients: totalItems,
                successfulSends: completedItems,
                failedSends: failedItems
              }
            );
          }
        } catch (error) {
          console.error(`Failed to check completion for campaign ${campaign.id}:`, error);
        }
      }
    } catch (error) {
      console.error('Failed to check campaign completion:', error);
    }
  }

  
  
  

  public async getQueueStats(companyId: number): Promise<CampaignStats> {
    try {
      const stats = await db.select({
        total: sql`COUNT(*)`,
        pending: sql`COUNT(*) FILTER (WHERE status = 'pending')`,
        processing: sql`COUNT(*) FILTER (WHERE status = 'processing')`,
        completed: sql`COUNT(*) FILTER (WHERE status = 'completed')`,
        failed: sql`COUNT(*) FILTER (WHERE status = 'failed')`,
        cancelled: sql`COUNT(*) FILTER (WHERE status = 'cancelled')`
      })
      .from(campaignQueue)
      .leftJoin(campaigns, eq(campaignQueue.campaignId, campaigns.id))
      .where(eq(campaigns.companyId, companyId));

      const result = stats[0];
      if (!result) {
        return {
          total: 0,
          pending: 0,
          processing: 0,
          completed: 0,
          failed: 0,
          cancelled: 0,
          paused: 0
        };
      }

      return {
        total: parseInt(String(result.total)),
        pending: parseInt(String(result.pending)),
        processing: parseInt(String(result.processing)),
        completed: parseInt(String(result.completed)),
        failed: parseInt(String(result.failed)),
        cancelled: parseInt(String(result.cancelled)),
        paused: 0
      };
    } catch (error) {
      throw new Error(`Failed to get queue stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async clearFailedQueueItems(companyId: number, olderThanDays: number = 7): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);


      const companyCampaigns = await db.select({ id: campaigns.id })
        .from(campaigns)
        .where(eq(campaigns.companyId, companyId));

      const campaignIds = companyCampaigns.map(c => c.id);

      if (campaignIds.length === 0) {
        return 0;
      }

      const result = await db.delete(campaignQueue)
        .where(and(
          eq(campaignQueue.status, 'failed'),
          inArray(campaignQueue.campaignId, campaignIds),
          sql`${campaignQueue.lastErrorAt} IS NOT NULL AND ${campaignQueue.lastErrorAt} < ${cutoffDate}`
        ));

      return (result as any).rowCount || 0;
    } catch (error) {
      throw new Error(`Failed to clear failed queue items: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async pauseCampaignQueue(campaignId: number): Promise<void> {
    try {

      await db.update(campaigns)
        .set({
          status: 'paused',
          pausedAt: new Date()
        })
        .where(eq(campaigns.id, campaignId));

    } catch (error) {
      throw new Error(`Failed to pause campaign queue: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async resumeCampaignQueue(campaignId: number): Promise<void> {
    try {

      await db.update(campaigns)
        .set({
          status: 'running',
          pausedAt: null
        })
        .where(eq(campaigns.id, campaignId));

    } catch (error) {
      throw new Error(`Failed to resume campaign queue: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async cancelCampaignQueue(campaignId: number): Promise<void> {
    try {

      await db.update(campaignQueue)
        .set({
          status: 'cancelled'
        })
        .where(and(
          eq(campaignQueue.campaignId, campaignId),
          eq(campaignQueue.status, 'pending')
        ));


      await db.update(campaigns)
        .set({
          status: 'cancelled'
        })
        .where(eq(campaigns.id, campaignId));

    } catch (error) {
      throw new Error(`Failed to cancel campaign queue: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  
  
  

  private async updateCampaignStatistics(campaignId: number): Promise<void> {
    try {
      
      const recipientStats = await db.select({
        total: sql`COUNT(*)`,
        sent: sql`COUNT(*) FILTER (WHERE status IN ('sent', 'delivered', 'read'))`,
        failed: sql`COUNT(*) FILTER (WHERE status = 'failed')`,
        pending: sql`COUNT(*) FILTER (WHERE status IN ('pending', 'processing'))`
      })
      .from(campaignRecipients)
      .where(eq(campaignRecipients.campaignId, campaignId));

      const stats = recipientStats[0];
      const totalRecipients = parseInt(String(stats.total)) || 0;
      const successfulSends = parseInt(String(stats.sent)) || 0;
      const failedSends = parseInt(String(stats.failed)) || 0;
      const processedRecipients = successfulSends + failedSends;

      
      await db.update(campaigns)
        .set({
          totalRecipients,
          processedRecipients,
          successfulSends,
          failedSends,
          updatedAt: new Date()
        })
        .where(eq(campaigns.id, campaignId));

    } catch (error) {
      console.error(`Failed to update campaign statistics for campaign ${campaignId}:`, error);
    }
  }

  
  
  

  private async getCampaignProgress(campaignId: number): Promise<{
    totalRecipients: number;
    processedRecipients: number;
    successfulSends: number;
    failedSends: number;
    progressPercentage: number;
  }> {
    try {
      
      const [campaign] = await db.select({
        totalRecipients: campaigns.totalRecipients,
        processedRecipients: campaigns.processedRecipients,
        successfulSends: campaigns.successfulSends,
        failedSends: campaigns.failedSends
      })
      .from(campaigns)
      .where(eq(campaigns.id, campaignId));

      if (!campaign) {
        throw new Error('Campaign not found');
      }

      const totalRecipients = campaign.totalRecipients || 0;
      const processedRecipients = campaign.processedRecipients || 0;
      const successfulSends = campaign.successfulSends || 0;
      const failedSends = campaign.failedSends || 0;

      const progressPercentage = totalRecipients > 0
        ? Math.round((processedRecipients / totalRecipients) * 100)
        : 0;

      return {
        totalRecipients,
        processedRecipients,
        successfulSends,
        failedSends,
        progressPercentage
      };
    } catch (error) {
      console.error('Failed to get campaign progress:', error);
      return {
        totalRecipients: 0,
        processedRecipients: 0,
        successfulSends: 0,
        failedSends: 0,
        progressPercentage: 0
      };
    }
  }

  
  
  
  

  
  
  

  public getProcessingStatus(): { isProcessing: boolean; accountRotationSize: number } {
    return {
      isProcessing: this.isProcessing,
      accountRotationSize: this.accountRotation.size
    };
  }

  public clearAccountRotation(): void {
    this.accountRotation.clear();
  }

  
  
  

  private getMediaTypeFromUrl(mediaUrl: string): 'image' | 'video' | 'audio' | 'document' {
    const urlPath = mediaUrl.toLowerCase();

    if (urlPath.includes('/image/') || urlPath.match(/\.(jpg|jpeg|png|gif|webp)$/)) {
      return 'image';
    } else if (urlPath.includes('/video/') || urlPath.match(/\.(mp4|avi|mov|wmv|3gp)$/)) {
      return 'video';
    } else if (urlPath.includes('/audio/') || urlPath.match(/\.(mp3|wav|ogg|m4a|aac)$/)) {
      return 'audio';
    } else {
      return 'document';
    }
  }

  private getMediaPathFromUrl(mediaUrl: string): string {
    
    
    
    const relativePath = mediaUrl.startsWith('/') ? mediaUrl.slice(1) : mediaUrl;
    return path.join(process.cwd(), relativePath);
  }
}
